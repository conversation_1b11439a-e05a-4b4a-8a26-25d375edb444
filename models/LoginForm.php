<?php

namespace app\models;

use app\controllers\ProviderController;
use app\helpers\GeneralController;
use Yii;
use yii\base\Model;
use yii\web\Controller;

/**
 * LoginForm is the model behind the login form.
 *
 *
 */
class LoginForm extends Model
{
    public $username;
    public $password;
    public $email;
    public $rememberMe = true;
    public $code;
    public $generateCookie;
    public $cookie_value;
    public $user_id;
    public $save_browser, $rfc, $token_fiel;

    public $es_funcionario = false;

    private $_user = false;


    /**
     * @return array the validation rules.
     */
    public function rules()
    {
        return [
            // username and password are both required
            [['username', 'password'], 'required'],
            [['code','cookie_value','user_id'],'string'],
            // rememberMe must be a boolean value
            [['rememberMe','generateCookie','save_browser', 'es_funcionario'], 'boolean'],
            // password is validated by validatePassword()
            ['password', 'validatePassword'],
            //['password', 'match', 'pattern' => '/^(?=\w*\d)(?=\w*[A-Z])(?=\w*[a-z])\S{6,16}$/', 'message'=>'La contraseña debe contener al menos una mayúscula, una minúscula y un número.'],
            
        ];
    }
    public function attributeLabels()
    {
        return [
            'username' => 'Usuario',
            'password' => 'Contraseña',
            'rememberMe' => 'Recordar usuario',
            'es_funcionario' => '¿Es funcionario?',
            'generateCookie' => '',
            'code' => 'Código'
        ];
    }
    /**
     * Validates the password.
     * This method serves as the inline validation for password.
     *
     * @param string $attribute the attribute currently being validated
     * @param array $params the additional name-value pairs given in the rule
     */
    public function validatePassword($attribute, $params)
    {
        if (!$this->hasErrors()) {
            $user = $this->getUser();

            if (!$user || !$user->validatePassword($this->password)) {
                $this->addError($attribute, 'Usuario y/o contraseña incorrecta.');
            }
        }
    }

    /**
     * Logs in a user using the provided username and password.
     * @return boolean whether the user is logged in successfully
     */
    public function login()
    {
        if ($this->validate()) {
            return Yii::$app->user->login($this->getUser(), $this->rememberMe ? 3600*2 : 0);
        }
        if($this->isLoginFiel()){
            $rfc = $_POST['LoginForm']['rfc'];

            // Log para debugging
            Yii::info([
                'attempting_fiel_login' => true,
                'rfc' => $rfc,
                'token' => $_POST['LoginForm']['token_fiel']
            ], 'fiel.login');

            // Buscar usuario directamente por RFC
            $this->_user = Usuarios::find()->where(['rfc'=>$rfc])->one();

            // Si no se encuentra, buscar en Provider
            if(!$this->_user){
                $prov = Provider::find()->where(['rfc'=>$rfc])->one();
                if($prov) {
                    $this->_user = Usuarios::findOne($prov['user_id']);
                }
            }

            // Si no se encuentra, buscar en tabla Rfc
            if(!$this->_user){
                $rfcModel = Rfc::find()->where(['rfc'=>$rfc])->one();
                if($rfcModel) {
                    $prov = Provider::findOne($rfcModel['provider_id']);
                    if($prov) {
                        $this->_user = Usuarios::findOne($prov->user_id);
                    }
                }
            }

            if(!$this->_user){
                Yii::info([
                    'fiel_login_failed' => true,
                    'rfc' => $rfc,
                    'reason' => 'user_not_found'
                ], 'fiel.login');

                Yii::$app->session->setFlash('error',"Usuario no registrado en la plataforma con RFC: $rfc. Revise sus datos o regístrese para continuar.");
                return false;
            }
            //solamente se puede comparar el nombre o razon social cuando no esté en validación el expediente
            $provider = Provider::findOne(['user_id' => $this->_user->user_id ]);
            $enRevision = Asignacion::find()->where(['and',['id_proveedor'=>$provider->provider_id],['activo'=>true]])->asArray()->one();
            if(!$enRevision)
                $this->validacionRazonSocial($this->_user->user_id);
            return Yii::$app->user->login($this->_user,3600*24*3 );
        }
        return false;
    }

    /** 
     * Funcion que valida si hay algun cambio de razon social en la FIEL al momento de loggearse
     * @param int user_id - Id del usuario a validar
     * @return void
    */
    public function validacionRazonSocial($user_id){
        if(isset($_POST['LoginForm']['rfc']) && isset($_POST['LoginForm']['token_fiel'])){
            $data = $this->getDataFromFIEL($_POST['LoginForm']['token_fiel']); //
            $rfc_fiel = trim($_POST['LoginForm']['rfc']);
            $isPersonaMoral = (strlen($rfc_fiel) == 12) ? true : false;
            if($isPersonaMoral){
                $razon_social_fiel = ProviderController::traduce($data['CN']);
                $provider = Provider::findOne(['user_id' => $user_id ]);
                if ( !is_null($provider) && (strtoupper($razon_social_fiel) != strtoupper($provider->name_razon_social))){
                    $provider->name_razon_social = $razon_social_fiel;
                    if($provider->status_carta_bys == Status::CARTA_FIRMADA){
                        $provider->status_carta_bys = Status::CARTA_GENERADA;
                    }
                    $provider->save();
                    GeneralController::updateModulesBys($provider->provider_id, 'bys_perfil');
                }
            }            
        }
    }

    /**
     * Finds user by [[username]]
     *
     * @return User|null
     */
    public function getUser($funcionario = false)
    {
        if ($this->_user === false) {
            $this->_user = Usuarios::findByUsername($this->username, $funcionario);
        }

        return $this->_user;
    }

    public function esFuncionario(){
        if (!$this->hasErrors()) {
            $user = $this->getUser();

            return $user->es_funcionario;
        }

        return false;
    }

    public function verifyUserAndPass($funcionario = false){

        if(!$this->hasErrors()){
            $user = $this->getUser($funcionario);

            return $user && ($user->validatePassword($this->password) || $funcionario);
        }

        return false;

    }

    public function isLoginFiel(){
        // Log para debugging
        Yii::info([
            'checking_fiel_login' => true,
            'post_data' => $_POST,
            'has_rfc' => isset($_POST['LoginForm']['rfc']),
            'has_token' => isset($_POST['LoginForm']['token_fiel'])
        ], 'fiel.login');

        if(isset($_POST['LoginForm']['rfc']) && isset($_POST['LoginForm']['token_fiel'])){
            $data = $this->getDataFromFIEL($_POST['LoginForm']['token_fiel']);
            $expectedRFC = trim($_POST['LoginForm']['rfc']);

            // Log datos extraídos
            Yii::info([
                'extracted_data' => $data,
                'expected_rfc' => $expectedRFC
            ], 'fiel.login');

            // Buscar RFC en diferentes posiciones posibles
            $possibleRFCs = [
                isset($data['undefined2']) ? trim($data['undefined2']) : '',
                isset($data['undefined1']) ? trim($data['undefined1']) : '',
                isset($data['undefined0']) ? trim($data['undefined0']) : '',
                isset($data['SN']) ? trim($data['SN']) : '',
                isset($data['serialNumber']) ? trim($data['serialNumber']) : ''
            ];

            Yii::info([
                'possible_rfcs' => $possibleRFCs
            ], 'fiel.login');

            foreach ($possibleRFCs as $rfc) {
                if (!empty($rfc) && $rfc === $expectedRFC) {
                    Yii::info(['fiel_match_found' => true, 'matched_rfc' => $rfc], 'fiel.login');
                    return true;
                }
                // También verificar si el RFC está contenido en el campo (para casos como OASH970823HVZLLR00)
                if (!empty($rfc) && strpos($rfc, $expectedRFC) === 0) {
                    Yii::info(['fiel_partial_match_found' => true, 'matched_rfc' => $rfc], 'fiel.login');
                    return true;
                }
            }

            Yii::info(['fiel_no_match_found' => true], 'fiel.login');
        }
        return false;
    }

    public function getDataFromFIEL($subject){
        $split = explode('/',$subject);
        $data = [];
        $und = 0;
        $non = 0;

        for ($i = 0; $i < count($split); $i++) {
            $part = trim($split[$i]);
            if (empty($part)) continue;

            $aux = explode('=',$part, 2); // Limitar a 2 partes para manejar valores con =
            if(count($aux) == 1) {
                $data['und'.($non++)] = $aux[0];
            } else {
                $key = $aux[0];
                $value = $aux[1];

                // Manejar campos undefined con numeración
                if ($key == 'undefined') {
                    $data['undefined'.($und++)] = $value;
                } else {
                    $data[$key] = $value;
                }
            }
        }

        // Debug: log para ver qué datos se están extrayendo
        Yii::info([
            'subject' => $subject,
            'parsed_data' => $data
        ], 'fiel.login');

        return $data;
    }


    /*
        public function verifyUserAndPass(){

        if (!$this->hasErrors()) {
            $user = $this->getUser();
            $error = 'Nombre de usuario incorrecto';
            if($user){
                if ($user->validatePassword($this->password)) {
                    return true;
                }
                $error = 'Contraseña incorrecta';
            }
            Yii::$app->session->setFlash('error', $error);
            return false;
        }

    }



     * */
}
