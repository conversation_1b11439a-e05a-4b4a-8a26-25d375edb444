<?php

namespace app\commands;

use app\helpers\GeneralController;
use app\models\AccountDelete;
use app\models\Calendar;
use app\models\ErrorInesperado;
use app\models\FirstUpdateCertificate;
use app\models\HistoricoCertificados;
use app\models\Movements;
use app\models\NonWorkingDays;
use app\models\NotAssist;
use app\models\Provider;
use app\models\Token;
use app\models\Usuarios;
use app\models\VerifyUserSecop;
use yii\console\Controller;
use yii\helpers\ArrayHelper;

class CitasController extends Controller
{
    public function actionCitas()
    {

        $date = $this->yesterday();

        Calendar::updateAll(['status' => 'NO ASISTIO', 'status_visit' => 'NO ASISTIO'], ['and', ['visit_day' => $date], ['status' => 'CONFIRMADA']]);

        $user = ArrayHelper::getColumn(Calendar::find()->select('user')->where(['and', ['visit_day' => $date], ['status' => 'NO ASISTIO']])->all(), 'user');

        if (!empty($user)) {
            foreach ($user as $val) {
                if (($model = NotAssist::find()->where(['user_id' => $val])->one()) !== null) {
                    $model->number_not_assist = $model->number_not_assist + 1;
                    $model->save(false);
                } else {
                    $model = new NotAssist();
                    $model->user_id = $val;
                    $model->number_not_assist = 1;
                    $model->save();
                }
            }
        }
    }

    public function yesterday()
    {
        $date = date('Y-m-d');
        $nuevafecha = strtotime('-1 day', strtotime($date));
        $nuevafecha = date('Y-m-d', $nuevafecha);
        return $nuevafecha;
    }


    public function actionDiasFestivos()
    {

        $data = ['username' => 'fun1', 'password' => 'prueba123'];

        $ch = curl_init('https://insumos.nl.gob.mx/api/auth');
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $data);

        $response = curl_exec($ch);
        curl_close($ch);

        $res = json_decode($response);

        if (isset($res->token) && !empty($res->token)) {
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, 'https://insumos.nl.gob.mx/api/disableday' . '?access_token=' . $res->token); //Url together with parameters
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1); //Return data instead printing directly in Browser
            curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 7); //Timeout after 7 seconds
            curl_setopt($ch, CURLOPT_HEADER, 0);

            $result = curl_exec($ch);
            curl_close($ch);
            $resDay = json_decode($result);

            if (isset($resDay->data) && !empty($resDay->data)) {
                foreach ($resDay->data as $val) {
                    if (($count = NonWorkingDays::find()->where(['and', ['non_working_day' => $val], ['holiday' => true], ['user_type' => 'ADMIN']])->count('1')) == 0) {
                        $model = new NonWorkingDays();
                        $model->non_working_day = $val;
                        $model->holiday = true;
                        $model->user_type = 'ADMIN';
                        $model->save();
                    }
                }
            }
        }

    }


    public function actionVerifyProv()
    {

        /*

        select p.user_id as clave,p.\"Clave_ProveedorSire\" as clave_sire, p.name_comercial as nombre,
        u.username as usuario,u.pass_tmp as contrasena,u.email as correo, p.rfc as rfcprov
        from usuarios u
        join provider p using(user_id)
        join verify_user_secop vu on vu.user_id = u.user_id
        where secop is false and role = 'PROVIDER' and status = 'ACTIVO' and p.rfc is not null
         * */
        $dataProvider = \Yii::$app->db->createCommand("
                select p.user_id as clave,p.\"Clave_ProveedorSire\" as clave_sire, CASE WHEN (p.name_razon_social is not null and p.name_razon_social != '') then p.name_razon_social else concat_ws(' ',p.pf_ap_paterno,p.pf_ap_materno,p.pf_nombre) end as nombre,
                    u.username as usuario,u.pass_tmp as contrasena,u.email as correo, p.rfc as rfcprov
                    from usuarios u
                    join provider p using(user_id)
                    join verify_user_secop vu on vu.user_id = u.user_id
                    where secop is false and role = 'PROVIDER' and status = 'ACTIVO' and p.rfc is not null")->queryAll();

        if ($dataProvider) {
            foreach ($dataProvider as $val) {
                $user_id = $val['clave'];
                try {
                    $request = new \SoapClient(
                        'http://sistemas.nl.gob.mx/wsSA_ProveedoresSU/wsProveedores.asmx?WSDL', array('exceptions' => true));

                    //$userResponseDecode = $request->ConsultaRFC(['rfc' => $val['rfcprov'],'prov_pro_tipoproveedor'=>1]);
                    $userResponseDecode = null;
                    if (isset($userResponseDecode->ConsultaRFCResult) && !empty($userResponseDecode->ConsultaRFCResult)) {
                        $userResponseData = json_decode($userResponseDecode->ConsultaRFCResult, true);
                        if (isset($userResponseData[0]) && isset($userResponseData[0]['estatus']) && ($userResponseData[0]['estatus'] || $userResponseData[0]['estatus'] == '1')) {
                            if (isset($userResponseData[0]['Clave_ProveedorSire']) && $val['clave_sire'] == '') {
                                $val['clave_sire'] = $userResponseData[0]['Clave_ProveedorSire'];
                                Provider::updateAll(['Clave_ProveedorSire' => $userResponseData[0]['Clave_ProveedorSire']], ['user_id' => $user_id]);
                            }

                            if ($val['clave_sire']) {
                                $val['usuario'] = $val['clave_sire'];
                                $dataInsertSecop = json_decode($this->insertProvSecop($val, $user_id), true);
                                if ($dataInsertSecop['msg'] == 'success') {
                                    Usuarios::updateAll(['secop' => true], ['user_id' => $user_id]);
                                    VerifyUserSecop::deleteAll(['user_id' => $user_id]);
                                }
                            }

                        }
                    } else {
                        $error = new ErrorInesperado();
                        $error->data = $userResponseDecode;
                        $error->user_id = $user_id;
                        $error->tipo_error = 'Consultar proveedor SECOP-SIRE No retorno variable esperada';
                        $error->save();
                    }

                } catch (\Exception $e) {
                    $error = new ErrorInesperado();
                    $error->data = $userResponseDecode;
                    $error->user_id = $user_id;
                    $error->tipo_error = 'Error inesperado';
                    $error->save();
                }
            }
        }
    }


    public function insertProvSecop($data, $user_id)
    {
        try {
            unset($data['clave']);
            unset($data['provider_id']);

            $request = new \SoapClient(
                'http://sistemas.nl.gob.mx/wsSA_ProveedoresSU/wsProveedores.asmx?WSDL', array('exceptions' => true));


            $provResponseDecode = $request->InsertaProv($data);

            $error = new ErrorInesperado();
            $error->data = $provResponseDecode;
            $error->user_id = $user_id;

            if (isset($provResponseDecode->InsertaProvResult) && !empty($provResponseDecode->InsertaProvResult)) {

                $userResponseData = json_decode($provResponseDecode->InsertaProvResult, true);
                if ($userResponseData['status'] == 'success') {
                    $error->tipo_error = 'Insertar proveedor SECOP Success';
                    $error->save();
                    return json_encode(['status' => true, 'msg' => $userResponseData['status']]);
                }

            }
            $error->tipo_error = 'Insertar proveedor SECOP Error';
            $error->save();
            return json_encode(['status' => false, 'msg' => 'Error inesperado']);

        } catch (\Exception $e) {
            $error = new ErrorInesperado();
            $error->tipo_error = 'Insertar proveedor SECOP Exception';
            $error->data = $e;
            $error->user_id = $user_id;
            $error->save();

            return json_encode(['status' => false, 'msg' => 'Error inesperado']);
        }
    }

    public function actionCancelAccount()
    {

        $tokens = \Yii::$app->db->createCommand("
            with ct as
            (
            select distinct 
                created_at,user_id,
                row_number() over (partition by user_id order by created_at desc) rn
            from
                token where type = 0 order by created_at
            )
              select ct.created_at,ct.user_id,p.rfc,u.email
              from ct
              left join provider p on p.user_id = ct.user_id
              join usuarios u on u.user_id = ct.user_id
              where rn = 1")->queryAll();

        foreach ($tokens as $t) {
            $userId = $t['user_id'];
            $rfc = $t['rfc'];
            $email = $t['email'];
            $registrationDate = date("Y-m-d", $t['created_at']);

            $current = strtotime(date('Y-m-d'));
            $fourMonths = date("Y-m-d", strtotime("-4 month", $current));

            if ($fourMonths > $registrationDate) {
                Token::deleteAll(['user_id' => $userId]);
                Provider::updateAll(['rfc' => '', 'enabled' => false], ['user_id' => $userId]);
                \Yii::$app->db->createCommand("update usuarios set email = '', status = 'INACTIVO' where user_id = :id", [':id' => $userId])->execute();
                $accountDelete = new AccountDelete();
                $accountDelete->user_id = $userId;
                $accountDelete->email = $email;
                $accountDelete->rfc = $rfc;
                $accountDelete->register_user = $registrationDate;
                $accountDelete->save();
            }
        }

    }

    public function actionGenerateFirstCert()
    {
        $data = \Yii::$app->db->createCommand("select provider_id,user_id from provider where \"Clave_ProveedorSire\" is not null and provider_id 
        not in(select distinct provider_id from historico_certificados where tipo = 'CERTIFICADO' and provider_type = 'bys')")->queryAll();


        foreach ($data as $v) {
            $mod = FirstUpdateCertificate::find()->where(['provider_id' => $v['provider_id']])->one();
            if ($mod) {
                $hcId = $this->documentocertificadocron($mod->provider_id, 'bys', base64_encode($mod->created_at));
                if($hcId){
                    $first_update_certificate = new FirstUpdateCertificate();
                    $first_update_certificate->provider_id = $mod->provider_id;
                    $first_update_certificate->first_certificate = true;
                    $first_update_certificate->historico_certificado_id = $hcId;
                    $first_update_certificate->save();
                    Movements::updateAll(['first_update_certificate_id' => $first_update_certificate->first_update_certificate_id], ['and', ['provider_id' => $mod->provider_id], ['first_update_certificate_id' => null]]);
                    GeneralController::AllSendNotification($v['user_id'], 'bys', null, 'CERTIFICADO', null, '¡Felicidades!', 'PROVIDER');
                }
            }
        }
    }

    public function documentocertificadocron($id = null, $tipo = null, $date = null)
    {

        $proveedor = Provider::findOne($id);

        if (!$proveedor) {
            return false;
        }

        $datosFirma = ['firmante' => '', 'cargo' => '', 'firma' => ''];

        $datosQr = ['img' => ''];

        $path_doc = 'prov_certificado';
        if (!file_exists($path_doc)) {
            mkdir($path_doc, 0777, true);
        }

        $dataExperiencia = [];
        $capacidadFinanciera = 0;
        $capFin = 0;
        if ($tipo == 'op') {
            $dataExperiencia = \Yii::$app->db->createCommand("
                    select cs.nombre as especialidad,e.id_obra as id,SUM(e.monto) as monto from provider.experiencia e
                    join provider.subespecialidad_category cs on cs.category_id = e.id_obra
                    where e.provider_id = :id and status_op = 'VALIDADO' AND e.activo is TRUE 
                    group by especialidad,id order by monto DESC", [':id' => $id])->queryAll();


            $capFin = \Yii::$app->db->createCommand("select capacidad_contratacion_resultado from provider.capacidad_contratacion
                      where provider_id = :id ORDER BY capacidad_contratacion_id DESC", [':id' => $id])->queryOne()['capacidad_contratacion_resultado'];
            $capacidadFinanciera = array_sum(ArrayHelper::getColumn($dataExperiencia, 'monto'));
        }


        $content = $this->renderPartial('/carta/certificado' . $tipo, [
            'proveedor' => $proveedor,
            'dataExperiencia' => $dataExperiencia,
            'capacidadFinanciera' => $capacidadFinanciera,
            'capFin' => $capFin,
            'datosFirma' => $datosFirma,
            'datosQr' => $datosQr,
            'date' => $date
        ]);

        $mpdf = new \mPDF();
        $mpdf->SetTitle('Certificado');
        $mpdf->SetHTMLHeader($this->renderPartial('/carta/header', ['tipo_provider' => $tipo]));
        $mpdf->SetHTMLFooter($this->renderPartial('/carta/certificado_footer', ['datosFirma' => $datosFirma, 'datosQr' => $datosQr, 'tipo_provider' => $tipo]));
        $mpdf->defaultfooterline = 0;
        $mpdf->WriteHTML($content);
        $filename = $path_doc . '/' . md5($proveedor->provider_id) . '_certificado_proveedor' . time() . '.pdf';
        $mpdf->Output($filename, 'F');

        chmod($filename, 0777);

        $date_year = date("Y");
        $date_month = date("m");
        $filenameFinal = 'documentos_firmados' . '/' . $proveedor->rfc . '/' . $proveedor->rfc . '_certificado_' . $date_year . '_' . $date_month . '_' . $tipo . time() . '.pdf';


        $codigoV = $this->codigoVerificacionCron($proveedor->provider_id);
        $token = $this->solicitaDatosGetCron(\Yii::$app->params['urlToken'])['token'];
        $datosQr = $this->solicitaDatosPostCron(\Yii::$app->params['urlQr'], ['encode' => $codigoV, 'base64' => 'true', 'validador' => true]);

        $datosBarCode = $this->solicitaBarCodeCron(\Yii::$app->params['urlBarCode'], $codigoV);
        $datosFirma = $this->solicitaDatosPostCron(\Yii::$app->params['urlFirma'], ['code' => $codigoV, 'token' => $token, 'type' => strtoupper($tipo), 'path' => \yii\helpers\Url::home('https') . $filename, 'signed_document' => \yii\helpers\Url::home('https') . $filenameFinal]);

        unlink($filename);

        $content = $this->renderPartial('/carta/certificado' . $tipo, [
            'proveedor' => $proveedor,
            'datosFirma' => $datosFirma,
            'datosQr' => $datosQr,
            'dataExperiencia' => $dataExperiencia,
            'capacidadFinanciera' => $capacidadFinanciera,
            'capFin' => $capFin,
            'date' => $date
        ]);


        $mpdf = new \mPDF();
        $mpdf->SetTitle('Certificado');
        $mpdf->SetHTMLHeader($this->renderPartial('/carta/header', ['tipo_provider' => $tipo]));
        $mpdf->SetHTMLFooter($this->renderPartial('/carta/certificado_footer', ['datosFirma' => $datosFirma, 'datosQr' => $datosQr, 'tipo_provider' => $tipo, 'barCode' => $datosBarCode, 'code' => $codigoV]));
        $mpdf->defaultfooterline = 0;
        $mpdf->WriteHTML($content);

        $mpdf->Output($filenameFinal, 'F');
        chmod($filenameFinal, 0777);

        $historicoCerId = null;
        if ($datosFirma['status'] != 'Error' && file_exists($filenameFinal)) {
            $hc = new HistoricoCertificados();
            $hc->provider_id = $proveedor->provider_id;
            $hc->codigo_verificacion = $codigoV;
            $hc->url_certificado = $filenameFinal;
            $hc->provider_type = $tipo;
            if (!empty($date)) {
                $date = base64_decode($date);
                $hc->created_at = $date;
            }
            $hc->save();
            $historicoCerId = $hc->historico_certificados_id;
            $subject_correo = $tipo == 'bys' ? '¡Felicidades! ya formas parte del Padrón de Proveedores': '¡Felicidades! ya formas parte del Registro Estatal de Contratistas';
            GeneralController::sendEmail('/provider/correos/certificacion', null, $proveedor->email, $subject_correo, ['tipo_provider' => $tipo], $filenameFinal);
        } else {
            $error = new ErrorInesperado();
            $error->tipo_error = 'FIRMAR CERTIFICADO';
            $error->data = $datosFirma;
            $error->user_id = 1;
            $error->save(false);
        }
        return $historicoCerId;
    }

    public function codigoVerificacionCron($id)
    {

        $axu = 16 - strlen($id);

        $rnd_str = substr(str_shuffle("0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ"), 0, $axu);

        $real_password = $rnd_str . $id;

        return $real_password;
    }

    public function solicitaDatosGetCron($url)
    {
        $curl = curl_init();
        curl_setopt($curl, CURLOPT_URL, $url);
        curl_setopt($curl, CURLOPT_HEADER, false);
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($curl, CURLOPT_ENCODING, "");
        $curlData = curl_exec($curl);
        curl_close($curl);
        return json_decode($curlData, true);
    }


    public function solicitaDatosPostCron($url, $params = [])
    {


        $curl = curl_init();
        curl_setopt($curl, CURLOPT_URL, $url);
        curl_setopt($curl, CURLOPT_POST, 1);
        curl_setopt($curl, CURLOPT_POSTFIELDS, json_encode($params));
        curl_setopt($curl, CURLOPT_HTTPHEADER, array('Content-Type: application/json'));
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
        $curlData = curl_exec($curl);
        curl_close($curl);
        return json_decode($curlData, true);
    }


    public function solicitaBarCodeCron($url, $code)
    {


        $params = [
            'encode' => $code,
            'base64' => 'true',
            'mostrar_valor' => 'false',
            'path' => 'uploads/datos/'
        ];

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($params));
        curl_setopt($ch, CURLOPT_HTTPHEADER, array('Content-Type: application/json'));
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

        $variableQr = curl_exec($ch);
        curl_close($ch);

        return json_decode($variableQr, true);
    }
}
