<?php

namespace app\commands;

use app\helpers\GeneralController;
use app\models\HistoricoCartaProtesta;
use app\models\HistoricoCertificados;
use app\models\NoLocalizados;
use yii\console\Controller;
use yii\db\Query;
use yii\helpers\ArrayHelper;
use app\models\Provider;
use Yii;


/**
 * <AUTHOR> <<EMAIL>>
 * @since 2.0
 */
class NoLocalizadosController extends Controller
{


    public function actionVerify(){

        NoLocalizados::deleteAll(['and',['type_register' => 'NO_LOCALIZADOS'],['status' => true]]);
        $dataProvider = ArrayHelper::getColumn(HistoricoCartaProtesta::find()->select('provider_id')->where(['provider_type' => 'bys'])->distinct()->asArray()->all(),'provider_id');
        foreach ($dataProvider as $v){
            if(GeneralController::verificarModulosValidador('status_bys', $v, 'bys', 'FIRMADA')){
                $status = Provider::find()->select('status_cotejar')->where(['provider_id' => $v])->one()['status_cotejar'];
                if($status ==''){
                    if(($mo = NoLocalizados::find()->where(['and',['type_register' => 'INCOMPLETOS'],['provider_id' => $v],['status' => true]])->count(1))==0){
                        $model = new NoLocalizados();
                        $model->provider_id = $v;
                        $model->type_register = 'NO_LOCALIZADOS';
                        $model->save();
                    }
                }
            }
        }
    }

    public function actionVerifyCert()
    {
        NoLocalizados::deleteAll(['and',['type_register' => 'INCOMPLETOS'],['status' => true]]);

        $dataProvider = ArrayHelper::getColumn(HistoricoCertificados::find()->select('provider_id')->where(['and', ['tipo' => 'CERTIFICADO'], ['provider_type' => 'bys']])->distinct()->asArray()->all(), 'provider_id');
        foreach ($dataProvider as $v) {
            if (!GeneralController::verificarModulosValidador('status_bys', $v, 'bys', 'FIRMADA')) {
                if(($mo = NoLocalizados::find()->where(['and',['type_register' => 'INCOMPLETOS'],['provider_id' => $v],['status' => true]])->count(1))==0){
                    $model = new NoLocalizados();
                    $model->provider_id = $v;
                    $model->type_register = 'INCOMPLETOS';
                    $model->save();
                }
            }
        }
    }


}
