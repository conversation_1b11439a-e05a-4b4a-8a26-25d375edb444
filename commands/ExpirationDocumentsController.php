<?php

namespace app\commands;

use app\models\Calendar;
use app\models\Certificacion;
use app\models\ComprobanteDomicilio;
use app\models\ExpirationDocuments;
use app\models\IdOficial;
use app\models\NoSendEmail;
use app\models\NumberNotification;
use app\models\Perfil;
use app\models\Provider;
use app\models\RepresentanteLegal;
use app\models\Rfc;
use app\models\SaveNotification;
use app\models\Status;
use app\models\UltimaDeclaracion;
use app\models\UserNotification;
use app\models\Visit;
use app\models\VisitDetails;
use yii\console\Controller;

class ExpirationDocumentsController extends Controller
{

    /* Ya no se utiliza validacion */
    public function actionVerifyExpirationUltimaDeclaracion()
    {
        /* $data = \Yii::$app->db->createCommand("with ct as(select distinct
                historico_certificados_id,provider_id, created_at,
                row_number() over (partition by provider_id order by created_at desc) rn
                from
                historico_certificados where tipo ='CERTIFICADO' and provider_type = 'bys' order by created_at
                )
                select
                ct.provider_id
                from
                ct
                where
                rn = 1")->queryAll();

        $dateNow = date('Y-m-d');

        foreach ($data as $d) {


            $ultimaDec = UltimaDeclaracion::find()->select(['ultima_declaracion_id', new \yii\db\Expression('created_date::date')])->where(['provider_id' => $d['provider_id']])->one();

            $exp = NoSendEmail::find()->select('vigencia')->where(['provider_id' => $d['provider_id']])->one()['vigencia'];
            $exp = $exp?$exp:'2020-01-01';
            if ($ultimaDec && $exp<date('Y-m-d')) {
                $fechaUp = $ultimaDec->created_date;
                $fecha_actual = new \DateTime($dateNow);
                $fecha_final = new \DateTime($fechaUp);
                $dias = $fecha_actual->diff($fecha_final)->format('%r%a');

                if ($dias == 30) {
                    if ((ExpirationDocuments::find()->where(['and', ['register_id' => $ultimaDec->ultima_declaracion_id], ['type' => '30 DIAS'], ['module' => 'ultima_declaracion'], ['expitation_date' => $fechaUp], ['permanent' => false]])->count('1')) == 0) {
                        $expDoc = new ExpirationDocuments();
                        $expDoc->provider_id = $d['provider_id'];
                        $expDoc->module = 'ultima_declaracion';
                        $expDoc->register_id = $ultimaDec->ultima_declaracion_id;
                        $expDoc->msg = 'Modulo proximo a vencer en (30 días)';
                        $expDoc->expitation_date = $fechaUp;
                        $expDoc->type = "30 DIAS";
                        $expDoc->save();
                    }
                } else if ($dias == 15) {
                    if ((ExpirationDocuments::find()->where(['and', ['register_id' => $ultimaDec->ultima_declaracion_id], ['type' => '15 DIAS'], ['module' => 'ultima_declaracion'], ['expitation_date' => $fechaUp], ['permanent' => false]])->count('1')) == 0) {
                        $expDoc = new ExpirationDocuments();
                        $expDoc->provider_id = $d['provider_id'];
                        $expDoc->module = 'ultima_declaracion';
                        $expDoc->register_id = $ultimaDec->ultima_declaracion_id;
                        $expDoc->msg = 'Modulo proximo a vencer en (15 días)';
                        $expDoc->expitation_date = $fechaUp;
                        $expDoc->type = "15 DIAS";
                        $expDoc->save();
                    }
                } else if ($dias == 0 || $dias < -366) {
                    if ((ExpirationDocuments::find()->where(['and', ['register_id' => $ultimaDec->ultima_declaracion_id], ['type' => 'EXPIRED'], ['module' => 'ultima_declaracion'], ['expitation_date' => $fechaUp], ['permanent' => false]])->count('1')) == 0) {
                        $expDoc = new ExpirationDocuments();
                        $expDoc->provider_id = $d['provider_id'];
                        $expDoc->module = 'ultima_declaracion';
                        $expDoc->register_id = $ultimaDec->ultima_declaracion_id;
                        $expDoc->msg = 'Modulo vencido';
                        $expDoc->expitation_date = $fechaUp;
                        $expDoc->type = "EXPIRED";
                        $expDoc->save();
                        if (UltimaDeclaracion::updateAll(['status_bys' => 'RECHAZADO'], ['ultima_declaracion_id' => $ultimaDec->ultima_declaracion_id])) {
                            $rech = new Status();
                            $rech->modelo = 'ultima_declaracion';
                            $rech->register_id = $d['provider_id'];
                            $rech->created_id = 168;
                            $rech->motivo = "ESTIMADO INTERESADO:<br>
                            CON EL FIN DE MANTENER SU REGISTRO ACTIVO EN EL PADRON DE PROVEEDORES, LO INVITAMOS A ACTUALIZAR LA SIGUIENTE INFORMACIÓN,<br>
                            BALANCE GENERAL Y ESTADO DE RESULTADOS, OPINION DE CUMPLIMIENTO, PAGOS PROVISIONALES  DE IMPUESTOS FEDERALES,IMPUESTO DE NOMINA";
                            $rech->status_bys = 'PENDIENTE';
                            $rech->expiration = true;
                            $rech->provider_id = $ultimaDec->provider_id;
                            if($rech->save()){
                                $this->eliminarCotejoCite($rech->provider_id);
                            }
                        }
                    }
                }
            }
        } */
    }


    /* Ya no se utiliza validacion */
    public function actionVerifyExpirationUltimaDeclaracionPermanent($provider, $type)
    {

        /* $providerTipe = $provider == 'pf' ? 'Persona física' : 'Persona moral';
        $data = \Yii::$app->db->createCommand("with ct as(select distinct
                historico_certificados_id,provider_id, created_at,
                row_number() over (partition by provider_id order by created_at desc) rn
                from
                historico_certificados where tipo ='CERTIFICADO' and provider_type = 'bys' order by created_at
                )
                select
                ct.provider_id
                from
                ct
                join provider p on p.provider_id = ct.provider_id
                where
                rn = 1 and p.tipo_persona = :p", [':p' => $providerTipe])->queryAll();


        foreach ($data as $d) {

            $ultimaDec = UltimaDeclaracion::find()->select('ultima_declaracion_id')->where(['provider_id' => $d['provider_id']])->one();
            $exp = NoSendEmail::find()->select('vigencia')->where(['provider_id' => $d['provider_id']])->one()['vigencia'];
            $exp = $exp?$exp:'2020-01-01';
            if ($exp<date('Y-m-d')) {
                if ($type == 30) {
                    $expDoc = new ExpirationDocuments();
                    $expDoc->provider_id = $d['provider_id'];
                    $expDoc->module = 'ultima_declaracion';
                    $expDoc->register_id = $ultimaDec->ultima_declaracion_id;
                    $expDoc->msg = 'Modulo proximo a vencer en (30 días)';
                    $expDoc->expitation_date = $provider == 'pf' ? date('Y') . '-04-30' : date('Y') . '-03-31';
                    $expDoc->type = "30 DIAS";
                    $expDoc->permanent = true;
                    $expDoc->save();
                } else if ($type == 15) {
                    $expDoc = new ExpirationDocuments();
                    $expDoc->provider_id = $d['provider_id'];
                    $expDoc->module = 'ultima_declaracion';
                    $expDoc->register_id = $ultimaDec->ultima_declaracion_id;
                    $expDoc->msg = 'Modulo proximo a vencer en (15 días)';
                    $expDoc->expitation_date = $provider == 'pf' ? date('Y') . '-04-30' : date('Y') . '-03-31';
                    $expDoc->type = "15 DIAS";
                    $expDoc->permanent = true;
                    $expDoc->save();
                } else if ($type == 0) {
                    $expDoc = new ExpirationDocuments();
                    $expDoc->provider_id = $d['provider_id'];
                    $expDoc->module = 'ultima_declaracion';
                    $expDoc->register_id = $ultimaDec->ultima_declaracion_id;
                    $expDoc->msg = 'Modulo vencido';
                    $expDoc->expitation_date = $provider == 'pf' ? date('Y') . '-04-30' : date('Y') . '-03-31';
                    $expDoc->type = "EXPIRED";
                    $expDoc->permanent = true;
                    $expDoc->save();
                    if (UltimaDeclaracion::updateAll(['status_bys' => 'RECHAZADO'], ['ultima_declaracion_id' => $ultimaDec->ultima_declaracion_id])) {
                        $rech = new Status();
                        $rech->modelo = 'ultima_declaracion';
                        $rech->register_id = $d['provider_id'];
                        $rech->created_id = 168;
                        $rech->motivo = "ESTIMADO INTERESADO:<br>
                            CON EL FIN DE MANTENER SU REGISTRO ACTIVO EN EL PADRON DE PROVEEDORES, LO INVITAMOS A ACTUALIZAR LA SIGUIENTE INFORMACIÓN,<br>
                            DECLARACION ANUAL";
                        $rech->status_bys = 'PENDIENTE';
                        $rech->expiration = true;
                        $rech->provider_id = $ultimaDec->provider_id;
                        if($rech->save()){
                            $this->eliminarCotejoCite($rech->provider_id);
                        }
                    }
                }
            }
        } */
    }

    /* Validacion ya no se utiliza */
    public function actionVerifyExpirationUltimaDeclaracionPermanentNow()
    {
        /* $data = \Yii::$app->db->createCommand("with ct as(select distinct
                    historico_certificados_id,provider_id, created_at,
                    row_number() over (partition by provider_id order by created_at desc) rn
                    from
                    historico_certificados where tipo ='CERTIFICADO' and provider_type = 'bys' and created_at::date<='2020-04-30' order by created_at
                    )
                    select
                    ct.provider_id,p.tipo_persona
                    from
                    ct
                    join provider p on p.provider_id = ct.provider_id
                    where
                    rn = 1")->queryAll();


        foreach ($data as $d) {

            $ultimaDec = UltimaDeclaracion::find()->select('ultima_declaracion_id')->where(['provider_id' => $d['provider_id']])->one();

            $expDoc = new ExpirationDocuments();
            $expDoc->provider_id = $d['provider_id'];
            $expDoc->module = 'ultima_declaracion';
            $expDoc->register_id = $ultimaDec->ultima_declaracion_id;
            $expDoc->msg = 'Modulo vencido';
            $expDoc->expitation_date = $d['tipo_persona'] == 'Persona moral' ? '2020-03-31' : '2020-04-30';
            $expDoc->type = "EXPIRED";
            $expDoc->permanent = true;
            $expDoc->save();
            if (UltimaDeclaracion::updateAll(['status_bys' => 'RECHAZADO'], ['ultima_declaracion_id' => $ultimaDec->ultima_declaracion_id])) {
                $rech = new Status();
                $rech->modelo = 'ultima_declaracion';
                $rech->register_id = $d['provider_id'];
                $rech->created_id = 168;
                $rech->motivo = "ESTIMADO INTERESADO:<br>
                            CON EL FIN DE MANTENER SU REGISTRO ACTIVO EN EL PADRON DE PROVEEDORES, LO INVITAMOS A ACTUALIZAR LA SIGUIENTE INFORMACIÓN,<br>
                            DECLARACION ANUAL";
                $rech->status_bys = 'PENDIENTE';
                $rech->expiration = true;
                $rech->provider_id = $ultimaDec->provider_id;
                if($rech->save()){
                    $this->eliminarCotejoCite($rech->provider_id);
                }
            }
        } */
    }

    /* Validacion ya no se utiliza */
    public function actionVerifyExpirationComprobanteDomicilio()
    {
        /* $data = \Yii::$app->db->createCommand("with ct as(select distinct
                historico_certificados_id,provider_id, created_at,
                row_number() over (partition by provider_id order by created_at desc) rn
                from
                historico_certificados where tipo ='CERTIFICADO' and provider_type = 'bys' order by created_at
                )
                select
                ct.provider_id
                from
                ct
                where
                rn = 1")->queryAll();

        $dateNow = date('Y-m-d');

        foreach ($data as $d) {

            $compDom = ComprobanteDomicilio::find()->select(['comprobante_domicilio_id', new \yii\db\Expression('created_date::date')])->where(['provider_id' => $d['provider_id']])->one();
            if ($compDom) {
                $fechaUp = $compDom->created_date;
                $fecha_actual = new \DateTime($dateNow);
                $fecha_final = new \DateTime($fechaUp);
                $dias = $fecha_actual->diff($fecha_final)->format('%r%a');

                if ($dias == 30) {
                    if ((ExpirationDocuments::find()->where(['and', ['register_id' => $compDom->comprobante_domicilio_id], ['type' => '30 DIAS'], ['module' => 'perfil'], ['expitation_date' => $fechaUp], ['permanent' => false], ['column_name' => 'Comprobante de domicilio']])->count('1')) == 0) {
                        $expDoc = new ExpirationDocuments();
                        $expDoc->provider_id = $d['provider_id'];
                        $expDoc->module = 'perfil';
                        $expDoc->column_name = 'Comprobante de domicilio';
                        $expDoc->register_id = $compDom->comprobante_domicilio_id;
                        $expDoc->msg = 'Modulo proximo a vencer en (30 días)';
                        $expDoc->expitation_date = $fechaUp;
                        $expDoc->type = "30 DIAS";
                        $expDoc->save();
                    }
                } else if ($dias == 15) {
                    if ((ExpirationDocuments::find()->where(['and', ['register_id' => $compDom->comprobante_domicilio_id], ['type' => '15 DIAS'], ['module' => 'perfil'], ['expitation_date' => $fechaUp], ['permanent' => false], ['column_name' => 'Comprobante de domicilio']])->count('1')) == 0) {
                        $expDoc = new ExpirationDocuments();
                        $expDoc->provider_id = $d['provider_id'];
                        $expDoc->module = 'perfil';
                        $expDoc->column_name = 'Comprobante de domicilio';
                        $expDoc->register_id = $compDom->comprobante_domicilio_id;
                        $expDoc->msg = 'Modulo proximo a vencer en (15 días)';
                        $expDoc->expitation_date = $fechaUp;
                        $expDoc->type = "15 DIAS";
                        $expDoc->save();
                    }
                } else if ($dias == 0 || $dias < -366) {
                    if ((ExpirationDocuments::find()->where(['and', ['register_id' => $compDom->comprobante_domicilio_id], ['type' => 'EXPIRED'], ['module' => 'perfil'], ['expitation_date' => $fechaUp], ['permanent' => false], ['column_name' => 'Comprobante de domicilio']])->count('1')) == 0) {
                        $expDoc = new ExpirationDocuments();
                        $expDoc->provider_id = $d['provider_id'];
                        $expDoc->module = 'perfil';
                        $expDoc->column_name = 'Comprobante de domicilio';
                        $expDoc->register_id = $compDom->comprobante_domicilio_id;
                        $expDoc->msg = 'Modulo vencido';
                        $expDoc->expitation_date = $fechaUp;
                        $expDoc->type = "EXPIRED";
                        $expDoc->save();
                        if (Perfil::updateAll(['status_bys' => 'RECHAZADO'], ['provider_id' => $d['provider_id']])) {
                            $rech = new Status();
                            $rech->modelo = 'perfil';
                            $rech->register_id = $d['provider_id'];
                            $rech->created_id = 168;
                            $rech->motivo = "ESTIMADO INTERESADO:<br>
                            CON EL FIN DE MANTENER SU REGISTRO ACTIVO EN EL PADRON DE PROVEEDORES, LO INVITAMOS A ACTUALIZAR LA SIGUIENTE INFORMACIÓN,<br>
                            COMPROBANTE DE DOMICILIO";
                            $rech->status_bys = 'PENDIENTE';
                            $rech->expiration = true;
                            $rech->provider_id = $d['provider_id'];
                            if($rech->save()){
                                $this->eliminarCotejoCite($rech->provider_id);
                            }
                        }
                    }
                }
            }
        } */
    }

    public function actionVerifyExpirationPasaporte()
    {
        $data = \Yii::$app->db->createCommand("with ct as(select distinct
                historico_certificados_id,provider_id, created_at,
                row_number() over (partition by provider_id order by created_at desc) rn
                from
                historico_certificados where tipo ='CERTIFICADO' and provider_type = 'bys' order by created_at
                )
                select
                ct.provider_id
                from
                ct
                where
                rn = 1")->queryAll();

        $dateNow = date('Y-m-d');

        foreach ($data as $d) {

            $PasaPorte = IdOficial::find()->select(['idoficial_id', 'expiration_date'])->where(['and', ['provider_id' => $d['provider_id']], ['tipo_identificacion' => 'PASAPORTE']])->one();
            if ($PasaPorte) {
                $fechaUp = $PasaPorte->expiration_date;
                $fecha_actual = new \DateTime($dateNow);
                $fecha_final = new \DateTime($fechaUp);
                $dias = $fecha_actual->diff($fecha_final)->format('%r%a');

                if ($dias == 30) {
                    if ((ExpirationDocuments::find()->where(['and', ['register_id' => $PasaPorte->idoficial_id], ['type' => '30 DIAS'], ['module' => 'rfc'], ['expitation_date' => $fechaUp], ['permanent' => false], ['column_name' => 'Pasaporte']])->count('1')) == 0) {
                        $expDoc = new ExpirationDocuments();
                        $expDoc->provider_id = $d['provider_id'];
                        $expDoc->module = 'rfc';
                        $expDoc->column_name = 'Pasaporte';
                        $expDoc->register_id = $PasaPorte->idoficial_id;
                        $expDoc->msg = 'Modulo proximo a vencer en (30 días)';
                        $expDoc->expitation_date = $fechaUp;
                        $expDoc->type = "30 DIAS";
                        $expDoc->save();
                    }
                } else if ($dias == 15) {
                    if ((ExpirationDocuments::find()->where(['and', ['register_id' => $PasaPorte->idoficial_id], ['type' => '15 DIAS'], ['module' => 'rfc'], ['expitation_date' => $fechaUp], ['permanent' => false], ['column_name' => 'Pasaporte']])->count('1')) == 0) {
                        $expDoc = new ExpirationDocuments();
                        $expDoc->provider_id = $d['provider_id'];
                        $expDoc->module = 'rfc';
                        $expDoc->column_name = 'Pasaporte';
                        $expDoc->register_id = $PasaPorte->idoficial_id;
                        $expDoc->msg = 'Modulo proximo a vencer en (15 días)';
                        $expDoc->expitation_date = $fechaUp;
                        $expDoc->type = "15 DIAS";
                        $expDoc->save();
                    }
                } else if ($dias == 0 || $dias < -366) {
                    if ((ExpirationDocuments::find()->where(['and', ['register_id' => $PasaPorte->idoficial_id], ['type' => 'EXPIRED'], ['module' => 'rfc'], ['expitation_date' => $fechaUp], ['permanent' => false], ['column_name' => 'Pasaporte']])->count('1')) == 0) {
                        $expDoc = new ExpirationDocuments();
                        $expDoc->provider_id = $d['provider_id'];
                        $expDoc->module = 'rfc';
                        $expDoc->column_name = 'Pasaporte';
                        $expDoc->register_id = $PasaPorte->idoficial_id;
                        $expDoc->msg = 'Modulo vencido';
                        $expDoc->expitation_date = $fechaUp;
                        $expDoc->type = "EXPIRED";
                        $expDoc->save();
                        if (Rfc::updateAll(['status_bys' => 'RECHAZADO'], ['provider_id' => $d['provider_id']])) {
                            $rech = new Status();
                            $rech->modelo = 'rfc';
                            $rech->register_id = $d['provider_id'];
                            $rech->created_id = 168;
                            $rech->motivo = "ESTIMADO INTERESADO:<br>
                            CON EL FIN DE MANTENER SU REGISTRO ACTIVO EN EL PADRON DE PROVEEDORES, LO INVITAMOS A ACTUALIZAR LA SIGUIENTE INFORMACIÓN,<br>
                            IDENTIFICACIÓN OFICIAL PASAPORTE";
                            $rech->status_bys = 'PENDIENTE';
                            $rech->expiration = true;
                            $rech->provider_id = $d['provider_id'];
                            if($rech->save()){
                                $this->eliminarCotejoCite($rech->provider_id);
                            }
                        }
                    }
                }
            }
        }
    }

    public function actionVerifyExpirationIne($type)
    {
        $data = \Yii::$app->db->createCommand("with ct as(select distinct
                historico_certificados_id,provider_id, created_at,
                row_number() over (partition by provider_id order by created_at desc) rn
                from
                historico_certificados where tipo ='CERTIFICADO' and provider_type = 'bys' order by created_at
                )
                select
                ct.provider_id
                from
                ct
                where
                rn = 1")->queryAll();

        foreach ($data as $d) {

            $vig = $type == 30 || $type == 15 ? date('Y') . '-12-31' : date('Y') . '-01-01';

            $INE = IdOficial::find()->select(['idoficial_id', 'expiration_date'])->where(['and', ['provider_id' => $d['provider_id']], ['tipo_identificacion' => 'IFE'], ['<', 'expiration_date', $vig]])->one();

            if ($INE && isset($INE['expiration_date']) && !empty($INE['expiration_date'])) {
                $expl = explode('-', $INE['expiration_date']);
                if ($type == 30) {
                    if ($expl[0] <= date('Y')) {
                        $expDoc = new ExpirationDocuments();
                        $expDoc->provider_id = $d['provider_id'];
                        $expDoc->module = 'rfc';
                        $expDoc->column_name = 'INE';
                        $expDoc->register_id = $INE->idoficial_id;
                        $expDoc->msg = 'Modulo proximo a vencer en (30 días)';
                        $expDoc->expitation_date = date('Y') . '-12-31';
                        $expDoc->type = "30 DIAS";
                        $expDoc->save();
                    }
                } else if ($type == 15) {
                    if ($expl[0] <= date('Y')) {
                        $expDoc = new ExpirationDocuments();
                        $expDoc->provider_id = $d['provider_id'];
                        $expDoc->module = 'rfc';
                        $expDoc->column_name = 'INE';
                        $expDoc->register_id = $INE->idoficial_id;
                        $expDoc->msg = 'Modulo proximo a vencer en (15 días)';
                        $expDoc->expitation_date = date('Y') . '-12-31';
                        $expDoc->type = "15 DIAS";
                        $expDoc->save();
                    }
                } else if ($type == 0) {
                    if ($expl[0] < date('Y')) {
                        $expDoc = new ExpirationDocuments();
                        $expDoc->provider_id = $d['provider_id'];
                        $expDoc->module = 'rfc';
                        $expDoc->column_name = 'INE';
                        $expDoc->register_id = $INE->idoficial_id;
                        $expDoc->msg = 'Modulo vencido';
                        $expDoc->expitation_date = (intval(date('Y')) - 1) . '-12-31';
                        $expDoc->type = "EXPIRED";
                        $expDoc->save();
                        if (Rfc::updateAll(['status_bys' => 'RECHAZADO'], ['provider_id' => $d['provider_id']])) {
                            $rech = new Status();
                            $rech->modelo = 'rfc';
                            $rech->register_id = $d['provider_id'];
                            $rech->created_id = 168;
                            $rech->motivo = "ESTIMADO INTERESADO:<br>
                            CON EL FIN DE MANTENER SU REGISTRO ACTIVO EN EL PADRON DE PROVEEDORES, LO INVITAMOS A ACTUALIZAR LA SIGUIENTE INFORMACIÓN,<br>
                            IDENTIFICACIÓN OFICIAL INE";
                            $rech->status_bys = 'PENDIENTE';
                            $rech->expiration = true;
                            $rech->provider_id = $d['provider_id'];
                            if($rech->save()){
                                $this->eliminarCotejoCite($rech->provider_id);
                            }
                        }
                    }
                }
            }
        }
    }

    public function actionVerifyExpirationIneJunio($type)
    {
        /* $data = \Yii::$app->db->createCommand("with ct as(select distinct
                historico_certificados_id,provider_id, created_at,
                row_number() over (partition by provider_id order by created_at desc) rn
                from
                historico_certificados where tipo ='CERTIFICADO' and provider_type = 'bys' order by created_at
                )
                select
                ct.provider_id
                from
                ct
                where
                rn = 1")->queryAll();

        foreach ($data as $d) {

            $INE = IdOficial::find()->select(['idoficial_id', 'expiration_date'])->where(['and', ['provider_id' => $d['provider_id']], ['tipo_identificacion' => 'IFE'], ['<', 'expiration_date', '2021-01-01']])->one();

            if ($INE && isset($INE['expiration_date']) && !empty($INE['expiration_date'])) {
                if ($type == 30) {
                    $expDoc = new ExpirationDocuments();
                    $expDoc->provider_id = $d['provider_id'];
                    $expDoc->module = 'rfc';
                    $expDoc->column_name = 'INE';
                    $expDoc->register_id = $INE->idoficial_id;
                    $expDoc->msg = 'Modulo proximo a vencer en (30 días)';
                    $expDoc->expitation_date = '2021-06-07';
                    $expDoc->type = "30 DIAS";
                    $expDoc->save();
                } else if ($type == 15) {
                    $expDoc = new ExpirationDocuments();
                    $expDoc->provider_id = $d['provider_id'];
                    $expDoc->module = 'rfc';
                    $expDoc->column_name = 'INE';
                    $expDoc->register_id = $INE->idoficial_id;
                    $expDoc->msg = 'Modulo proximo a vencer en (15 días)';
                    $expDoc->expitation_date = '2021-06-07';
                    $expDoc->type = "15 DIAS";
                    $expDoc->save();
                } else if ($type == 0) {
                    $expDoc = new ExpirationDocuments();
                    $expDoc->provider_id = $d['provider_id'];
                    $expDoc->module = 'rfc';
                    $expDoc->column_name = 'INE';
                    $expDoc->register_id = $INE->idoficial_id;
                    $expDoc->msg = 'Modulo vencido';
                    $expDoc->expitation_date = '2021-06-07';
                    $expDoc->type = "EXPIRED";
                    $expDoc->save();
                    if (Rfc::updateAll(['status_bys' => 'RECHAZADO'], ['provider_id' => $d['provider_id']])) {
                        $rech = new Status();
                        $rech->modelo = 'rfc';
                        $rech->register_id = $d['provider_id'];
                        $rech->created_id = 168;
                        $rech->motivo = "ESTIMADO INTERESADO:<br>
                            CON EL FIN DE MANTENER SU REGISTRO ACTIVO EN EL PADRON DE PROVEEDORES, LO INVITAMOS A ACTUALIZAR LA SIGUIENTE INFORMACIÓN,<br>
                            IDENTIFICACIÓN OFICIAL INE";
                        $rech->status_bys = 'PENDIENTE';
                        $rech->expiration = true;
                        $rech->provider_id = $d['provider_id'];
                        if($rech->save()){
                            $this->eliminarCotejoCite($rech->provider_id);
                        }
                    }
                }
            }
        } */
    }

    public function actionVerifyExpirationIneRp($type)
    {
        $data = \Yii::$app->db->createCommand("with ct as(select distinct
                historico_certificados_id,provider_id, created_at,
                row_number() over (partition by provider_id order by created_at desc) rn
                from
                historico_certificados where tipo ='CERTIFICADO' and provider_type = 'bys' order by created_at
                )
                select
                ct.provider_id
                from
                ct
                where
                rn = 1")->queryAll();

        foreach ($data as $d) {

            $vig = $type == 30 || $type == 15 ? date('Y') . '-12-31' : date('Y') . '-01-01';

            $dat = RepresentanteLegal::find()->select(['representante_legal_id', 'vencimiento_identificacion'])
                ->where(['and', ['provider_id' => $d['provider_id']], ['tipo_identificacion' => 'IFE'], ['activo' => true], ['<', 'vencimiento_identificacion', $vig], ['status_bys' => 'VALIDADO']])->asArray()->all();

            foreach ($dat as $INE) {
                if ($INE && isset($INE['vencimiento_identificacion']) && !empty($INE['vencimiento_identificacion'])) {
                    $expl = explode('-', $INE['vencimiento_identificacion']);
                    if ($type == 30) {
                        if ($expl[0] <= date('Y')) {
                            $expDoc = new ExpirationDocuments();
                            $expDoc->provider_id = $d['provider_id'];
                            $expDoc->module = 'representante_legal';
                            $expDoc->column_name = 'INE';
                            $expDoc->register_id = $INE['representante_legal_id'];
                            $expDoc->msg = 'Modulo proximo a vencer en (30 días)';
                            $expDoc->expitation_date = date('Y') . '-12-31';
                            $expDoc->type = "30 DIAS";
                            $expDoc->save();
                        }
                    } else if ($type == 15) {
                        if ($expl[0] <= date('Y')) {
                            $expDoc = new ExpirationDocuments();
                            $expDoc->provider_id = $d['provider_id'];
                            $expDoc->module = 'representante_legal';
                            $expDoc->column_name = 'INE';
                            $expDoc->register_id = $INE['representante_legal_id'];
                            $expDoc->msg = 'Modulo proximo a vencer en (15 días)';
                            $expDoc->expitation_date = date('Y') . '-12-31';
                            $expDoc->type = "15 DIAS";
                            $expDoc->save();
                        }
                    } else if ($type == 0) {
                        if ($expl[0] < date('Y')) {
                            $expDoc = new ExpirationDocuments();
                            $expDoc->provider_id = $d['provider_id'];
                            $expDoc->module = 'representante_legal';
                            $expDoc->column_name = 'INE';
                            $expDoc->register_id = $INE['representante_legal_id'];
                            $expDoc->msg = 'Modulo vencido';
                            $expDoc->expitation_date = (intval(date('Y')) - 1) . '-12-31';
                            $expDoc->type = "EXPIRED";
                            $expDoc->save();
                            if (RepresentanteLegal::updateAll(['status_bys' => 'RECHAZADO'], ['representante_legal_id' => $INE['representante_legal_id']])) {
                                $rech = new Status();
                                $rech->modelo = 'representante_legal';
                                $rech->register_id = $INE['representante_legal_id'];
                                $rech->created_id = 168;
                                $rech->motivo = "ESTIMADO INTERESADO:<br>
                                CON EL FIN DE MANTENER SU REGISTRO ACTIVO EN EL PADRON DE PROVEEDORES, LO INVITAMOS A ACTUALIZAR LA SIGUIENTE INFORMACIÓN,<br>
                                REPRESENTANTE LEGAL IDENTIFICACIÓN OFICIAL INE";
                                $rech->status_bys = 'PENDIENTE';
                                $rech->expiration = true;
                                $rech->provider_id = $d['provider_id'];
                                if($rech->save()){
                                    $this->eliminarCotejoCite($rech->provider_id);
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    public function actionVerifyExpirationPasaporteRp()
    {
        $data = \Yii::$app->db->createCommand("with ct as(select distinct
                historico_certificados_id,provider_id, created_at,
                row_number() over (partition by provider_id order by created_at desc) rn
                from
                historico_certificados where tipo ='CERTIFICADO' and provider_type = 'bys' order by created_at
                )
                select
                ct.provider_id
                from
                ct
                where
                rn = 1")->queryAll();

        $dateNow = date('Y-m-d');

        foreach ($data as $d) {

            $dat = RepresentanteLegal::find()->select(['representante_legal_id', 'vencimiento_identificacion'])
                ->where(['and', ['provider_id' => $d['provider_id']], ['tipo_identificacion' => 'PASAPORTE'], ['activo' => true], ['status_bys' => 'VALIDADO']])->asArray()->all();
            foreach ($dat as $PasaPorte) {
                if ($PasaPorte && isset($PasaPorte['vencimiento_identificacion']) && !empty($PasaPorte['vencimiento_identificacion'])) {
                    $fechaUp = $PasaPorte['vencimiento_identificacion'];
                    $fecha_actual = new \DateTime($dateNow);
                    $fecha_final = new \DateTime($fechaUp);
                    $dias = $fecha_actual->diff($fecha_final)->format('%r%a');

                    if ($dias == 30) {
                        if ((ExpirationDocuments::find()->where(['and', ['register_id' => $PasaPorte['representante_legal_id']], ['type' => '30 DIAS'], ['module' => 'representante_legal'], ['expitation_date' => $fechaUp], ['permanent' => false], ['column_name' => 'Pasaporte']])->count('1')) == 0) {
                            $expDoc = new ExpirationDocuments();
                            $expDoc->provider_id = $d['provider_id'];
                            $expDoc->module = 'representante_legal';
                            $expDoc->column_name = 'Pasaporte';
                            $expDoc->register_id = $PasaPorte['representante_legal_id'];
                            $expDoc->msg = 'Modulo proximo a vencer en (30 días)';
                            $expDoc->expitation_date = $fechaUp;
                            $expDoc->type = "30 DIAS";
                            $expDoc->save();
                        }
                    } else if ($dias == 15) {
                        if ((ExpirationDocuments::find()->where(['and', ['register_id' => $PasaPorte['representante_legal_id']], ['type' => '15 DIAS'], ['module' => 'representante_legal'], ['expitation_date' => $fechaUp], ['permanent' => false], ['column_name' => 'Pasaporte']])->count('1')) == 0) {
                            $expDoc = new ExpirationDocuments();
                            $expDoc->provider_id = $d['provider_id'];
                            $expDoc->module = 'representante_legal';
                            $expDoc->column_name = 'Pasaporte';
                            $expDoc->register_id = $PasaPorte['representante_legal_id'];
                            $expDoc->msg = 'Modulo proximo a vencer en (15 días)';
                            $expDoc->expitation_date = $fechaUp;
                            $expDoc->type = "15 DIAS";
                            $expDoc->save();
                        }
                    } else if ($dias == 0 || $dias < -366) {
                        if ((ExpirationDocuments::find()->where(['and', ['register_id' => $PasaPorte['representante_legal_id']], ['type' => 'EXPIRED'], ['module' => 'representante_legal'], ['expitation_date' => $fechaUp], ['permanent' => false], ['column_name' => 'Pasaporte']])->count('1')) == 0) {
                            $expDoc = new ExpirationDocuments();
                            $expDoc->provider_id = $d['provider_id'];
                            $expDoc->module = 'representante_legal';
                            $expDoc->column_name = 'Pasaporte';
                            $expDoc->register_id = $PasaPorte['representante_legal_id'];
                            $expDoc->msg = 'Modulo vencido';
                            $expDoc->expitation_date = $fechaUp;
                            $expDoc->type = "EXPIRED";
                            $expDoc->save();
                            if (RepresentanteLegal::updateAll(['status_bys' => 'RECHAZADO'], ['provider_id' => $PasaPorte['representante_legal_id']])) {
                                $rech = new Status();
                                $rech->modelo = 'representante_legal';
                                $rech->register_id = $PasaPorte['representante_legal_id'];
                                $rech->created_id = 168;
                                $rech->motivo = "ESTIMADO INTERESADO:<br>
                            CON EL FIN DE MANTENER SU REGISTRO ACTIVO EN EL PADRON DE PROVEEDORES, LO INVITAMOS A ACTUALIZAR LA SIGUIENTE INFORMACIÓN,<br>
                            REPRESENTANTE LEGAL IDENTIFICACIÓN OFICIAL PASAPORTE";
                                $rech->status_bys = 'PENDIENTE';
                                $rech->expiration = true;
                                $rech->provider_id = $d['provider_id'];
                                if($rech->save()){
                                    $this->eliminarCotejoCite($rech->provider_id);
                                }
                            }
                        }
                    }
                }
            }

        }
    }

    /* Validacion ya no se usa */
    public function actionVerifyExpirationIneJunioRp($type)
    {
        /* $data = \Yii::$app->db->createCommand("with ct as(select distinct
                historico_certificados_id,provider_id, created_at,
                row_number() over (partition by provider_id order by created_at desc) rn
                from
                historico_certificados where tipo ='CERTIFICADO' and provider_type = 'bys' order by created_at
                )
                select
                ct.provider_id
                from
                ct
                where
                rn = 1")->queryAll();

        foreach ($data as $d) {

            $dat = RepresentanteLegal::find()->select(['representante_legal_id', 'vencimiento_identificacion'])
                ->where(['and', ['provider_id' => $d['provider_id']], ['tipo_identificacion' => 'IFE'], ['activo' => true], ['<', 'vencimiento_identificacion', '2021-01-01'], ['status_bys' => 'VALIDADO']])->asArray()->all();

            foreach ($dat as $INE) {
                if ($INE && isset($INE['vencimiento_identificacion']) && !empty($INE['vencimiento_identificacion'])) {
                    if ($type == 30) {
                        $expDoc = new ExpirationDocuments();
                        $expDoc->provider_id = $d['provider_id'];
                        $expDoc->module = 'representante_legal';
                        $expDoc->column_name = 'INE';
                        $expDoc->register_id = $INE['representante_legal_id'];
                        $expDoc->msg = 'Modulo proximo a vencer en (30 días)';
                        $expDoc->expitation_date = '2021-06-07';
                        $expDoc->type = "30 DIAS";
                        $expDoc->save();
                    } else if ($type == 15) {
                        $expDoc = new ExpirationDocuments();
                        $expDoc->provider_id = $d['provider_id'];
                        $expDoc->module = 'representante_legal';
                        $expDoc->column_name = 'INE';
                        $expDoc->register_id = $INE['representante_legal_id'];
                        $expDoc->msg = 'Modulo proximo a vencer en (15 días)';
                        $expDoc->expitation_date = '2021-06-07';
                        $expDoc->type = "15 DIAS";
                        $expDoc->save();
                    } else if ($type == 0) {
                        $expDoc = new ExpirationDocuments();
                        $expDoc->provider_id = $d['provider_id'];
                        $expDoc->module = 'representante_legal';
                        $expDoc->column_name = 'INE';
                        $expDoc->register_id = $INE['representante_legal_id'];
                        $expDoc->msg = 'Modulo vencido';
                        $expDoc->expitation_date = '2021-06-07';
                        $expDoc->type = "EXPIRED";
                        $expDoc->save();
                        if (RepresentanteLegal::updateAll(['status_bys' => 'RECHAZADO'], ['representante_legal_id' => $INE['representante_legal_id']])) {
                            $rech = new Status();
                            $rech->modelo = 'representante_legal';
                            $rech->register_id = $INE['representante_legal_id'];
                            $rech->created_id = 168;
                            $rech->motivo = "ESTIMADO INTERESADO:<br>
                            CON EL FIN DE MANTENER SU REGISTRO ACTIVO EN EL PADRON DE PROVEEDORES, LO INVITAMOS A ACTUALIZAR LA SIGUIENTE INFORMACIÓN,<br>
                            REPRESENTANTE LEGAL IDENTIFICACIÓN OFICIAL INE";
                            $rech->status_bys = 'PENDIENTE';
                            $rech->expiration = true;
                            $rech->provider_id = $d['provider_id'];
                            if($rech->save()){
                                $this->eliminarCotejoCite($rech->provider_id);
                            }
                        }
                    }
                }
            }

        } */
    }

    /* Validacion ya no se usa */
    public function actionVerifyExpirationPermisos()
    {
        /* $data = \Yii::$app->db->createCommand("with ct as(select distinct
                historico_certificados_id,provider_id, created_at,
                row_number() over (partition by provider_id order by created_at desc) rn
                from
                historico_certificados where tipo ='CERTIFICADO' and provider_type = 'bys' order by created_at
                )
                select
                ct.provider_id
                from
                ct
                where
                rn = 1")->queryAll();

        $dateNow = date('Y-m-d');

        foreach ($data as $d) {

            $dat = Certificacion::find()->select(['certificacion_id', 'vigencia'])->where(['and',['is not','certificador',null], ['provider_id' => $d['provider_id']], ['undefined' => false], ['activo' => true], ['status_bys' => 'VALIDADO'],['permissions' => true]])->asArray()->all();
            foreach ($dat as $cert) {
                $fechaUp = $cert['vigencia'];
                $fecha_actual = new \DateTime($dateNow);
                $fecha_final = new \DateTime($fechaUp);
                $dias = $fecha_actual->diff($fecha_final)->format('%r%a');

                if ($dias == 30) {
                    if ((ExpirationDocuments::find()->where(['and', ['register_id' => $cert['certificacion_id']], ['type' => '30 DIAS'], ['module' => 'certificacion'], ['expitation_date' => $fechaUp], ['permanent' => false], ['column_name' => 'Permiso']])->count('1')) == 0) {
                        $expDoc = new ExpirationDocuments();
                        $expDoc->provider_id = $d['provider_id'];
                        $expDoc->module = 'certificacion';
                        $expDoc->column_name = 'Permiso';
                        $expDoc->register_id = $cert['certificacion_id'];
                        $expDoc->msg = 'Modulo proximo a vencer en (30 días)';
                        $expDoc->expitation_date = $fechaUp;
                        $expDoc->type = "30 DIAS";
                        $expDoc->save();
                    }
                } else if ($dias == 15) {
                    if ((ExpirationDocuments::find()->where(['and', ['register_id' => $cert['certificacion_id']], ['type' => '15 DIAS'], ['module' => 'certificacion'], ['expitation_date' => $fechaUp], ['permanent' => false], ['column_name' => 'Permiso']])->count('1')) == 0) {
                        $expDoc = new ExpirationDocuments();
                        $expDoc->provider_id = $d['provider_id'];
                        $expDoc->module = 'certificacion';
                        $expDoc->column_name = 'Permiso';
                        $expDoc->register_id = $cert['certificacion_id'];
                        $expDoc->msg = 'Modulo proximo a vencer en (15 días)';
                        $expDoc->expitation_date = $fechaUp;
                        $expDoc->type = "15 DIAS";
                        $expDoc->save();
                    }
                } else if ($dias == 0 || $dias < -366) {
                    if ((ExpirationDocuments::find()->where(['and', ['register_id' => $cert['certificacion_id']], ['type' => 'EXPIRED'], ['module' => 'certificacion'], ['expitation_date' => $fechaUp], ['permanent' => false], ['column_name' => 'Permiso']])->count('1')) == 0) {
                        $expDoc = new ExpirationDocuments();
                        $expDoc->provider_id = $d['provider_id'];
                        $expDoc->module = 'certificacion';
                        $expDoc->column_name = 'Permiso';
                        $expDoc->register_id = $cert['certificacion_id'];
                        $expDoc->msg = 'Modulo vencido';
                        $expDoc->expitation_date = $fechaUp;
                        $expDoc->type = "EXPIRED";
                        $expDoc->save();
                        if (Certificacion::updateAll(['status_bys' => 'RECHAZADO'], ['certificacion_id' => $cert['certificacion_id']])) {
                            $rech = new Status();
                            $rech->modelo = 'certificacion';
                            $rech->register_id = $cert['certificacion_id'];
                            $rech->created_id = 168;
                            $rech->motivo = "ESTIMADO INTERESADO:<br>
                            CON EL FIN DE MANTENER SU REGISTRO ACTIVO EN EL PADRON DE PROVEEDORES, LO INVITAMOS A ACTUALIZAR LA SIGUIENTE INFORMACIÓN,<br>
                            PERMISOS";
                            $rech->status_bys = 'PENDIENTE';
                            $rech->expiration = true;
                            $rech->provider_id = $d['provider_id'];
                            if($rech->save()){
                                $this->eliminarCotejoCite($rech->provider_id);
                            }
                        }
                    }
                }
            }

        } */
    }

    /* Validacion ya no se usa */
    public function actionSendEmailNot()
    {
        /* $data = \Yii::$app->db->createCommand("select * from get_expiration()")->queryAll();

        if ($data) {
            foreach ($data as $d) {

                $modulo = $this->getModelo($d['module']);

                $campos = '';
                $modelNameNot = '';
                if ($d['module'] == 'ultima_declaracion' && !$d['permanent']) {
                    $campos = $this->getCampos('ultima_declaracion');
                    $modelNameNot = 'ultima_declaracion';
                } elseif ($d['module'] == 'ultima_declaracion' && $d['permanent']) {
                    $campos = $this->getCampos('ultima_declaracion_permanent');
                    $modelNameNot = 'ultima_declaracion_permanent';
                } elseif ($d['module'] == 'perfil' && $d['column_name'] == 'Comprobante de domicilio') {
                    $campos = $this->getCampos('perfil_domicilio');
                    $modelNameNot = 'perfil_domicilio';
                } elseif ($d['module'] == 'rfc' && $d['column_name'] == 'Pasaporte') {
                    $campos = $this->getCampos('rfc_pasaporte');
                    $modelNameNot = 'rfc_pasaporte';
                } elseif ($d['module'] == 'rfc' && $d['column_name'] == 'INE') {
                    $campos = $this->getCampos('rfc_ine');
                    $modelNameNot = 'rfc_ine';
                } elseif ($d['module'] == 'representante_legal' && $d['column_name'] == 'Pasaporte') {
                    $campos = $this->getCampos('representante_pasaporte');
                    $modelNameNot = 'representante_pasaporte';
                } elseif ($d['module'] == 'representante_legal' && $d['column_name'] == 'INE') {
                    $campos = $this->getCampos('representante_ine');
                    $modelNameNot = 'representante_ine';
                } elseif ($d['module'] == 'certificacion') {
                    $campos = $this->getCampos('certificacion');
                    $modelNameNot = 'certificacion';
                }

                    self::sendEmail('/provider/correos/renovacion', '<EMAIL>', $d['email'], 'Actualiza tu información',
                        ['nombre' => $d['proveedor'], 'modulo' => $modulo, 'campos' => $campos, 'tipo_provider' => 'bys', 'tipo' => $d['type']]);

                $userSendMsj[0] = $d;


                $idMo = \Yii::$app->db->createCommand("select module_notification_url_id 
                from module_notification_url 
                where module_notification_name = :t and model_name = :mn", [':t' => $d['type'], ':mn' => $modelNameNot])->queryOne()['module_notification_url_id'];

                $save_notification = new SaveNotification();
                $save_notification->module_notification_url_id = $idMo;
                $save_notification->op_bys = 'bys';
                $save_notification->module_validation_name = $modulo;
                $save_notification->msg_extra = $d['msg'];
                if ($save_notification->save()) {
                    $user_notifiction = new UserNotification();
                    $user_notifiction->save_notification_id = $save_notification->save_notification_id;
                    $user_notifiction->user_id = $d['user_id'];
                    if ($user_notifiction->save()) {
                        if (($number_noti = NumberNotification::find()->where(['user_id' => $d['user_id']])->one()) !== null) {
                            $number_noti->number_notification = $number_noti->viewed == false ? $number_noti->number_notification + 1 : 1;
                            $number_noti->viewed = false;
                            $number_noti->save();
                        } else {
                            $number_noti = new NumberNotification();
                            $number_noti->number_notification = 1;
                            $number_noti->user_id = $d['user_id'];
                            $number_noti->save();
                        }
                    }

                }

            }
        } */
    }

    public static function sendEmail($view = null, $from = null, $email = null, $subject = null, $params = [], $attach = null)
    {

        try {

            $mailer = \Yii::$app->mailer->compose($view, $params)
                ->setFrom($from)
                ->setTo($email)
                ->setSubject($subject);
            if ($attach != null) {
                $mailer->attach($attach);
            }
            $mailer->send();
        } catch (\Exception $e) {
            //var_dump($e);exit();
        }
    }


    public function getModelo($modelo)
    {

        $modulo = 'Modulo';

        switch ($modelo) {
            case 'perfil':
                $modulo = 'Perfil';
                break;
            case 'rfc':
                $modulo = 'Datos legales';
                break;
            case 'alta_hacienda':
                $modulo = 'Actividad económica';
                break;
            case 'relacion_accionistas':
                $modulo = 'Relación accionistas';
                break;
            case 'intervencion_bancaria':
                $modulo = 'Método de pago';
                break;
            case 'ultima_declaracion':
                $modulo = 'Datos financieros';
                break;
            case 'certificacion':
                $modulo = 'Permisos';
                break;
            case 'ubicacion':
                $modulo = 'Ubicación';
                break;
            case 'direccion_nl':
                $modulo = 'Direccion Nl';
                break;
            case 'fotografia_negocio':
                $modulo = 'Fotografía negocio';
                break;
            case 'clientes_contratos':
                $modulo = 'Currículum';
                break;
            case 'modificacion_acta':
                $modulo = 'Modificación acta';
                break;

            case 'representante_legal':
                $modulo = 'Representante legal';
                break;


            //Contratistas
            case 'experiencia':
                $modulo = 'Experiencia comercial';
                break;
            case 'personal_tecnico':
                $modulo = 'Personal Técnico';
                break;
            case 'maquinaria_equipos':
                $modulo = 'Maquinaria y equipos';
                break;
            case 'organigrama':
                $modulo = 'Organigrama';
                break;
            case 'estado_financiero':
                $modulo = 'Estados financiero';
                break;
            case 'historico_carta_protesta':
                $modulo = 'Carta protesta';
                break;

        }

        return $modulo;

    }

    public function getCampos($module)
    {
        $modulo = '';
        switch ($module) {
            case 'ultima_declaracion':
                $modulo = 'BALANCE GENERAL Y ESTADO DE RESULTADOS, OPINION DE CUMPLIMIENTO, PAGOS PROVISIONALES  DE IMPUESTOS FEDERALES,IMPUESTO DE NOMINA';
                break;

            case 'ultima_declaracion_permanent':
                $modulo = 'DECLARACION ANUAL';
                break;

            case 'perfil_domicilio':
                $modulo = 'COMPROBANTE DE DOMICILIO';
                break;

            case 'rfc_pasaporte':
                $modulo = 'IDENTIFICACIÓN OFICIAL PASAPORTE';
                break;

            case 'rfc_ine':
                $modulo = 'IDENTIFICACIÓN OFICIAL INE';
                break;

            case 'representante_pasaporte':
                $modulo = 'IDENTIFICACIÓN OFICIAL PASAPORTE';
                break;

            case 'representante_ine':
                $modulo = 'IDENTIFICACIÓN OFICIAL INE';
                break;

            case 'certificacion':
                $modulo = 'PERMISOS';
                break;


        }

        return $modulo;

    }

    public function eliminarCotejoCite($provider_id){


        $id = Provider::find()->select('user_id')->where(['provider_id' => $provider_id])->one()['user_id'];

        Calendar::updateAll(['status' => 'CANCELADA'], ['and', ['status' => 'CONFIRMADA'], ['user' => $id]]);

        Provider::updateAll(['status_cotejar' => null], ['user_id' => $id]);

        Status::updateAll(['status_bys' => 'TERMINADO'], ['and', ['status_bys' => 'PENDIENTE'], ['modelo' => 'cotejar'], ['register_id' => $provider_id]]);

        $visit = Visit::find()->where(['and',['provider_id' => $provider_id],['status' => true]])->asArray()->all();

        if($visit){
            foreach ($visit as $v){

                if($v['modify']){
                    Visit::updateAll(['status' => false],['visit_id' => $v['visit_id']]);
                }else{
                    VisitDetails::deleteAll(['visit_id' => $v['visit_id']]);
                    Visit::deleteAll(['visit_id' => $v['visit_id']]);
                }

            }
        }



        return true;

    }

}
