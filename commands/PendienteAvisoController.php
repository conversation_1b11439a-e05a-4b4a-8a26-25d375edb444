<?php

namespace app\commands;

use app\helpers\GeneralController;
use app\models\NonWorkingDays;
use yii\console\Controller;
use yii\db\Query;
use yii\helpers\ArrayHelper;
use app\models\Provider;
use app\models\Usuarios;
use Yii;


/**
 * <AUTHOR> <<EMAIL>>
 * @since 2.0
 */
class PendienteAvisoController extends Controller
{

    public function actionAvisosProveedoresPendientes(){
        $proveedores_pendientes = [];
        $proveedores_prox_vencidos = [];
        $administradores = Usuarios::find()->where(['and', ['role' => Usuarios::ROLE_ADMIN_PROVIDER], ['status' => 'ACTIVO']])->all(); //Filtro de adminstradores
        $diasFeriados = ArrayHelper::getColumn( NonWorkingDays::find()->where(['and', ['holiday' => true], ['user_type' => 'ADMIN']])->all(), 'non_working_day');
        $diaActualStr = date("Y-m-d");
        $proveedores_revision = Provider::proveedoresPendientesRevision();
        $proveedores_validacion = Provider::proveedoresPendientesValidacion();
        foreach($proveedores_revision as $proveedor){
            $diasHabiles = GeneralController::getFechaHabiles($proveedor['fecha'], $diaActualStr, $diasFeriados);
            if(count($diasHabiles) == 5){ $proveedor['bandeja'] = 'Revision'; array_push($proveedores_pendientes, $proveedor); } //Si los dias hables son igual a 5, anadirlos a notificacion
            if(count($diasHabiles) == 8){ $proveedor['bandeja'] = 'Revision'; array_push($proveedores_prox_vencidos, $proveedor); } //Si los dias hables son igual a 8, estan proximos a vencer
        }
        foreach($proveedores_validacion as $proveedor_v){
            $diasHabiles = GeneralController::getFechaHabiles($proveedor_v['fecha'], $diaActualStr, $diasFeriados);
            if(count($diasHabiles) == 5){ $proveedor_v['bandeja'] = 'Validacion'; array_push($proveedores_pendientes, $proveedor_v); } //Si los dias hables son igual a 5, anadirlos a notificacion
            if(count($diasHabiles) == 8){ $proveedor_v['bandeja'] = 'Validacion'; array_push($proveedores_prox_vencidos, $proveedor_v); } //Si los dias hables son igual a 8, estan proximos a vencer
        }

        foreach($administradores as $admin){            
            if( count($proveedores_pendientes) > 0) { GeneralController::sendEmail('/provider/correos/aviso_proveedores_revision',null,$admin->email,'Expedientes por vencer en 3 días',[ 'proveedores' => $proveedores_pendientes ]); }
            if( count($proveedores_prox_vencidos) > 0) { GeneralController::sendEmail('/provider/correos/aviso_proveedores_validacion',null,$admin->email,'Expedientes a vencer el día de hoy',[ 'proveedores' => $proveedores_prox_vencidos ]); }
        }
    }

    /* Funcion ya no se esta utilizando */
    public function actionAviso(){

                /* $query = new Query;
                $query	->select('usuarios.email, usuarios.user_id')
                        ->from('usuarios')
                        ->where(['usuarios.role' => 'VALIDADOR PROVEEDORES', 'usuarios.status' => 'ACTIVO']);
                $command = $query->createCommand();
                $correos_validadores = ArrayHelper::map($command->queryAll(),'email','user_id');

                $array = [
                    // 'rfc' => 'Datos legales',
                    // 'alta_hacienda' => 'Actividad ecónomica',
                    // 'intervencion_bancaria' => 'Método de pago',
                    // 'ubicacion' => 'Ubicación',
                    'fotografia_negocio' => 'Fotografía',
                    // 'clientes_contratos' => 'Credenciales',
                    // 'curriculum' => 'Mi perfil',
                    // 'ultima_declaracion' => 'Datos Financieros'
                ];

                foreach($correos_validadores as $correo => $user_id){    
                    
                    foreach($array as $tabla=>$modulo){

                        $validado = new Query;
                        $validado->select('provider_id')
                                ->from('provider.'.$tabla)
                                ->where("status_bys = 'PENDIENTE POR VALIDAR' 
                                and created_date::timestamp <= (now() - interval '48 hours') 
                                and created_date::timestamp >= (now() - interval '66 hours')
                                ");
                        $command = $validado->createCommand();
                        $datos = ArrayHelper::getColumn($command->queryAll(),'provider_id');                    
                        
                        foreach($datos as $valor ){

                            $avisos = new Query;
                            $avisos->select('*')
                                    ->from('public.avisos')
                                    ->where(['provider_id' => $valor, 'status' => 'PENDIENTE POR MANDAR', 'tipo_aviso' => '24 HORAS']);
                            $comando = $avisos->createCommand();
                            $datos_avisos = ArrayHelper::getColumn($comando->queryAll(),'provider_id');
                            if($datos_avisos){

                            }else{
                                Yii::$app->db->createCommand()->insert('public.avisos', [
                                    'provider_id' => $valor,
                                    'modulo' => $modulo,
                                    'status' => 'PENDIENTE POR MANDAR', 
                                    'tipo_aviso' => '24 HORAS',
                                    'user_id' => $user_id,                                    
                                ])->execute();
                            }

                            $nombre_query = Provider::find()->select(['name_razon_social' => 'trim(concat_ws(\' \',name_razon_social,pf_nombre,pf_ap_paterno,pf_ap_materno))'])->where('provider_id='.$valor)->all();
                            $nombre_dato = ArrayHelper::getColumn($nombre_query,'name_razon_social');
                            $nombre = $nombre_dato[0];

                            $avisos = new Query;
                            $avisos->select('*')
                                    ->from('public.avisos')
                                    ->where(['provider_id' => $valor, 'status' => 'PENDIENTE POR MANDAR', 'tipo_aviso' => '24 HORAS']);
                            $comando = $avisos->createCommand();

                            $aviso_id = ArrayHelper::getColumn($comando->queryAll(),'aviso_id');

                            $datos_avisos = ArrayHelper::getColumn($comando->queryAll(),'provider_id');

                            if($datos_avisos){
                                 $mailer = Yii::$app->mailer;

                                   $mailer->compose('/provider/correos/pendiente_aviso',['modulo' => $modulo, 'nombre' => $nombre])
                                   ->setFrom('<EMAIL>')
                                   ->setTo($correo)
                                   ->setSubject('Proveedores pendientes por validar')
                                   ->send();
                                GeneralController::sendEmail('/provider/correos/pendiente_aviso',null,$correo,'Proveedores pendientes por validar',['modulo' => $modulo, 'nombre' => $nombre]);

                                $actualiza_aviso = Yii::$app->db->createCommand()
                                                ->update('public.avisos',['status' => 'MANDADO'],['and',['provider_id' => $valor],['aviso_id' => $aviso_id]]);
                                $actualiza_aviso->execute();
                            }

                            sleep(10);
                            

                        }

                    }

                } */

        }

/* Funcion ya no se esta utilizando */
        public function actionAvisoSeisHoras(){
/* 
                $query = new Query;
                $query	->select('usuarios.email, usuarios.user_id')
                        ->from('usuarios')
                        ->where(['usuarios.role' => 'VALIDADOR PROVEEDORES', 'usuarios.status' => 'ACTIVO']);
                $command = $query->createCommand();
                $correos_validadores = ArrayHelper::map($command->queryAll(),'email','user_id');

                $array = [
                    // 'rfc' => 'Datos legales',
                    // 'alta_hacienda' => 'Actividad ecónomica',
                    // 'intervencion_bancaria' => 'Método de pago',
                    // 'ubicacion' => 'Ubicación',
                    'fotografia_negocio' => 'Fotografía',
                    // 'clientes_contratos' => 'Credenciales',
                    // 'curriculum' => 'Mi perfil',
                    // 'ultima_declaracion' => 'Datos Financieros'
                ];


                foreach($correos_validadores as $correo => $user_id){    
                    
                    foreach($array as $tabla=>$modulo){

                        $validado = new Query;
                        $validado->select('provider_id')
                                ->from('provider.'.$tabla)
                                ->where("status_bys = 'PENDIENTE POR VALIDAR' 
                                and created_date::timestamp <= (now() - interval '66 hours') 
                                and created_date::timestamp >= (now() - interval '72 hours')");
                        $command = $validado->createCommand();
                        $datos = ArrayHelper::getColumn($command->queryAll(),'provider_id');                    
                        
                        foreach($datos as $valor ){

                            $avisos = new Query;
                            $avisos->select('*')
                                    ->from('public.avisos')
                                    ->where(['provider_id' => $valor, 'status' => 'PENDIENTE POR MANDAR', 'tipo_aviso' => '6 HORAS']);
                            $comando = $avisos->createCommand();
                            $datos_avisos = ArrayHelper::getColumn($comando->queryAll(),'provider_id');
                            if($datos_avisos){

                            }else{
                                Yii::$app->db->createCommand()->insert('public.avisos', [
                                    'provider_id' => $valor,
                                    'modulo' => $modulo,
                                    'status' => 'PENDIENTE POR MANDAR', 
                                    'tipo_aviso' => '6 HORAS',
                                    'user_id' => $user_id,                                    
                                ])->execute();
                            }

                            $nombre_query = Provider::find()->select(['name_razon_social' => 'trim(concat_ws(\' \',name_razon_social,pf_nombre,pf_ap_paterno,pf_ap_materno))'])->where('provider_id='.$valor)->all();
                            $nombre_dato = ArrayHelper::getColumn($nombre_query,'name_razon_social');
                            $nombre = $nombre_dato[0];

                            $avisos = new Query;
                            $avisos->select('*')
                                    ->from('public.avisos')
                                    ->where(['provider_id' => $valor, 'status' => 'PENDIENTE POR MANDAR', 'tipo_aviso' => '6 HORAS']);
                            $comando = $avisos->createCommand();

                            $aviso_id = ArrayHelper::getColumn($comando->queryAll(),'aviso_id');

                            $datos_avisos = ArrayHelper::getColumn($comando->queryAll(),'provider_id');

                            if($datos_avisos){
//                                    $mailer = Yii::$app->mailer;
//
//                                    $return = $mailer->compose('/provider/correos/pendiente_aviso_seis',['modulo' => $modulo, 'nombre' => $nombre])
//                                    ->setFrom('<EMAIL>')
//                                    ->setTo($correo)
//                                    ->setSubject('Tiene 6 horas para validar')
//                                    ->send();

                                GeneralController::sendEmail('/provider/correos/pendiente_aviso_seis',null,$correo,'Tiene 6 horas para validar',['modulo' => $modulo, 'nombre' => $nombre]);

                                $actualiza_aviso = Yii::$app->db->createCommand()
                                                ->update('public.avisos',['status' => 'MANDADO'],['and',['provider_id' => $valor],['aviso_id' => $aviso_id]]);
                                $actualiza_aviso->execute();
                            }

                            sleep(10);
                            

                        }

                    }

                } */
        }
            
/* Funcion ya no se esta utlizando */
        public function actionAvisoExpirado(){
            /* 
                $query = new Query;
                $query	->select('usuarios.email, usuarios.user_id')
                        ->from('usuarios')
                        ->where(['usuarios.role' => 'VALIDADOR PROVEEDORES', 'usuarios.status' => 'ACTIVO']);
                $command = $query->createCommand();
                $correos_validadores = ArrayHelper::map($command->queryAll(),'email','user_id');
                $array = [
                    // 'rfc' => 'Datos legales',
                    // 'alta_hacienda' => 'Actividad ecónomica',
                    // 'intervencion_bancaria' => 'Método de pago',
                    // 'ubicacion' => 'Ubicación',
                    'fotografia_negocio' => 'Fotografía',
                    // 'clientes_contratos' => 'Credenciales',
                    // 'curriculum' => 'Mi perfil',
                    // 'ultima_declaracion' => 'Datos Financieros'
                ];

            foreach($correos_validadores as $correo => $user_id){    
                
                foreach($array as $tabla=>$modulo){

                    $validado = new Query;
                    $validado->select('provider_id')
                            ->from('provider.'.$tabla)
                            ->where("status_bys = 'PENDIENTE POR VALIDAR' 
                            and created_date::timestamp <= (now() - interval '72 hours')");
                    $command = $validado->createCommand();
                    $datos = ArrayHelper::getColumn($command->queryAll(),'provider_id');                    
                    
                    foreach($datos as $valor ){

                        $avisos = new Query;
                        $avisos->select('*')
                                ->from('public.avisos')
                                ->where(['provider_id' => $valor, 'status' => 'PENDIENTE POR MANDAR', 'tipo_aviso' => 'EXPIRADO']);
                        $comando = $avisos->createCommand();
                        $datos_avisos = ArrayHelper::getColumn($comando->queryAll(),'provider_id');
                        if($datos_avisos){

                        }else{
                            Yii::$app->db->createCommand()->insert('public.avisos', [
                                'provider_id' => $valor,
                                'modulo' => $modulo,
                                'status' => 'PENDIENTE POR MANDAR', 
                                'tipo_aviso' => 'EXPIRADO',
                                'user_id' => $user_id,
                            ])->execute();
                        }

                        $nombre_query = Provider::find()->select(['name_razon_social' => 'trim(concat_ws(\' \',name_razon_social,pf_nombre,pf_ap_paterno,pf_ap_materno))'])->where('provider_id='.$valor)->all();
                        $nombre_dato = ArrayHelper::getColumn($nombre_query,'name_razon_social');
                        $nombre = $nombre_dato[0];

                        $avisos = new Query;
                        $avisos->select('*')
                                ->from('public.avisos')
                                ->where(['provider_id' => $valor, 'status' => 'PENDIENTE POR MANDAR', 'tipo_aviso' => 'EXPIRADO']);
                        $comando = $avisos->createCommand();

                        $aviso_id = ArrayHelper::getColumn($comando->queryAll(),'aviso_id');                        

                        $datos_avisos = ArrayHelper::getColumn($comando->queryAll(),'provider_id');

                        if($datos_avisos){
//
//                                $mailer = Yii::$app->mailer;
//
//                                $return = $mailer->compose('/provider/correos/aviso_expirado',['modulo' => $modulo, 'nombre' => $nombre])
//                                ->setFrom('<EMAIL>')
//                                ->setTo($correo)
//                                ->setSubject('Tiempo expirado para validar')
//                                ->send();

                            GeneralController::sendEmail('/provider/correos/aviso_expirado',null,$correo,'Tiempo expirado para validar',['modulo' => $modulo, 'nombre' => $nombre]);

                            $actualiza_aviso = Yii::$app->db->createCommand()
                                            ->update('public.avisos',['status' => 'MANDADO'],['and',['provider_id' => $valor],['aviso_id' => $aviso_id]]);
                            $actualiza_aviso->execute();
                        }

                        sleep(10);
                        

                    }

                }

            } */
        }

        /* Funcion ya no se esta utilizando */
        public function actionAvisoExpiradoAdmin(){
            /* 
                $query = new Query;
                $query	->select('usuarios.email, usuarios.user_id')
                        ->from('usuarios')
                        ->where(['usuarios.role' => 'ADMIN PROVIDER', 'usuarios.status' => 'ACTIVO']);
                $command = $query->createCommand();
                $correos_admin = ArrayHelper::map($command->queryAll(),'email','user_id');

                $proveedores = new Query;
                $proveedores ->select(['nombres' => 'trim(concat_ws(\' \',nombre,primer_apellido,segundo_apellido))'])
                        ->from('usuarios')
                        ->where(['usuarios.role' => 'VALIDADOR PROVEEDORES', 'usuarios.status' => 'ACTIVO']);
                $c = $proveedores->createCommand();
                $nombre_proveedores = ArrayHelper::getColumn($c->queryAll(),'nombres');
                $proveedor = json_encode($nombre_proveedores);

                $cuenta = new Query;                
                $cuenta ->select(['COUNT(*) AS cuenta'])
                    ->from('usuarios')
                    ->where(['usuarios.role' => 'VALIDADOR PROVEEDORES', 'usuarios.status' => 'ACTIVO']);
                $d = $cuenta->createCommand();

                $numero = json_encode(ArrayHelper::getColumn($d->queryAll(),'cuenta'));
                               
                $array = [
                    // 'rfc' => 'Datos legales',
                    // 'alta_hacienda' => 'Actividad ecónomica',
                    // 'intervencion_bancaria' => 'Método de pago',
                    // 'ubicacion' => 'Ubicación',
                    'fotografia_negocio' => 'Fotografía',
                    // 'clientes_contratos' => 'Credenciales',
                    // 'curriculum' => 'Mi perfil',
                    // 'ultima_declaracion' => 'Datos Financieros'
                ];

            foreach($correos_admin as $correo => $user_id){    
                
                foreach($array as $tabla=>$modulo){

                    $validado = new Query;
                    $validado->select('provider_id')
                            ->from('provider.'.$tabla)
                            ->where("status_bys = 'PENDIENTE POR VALIDAR' 
                            and created_date::timestamp <= (now() - interval '72 hours')");
                    $command = $validado->createCommand();
                    $datos = ArrayHelper::getColumn($command->queryAll(),'provider_id');                    
                    
                    foreach($datos as $valor ){

                        $avisos = new Query;
                        $avisos->select('*')
                                ->from('public.avisos')
                                ->where(['provider_id' => $valor, 'status' => 'PENDIENTE POR MANDAR', 'tipo_aviso' => 'EXPIRADO']);
                        $comando = $avisos->createCommand();
                        $datos_avisos = ArrayHelper::getColumn($comando->queryAll(),'provider_id');
                        if($datos_avisos){

                        }else{
                            Yii::$app->db->createCommand()->insert('public.avisos', [
                                'provider_id' => $valor,
                                'modulo' => $modulo,
                                'status' => 'PENDIENTE POR MANDAR', 
                                'tipo_aviso' => 'EXPIRADO',
                                'user_id' => $user_id,
                            ])->execute();
                        }

                        $nombre_query = Provider::find()->select(['name_razon_social' => 'trim(concat_ws(\' \',name_razon_social,pf_nombre,pf_ap_paterno,pf_ap_materno))'])->where('provider_id='.$valor)->all();
                        $nombre_dato = ArrayHelper::getColumn($nombre_query,'name_razon_social');
                        $nombre = $nombre_dato[0];

                        $avisos = new Query;
                        $avisos->select('*')
                                ->from('public.avisos')
                                ->where(['provider_id' => $valor, 'status' => 'PENDIENTE POR MANDAR', 'tipo_aviso' => 'EXPIRADO']);
                        $comando = $avisos->createCommand();

                        $aviso_id = ArrayHelper::getColumn($comando->queryAll(),'aviso_id');                        

                        $datos_avisos = ArrayHelper::getColumn($comando->queryAll(),'provider_id');

                        if($datos_avisos){
//                                $mailer = Yii::$app->mailer;
//
//                                $return = $mailer->compose('/provider/correos/aviso_expirado_admin',['modulo' => $modulo, 'nombre' => $nombre, 'proveedor' => $proveedor, 'numero' => $numero])
//                                ->setFrom('<EMAIL>')
//                                ->setTo($correo)
//                                ->setSubject('Tiempo expirado para validar')
//                                ->send();

                            GeneralController::sendEmail('/provider/correos/aviso_expirado_admin',null,'<EMAIL>','Tiempo expirado para validar',['modulo' => $modulo, 'nombre' => $nombre]);

                            $actualiza_aviso = Yii::$app->db->createCommand()
                                            ->update('public.avisos',['status' => 'MANDADO'],['and',['provider_id' => $valor],['aviso_id' => $aviso_id]]);
                            $actualiza_aviso->execute();
                        }
                        sleep(10);
                        

                    }

                }

            } */
        }


}
