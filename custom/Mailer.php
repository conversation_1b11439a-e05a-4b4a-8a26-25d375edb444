<?php

/*
 * This file is part of the Dektrium project.
 *
 * (c) Dektrium project <http://github.com/dektrium/>
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace app\custom;

use app\models\Token;
use app\models\Usuarios;
use Yii;
use yii\base\Component;

/**
 * Mailer.
 *
 * <AUTHOR> <<EMAIL>>
 */
class Mailer extends Component
{
    /** @var string */
    public $viewPath = '@app/views/mail';

    /** @var string|array Default: `Yii::$app->params['adminEmail']` OR `<EMAIL>` */
    public $sender;

    /** @var string */
    protected $welcomeSubject;

    /** @var string */
    protected $confirmationSubject;

    /** @var string */
    protected $reconfirmationSubject;

    /** @var string */
    protected $recoverySubject;

    /** @var \app\custom\Module */
    protected $module;

    /**
     * @return string
     */
    public function getWelcomeSubject()
    {
        if ($this->welcomeSubject == null) {
            $this->setWelcomeSubject(Yii::t('user', 'Welcome to {0}', Yii::$app->name));
        }

        return $this->welcomeSubject;
    }

    /**
     * @param string $welcomeSubject
     */
    public function setWelcomeSubject($welcomeSubject)
    {
        $this->welcomeSubject = $welcomeSubject;
    }

    /**
     * @return string
     */
    public function getConfirmationSubject()
    {
        if ($this->confirmationSubject == null) {
            $this->setConfirmationSubject(Yii::t('user', 'Confirm account on {0}', Yii::$app->name));
        }

        return $this->confirmationSubject;
    }

    /**
     * @param string $confirmationSubject
     */
    public function setConfirmationSubject($confirmationSubject)
    {
        $this->confirmationSubject = $confirmationSubject;
    }

    /**
     * @return string
     */
    public function getReconfirmationSubject()
    {
        if ($this->reconfirmationSubject == null) {
            $this->setReconfirmationSubject(Yii::t('user', 'Confirm email change on {0}', Yii::$app->name));
        }

        return $this->reconfirmationSubject;
    }

    /**
     * @param string $reconfirmationSubject
     */
    public function setReconfirmationSubject($reconfirmationSubject)
    {
        $this->reconfirmationSubject = $reconfirmationSubject;
    }

    /**
     * @return string
     */
    public function getRecoverySubject()
    {
        if ($this->recoverySubject == null) {
            $this->setRecoverySubject(Yii::t('user', 'Complete password reset on {0}', Yii::$app->name));
        }

        return $this->recoverySubject;
    }

    /**
     * @param string $recoverySubject
     */
    public function setRecoverySubject($recoverySubject)
    {
        $this->recoverySubject = $recoverySubject;
    }

    /** @inheritdoc */
    public function init()
    {
        $this->module = Yii::$app->getModule('user');
        parent::init();
    }

    /**
     * Sends an email to a user after registration.
     *
     * @param Usuarios  $usuarios
     * @param Token $token
     * @param bool  $showPassword
     *
     * @return bool
     */
    public function sendWelcomeMessage(Usuarios $usuarios, Token $token = null, $showPassword = false)
    {
        return $this->sendMessage(
            $usuarios->email,
            $this->getWelcomeSubject(),
            'welcome',
            ['user' => $usuarios, 'token' => $token, 'module' => $this->module, 'showPassword' => $showPassword]
        );
    }

    /**
     * Sends an email to a user with confirmation link.
     *
     * @param Usuarios  $usuarios
     * @param Token $token
     *
     * @return bool
     */
    public function sendConfirmationMessage(Usuarios $usuarios, Token $token)
    {
        return $this->sendMessage(
            $usuarios->email,
            $this->getConfirmationSubject(),
            'confirmation',
            ['user' => $usuarios, 'token' => $token]
        );
    }

    /**
     * Sends an email to a user with reconfirmation link.
     *
     * @param Usuarios  $usuarios
     * @param Token $token
     *
     * @return bool
     */
    public function sendReconfirmationMessage(Usuarios $usuarios, Token $token)
    {
        if ($token->type == Token::TYPE_CONFIRM_NEW_EMAIL) {
            $email = $usuarios->unconfirmed_email;
        } else {
            $email = $usuarios->email;
        }

        return $this->sendMessage(
            $email,
            $this->getReconfirmationSubject(),
            'reconfirmation',
            ['user' => $usuarios, 'token' => $token]
        );
    }

    /**
     * Sends an email to a user with recovery link.
     *
     * @param Usuarios  $usuarios
     * @param Token $token
     *
     * @return bool
     */
    public function sendRecoveryMessage(Usuarios $usuarios, Token $token)
    {
        return $this->sendMessage(
            $usuarios->email,
            $this->getRecoverySubject(),
            'recovery',
            ['user' => $usuarios, 'token' => $token]
        );
    }

    /**
     * @param string $to
     * @param string $subject
     * @param string $view
     * @param array  $params
     *
     * @return bool
     */
    protected function sendMessage($to, $subject, $view, $params = [])
    {
        /** @var \yii\mail\BaseMailer $mailer */
        $mailer = Yii::$app->mailer;
        $mailer->viewPath = $this->viewPath;
        $mailer->getView()->theme = Yii::$app->view->theme;

        if ($this->sender === null) {
            $this->sender = isset(Yii::$app->params['adminEmail']) ?
                Yii::$app->params['adminEmail']
                : '<EMAIL>';
        }

        try{
            return $mailer->compose(['html' => $view, 'text' => 'text/' . $view], $params)
                ->setTo($to)
                ->setFrom($this->sender)
                ->setSubject($subject)
                ->send();
        }catch (\Exception $e){

        }
    }
}
