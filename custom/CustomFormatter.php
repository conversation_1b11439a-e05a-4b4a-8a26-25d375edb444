<?php
namespace app\custom;
use yii\helpers\Html;
use yii\helpers\Url;

class CustomFormatter extends \yii\i18n\Formatter{

    public function asFormatFecha($value){
        if($value==''){
            return 'No se agregó fecha';
        }else {
            $arrayMeses = array('En<PERSON>', 'Febrero', 'Mar<PERSON>', '<PERSON>bril', 'Mayo', '<PERSON><PERSON>',
                'Julio', 'Agosto', 'Septiembre', 'Octubre', 'Noviembre', 'Diciembre');
            return $arrayMeses[date("m", strtotime($value)) - 1] . " " . date("j", strtotime($value)) . ", " . date("Y", strtotime($value));
        }
    }

    public function asFormatPdf($value){
        if($value=='' || $value == '|'){
            return 'No se agregó archivo';
        }else {
            if(!strpos($value,'|')){
                return Html::button('<i class="material-icons">insert_drive_file</i> Archivo', ['value' => '/site/visor?pdf='.$value, 'title' => 'Archivo adjunto',
                    'class' => 'btn-archivo showModalButtonImage ']);
                //Html::a('<i class="material-icons">insert_drive_file</i> ARCHIVO', '/'. $value, ['class' => 'btn-archivo','target' => '_blank','data-pjax' => '0']);
            }else{
                $data = explode('|',$value);
                $html = '';
                foreach ($data as $d){
                    if($d) {
                        $html .= ' <a href="' . $d . '" target="_blank"><button class="btn-archivo"><i class="material-icons">insert_drive_file</i> Archivo </button></a>';
                    }
                }
                return $html;
            }

        }
    }

    public function asViewAction($url){
        return Html::button('<i class="material-icons">visibility</i> Ver',
            [
                'value' => $url,
                'title'=>'Archivo adjunto',
                'class' => 'btn pull-right showModalButtonImage'
            ]
        );
    }

    public function asViewPdf($value){
        return Html::button('<i class="material-icons">visibility</i> Ver',
            [
                'value' => '/site/visor?pdf='.$value,
                'title'=>'Archivo adjunto',
                'class' => 'btn pull-right '.(( $value == null ) ? 'disabled' : 'showModalButtonImage')
            ]
        );
    }

    public function asViewVideo($value){
        return Html::button('<i class="material-icons">visibility</i> Ver',
            [
                'value' =>$value,
                'title'=>'Ver',
                'class' => 'btn pull-right '.(( $value == null ) ? 'disabled' : 'showVideoModal')
            ]
        );
    }

    public function asFormatVideof($value){
        if($value==''){
            return 'No se agregó Video';
        }else {
            return Html::a('<i class="material-icons">play_circle_outline</i> Ver video', $value, ['target' => '_blank','style'=>'position:relative; z-index:1;']);
        }
    }

    public function asFormatDinero($value){
        $nombre_format = number_format($value, 2, '.', ',');
        return '$ '.$nombre_format;
    }

    public function asFormatPdfOLD($value){
        if($value==''){
            return 'No se agregó archivo';
        }else {
            return Html::button('<i class="material-icons">insert_drive_file</i> Archivo', ['value' => '/site/visor?pdf=' . $value, 'title' => ' - ',
                'class' => 'btn pull-right btn-archivo showModalButtonImage ','style'=>'height:35px']);
        }
    }

    public function asLink($value){
        if($value==''){
            return ' - ';
        }else {
            return Html::a(' Ver ',
                Url::home().$value,
                ['target' => '_blank','style'=>'position:relative; z-index:1;']);
        }
    }


    public function asFormatNumber2($value){
        if($value==''){
            return '';
        }else {
            return number_format($value);
        }
    }

    public function asJson($value){
        $str = '';
        try{
            if(in_array(gettype($value),['object','array'])){
                foreach ($value as $key=>$val){
                    if(in_array(gettype($val),['object','array'])){
                        $str.= "<b>".ucfirst($key).":</b> ";
                        foreach ($val as $v){
                            $str.= "$v, ";
                        }
                    }elseif(in_array(gettype($val),['boolean'])){
                        $str.= "<b>".ucfirst($key).":</b> ".($val?'Si':'No') ;
                    }else{
                        if(strlen($val)!=0 && $val !=' '){
                            $str.= "<b>".ucfirst($key).":</b> $val ";
                        }
                    }
                    $str.= "<br>";
                }
            }else{
                $str .= "[]";
            }
        }catch (Exception $e){
            return "-";
        }
        return $str;
    }



}