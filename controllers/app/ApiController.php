<?php
namespace app\controllers\app;


use app\controllers\concursos\InscripcionController;
use app\controllers\DocumentosController;
use app\models\RegistroConcursos;
use app\models\Provider;
use app\models\Usuarios;
use Yii;
use <PERSON><PERSON>bu<PERSON>\JWT\Signer\Hmac\Sha256;
use yii\filters\auth\HttpBearerAuth;
use yii\filters\VerbFilter;
use yii\web\Controller;
use yii\web\Response;

class ApiController extends Controller
{

    public function beforeAction($action){
        \Yii::$app->response->format = Response::FORMAT_JSON;
        return parent::beforeAction($action);
    }

    public function behaviors()
    {
        return [
            'authenticator' => [
                'class' => HttpBearerAuth::class,
                'except' => ['login', 'firmar']
            ],
            'verbs' => [
                'class' => VerbFilter::class,
                'actions' => [
                    'login' => ['POST'],
                ],
            ],
        ];
    }


    private static function generateToken($user,$provider_id){
        $token =  Yii::$app->jwt->getBuilder()->setIssuer('https://proveedores.nl.gob.mx')
            ->setAudience('https://proveedores.nl.gob.mx')
            ->setId('4f1g23a12aa', true)
            ->set('userId',$user->user_id)
            ->set('username',$user->username)
            ->set('providerId',$provider_id)
            ->setIssuedAt(time())
            ->setExpiration(time() + (2 * 24 * 60))
            ->sign(new Sha256(), Yii::$app->params['sign'])
            ->getToken();
        return (string) $token;
    }


    public function actionLogin(){
        $username = Yii::$app->request->post('username');
        $pass = Yii::$app->request->post('password');

        $model = Usuarios::findByUsername($username);

        if ($model && $model->validatePassword($pass)) {
            $provider = Provider::find()->where(['user_id'=>$model->user_id])->one();
            return [
                'status' => true,
                'token' => self::generateToken($model,$provider->provider_id)
            ];
        }
        return [
            'status' => false,
            'message' => 'Credenciales incorrectas.',
        ];

    }


    public function actionGetdataprovider(){
        $token = str_replace('Bearer ','',Yii::$app->request->headers->get('authorization'));
        $prov = json_decode(base64_decode(explode(".", $token)[1]))->user;

        $dataProv = InscripcionController::getDataConcurso($prov);
        $dataReturn = ['status'=>true];
        if($dataProv){
            $dataReturn['data'] = $dataProv;
        }else{
            $dataReturn['status'] = false;
        }
        return $dataReturn;

    }

    public function actionGetConcursos($limit = 10, $fallo = 0, $offset = 0, $order = null){
        $concursos = RegistroConcursos::find();
        if(!$fallo){
            $concursos->where("fecha_h_fallo_definitivo > '" . date('Y-m-d H:i:s') . "'");
        }
        if($order == "asc"){
            $concursos->orderBy('concurso_id asc');
        }else{
            $concursos->orderBy('concurso_id desc');
        }
        if((ctype_digit($offset) || is_int($offset)) && (ctype_digit($limit) || is_int($limit))){
            $concursos->offset($offset * $limit);
        }
        $concursos->limit($limit);
        return $concursos->all();
    }

    public function actionFirmar($id){
        $token = str_replace('Bearer ','',Yii::$app->request->headers->get('authorization'));
        $user_id = json_decode(base64_decode(explode(".", $token)[1]))->user_id;
        $provider = Provider::findOne(['user_id' => $user_id]);
        $concurso = RegistroConcursos::findOne(intval($id));
        $url_doc = DocumentosController::generarDocumentoInscripcionConcurso($concurso, $provider, $user_id, null, Yii::$app->request->post('firma'));
        return $url_doc;
    }

}