<?php

namespace app\controllers;

use app\helpers\GeneralController;
use app\models\ConceptoFamilia;
use app\models\ConceptoFamiliaInegi;
use app\models\ConceptoFamiliaSearch;
use yii\web\Controller;
use yii\web\NotFoundHttpException;
use yii\filters\VerbFilter;

/**
 * ConceptoFamiliaController implements the CRUD actions for ConceptoFamilia model.
 */
class ConceptoFamiliaController extends GeneralController
{
    /**
     * @inheritDoc
     */
    public function behaviors()
    {
        return array_merge(
            parent::behaviors(),
            [
                'verbs' => [
                    'class' => VerbFilter::className(),
                    'actions' => [
                        'delete' => ['POST'],
                    ],
                ],
            ]
        );
    }

    /**
     * Lists all ConceptoFamilia models.
     * @return mixed
     */
    public function actionIndex()
    {
        $searchModel = new ConceptoFamiliaSearch();
        $dataProvider = $searchModel->search($this->request->queryParams);

        return $this->render('index', [
            'searchModel' => $searchModel,
            'dataProvider' => $dataProvider,
        ]);
    }

    /**
     * Displays a single ConceptoFamilia model.
     * @param int $concepto_familia_id Concepto Familia ID
     * @return mixed
     * @throws NotFoundHttpException if the model cannot be found
     */
    public function actionView($id)
    {
        return $this->render('view', [
            'model' => $this->findModel($id),
        ]);
    }

    /**
     * Creates a new ConceptoFamilia model.
     * If creation is successful, the browser will be redirected to the 'view' page.
     * @return mixed
     */
    public function actionCreate()
    {
        $model = new ConceptoFamilia();

        $modelInegi = new ConceptoFamiliaInegi();
        if ($this->request->isPost) {
            $t = \Yii::$app->db->beginTransaction();
            if ($model->load($this->request->post()) && $modelInegi->load($this->request->post())) {
                if($model->save()){
                    $modelInegi->concepto_familia_id = $model->concepto_familia_id;
                    if($modelInegi->save()){
                        $t->commit();
                        return $this->redirect(['view', 'id' => $model->concepto_familia_id]);
                    }else{
                        $t->rollBack();
                    }
                }else{$t->rollBack();}
            }
        } else {
            $model->loadDefaultValues();
        }

        return $this->render('create', [
            'model' => $model,
            'modelInegi' => $modelInegi
        ]);
    }

    /**
     * Updates an existing ConceptoFamilia model.
     * If update is successful, the browser will be redirected to the 'view' page.
     * @param int $concepto_familia_id Concepto Familia ID
     * @return mixed
     * @throws NotFoundHttpException if the model cannot be found
     */
    public function actionUpdate($id)
    {
        $model = $this->findModel($id);
        $modelInegi = $this->findModelInegi($id);

        if ($this->request->isPost && $model->load($this->request->post()) && $modelInegi->load($this->request->post())) {
            $model->save();
            $modelInegi->save();
            return $this->redirect(['view', 'id' => $model->concepto_familia_id]);
        }

        return $this->render('update', [
            'model' => $model,
            'modelInegi' => $modelInegi
        ]);
    }

    /**
     * Deletes an existing ConceptoFamilia model.
     * If deletion is successful, the browser will be redirected to the 'index' page.
     * @param int $concepto_familia_id Concepto Familia ID
     * @return mixed
     * @throws NotFoundHttpException if the model cannot be found
     */
    public function actionDelete($id)
    {
        $this->findModel($id)->delete();

        return $this->redirect(['index']);
    }

    /**
     * Finds the ConceptoFamilia model based on its primary key value.
     * If the model is not found, a 404 HTTP exception will be thrown.
     * @param int $concepto_familia_id Concepto Familia ID
     * @return ConceptoFamilia the loaded model
     * @throws NotFoundHttpException if the model cannot be found
     */
    protected function findModel($id)
    {
        if (($model = ConceptoFamilia::findOne($id)) !== null) {
            return $model;
        }

        throw new NotFoundHttpException('The requested page does not exist.');
    }

    protected function findModelInegi($id)
    {
        if (($model = ConceptoFamiliaInegi::findOne(['concepto_familia_id' => $id])) !== null) {
            return $model;
        }

        throw new NotFoundHttpException('The requested page does not exist.');
    }
}
