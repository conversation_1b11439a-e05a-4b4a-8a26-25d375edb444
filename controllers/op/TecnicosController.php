<?php

namespace app\controllers\op;

use app\helpers\GeneralController;
use app\models\DatosValidados;
use app\models\Organigrama;
use app\models\Provider;
use app\models\Status;
use app\models\Usuarios;
use kartik\form\ActiveForm;
use Yii;
use app\models\PersonalTecnico;
use app\models\PersonalTecnicoSearch;
use yii\filters\VerbFilter;
use yii\web\Response;
use yii\web\UploadedFile;

/**
 * PersonalTecnicoController implements the CRUD actions for PersonalTecnico model.
 */
class TecnicosController extends GeneralController
{
    /**
     * @inheritdoc
     */

    public function behaviors()
    {
        return [
            'verbs' => [
                'class' => VerbFilter::className(),
                'actions' => [
                    'delete' => ['POST','GET'],
                ],
            ],
        ];
    }


    public function actionIndex()
    {
        $provider_id = Yii::$app->user->identity->providerid;
        $model = $this->findModelOrganigrama($provider_id);

        $rechazo = [];
        if($model->organigrama_id!==null){
            $rechazo = Status::find()->getStatus($model->organigrama_id,'organigrama','op');
        }


        $m = new PersonalTecnicoSearch();
        $modelos = (Object)[
            'edicion' => $m->search(Yii::$app->request->queryParams,'edicionFilter'),
            'rechazados' => $m->search(Yii::$app->request->queryParams,'rechazadosFilter'),
            'validados' => $m->search(Yii::$app->request->queryParams,'validadosFilter'),
            'pendientes' => $m->search(Yii::$app->request->queryParams,'pendientesFilter'),
            'filtro' => $m
        ];

        return $this->render('index', [
            'rechazo'=>$rechazo,
            'model' => $model,
            'modelos' => $modelos
        ]);
    }


    public function actionView($id)
    {
        $model_status = new Status();
        $model_status->scenario = Status::SCENARIO_CREATE;
        return $this->renderAjax('view', [
            'model' => $this->findModel(intval(base64_decode($id))),
            'model_status' => $model_status
        ]);
    }


    public function actionUpdate($id=0)
    {
        if(Yii::$app->request->isAjax || Yii::$app->request->post()){
            $model = $this->findModel(intval(base64_decode($id)));


            $model->url_id_oficialOLD = $model->url_id_oficial;
            $model->url_cedulaOLD = $model->url_cedula;
            $model->url_curriculumOLD = $model->url_curriculum;
            $model->comprobante_relacionOLD = $model->comprobante_relacion;

            $rechazo = [];
            if($model->personal_tecnico_id!==null){
                $rechazo = Status::find()->getStatus($model->personal_tecnico_id,'personal_tecnico','op');
            }

            $transaccion = $model->getDb()->beginTransaction();
            $model->provider_id = Yii::$app->user->identity->providerid;
            if(Yii::$app->request->isAjax && $model->load(Yii::$app->request->post())){
                Yii::$app->response->format = Response::FORMAT_JSON;
                return ActiveForm::validate($model);
            }else if ($model->load(Yii::$app->request->post())) {
                $model->status_op = Status::STATUS_ENEDICION;

                $this->makeDir($model->path);

                if(!empty($model->url_id_oficial) && $model->url_id_oficial!=$model->url_id_oficialOLD){
                    $new_nameId = str_replace('archivos_tmp/',$model->path.'/',$model->url_id_oficial);
                    $this->copyFile($model->url_id_oficial,$new_nameId);
                    $model->url_id_oficial = $new_nameId;
                }

                if(!empty($model->url_cedula) && $model->url_cedula!=$model->url_cedulaOLD){
                    $new_nameCed = str_replace('archivos_tmp/',$model->path.'/',$model->url_cedula);
                    $this->copyFile($model->url_cedula,$new_nameCed);
                    $model->url_cedula = $new_nameCed;
                }

                if(!empty($model->url_curriculum) && $model->url_curriculum!=$model->url_curriculumOLD){
                    $new_nameCu = str_replace('archivos_tmp/',$model->path.'/',$model->url_curriculum);
                    $this->copyFile($model->url_curriculum,$new_nameCu);
                    $model->url_curriculum = $new_nameCu;
                }

                if(!empty($model->comprobante_relacion) && $model->comprobante_relacion!=$model->comprobante_relacionOLD){
                    $new_nameCr = str_replace('archivos_tmp/',$model->path.'/',$model->comprobante_relacion);
                    $this->copyFile($model->comprobante_relacion,$new_nameCr);
                    $model->comprobante_relacion = $new_nameCr;
                }

                if( $model->save()){
                    if(($requi_status = Status::find()->where(['and',['register_id' => $model->personal_tecnico_id],['status_op' => Status::STATUS_PENDIENTE],['modelo'=>'personal_tecnico']])->one())!==null){
                        $requi_status->status_op = Status::STATUS_TERMINADO_PRO;
                        $requi_status->save();
                    }
                    $transaccion->commit();
                    Yii::$app->session->setFlash('success','Registro creado/actualizado con éxito!');
                    return $this->redirect(['index']);
                }else{
                    return $this->goHome();
                }

            }
            return $this->renderAjax('update', [
                'model' => $model,
                'rechazo' => $rechazo,
            ]);
        }

        return $this->goHome();

    }


    public function actionDelete($id=0)
    {
        $model = PersonalTecnico::find()
            ->where(['and',['personal_tecnico_id' => intval(base64_decode($id)),'provider_id'=> Yii::$app->user->identity->providerid, 'status_op'=> $this->statusParaEliminar]])
            ->one();
        if($model){
            $this->deleteFile($model->url_id_oficial);
            $this->deleteFile($model->url_cedula);
            $this->deleteFile($model->url_curriculum);
            $this->deleteFile($model->comprobante_relacion);
            $model->activo = false;
            $model->save();
            Yii::$app->session->setFlash('success','Registro eliminado con éxito!');
        }else{
            Yii::$app->session->setFlash('error','Registro no eliminado, valide sus datos o intente nuevamente');
        }
        return $this->redirect(['index']);
    }


    public function actionTerminar($id=null){

        $IdLastVal = $this->saveLastSendValidation(Yii::$app->user->identity->getProvider(),'validPersonal','personal_tecnico','op');

        if($id!=null){
            $status = $this->terminarUno('PersonalTecnico',intval(base64_decode($id)),'personal_tecnico_id','personal_tecnico','status_op','',$IdLastVal);
        }else{
            $status = $this->terminarTodos('PersonalTecnico','personal_tecnico_id','personal_tecnico','status_op','',$IdLastVal);
        }

        if($status){
            $correos_validadores = self::getEmailValidador(2,'op');

            self::sendEmail('/provider/correos/nuevo', null, $correos_validadores, 'Nuevo proveedor', ['modulo' => 'Datos tecnicos','tipo_provider' => 'op']);

            self::AllSendNotification(null,'op',null,'DATOS TECNICOS','Técnicos');

            $provider = Provider::findOne(Yii::$app->user->identity->providerid);
            GeneralController::sendModuloRevisionEmail($provider->email, 'Datos Técnicos (Personal Técnico)');
        }
        
        return $this->redirect(['index']);
    }


    public function actionTerminarorganigrama(){
        $model = Organigrama::find()->where(['and',['in','status_op',$this->statusParaTerminar],['provider_id'=> Yii::$app->user->identity->providerid]])->one();
        if(!$model){
            return $this->redirect('index');
        }
        if( $model->url_archivo && $model->url_archivo != '' ){
            $model->status_op = Status::STATUS_PORVALIDAR;
            $model->save();
            Status::updateAll(['status_op' => Status::STATUS_TERMINADO_PRO],['register_id' => $model->organigrama_id,'modelo'=>'organigrama']);
            Yii::$app->session->setFlash('success','Registro enviado a validar correctamente!');

            $correos_validadores = self::getEmailValidador(2);

            $IdLastVal = $this->saveLastSendValidation($model->provider_id,'validOrganigrama','organigrama','op');
            $this->saveLastSendValidationRelation($IdLastVal,$model->provider_id,'organigrama',$model->organigrama_id,'op');

            self::sendEmail('/provider/correos/nuevo', null, $correos_validadores, 'Nuevo proveedor', ['modulo' => 'Organigrama','tipo_provider' => 'op']);

            self::AllSendNotification(null,'op',null,'DATOS TECNICOS','Organigrama');

            $provider = Provider::findOne(Yii::$app->user->identity->providerid);
            GeneralController::sendModuloRevisionEmail($provider->email, 'Datos Técnicos (Organigrama)');

        }else{
            Yii::$app->session->setFlash('error', "Solo los registros llenados completamente, se pueden enviar a revisar.");
        }
        return $this->redirect('index');
    }


    public function actionViewValidar($id=null){

        $queryParams = Yii::$app->request->queryParams;
        $id = isset($queryParams['id']) ? intval( $queryParams['id'] ) : null;
        $opcionesArray = isset($queryParams['opciones']) ? $queryParams['opciones'] : null;
        $opcionesSerial = isset($opcionesArray) ? serialize($opcionesArray) : null;
        $opcionesBase64 = isset($opcionesSerial) ? base64_encode($opcionesSerial) : null;

        $model = PersonalTecnico::find()->where(['personal_tecnico_id' => intval($id)])->one();

        if(Yii::$app->user->isGuest || $id == null || !Yii::$app->user->can(Usuarios::ROLE_VALIDADOR_CONTRATISTAS) || !$model){
            $this->redirect('/');
        }

        $status = new Status();
        $status->scenario = Status::SCENARIO_CREATE;
        $validar = new DatosValidados();
        $validar->scenario = DatosValidados::SCENARIO_CREATE;
        $rechazo = Status::find()->getStatus($id,'personal_tecnico','op','TERMINADO PRO');

        return $this->renderAjax('view-validar',[
            'modelStatus' => $status,
            'modelValidado' => $validar,
            'opciones' => $opcionesBase64,
            'model' => $model,
            'rechazo' => $rechazo,
            'namePro' => $this->getNameProvider($model->provider_id)
        ]);

    }



    public function actionViewValidarOrganigrama($id=null){
        $queryParams = Yii::$app->request->queryParams;
        $id = isset($queryParams['id']) ? intval( $queryParams['id'] ) : null;
        $opcionesArray = isset($queryParams['opciones']) ? $queryParams['opciones'] : null;
        $opcionesSerial = isset($opcionesArray) ? serialize($opcionesArray) : null;
        $opcionesBase64 = isset($opcionesSerial) ? base64_encode($opcionesSerial) : null;

        if($id == null || strlen($id)>15 || !Provider::verifyProviderExistence($id)){
            $this->msgError();
        }
        $modelos = [];


        $models = (Object)[
            'organigrama' => $this->findModelOrganigrama($id)
        ];

        $model_status = new Status();
        $model_status->scenario = Status::SCENARIO_CREATE;
        $model_validado = new DatosValidados();
        $model_validado->scenario = DatosValidados::SCENARIO_CREATE;
        if(isset($models->organigrama->provider_id) && !empty($models->organigrama->provider_id)){
            $modelos[$models->organigrama->formName()] = $models->organigrama->attributes;
        }

        $rechazo = Status::find()->getStatus($models->organigrama->organigrama_id,'organigrama','op','TERMINADO PRO');
        return $this->renderAjax('view-validar-organigrama', [
            'modelStatus' => $model_status,
            'modelValidado' => $model_validado,
            'modelos' => base64_encode(json_encode($modelos)),
            'models' => $models,
            'provider_id' => $id,
            'rechazo' => $rechazo,
            'opciones' => $opcionesBase64,
            'namePro' => $this->getNameProvider($id)

        ]);

    }



    protected function findModel($id)
    {
        $model = PersonalTecnico::find()->where(['and',['provider_id'=>Yii::$app->user->identity->providerid],['personal_tecnico_id' => intval($id)]])->one();
        if (!$model) {
            $model = new PersonalTecnico();
        }
        return $model;
    }


    protected function findModelOrganigrama($id)
    {
        $model = Organigrama::find()->where(['provider_id'=>$id])->one();
        if (!$model) {
            $model = new Organigrama();
        }
        return $model;
    }


    public function actionUpdateorganigrama(){

        $provider_id = Yii::$app->user->identity->providerid;
        $model = $this->findModelOrganigrama($provider_id);

        $model->url_archivoOLD = $model->url_archivo;
        $model->contratos_bimOLD = $model->contratos_bim;
        $model->provider_id = $provider_id;

        if($model->load(Yii::$app->request->post())){

            $this->makeDir($model->path);

            if(!empty($model->url_archivo) && $model->url_archivo!=$model->url_archivoOLD){
                $new_nameArc = str_replace('archivos_tmp/',$model->path.'/',$model->url_archivo);
                $this->copyFile($model->url_archivo,$new_nameArc);
                $model->url_archivo = $new_nameArc;
            }

            if(!empty($model->contratos_bim) && $model->contratos_bim!=$model->contratos_bimOLD){
                $new_nameArc = str_replace('archivos_tmp/',$model->path.'/',$model->contratos_bim);
                $this->copyFile($model->contratos_bim,$new_nameArc);
                $model->contratos_bim = $new_nameArc;
            }

            $model->status_op = Status::STATUS_ENEDICION;
            if($model->uso_bim)
                $model->uso_bim = implode(', ',$model->uso_bim);
            if($model->save()){
                if(($requi_status = Status::find()->where(['and',['register_id' => $model->organigrama_id],['status_op' => Status::STATUS_PENDIENTE],['modelo'=>'organigrama']])->one())!==null){
                    $requi_status->status_op = Status::STATUS_TERMINADO_PRO;
                    $requi_status->save();
                }
                Yii::$app->session->setFlash('success','Registro creado/actualizado correctamente!');
                $this->redirect('index');
            }
        }
        if($model->uso_bim)
            $model->uso_bim = explode(', ',$model->uso_bim);
        return $this->renderAjax('updateorganigrama',[
            'modelOrganigrama' => $model
        ]);

    }





}
