<?php

namespace app\controllers\op;

use app\helpers\GeneralController;
use app\models\CatAsentamientos;
use app\models\CatEntidades;
use app\models\CatMunicipios;
use app\models\DatosValidados;
use app\models\Experiencia;
use app\models\Porcentaje;
use app\models\Provider;
use app\models\Status;
use app\models\Ubicacion;
use app\models\Usuarios;
use kartik\form\ActiveForm;
use Yii;
use yii\db\Query;
use app\models\FotografiaNegocio;
use app\models\FotografiaNegocioSearch;
use yii\helpers\ArrayHelper;
use yii\web\Controller;
use yii\web\NotFoundHttpException;
use yii\filters\VerbFilter;
use yii\web\Response;
use yii\web\UploadedFile;

if (Yii::$app->user->can(Usuarios::ROLE_PROVIDER)) {
    Yii::$app->params['provider_id'] = Yii::$app->user->identity->getProvider();
}

/**
 * FotografiaController implements the CRUD actions for Fotografia model.
 */
class FotografiaController extends GeneralController
{
    /**
     * @inheritdoc
     */
    public function behaviors()
    {
        return [
            'verbs' => [
                'class' => VerbFilter::className(),
                'actions' => [
                    'delete' => ['POST','GET'],
                ],
            ],
        ];
    }

    /**
     * Lists all Fotografia models.
     * @return mixed
     */
    public function actionIndex($id = null)
    {
        $id = intval($id);
        if (Yii::$app->user->can(Usuarios::ROLE_PROVIDER)) {
            $id = Yii::$app->params['provider_id'];
        }
        if ($id == null || strlen($id) > 15 || !Provider::verifyProviderExistence($id) || !Provider::verifyEtapa3($id) || !Yii::$app->user->can(Usuarios::ROLE_PROVIDER)) {
            $this->msgError();
        }
        $searchModel = new FotografiaNegocioSearch();
        $dataProvider = $searchModel->search(Yii::$app->request->queryParams, 'mainFilter');
        $rechazados = $searchModel->search(Yii::$app->request->queryParams, 'rechazadosFilter');
        $validados = $searchModel->search(Yii::$app->request->queryParams, 'validadosFilter');
        $pendientes = $searchModel->search(Yii::$app->request->queryParams, 'pendientesFilter');

        if (!Yii::$app->request->get()) {
            $model_porcentaje = $this->findModelPorcentaje($id);
            $verify = Porcentaje::find()->select(['register_id'])->where(['and', ['register_id' => $id], ['modelo' => 'fotografia_negocio']])->one();
            if ($verify) {
                if (($dataProvider->getTotalCount() + $pendientes->getTotalCount() + $rechazados->getCount() + $validados->getCount()) != 0) {
                    $model_porcentaje->porcentaje = (($pendientes->getTotalCount() + $validados->getCount()) / ($dataProvider->getTotalCount() + $pendientes->getTotalCount() + $rechazados->getCount() + $validados->getCount()) * 100);
                    \Yii::$app->db->createCommand("update provider.porcentaje set porcentaje = $model_porcentaje->porcentaje where modelo='fotografia_negocio' and register_id =:id")
                        ->bindValue(':id', $id)
                        ->execute();
                }
            } else {
                $porcen = new Porcentaje();
                $porcen->modelo = 'fotografia_negocio';
                $porcen->register_id = $id;
                if ($dataProvider->getTotalCount() + $pendientes->getTotalCount() + $validados->getCount() > 0) {
                    $porcen->porcentaje = round(($pendientes->getTotalCount() + $validados->getCount()) / ($dataProvider->getTotalCount() + $pendientes->getTotalCount() + $rechazados->getCount() + $validados->getCount()) * 100);
                } else {
                    $porcen->porcentaje = 0;
                }
                $porcen->save();
            }
        }
        $rechazo = Status::find()->getStatusRechazarCotejo($id);
        return $this->render('index', [
            'searchModel' => $searchModel,
            'dataProvider' => $dataProvider,
            'rechazados' => $rechazados,
            'validados' => $validados,
            'pendientes' => $pendientes,
            'rechazo' => $rechazo
        ]);
    }

    /**
     * Displays a single Fotografia model.
     * @param string $id
     * @return mixed
     */

    public static function getLocation($address)
    {
        $dir = urlencode($address);
        $url = "http://maps.googleapis.com/maps/api/geocode/json?address=$dir&sensor=false";

        $curl = curl_init();
        curl_setopt($curl, CURLOPT_URL, $url);
        curl_setopt($curl, CURLOPT_HEADER, false);
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($curl, CURLOPT_ENCODING, "");
        $curlData = curl_exec($curl);
        curl_close($curl);

        $address = json_decode($curlData, true);
        $location = [];
        if ($address && isset($address['results'][0]['geometry']['location'])) {
            $location = [$address['results'][0]['geometry']['location']['lat'], $address['results'][0]['geometry']['location']['lng']];
        }
        return $location;
    }


    public function actionView($id = null, $user_id = null)
    {
        $user_id = intval($user_id);
        $id = intval($id);
        $modelos = [];


        if ($user_id == null
            || strlen($user_id) > 15
            || !FotografiaNegocio::verifyFotografia($id)
            || (!FotografiaNegocio::verifyProviderExistenceFot($id) && Yii::$app->user->can(Usuarios::ROLE_PROVIDER))
            || (!Provider::verifyProvider($user_id) && Yii::$app->user->can(Usuarios::ROLE_PROVIDER))
            || !Provider::verifyEtapa3($user_id)) {
            $this->msgError();
        }
        $model = $this->findModel($id);
        $ubicacion = $this->findModelUbicacion($model->ubicacion_id);
        $model_status = new Status();
        $model_status->scenario = Status::SCENARIO_CREATE;
        $num = !empty($model->provider->numero_fiscal) ? $model->provider->numero_fiscal : '';
        $calle = !empty($model->provider->calle_fiscal) ? $model->provider->calle_fiscal : '';
        $colonia = !empty($model->provider->colonia_fiscal) ? CatAsentamientos::find()->select(['asentamiento_id', 'nombre'])->where(['asentamiento_id' => $model->provider->colonia_fiscal])->one()['nombre'] : '';
        $city = !empty($model->provider->city_fiscal) ? CatMunicipios::find()->select(['nombre'])->where(['municipio_id' => $model->provider->city_fiscal])->one()['nombre'] : '';
        $state_col = $model->provider->colonia_fiscal ? CatAsentamientos::find()->select(['entidad_id'])->where(['asentamiento_id' => $model->provider->colonia_fiscal])->one()['entidad_id'] : '';
        $state = !empty($state_col) ? CatEntidades::find()->select(['nombre'])->where(['entidad_id' => $state_col])->one()['nombre'] : '';
        $address = $calle . ' ' . $num . ' ' . $colonia . ' ' . $city . ' ' . $state;
        $location = Self::getLocation($address);
        if (Yii::$app->user->can(Usuarios::ROLE_VALIDADOR_CONTRATISTAS)) {
            //Usuarios::validarResponsabilidadFoto()) {
            if (isset($model->provider_id) && !empty($model->provider_id)) {
                $modelos[$model->formName()] = $model->attributes;
            }
            $modelos = base64_encode(json_encode($modelos));
        }
        //}
        return $this->renderAjax('view', [
            'model' => $model,
            'model_status' => $model_status,
            'location' => $location,
            'modelos' => $modelos,
            'ubicacion' => $ubicacion
        ]);
    }

    public function actionViewValidar($id = null, $user_id = null)
    {
        $queryParams = Yii::$app->request->queryParams;
        $id = isset($queryParams['id']) ? intval( $queryParams['id'] ) : null;
        $user_id = isset($queryParams['user_id']) ? intval( $queryParams['user_id'] ) : null;
        $opcionesArray = isset($queryParams['opciones']) ? $queryParams['opciones'] : null;
        $opcionesSerial = isset($opcionesArray) ? serialize($opcionesArray) : null;
        $opcionesBase64 = isset($opcionesSerial) ? base64_encode($opcionesSerial) : null;

        $modelos = [];


        if ($user_id == null
            || strlen($user_id) > 15
            || !FotografiaNegocio::verifyFotografia($id)
            || (!FotografiaNegocio::verifyProviderExistenceFot($id) && Yii::$app->user->can(Usuarios::ROLE_PROVIDER))
            || (!Provider::verifyProvider($user_id) && Yii::$app->user->can(Usuarios::ROLE_PROVIDER))
            || !Provider::verifyEtapa3($user_id)) {
            $this->msgError();
        }
        $model = $this->findModel($id);
        $ubicacion = $this->findModelUbicacion($model->ubicacion_id);
        $model_status = new Status();
        $model_status->scenario = Status::SCENARIO_CREATE;
        $model_validado = new DatosValidados();
        $model_validado->scenario = DatosValidados::SCENARIO_CREATE;
        $num = !empty($model->provider->numero_fiscal) ? $model->provider->numero_fiscal : '';
        $calle = !empty($model->provider->calle_fiscal) ? $model->provider->calle_fiscal : '';
        $colonia = !empty($model->provider->colonia_fiscal) ? CatAsentamientos::find()->select(['asentamiento_id', 'nombre'])->where(['asentamiento_id' => $model->provider->colonia_fiscal])->one()['nombre'] : '';
        $city = !empty($model->provider->city_fiscal) ? CatMunicipios::find()->select(['nombre'])->where(['municipio_id' => $model->provider->city_fiscal])->one()['nombre'] : '';
        $state_col = $model->provider->colonia_fiscal ? CatAsentamientos::find()->select(['entidad_id'])->where(['asentamiento_id' => $model->provider->colonia_fiscal])->one()['entidad_id'] : '';
        $state = !empty($state_col) ? CatEntidades::find()->select(['nombre'])->where(['entidad_id' => $state_col])->one()['nombre'] : '';
        $address = $calle . ' ' . $num . ' ' . $colonia . ' ' . $city . ' ' . $state;
        $location = Self::getLocation($address);
        if (Yii::$app->user->can(Usuarios::ROLE_VALIDADOR_CONTRATISTAS)) {
            if (isset($model->provider_id) && !empty($model->provider_id)) {
                $modelos[$model->formName()] = $model->attributes;
            }
            $modelos = base64_encode(json_encode($modelos));
        }
        $id_provider = FotografiaNegocio::find()->select('provider_id')->where(['fotografia_negocio_id' => $id])->one()['provider_id'];
        $rechazo = Status::find()->getStatus($id, 'fotografia_negocio', 'op','TERMINADO PRO');

        return $this->renderAjax('view-validar', [
            'model' => $model,
            'model_status' => $model_status,
            'location' => $location,
            'modelos' => $modelos,
            'id_provider' => $id_provider,
            'model_validado' => $model_validado,
            'ubicacion' => $ubicacion,
            'rechazo' => $rechazo,
            'opciones' => $opcionesBase64,
            'namePro' => $this->getNameProvider($id_provider)
        ]);
    }


    /**
     * Creates a new FotografiaNegocio model.
     * If creation is successful, the browser will be redirected to the 'view' page.
     * @return mixed
     */
    public function actionCreate($user_id)
    {
        if(Yii::$app->request->isAjax || Yii::$app->request->post()) {
            $model = new FotografiaNegocio();
            $model->url_archivoURL = UploadedFile::getInstance($model, 'url_archivo');
            $t = $model->getDb()->beginTransaction();
            $ubicacion = $this->getArryUbicaciones($user_id);
            if ($user_id == null || !Provider::verifyProvider($user_id) || !Provider::verifyEtapa3($user_id)) {
                $this->msgError();
            }
            if (Yii::$app->request->isAjax && $model->load(Yii::$app->request->post())) {
                Yii::$app->response->format = Response::FORMAT_JSON;
                return ActiveForm::validate($model);
            } else if ($model->load(Yii::$app->request->post())) {
                $this->makeDir($model->path);

                if (!empty($model->url_archivo) && $model->url_archivo != $model->url_archivoOLD) {
                    $new_nameFot = str_replace('archivos_tmp/', $model->path . '/', $model->url_archivo);
                    $this->copyFile($model->url_archivo, $new_nameFot);
                    $model->url_archivo = $new_nameFot;
                }

                $porcentaje = 0;
                if (isset($_POST['porcentaje_total_m']) && $_POST['porcentaje_total_m'] != '') {
                    $porcentaje = round($_POST['porcentaje_total_m']);
                }
                $model->porcentaje = $porcentaje;
                $model->provider_id = $user_id;
                if (!$model->save()) {
                    return $this->goHome();
                }
                self::eliminarCita();
                $t->commit();
                return $this->redirect(['index']);

            }
            return $this->renderAjax('create', [
                'model' => $model,
                'ubicacion' => $ubicacion
            ]);
        }
        return $this->goHome();
    }

    /**
     * Updates an existing FotografiaNegocio model.
     * If update is successful, the browser will be redirected to the 'view' page.
     * @param string $id
     * @return mixed
     */
    public function actionUpdate($user_id, $id)
    {
        if(Yii::$app->request->isAjax || Yii::$app->request->post()) {
            $user_id = intval($user_id);
            $id = intval($id);
            if ($user_id == null || !Provider::verifyProvider($user_id) || !Provider::verifyEtapa3($user_id) || !self::verifyUpdate($user_id, $id)) {
                echo $this->render('/site/error', ['name' => 'Lo sentimos...', 'message' => 'Acceso denegado']);

                return false;
            }
            $model = $this->findModel($id);

            $rechazo = Status::find()->getStatus($id, 'fotografia_negocio', 'op');
            $model->url_archivoURL = UploadedFile::getInstance($model, 'url_archivo');
            $t = $model->getDb()->beginTransaction();
            $ubicacion = $this->getArryUbicaciones($user_id);

            $model->url_archivoOLD = $model->url_archivo;
            if (Yii::$app->request->isAjax && $model->load(Yii::$app->request->post())) {
                Yii::$app->response->format = Response::FORMAT_JSON;
                return ActiveForm::validate($model);
            } else if ($model->load(Yii::$app->request->post())) {

                $this->makeDir($model->path);
                if (!empty($model->url_archivo) && $model->url_archivo != $model->url_archivoOLD) {
                    $new_nameFot = str_replace('archivos_tmp/', $model->path . '/', $model->url_archivo);
                    $this->copyFile($model->url_archivo, $new_nameFot);
                    $model->url_archivo = $new_nameFot;
                }

                $porcentaje = 0;
                if (isset($_POST['porcentaje_total_m']) && $_POST['porcentaje_total_m'] != '') {
                    $porcentaje = round($_POST['porcentaje_total_m']);
                }
                $model->porcentaje = $porcentaje;
                $model->provider_id = $user_id;
                $model->status_op = Status::STATUS_ENEDICION;

                if ($model->save()) {
                    if (($requi_status = Status::find()->where(['and', ['register_id' => $id], ['status_op' => Status::STATUS_PENDIENTE], ['modelo' => 'fotografia_negocio']])->one()) !== null) {
                        $requi_status->status_op = Status::STATUS_TERMINADO_PRO;
                        $requi_status->save();
                    }
                    self::eliminarCita();
                    $t->commit();
                    return $this->redirect(['index']);
                }else{
                    return $this->goHome();
                }

            }
            return $this->renderAjax('update', [
                'model' => $model,
                'rechazo' => $rechazo,
                'ubicacion' => $ubicacion
            ]);
        }
        return $this->goHome();
    }

    public function actionTerminar($id = null)
    {


        $provider_id = Yii::$app->user->identity->providerid;
        $allUbicaciones =ArrayHelper::getColumn(Ubicacion::find()->select('ubicacion_id')
            ->where(['and',['provider_id' => $provider_id],['activo' => true]])->asArray()->all(),'ubicacion_id');
        $trueExt = true;
        for ($x = 0,$cU = count($allUbicaciones);$x<$cU && $trueExt;$x++){
            if(FotografiaNegocio::find()->where(['and',['provider_id' => $provider_id],['activo' => true],['ubicacion_id' => $allUbicaciones[$x]],['tipo_fotografia' => 'Exterior']])->count('1') == 0){
                $tiU = Ubicacion::find()->where(['ubicacion_id' => $allUbicaciones[$x]])->one()['type_address_prov'];
                if($tiU =='DOMICILIO FISCAL'){
                    $domFiscal = Ubicacion::find()->select('ubicacion_id')->where(['and',['provider_id' => $provider_id],['tipo' => 'DOMICILIO FISCAL'],['is','type_address_prov',null]])->one()['ubicacion_id'];
                    if(!empty($domFiscal)){
                        if(FotografiaNegocio::find()->where(['and',['provider_id' => $provider_id],['activo' => true],['ubicacion_id' => $domFiscal],['tipo_fotografia' => 'Exterior']])->count('1') == 0){
                            $trueExt = false;
                        }
                    }
                }else{
                    $trueExt = false;
                }

            }
        }

        if(!$trueExt){
            Yii::$app->session->setFlash('error', "Debe anexar fotografía de fachada exterior de cada uno de los domicilios que registra...");
            return $this->redirect('index');
        }


        $IdLastVal = $this->saveLastSendValidation(Yii::$app->user->identity->getProvider(),'validFotografia','fotografia_negocio','op');

        if ($id != null) {
            $status = $this->terminarUno('FotografiaNegocio', intval(base64_decode($id)), 'fotografia_negocio_id', 'fotografia_negocio', 'status_op','',$IdLastVal);
        } else {
            $status = $this->terminarTodos('FotografiaNegocio', 'fotografia_negocio_id', 'fotografia_negocio', 'status_op','',$IdLastVal);
        }


        if($status){
            $correos_validadores = self::getEmailValidador(4,'op');

            self::sendEmail('/provider/correos/nuevo', null, $correos_validadores, 'Nuevo proveedor', ['modulo' => 'Fotografía negocio','tipo_provider' => 'op']);

            self::AllSendNotification(null,'op',null,'FOTOGRAFIA NEGOCIO','Fotografía negocio');

            $provider = Provider::findOne(Yii::$app->user->identity->providerid);
            GeneralController::sendModuloRevisionEmail($provider->email, 'Fotografía');
        }

        return $this->redirect('index');
    }

    /**
     * Deletes an existing FotografiaNegocio model.
     * If deletion is successful, the browser will be redirected to the 'index' page.
     * @param string $id
     * @return mixed
     */
    public function actionDelete($id)
    {
        FotografiaNegocio::updateAll(['activo' => false], ['fotografia_negocio_id' => intval($id)]);

        return $this->redirect(['index']);
    }

    /**
     * Finds the FotografiaNegocio model based on its primary key value.
     * If the model is not found, a 404 HTTP exception will be thrown.
     * @param string $id
     * @return FotografiaNegocio the loaded model
     * @throws NotFoundHttpException if the model cannot be found
     */
    protected function findModel($id)
    {
        if (Yii::$app->user->can(Usuarios::ROLE_PROVIDER)) {
            $provider = Provider::find()->select('provider_id')->where(['user_id' => Yii::$app->user->getId()])->one()->provider_id;

            $model = FotografiaNegocio::find()->where(['and', ['provider_id' => $provider], ['fotografia_negocio_id' => $id]])->one();
        } else {
            $model = FotografiaNegocio::findOne($id);
        }

        if ($model !== null) {
            return $model;
        }
        return false;
    }

    protected function findModelUbicacion($id)
    {

        if (($model = Ubicacion::findOne($id)) === null) {
            $model = new Ubicacion();
        }
        return $model;
    }

    public function findModelPorcentaje($id)
    {
        $model = Porcentaje::find()->where(['register_id' => $id])->one();
        if ($model == null || empty($model)) {
            $model = new Porcentaje();
        }
        return $model;
    }

    public function verifyUpdate($provider, $id)
    {
        if ($provider) {
            $status = FotografiaNegocio::find()->select('status_op')->where(['and', ['provider_id' => $provider], ['fotografia_negocio_id' => $id]])->one()['status_op'];
            $rechazo = Status::find()->getStatusRechazarCotejo($provider);

            if ($status == Status::STATUS_ENEDICION || $status == Status::STATUS_RECHAZADO || $status == Status::STATUS_VALIDADO || !empty($rechazo)) {
                return true;
            }
        }
        return false;
    }

    public function getArryUbicaciones($provider_id){
        $resultQuery = Yii::$app->db->createCommand("
            with formatUb as (SELECT u.ubicacion_id, concat(u.calle_fiscal, ' ', u.num_ext_fiscal, ' ', u.cp_fiscal, ' (', u.tipo, ')' ) as direccion
            FROM provider.ubicacion u WHERE provider_id = $provider_id and activo = true )
            SELECT * FROM formatUb where direccion != ''")->queryAll();
        
        $arrQuery = ArrayHelper::map($resultQuery, 'ubicacion_id', 'direccion');

        return $arrQuery;
    }
}
