<?php

namespace app\controllers\op;

use app\helpers\GeneralController;
use app\models\ClientesContratos;
use app\models\ClientesContratosSearch;
use app\models\ComprobanteDomicilio;
use app\models\DatosValidados;
use app\models\ExpirationDocuments;
use app\models\Provider;
use app\models\Rfc;
use app\models\Status;
use app\models\Ubicacion;
use app\models\Usuarios;
use kartik\form\ActiveForm;
use Yii;
use yii\web\NotFoundHttpException;
use yii\filters\VerbFilter;
use app\models\Curriculum;
use yii\web\Response;


class PerfilController extends GeneralController
{

    public function behaviors()
    {
        return [
            'verbs' => [
                'class' => VerbFilter::className(),
                'actions' => [
                    'delete' => ['POST','GET'],
                ],
            ],
        ];
    }


    public function actionIndex($id=null)
    {
        $id = intval($id);
        if (Yii::$app->user->can(Usuarios::ROLE_PROVIDER)){
            $id=Yii::$app->user->identity->providerId;
        }
        if($id == null || strlen($id)>15 || !Provider::verifyProviderExistence($id) || /*!Provider::verifyEtapa3($id) ||*/ !Yii::$app->user->can(Usuarios::ROLE_PROVIDER)){
            $this->msgError();
        }

        $searchModel = new ClientesContratosSearch();
        $dataProvider = $searchModel->search(Yii::$app->request->queryParams);
        $validados = $searchModel->search(Yii::$app->request->queryParams, 'validadosFilter');
        $rechazados = $searchModel->search(Yii::$app->request->queryParams, 'rechazadosFilter');
        $pendientes = $searchModel->search(Yii::$app->request->queryParams, 'pendientesFilter');
        $modelProvider = $this->findModelProv($id);
        $modelCurriculum = $this->findModelCurriculum($id);
        $ubicacion = $this->findModelUbicacionProv($id);
        $model_rfc = $this->findModelRfcProv($id);
        return $this->render('index', [
            'modelCurriculum' => $modelCurriculum,
            'modelProvider' => $modelProvider,
            'searchModel' => $searchModel,
            'dataProvider' => $dataProvider,
            'validados' => $validados,
            'rechazados' => $rechazados,
            'pendientes' => $pendientes,
            'ubicacion' => $ubicacion,
            'model_rfc'  => $model_rfc,
            'model_comprobante_domicilio' => $this->findModelComprobanteDomicilio($id),

        ]);
    }


    public function findModelCurriculum($id)
    {
        $model = Curriculum::find()->where(['provider_id' => $id])->one();
        if($model == null || empty($model)){
            $model = new Curriculum();
        }
        return $model;
    }


    protected function findModelProv($id)
    {
        $model = Provider::find()->where(['provider_id' => $id])->one();
        if(!$model){
            throw new NotFoundHttpException('The requested page does not exist.');
        }
        return $model;
    }


//    public  function  verifyUpdate($provider, $id){
//        if($provider){
//            $status = ClientesContratos::find()->select('status_bys')->where(['and',['provider_id' => $provider],['clientes_contratos_id'=>$id]])->one()->status_bys;
//            if($status == 'PENDIENTE'|| $status == 'RECHAZADO'){
//                return true;
//            }
//        }
//        return false;
//    }


    public function actionCurriculumupdate()
    {
        if (!Yii::$app->user->can(Usuarios::ROLE_PROVIDER)){ //pendiente de validaciones
            $this->redirect("/");
        }
        $model = $this->findModelCurriculum(Yii::$app->user->identity->providerid);
        $model->provider_id = Yii::$app->user->identity->providerId;
        if($model->load(Yii::$app->request->post()) && $model->save()){
            $this->redirect('/op/perfil');
        }
        return $this->renderAjax('curriculumupdate', [
            'model' => $model,
        ]);
    }



    public function actionView($user_id = null, $id = null)
    {
        $user_id = intval(base64_decode($user_id));
        $id = intval(base64_decode($id));
        if ($user_id == null
            || strlen($user_id) > 15
            || (!Provider::verifyProviderExistence($user_id) && Yii::$app->user->can(Usuarios::ROLE_PROVIDER))
            || (!Provider::verifyProvider($user_id) && Yii::$app->user->can(Usuarios::ROLE_PROVIDER))) {
            $this->msgError();
        }
        $modelos = [];
        $model_status = new Status();
        $model_status->scenario = Status::SCENARIO_CREATE;
        $model_clientes = $this->findModel($id);
        $model_curriculum = $this->findModelCurriculum($id);

        if (Yii::$app->user->can(Usuarios::ROLE_VALIDADOR_PROVEEDORES)) {
            if (isset($model_clientes->provider_id) && !empty($model_clientes->provider_id)) {
                $modelos[$model_clientes->formName()] = $model_clientes->attributes;
            }
            if (isset($model_curriculum->provider_id) && !empty($model_curriculum->provider_id)) {
                $modelos[$model_curriculum->formName()] = $model_curriculum->attributes;
            }

            $modelos = base64_encode(json_encode($modelos));

        }


        return $this->renderAjax('view', [
            'model' => $model_clientes,
            'model_status' => $model_status,
            'model_curriculum' => $model_curriculum,
            'modelos' => $modelos
        ]);
    }

    /**
     * Displays a single ClientesContratos model.
     * @param string $id
     * @return mixed
     */
    public function actionViewValidar($id = null, $user_id = null)
    {
        $queryParams = Yii::$app->request->queryParams;
        $id = isset($queryParams['id']) ? intval( $queryParams['id'] ) : null;
        $user_id = isset($queryParams['user_id']) ? intval( $queryParams['user_id'] ) : null;
        $opcionesArray = isset($queryParams['opciones']) ? $queryParams['opciones'] : null;
        $opcionesSerial = isset($opcionesArray) ? serialize($opcionesArray) : null;
        $opcionesBase64 = isset($opcionesSerial) ? base64_encode($opcionesSerial) : null;
        if ($user_id == null
            || strlen($user_id) > 15
            || (!Provider::verifyProviderExistence($user_id) && Yii::$app->user->can(Usuarios::ROLE_PROVIDER))
            || (!Provider::verifyProvider($user_id) && Yii::$app->user->can(Usuarios::ROLE_PROVIDER))
            || !Provider::verifyEtapa3($user_id)) {
            $this->msgError();
        }
        $modelos = [];
        $model_status = new Status();
        $model_status->scenario = Status::SCENARIO_CREATE;
        $model_clientes = $this->findModel($id);
        $model_curriculum = $this->findModelCurriculum($id);
        $model_validado = new DatosValidados();
        $model_validado->scenario = DatosValidados::SCENARIO_CREATE;
        if (Yii::$app->user->can(Usuarios::ROLE_VALIDADOR_CONTRATISTAS)) {
            if (isset($model_clientes->provider_id) && !empty($model_clientes->provider_id)) {
                $modelos[$model_clientes->formName()] = $model_clientes->attributes;
            }
            if (isset($model_curriculum->provider_id) && !empty($model_curriculum->provider_id)) {
                $modelos[$model_curriculum->formName()] = $model_curriculum->attributes;
            }

            $modelos = base64_encode(json_encode($modelos));

        }

        $rechazo = Status::find()->getStatus($id, 'clientes_contratos', 'op','TERMINADO PRO');

        return $this->renderAjax('view-validar', [
            'model' => $model_clientes,
            'model_status' => $model_status,
            'model_curriculum' => $model_curriculum,
            'modelos' => $modelos,
            'model_validado' => $model_validado,
            'rechazo' => $rechazo,
            'opciones' => $opcionesBase64,
            'namePro' => $this->getNameProvider($model_clientes->provider_id)
        ]);
    }


    public function actionViewValidarPerfil($id = null, $user_id = null)
    {
        $queryParams = Yii::$app->request->queryParams;
        $id = isset($queryParams['id']) ? intval( $queryParams['id'] ) : null;
        $opcionesArray = isset($queryParams['opciones']) ? $queryParams['opciones'] : null;
        $opcionesSerial = isset($opcionesArray) ? serialize($opcionesArray) : null;
        $opcionesBase64 = isset($opcionesSerial) ? base64_encode($opcionesSerial) : null;
        if($id == null || strlen($id)>15 || !Provider::verifyProviderExistence($id)){
            $this->msgError();
        }
        $modelos = [];
        $model_status = new Status();
        $model_status->scenario = Status::SCENARIO_CREATE;
        $model_domicilio = $this->findModelComprobanteDomicilio($id);
        $model_rfc = $this->findModelRfcProv($id);
        $ubicacion = $this->findModelUbicacionProv($id);
        $model = $this->findModelProv($id);
        $model_validado = new DatosValidados();
        $model_validado->scenario = DatosValidados::SCENARIO_CREATE;
        if (Yii::$app->user->can(Usuarios::ROLE_VALIDADOR_CONTRATISTAS)) {
            if (isset($model_domicilio->provider_id) && !empty($model_domicilio->provider_id)) {
                $modelos[$model_domicilio->formName()] = $model_domicilio->attributes;
            }
            if (isset($model_rfc->provider_id) && !empty($model_rfc->provider_id)) {
                $modelos[$model_rfc->formName()] = $model_rfc->attributes;
            }
            if (isset($ubicacion->provider_id) && !empty($ubicacion->provider_id)) {
                $modelos[$ubicacion->formName()] = $ubicacion->attributes;
            }

            $modelos = base64_encode(json_encode($modelos));

        }

        $rechazo = Status::find()->getStatus($id, 'perfil', 'op','TERMINADO PRO');
        $movements = $this->countAllMovements($id,'perfil');

        return $this->renderAjax('view-validar-perfil', [
            'model' => $model,
            'model_status' => $model_status,
            'ubicacion' => $ubicacion,
            'modelos' => $modelos,
            'model_validado' => $model_validado,
            'rechazo' => $rechazo,
            'namePro' => $this->getNameProvider($model->provider_id),
            'movements' => $movements,
            'id' => $id,
            'opciones' => $opcionesBase64,
            'moduleName' => 'perfil',
            'model_comprobante_domicilio' => $model_domicilio,
            'model_rfc' => $model_rfc
        ]);
    }
    /**
     * Creates a new ClientesContratos model.
     * If creation is successful, the browser will be redirected to the 'view' page.
     * @return mixed
     */
    public function actionCreate($user_id)
    {
        if(Yii::$app->request->isAjax || Yii::$app->request->post()){
            if ($user_id == null || !Provider::verifyProvider($user_id) /*|| !Provider::verifyEtapa3($user_id)*/) {
                $this->msgError();
            }
            $model = new ClientesContratos();
            $model->scenario = ClientesContratos::SCENARIO_CREATE_OP;

            if (Yii::$app->request->isAjax && $model->load(Yii::$app->request->post())) {
                Yii::$app->response->format = Response::FORMAT_JSON;
                return ActiveForm::validate($model);
            }
            if ($model->load(Yii::$app->request->post())) {
                $porcentaje = 0;
                if (isset($_POST['porcentaje_total_m']) && $_POST['porcentaje_total_m'] != '') {
                    $porcentaje = round($_POST['porcentaje_total_m']);
                }
                $model->porcentaje = $porcentaje;
                $model->provider_id = $user_id;
                $model->tipo = 'op';
                if ($model->save()) {
                    Provider::updateAll(['status_carta_op' => 'PENDIENTE'], ['provider_id' => $model->provider_id]);

                    return $this->redirect(['index']);
                }else{
                    return $this->goHome();
                }
            }

            return $this->renderAjax('create', [
                'model' => $model,
            ]);
        }
        return $this->goHome();

    }

    /**
     * Updates an existing ClientesContratos model.
     * If update is successful, the browser will be redirected to the 'view' page.
     * @param string $id
     * @return mixed
     */
    public function actionUpdate($user_id, $id)
    {
        if ($user_id == null || !Provider::verifyProvider($user_id) || /*!Provider::verifyEtapa3($user_id) ||*/ !self::verifyUpdate($user_id, $id)) {
            echo $this->render('/site/error', ['name' => 'Lo sentimos...', 'message' => 'Acceso denegado']);

            return false;
        }
        $model = $this->findModel($id);
        $model->scenario = ClientesContratos::SCENARIO_CREATE_OP;
        $modelOld = $model->attributes;
        $rechazo = Status::find()->getStatus($id, 'clientes_contratos', 'op');
        if(Yii::$app->request->isAjax && $model->load(Yii::$app->request->post())){
            Yii::$app->response->format = Response::FORMAT_JSON;
            return ActiveForm::validate($model);
        }else
            if ($model->load(Yii::$app->request->post())) {
                $model->status_op = Status::STATUS_ENEDICION;
                $model->provider_id = $user_id;
                $porcentajeOld = $model->porcentaje;

                if (isset($_POST['porcentaje_total_m']) && $_POST['porcentaje_total_m'] != '') {
                    $porcentaje = round($_POST['porcentaje_total_m']);
                } else {
                    $porcentaje = $porcentajeOld;
                }
                $model->porcentaje = $porcentaje;
                $model->tipo = 'op';
                if ($model->save()) {
                    $modelNew = $model->attributes;
                    $deleteLetter = false;
                    $arrayChange = ['nombre_razon_social','persona_dirigirse','telefono','month','year','monto'];
                    foreach ($modelOld as $s => $d){
                        if(in_array($s,$arrayChange)){
                            if($d!=$modelNew[$s]){
                                $deleteLetter = true;
                            }
                        }
                    }

                    if($deleteLetter){
                        Provider::updateAll(['status_carta_op' => 'PENDIENTE'], ['provider_id' => $model->provider_id]);
                    }
                    if (($requi_status = Status::find()->where(['and', ['register_id' => $id], ['status_op' => Status::STATUS_PENDIENTE],['modelo' => 'clientes_contratos']])->one())!==null) {
                        $requi_status->status_op = Status::STATUS_TERMINADO_PRO;
                        $requi_status->save();
                    }
                    return $this->redirect(['index']);
                }
            }
        return $this->renderAjax('update', [
            'model' => $model,
            'rechazo' => $rechazo
        ]);
    }
    /**
     * Finds the ClientesContratos model based on its primary key value.
     * If the model is not found, a 404 HTTP exception will be thrown.
     * @param string $id
     * @return ClientesContratos the loaded model
     * @throws NotFoundHttpException if the model cannot be found
     */
    protected function findModel($id)
    {
        if (($model = ClientesContratos::findOne($id)) !== null) {
            return $model;
        } else {
            throw new NotFoundHttpException('The requested page does not exist.');
        }
    }

    public function actionTerminar($id = null)
    {

        $IdLastVal = $this->saveLastSendValidation(Yii::$app->user->identity->getProvider(),'validClientes','clientes_contratos','op');

        if ($id != null) {
            $status = $this->terminarUno('ClientesContratos', intval(base64_decode($id)), 'clientes_contratos_id', 'clientes_contratos', 'status_op','',$IdLastVal);
        } else {
            $status = $this->terminarTodos('ClientesContratos', 'clientes_contratos_id', 'clientes_contratos', 'status_op','',$IdLastVal);
        }

        if($status){
            $correos_validadores = self::getEmailValidador(2,'op');

            self::sendEmail('/provider/correos/nuevo', null, $correos_validadores, 'Nuevo proveedor', ['modulo' => 'Referencias comerciales','tipo_provider' => 'op']);

            self::AllSendNotification(null,'op',null,'DATOS TECNICOS','Referencias comerciales');

            $provider = Provider::findOne(Yii::$app->user->identity->providerid);
            GeneralController::sendModuloRevisionEmail($provider->email, 'Mi perfil (Referencias comerciales)');
        }

        return $this->redirect('index');
    }


    public function actionTerminarPerfil($id = null)
    {
        $id = intval(base64_decode($id));
        if($id == null || !Provider::verifyProvider($id) || !Provider::verifyEtapa3($id,true)){
            echo $this->render('/site/error',['name' => 'Lo sentimos...','message' => 'Acceso denegado']);

            return false;
        }
        $status='status_op';

        $rfc = Yii::$app->db->createCommand();
        $rfc->update('provider.perfil',[$status => Status::STATUS_PORVALIDAR],'provider_id ='.$id);
        $rfc->execute();

        $IdLastVal = $this->saveLastSendValidation($id,'mainPerfil','perfil','op');
        $this->saveLastSendValidationRelation($IdLastVal,$id,'perfil',$id,'op');

        self::AllSendNotification(null,'op',null,'PERFIL','Perfil');

        $provider = Provider::findOne(Yii::$app->user->identity->providerid);
        GeneralController::sendModuloRevisionEmail($provider->email, 'Mi perfil (Generales)');

        return $this->redirect('index');
    }


    public function verifyUpdate($provider, $id)
    {
        if ($provider) {
            $status = ClientesContratos::find()->select('status_op')->where(['and', ['provider_id' => $provider], ['clientes_contratos_id' => $id]])->one()['status_op'];
            if ($status == Status::STATUS_ENEDICION || $status == Status::STATUS_RECHAZADO || $status == Status::STATUS_VALIDADO) {
                return true;
            }
        }
        return false;
    }

    public function findModelUbicacionProv($id = 0)
    {

        if (($model = Ubicacion::find()->where(['and',['provider_id' => $id],['type_address_prov' => 'DOMICILIO FISCAL']])->one())===null) {
            $model = new Ubicacion();
        }
        return $model;
    }

    public function findModelRfcProv($id = 0)
    {

        if (($model = Rfc::find()->where(['provider_id' => $id])->one())===null) {
            $model = new Rfc();
        }
        return $model;
    }

    public function findModelComprobanteDomicilio($id)
    {
        $model = ComprobanteDomicilio::find()->where(['provider_id' => $id])->one();
        if ($model == null || empty($model)) {
            $model = new ComprobanteDomicilio();
        }
        return $model;
    }


    public function actionDelete($id)
    {
        $this->findModel($id)->delete();

        return $this->redirect(['index']);
    }

}
