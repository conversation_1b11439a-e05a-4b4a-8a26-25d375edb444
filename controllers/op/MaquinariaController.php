<?php

namespace app\controllers\op;

use app\helpers\GeneralController;
use app\models\DatosValidados;
use app\models\Status;
use app\models\Usuarios;
use kartik\form\ActiveForm;
use Yii;
use app\models\MaquinariaEquipos;
use app\models\MaquinariaEquiposSearch;
use app\models\Provider;
use yii\filters\VerbFilter;
use yii\web\Response;
use yii\web\UploadedFile;


class MaquinariaController extends GeneralController
{

    public function behaviors()
    {
        return [
            'verbs' => [
                'class' => VerbFilter::className(),
                'actions' => [
                    'delete' => ['POST','GET'],
                ],
            ],
        ];
    }


    public function actionIndex()
    {
        $m = new MaquinariaEquiposSearch();
        $modelos = (Object)[
            'edicion' => $m->search(Yii::$app->request->queryParams,'edicionFilter'),
            'rechazados' => $m->search(Yii::$app->request->queryParams,'rechazadosFilter'),
            'validados' => $m->search(Yii::$app->request->queryParams,'validadosFilter'),
            'pendientes' => $m->search(Yii::$app->request->queryParams,'pendientesFilter'),
            'filtro' => $m
        ];


        return $this->render('index', [
            'modelos' => $modelos
        ]);
    }


    public function actionView($id)
    {

        $model = MaquinariaEquipos::find()
            ->where(['and',['maquinaria_equipos_id' => intval(base64_decode($id)), 'provider_id' => Yii::$app->user->identity->providerid]])
            ->one();
        if(!$model){
            return $this->redirect('index');
        }
        return $this->renderAjax('view', [
            'model' => $model,
        ]);
    }


    public function actionUpdate($id=0) {
        if(Yii::$app->request->isAjax || Yii::$app->request->post()){
            $model = $this->findModel(intval(base64_decode($id)));

            $rechazo = [];
            if($model->maquinaria_equipos_id!=null){
                $rechazo = Status::find()->getStatus($model->maquinaria_equipos_id,'maquinaria_equipos','op');
            }


            $model->facturaOLD = $model->factura;
            $model->provider_id = Yii::$app->user->identity->providerid;
            if(Yii::$app->request->isAjax && $model->load(Yii::$app->request->post())){
                Yii::$app->response->format = Response::FORMAT_JSON;
                return ActiveForm::validate($model);
            }else if ($model->load(Yii::$app->request->post())) {
                $model->status_op = Status::STATUS_ENEDICION;

                $this->makeDir($model->path);

                if(!empty($model->factura) && $model->factura!=$model->facturaOLD){
                    $new_nameFactura = str_replace('archivos_tmp/',$model->path.'/',$model->factura);
                    $this->copyFile($model->factura,$new_nameFactura);
                    $model->factura = $new_nameFactura;
                }


                if($model->save()){

                    if(($requi_status = Status::find()->where(['and',['register_id' => $model->maquinaria_equipos_id],['status_op' => Status::STATUS_PENDIENTE],['modelo'=>'maquinaria_equipos']])->one())!==null){
                        $requi_status->status_op = Status::STATUS_TERMINADO_PRO;
                        $requi_status->save();
                    }
                    Yii::$app->session->setFlash('success','Registro creado/actualizado correctamente.');
                    return $this->redirect(['index']);
                }else{
                    return $this->goHome();
                }
            }
            return $this->renderAjax('update', [
                'model' => $model,
                'rechazo' => $rechazo
            ]);
        }

        return $this->goHome();

    }


    public function actionTerminar($id=null){

        $IdLastVal = $this->saveLastSendValidation(Yii::$app->user->identity->getProvider(),'validMaquinaria','maquinaria_equipos','op');

        if($id!=null){
            $status = $this->terminarUno('MaquinariaEquipos',intval(base64_decode($id)),'maquinaria_equipos_id','maquinaria_equipos','status_op','',$IdLastVal);
        }else{
            $status = $this->terminarTodos('MaquinariaEquipos','maquinaria_equipos_id','maquinaria_equipos','status_op','',$IdLastVal);
        }

        if($status){
            $correos_validadores = self::getEmailValidador(2,'op');

            self::sendEmail('/provider/correos/nuevo', null, $correos_validadores, 'Nuevo proveedor', ['modulo' => 'Maquinaría y equipos','tipo_provider' => 'op']);

            self::AllSendNotification(null,'op',null,'DATOS TECNICOS','Maquinaría y equipos');

            $provider = Provider::findOne(Yii::$app->user->identity->providerid);
            GeneralController::sendModuloRevisionEmail($provider->email, 'Maquinaria');
        }


        return $this->redirect(['index']);
    }


    public function actionDelete($id=0)
    {
        $model = MaquinariaEquipos::find()
            ->where(['and',['maquinaria_equipos_id' => intval(base64_decode($id)),'provider_id'=> Yii::$app->user->identity->providerid,'status_op'=>$this->statusParaEliminar]])
            ->one();
        if($model){
            $this->deleteFile($model->factura);
            $model->activo = false;
            $model->save();
            Yii::$app->session->setFlash('success','Registro eliminado correctamente.');
            return $this->redirect(['index']);
        }
        return json_encode(['status'=>'No permitido']);
    }


    public function actionViewValidar($id=null){

        $queryParams = Yii::$app->request->queryParams;
        $id = isset($queryParams['id']) ? intval( $queryParams['id'] ) : null;
        $opcionesArray = isset($queryParams['opciones']) ? $queryParams['opciones'] : null;
        $opcionesSerial = isset($opcionesArray) ? serialize($opcionesArray) : null;
        $opcionesBase64 = isset($opcionesSerial) ? base64_encode($opcionesSerial) : null;

        $model = MaquinariaEquipos::find()->where(['maquinaria_equipos_id' => intval($id)])->one();

        if(Yii::$app->user->isGuest || $id == null || !Yii::$app->user->can(Usuarios::ROLE_VALIDADOR_CONTRATISTAS) || !$model){
            $this->redirect('/');
        }

        $status = new Status();
        $status->scenario = Status::SCENARIO_CREATE;
        $validados = new DatosValidados();
        $validados->scenario = DatosValidados::SCENARIO_CREATE;
        $rechazo = Status::find()->getStatus($model->maquinaria_equipos_id,'maquinaria_equipos','op','TERMINADO PRO');

        return $this->renderAjax('view-validar',[
           'modelStatus' => $status,
           'modelValidado' => $validados,
           'model' => $model,
           'rechazo' => $rechazo,
           'opciones' => $opcionesBase64,
           'namePro' => $this->getNameProvider($model->provider_id)
        ]);

    }


    protected function findModel($id)
    {
        $model = MaquinariaEquipos::find()
            ->where(['and',['provider_id'=>Yii::$app->user->identity->providerid],['maquinaria_equipos_id' => $id]])
            ->one();
        if (!$model) {
            $model = new MaquinariaEquipos();
        }
        return $model;
    }



}
