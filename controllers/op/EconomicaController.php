<?php

namespace app\controllers\op;

use app\helpers\GeneralController;
use app\helpers\OpController;
use app\models\ActaConstitutiva;
use app\models\AltaHacienda;
use app\models\CatActividades;
use app\models\CatEntidades;
use app\models\CatMunicipios;
use app\models\CatRamas;
use app\models\CatSectores;
use app\models\City;
use app\models\DatosValidados;
use app\models\Giro;
use app\models\IdOficial;
use app\models\ModelGiro;
use app\models\ModificacionActa;
use app\models\Porcentaje;
use app\models\RepresentanteLegal;
use app\models\Provider;
use app\models\RepresentanteLegalSearch;
use app\models\Rfc;
use app\models\State;
use app\models\Status;
use app\models\RegistroActas;
use app\models\Usuarios;
use kartik\form\ActiveForm;
use Yii;
use yii\db\Query;
use yii\helpers\ArrayHelper;
use yii\filters\VerbFilter;
use yii\helpers\Json;
use yii\web\NotFoundHttpException;
use yii\web\Response;
use yii\web\UploadedFile;


class EconomicaController extends GeneralController
{
    public function behaviors()
    {
        return [
            'verbs' => [
                'class' => VerbFilter::className(),
                'actions' => [
                    'delete' => ['POST','GET'],
                ],
            ],
        ];
    }


    public function actionView()
    {
        $id = Yii::$app->user->identity->providerid;
        if($id == null || !Provider::verifyProvider($id) || !Provider::verifyEtapa2($id)){
            echo $this->render('/site/error',['name' => 'Lo sentimos...','message' => 'Acceso denegado']);

            return false;
        }

        $cot = false;
        $rechazo = Status::find()->getStatus($id,'alta_hacienda','op');

        $RL = new RepresentanteLegalSearch();
        $representante = (Object)[
            'edicion' => $RL->search(Yii::$app->request->queryParams, 'edicionFilter'),
            'rechazados' => $RL->search(Yii::$app->request->queryParams, 'rechazadosFilter'),
            'validados' => $RL->search(Yii::$app->request->queryParams, 'validadosFilter'),
            'pendientes' => $RL->search(Yii::$app->request->queryParams, 'pendientesFilter'),
            'filtro' => $RL
        ];

        $modelos = (Object)[
            'representante' => $representante
        ];
        if(empty($rechazo)){
            $rechazo = Status::find()->getStatusRechazarCotejo($id);
            $cot = true;
        }else{
            $rechazo = $rechazo[0];
        }

        return $this->render('view', [
            'model' => $this->findModel($id),
            //'model_representante_legal' => $this->findModelRepresentanteLegal($id),
            'model_act' => $this->findModelAct($id),
            'model_registro_acta' => $this->findModelActa($id),
            'rechazo' => $rechazo,
            'model_alta_hacienda' => $this->findModelAltaHacienda($id),
            'cot' => $cot,
            'modelos' => $modelos,
        ]);
    }

    public function actionUpdate()
    {
        $id = Yii::$app->user->identity->providerid;
        if ($id == null || !Provider::verifyProvider($id) || !Provider::verifyEtapa2($id) || !$this->verifyUpdate($id)) {
            return $this->render('/site/error', ['name' => 'Lo sentimos...', 'message' => 'Acceso denegado']);
        }

        $modelGiro = $this->findModelAct($id);
        $model = $this->findModel($id);
        $urlRfcOld = $model->url_rfc;
        $dataFiles = [];
        if($modelGiro){
            foreach ($modelGiro as $v){
                if(isset($v->url_factura_c_c) && !empty($v->url_factura_c_c)){
                    $dataFiles[$v->giro_id] = $v->url_factura_c_c;
                }
            }
        }
        if (ModelGiro::loadMultiple($modelGiro, Yii::$app->request->post()) && $model->load(Yii::$app->request->post())) {

            //$t = Yii::$app->db->beginTransaction();

            $update = Yii::$app->db->createCommand();
            $update->update('provider.alta_hacienda', ['status_op' => Status::STATUS_ENEDICION], 'provider_id =' . $id);
            $update->execute();

            if (($requi_status = Status::find()->where(['and', ['register_id' => $id], ['status_op' => Status::STATUS_PENDIENTE],['modelo' => 'alta_hacienda']])->one())!==null) {
                $requi_status->status_op = Status::STATUS_TERMINADO_PRO;
                $requi_status->save();
            }


                $oldIDs = ArrayHelper::map($modelGiro, 'giro_id', 'giro_id');
                $modelGiro = ModelGiro::createMultiple(Giro::classname(), $modelGiro);
                ModelGiro::loadMultiple($modelGiro, Yii::$app->request->post());
                $deletedIDs = array_diff($oldIDs, array_filter(ArrayHelper::map($modelGiro, 'giro_id', 'giro_id')));
                //$validar = ModelGiro::validateMultiple($modelGiro);

                if (!empty($deletedIDs)) {
                    Giro::updateAll(['active' => false, 'last_updated_at' => date('Y-m-d H:i:s'), 'last_updated_by' => Yii::$app->user->getId()],
                    ['in', 'giro_id', $deletedIDs]);
                    //Giro::deleteAll(['giro_id' => $deletedIDs]);
                }

            $total = 0;
            $error = false;
            $msgErr = null;
            $this->makeDir('op/actividad_economica/giro');
            foreach ($modelGiro as $modelAct) {
                //try{
                    $nombreAct = strtoupper(trim($modelAct->act_name));
                    if(($actName = CatActividades::find()->select('actividad_id')->where(['nombre_actividad' => $nombreAct])->one())===null){
                        $ramaId = CatRamas::find()->select('rama_id')->where(['nombre_rama' => 'OTROS'])->one()['rama_id'];
                        $actName = new CatActividades();
                        $actName->nombre_actividad = $nombreAct;
                        $actName->descripcion = $nombreAct;
                        $actName->rama_id = $ramaId;
                        $actName->save(false);

                    }

                    $modelAct->actividad_id = $actName->actividad_id;
                    $modelAct->provider_id = $id;
                    $modelAct->scenario = Giro::SCENARIO_OP;

                    if(is_null($modelAct->giro_id)){
                        $modelAct->created_by = Yii::$app->user->getId();
                        $modelAct->created_at = date('Y-m-d H:i:s');
                    }
    
                    $modelAct->active = true;
                    $modelAct->last_updated_at = date('Y-m-d H:i:s');
                    $modelAct->last_updated_by = Yii::$app->user->getId();

                    if (!($flag = $modelAct->save())) {
                        //$t->rollBack();
                        $err = isset( $modelAct->errors['act_name'] ) ? $modelAct->errors['act_name'] : 'No puede repetir la actividad';
                        Yii::$app->session->setFlash('error', strtr(json_encode($err), ["[","]","\""]));
                        return $this->redirect(['update']);
                        break;
                    }else{
                        if (!empty($modelAct->url_factura_c_c) && isset($dataFiles[$modelAct->giro_id]) && $modelAct->url_factura_c_c != $dataFiles[$modelAct->giro_id]) {
                            $new_nameFactura = str_replace('archivos_tmp/', 'op/actividad_economica/giro/', $modelAct->url_factura_c_c);
                            $this->copyFile($modelAct->url_factura_c_c, $new_nameFactura);
                            $modelAct->url_factura_c_c = $new_nameFactura;
                            $modelAct->save();
                        }else if (!empty($modelAct->url_factura_c_c)) {
                            $new_nameFactura = str_replace('archivos_tmp/', 'op/actividad_economica/giro/', $modelAct->url_factura_c_c);
                            $this->copyFile($modelAct->url_factura_c_c, $new_nameFactura);
                            $modelAct->url_factura_c_c = $new_nameFactura;
                            $modelAct->save();
                        }
                        $total += $modelAct->porcentaje;
                    }

               /* }catch (\Exception $e){
                    $error = true;
                    $msgErr = 'Seleccionar al menos una actividad';
                }
*/
            }

            $this->makeDir($model->path);

            if (!empty($model->url_rfc) && $model->url_rfc != $urlRfcOld) {
                $new_nameRfc = str_replace('archivos_tmp/', $model->path . '/', $model->url_rfc);
                $this->copyFile($model->url_rfc, $new_nameRfc);
                $model->url_rfc = $new_nameRfc;
            }

            $model->save();

            if($total!=100 || $error){
                //$t->rollBack();
                $msgErr = $msgErr!=null?$msgErr:"El porcentaje debe ser igual a 100";
                Yii::$app->session->setFlash('error', $msgErr);
                return $this->redirect(['update']);
            }

            //$t->commit();
            return $this->redirect(['view']);
        }

        $sector = CatSectores::find()->getSector();
        $rama = [];
        $actividad = [];

        if ($modelGiro[0]['actividad_id']) {
            for ($x = 0; $x < count($modelGiro); $x++) {
                $rama[$x] = !empty($modelGiro[$x]['actividad_id']) ? CatRamas::find()->getRama($modelGiro[$x]['actividad_id']) : [];
                $actividad[$x] = !empty($modelGiro[$x]['actividad_id']) ? CatActividades::find()->getActividad($modelGiro[$x]['actividad_id']) : [];
            }
        }

        $mods = ArrayHelper::map(
            ModificacionActa::find()->select(['modificacion_acta_id', 'nombre_documento'])
                ->where(['and', ['provider_id' => Yii::$app->user->identity->providerid], ['activo' => TRUE], ['status_' . Yii::$app->user->identity->tipo => Status::STATUS_VALIDADO]])
                ->asArray()->all(), 'modificacion_acta_id', 'nombre_documento');

        return $this->render('update', [
            'modificaciones' => $mods,
            'modelGiro' => (empty($modelGiro)) ? [new Giro()] : $modelGiro,
            'rama' => $rama,
            'sector' => $sector,
            'actividad' => $actividad,
            'provider_id' => $id,
            'model' => $model
        ]);
    }

    public function actionTerminar($id){
        $id = intval(base64_decode($id));

        if($id == null || !Provider::verifyProvider($id) || !Provider::verifyEtapa2($id)|| !self::verifyTerminar($id)){
            echo $this->render('/site/error',['name' => 'Lo sentimos...','message' => 'Acceso denegado']);

            return false;
        }

        $status='status_op';

        $rfc = Yii::$app->db->createCommand();
        $rfc->update('provider.alta_hacienda',[$status => Status::STATUS_PORVALIDAR],'provider_id ='.$id);
        $rfc->execute();


        $correos_validadores = self::getEmailValidador(1,'op');

        $IdLastVal = $this->saveLastSendValidation($id,'mainHacienda','alta_hacienda','op');
        $this->saveLastSendValidationRelation($IdLastVal,$id,'alta_hacienda',$id,'op');

        self::sendEmail('/provider/correos/nuevo', null, $correos_validadores, 'Nuevo proveedor', ['modulo' => 'Actividad ecónomica','tipo_provider' => 'op']);

        self::AllSendNotification(null,'op',null,'DATOS LEGALES','Actividad ecónomica');

        $provider = Provider::findOne(Yii::$app->user->identity->providerid);
        GeneralController::sendModuloRevisionEmail($provider->email, 'Actividad Económica');

        return $this->redirect('../../provider/dashboard');
    }

    public function actionViewValidar($id=null){
        $queryParams = Yii::$app->request->queryParams;
        $id = isset($queryParams['id']) ? intval( $queryParams['id'] ) : null;
        $opcionesArray = isset($queryParams['opciones']) ? $queryParams['opciones'] : null;
        $opcionesSerial = isset($opcionesArray) ? serialize($opcionesArray) : null;
        $opcionesBase64 = isset($opcionesSerial) ? base64_encode($opcionesSerial) : null;
        if($id == null || strlen($id)>15 || !Provider::verifyProviderExistence($id)){
            $this->msgError();
        }
        $modelos = [];
        $model_status = new Status();
        $model_status->scenario = Status::SCENARIO_CREATE;
        $model_validado = new DatosValidados();
        $model_validado->scenario = DatosValidados::SCENARIO_CREATE;
        $model_act = $this->findModelAct($id);

        $model_registro_acta = $this->findModelRegistroActas($id);
        $model = $this->findModel($id);

        if (isset($model_act[0]->provider_id) && !empty($model_act[0]->provider_id)) {
            for ($x = 0; $x < count($model_act); $x++) {
                $modelos[$model_act[$x]->formName()][] = $model_act[$x]->attributes;
            }
        }

        $tipo_persona = Provider::find()->select('tipo_persona')->where(['provider_id' => $id])->one()->tipo_persona;
        $status_global = 'status_op';
        $id_serial = AltaHacienda::find()->select('alta_hacienda_id')->where(['and',['provider_id' => $id],[$status_global => Status::STATUS_PORVALIDAR]])->one()['alta_hacienda_id'];
        $rechazo = Status::find()->getStatus($id,'alta_hacienda','op','TERMINADO PRO');

        return $this->renderAjax('view-validar', [
            'model_status' => $model_status,
            'model_act' => (empty($model_act)) ? [new Giro()] : $model_act,
            'tipo_persona' => $tipo_persona,
            'modelos' => base64_encode(json_encode($modelos)),
            'model_validado' => $model_validado,
            'id_provider' => $id,
            'id_serial' => $id_serial,
            'model_registro_acta' => $model_registro_acta,
            'rechazo' => $rechazo,
            'namePro' => $this->getNameProvider($id),
            'model'=> $model,
            'opciones' => $opcionesBase64,
        ]);

    }

    public function actionViewVal($id){

        $model = $this->findModel($id);
        $model_curp = $this->findModelCurp($id);
        $model_idoficial = $this->findModelIdoficial($id);
        $model_comprobante_domicilio = $this->findModelComprobanteDomicilio($id);
        $model_registro_imss = $this->findModelRegistroImss($id);
        $model_representante_legal = $this->findModelRepresentanteLegal($id);
        $model_status = new Status();
        $model_status->scenario = Status::SCENARIO_CREATE;
        $model_validado = new DatosValidados();
        $model_validado->scenario = DatosValidados::SCENARIO_CREATE;
        $model_act = $this->findModelAct($id);

        $rfc = Provider::find()->select('rfc')->where(['provider_id' => $id])->one()->rfc;

        return $this->render('view-validar', [
            'model' => $model,
            'model_curp' => $model_curp,
            'model_idoficial' => $model_idoficial,
            'model_comprobante_domicilio' => $model_comprobante_domicilio,
            'model_registro_imss' => $model_registro_imss,
            'model_representante_legal' => $model_representante_legal,
            'rfc'=>$rfc,
            'model_status' => $model_status,
            'model_act' => (empty($model_act)) ? [new Giro()] : $model_act,
            'model_validado' => $model_validado,
        ]);

    }

    protected function findModelRepresentanteLegalVal($id)
    {
        if (($model = RepresentanteLegal::findOne($id)) !== null) {
            return $model;
        } else {
            throw new NotFoundHttpException('The requested page does not exist.');
        }
    }

    public function findModelActa($id)
    {
        $model = ActaConstitutiva::find()->where(['provider_id' => $id])->one();
        if(!$model){
            $model = new ActaConstitutiva();
        }
        return $model;
    }


    public function findModelRegistroActas($id)
    {
        $model = ModificacionActa::find()->where(['provider_id' => $id])->one();
        if($model == null || empty($model)){
            $model = new ModificacionActa();
        }
        return $model;
    }

    public function findModelAltaHacienda($id)
    {
        $model = AltaHacienda::find()->where(['provider_id' => $id])->one();
        if($model == null || empty($model)){
            $model = new AltaHacienda();
        }
        return $model;
    }

    protected function findModel($id)
    {
        $model = Rfc::find()->where(['provider_id' => $id])->one();
        if($model == null || empty($model)){
            $model = new Rfc();
        }
        return $model;
    }

    public function actionList_city($id){

        $ciudades = ArrayHelper::map(CatMunicipios::find()->select(['cv_municipio','nombre'])->where(['cv_entidad' => $id])->asArray()->all(),'cv_municipio','nombre');
        $selector ='<option value=""></option>';

        if(count($ciudades)>0){
            foreach ($ciudades as $key=>$ciudad){
                $selector .='<option value="'.$key.'">'.$ciudad.'</option>';
            }
        }else{
            $selector .='<option value="">No hay ciudades</option>';
        }
        echo $selector;


    }


    public function findModelAct($id)
    {
        $model = Giro::find()->where(['and', ['provider_id' => $id, 'active' => true]])->orderBy(['giro_id'=>SORT_ASC])->all();
        if($model == null || empty($model)){
            $model = [new Giro()];
        }
        return $model;
    }





    public  function  verifyUpdate($provider){
        if($provider){
            $status = AltaHacienda::find()->select('status_op')->where(['provider_id' => $provider])->one()['status_op'];
            if($status == Status::STATUS_ENEDICION || $status == Status::STATUS_RECHAZADO || $status == Status::STATUS_VALIDADO){
                return true;
            }
        }
        return false;
    }


    public  function  verifyTerminar($provider){
        if($provider){
            $status = AltaHacienda::find()->select('status_op')->where(['provider_id' => $provider])->one()['status_op'];
            $model_act = $this->findModelAct($provider);

            $model = $this->findModel($provider);

            $esta_lleno = \app\helpers\GeneralController::estaLleno($model) && \app\helpers\GeneralController::estaLleno($model_act[0]);

            if(($status == Status::STATUS_ENEDICION || $status == Status::STATUS_RECHAZADO || $status == Status::STATUS_VALIDADO) && $esta_lleno){
                return true;
            }
        }
        return false;
    }

    public function actionRelacion($id = null){
        if($id != 'MODIFICACION'){
            return false;
        }
        $mods = ArrayHelper::map(
            ModificacionActa::find()->select(['modificacion_acta_id','nombre_documento'])
                ->where(['and',['provider_id' => Yii::$app->user->identity->providerid],['activo'=>TRUE],['status_'.Yii::$app->user->identity->tipo=>Status::STATUS_VALIDADO]])
                ->asArray()->all(),'modificacion_acta_id','nombre_documento');

        $selector ='<option value=""></option>';
        foreach ($mods as $key=>$value){
            $selector .='<option value="'.$key.'">'.$value.'</option>';
        }
        echo $selector;

    }



    public function actionList_rama($id=null){

        $con ='<option value=""></option>';
        if($id){
            $rama =  ArrayHelper::map(CatRamas::find()->select(['rama_id','nombre_rama'])
                ->where(['sector_id' => $id])
                ->orderBy(['nombre_rama'=>SORT_ASC])
                ->asArray()->all(),'rama_id','nombre_rama');
            if (count($rama) > 0) {
                foreach ($rama as $key => $value) $con .="<option value='" . $key . "'>" . $value . "</option>";
            } else {
                $con .="<option>No hay ramas</option>";
            }
        }else{
            $con .="<option>No hay ramas</option>";
        }


        echo $con;
    }


    public function actionList_actividad($id = null){

        $con ='<option value=""></option>';
        if($id){
            $act1 =  ArrayHelper::map(CatActividades::find()->select(['actividad_id','nombre_actividad'])
                ->where(['rama_id' => $id])
                ->orderBy(['nombre_actividad'=>SORT_ASC])
                ->asArray()->all(),'actividad_id','nombre_actividad');

            if (count($act1) > 0) {
                foreach ($act1 as $key => $value) $con .="<option value='" . $key . "'>" . $value . "</option>";
            } else {
                $con .="<option>No hay actividades</option>";
            }
        }else{
            $con .="<option>No hay ramas</option>";
        }

        echo $con;
    }

    public function actionList_name_sector_rama($id = null){

        $this->enableCsrfValidation = false;
        \Yii::$app->response->format = \yii\web\Response::FORMAT_JSON;
        $out = ['rama' => '', 'sector' => ''];
        if(Yii::$app->request->isAjax){

            if(!empty($id)){
                $data = Yii::$app->db->createCommand("select r.nombre_rama as rama, s.nombre_sector as sector from provider.cat_ramas r
                        join provider.cat_actividades a on a.rama_id = r.rama_id
                        join provider.cat_sectores s on s.sector_id = r.sector_id
                        where a.nombre_actividad = :id",[':id' => $id])->queryAll();

                if($data){
                    $out = array_values($data);
                }else{
                    $data = Yii::$app->db->createCommand("select r.nombre_rama as rama, s.nombre_sector as sector from provider.cat_ramas r
                        join provider.cat_sectores s on s.sector_id = r.sector_id
                        where s.nombre_sector = :id",[':id' => 'OTROS'])->queryAll();
                    $out = array_values($data);
                }

            }
        }


        return $out;

    }

    public function actionList_nama_act($q = null)
    {
//        $query = new Query();
//
//        $query->select('need')
//            ->from('origin')
//            ->where("need ILIKE ")
//            ->orderBy('need');
//        $command = $query->createCommand();
//        $data = $command->queryAll();
//
        $data = Yii::$app->db->createCommand("select nombre_actividad from provider.cat_actividades where nombre_actividad ilike :d", [':d' => "%$q%"])->queryAll();
        $out = [];
        foreach ($data as $d) {
            $out[] = ['value' => $d['nombre_actividad']];
        }
        echo Json::encode($out);exit();
    }



}
