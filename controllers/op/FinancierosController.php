<?php

namespace app\controllers\op;


use app\helpers\GeneralController;
use app\helpers\OpController;
use app\models\CapacidadContratacion;
use app\models\DatosValidados;
use app\models\DeclaracionIsr;
use app\models\EstadoFinancieroTipo;
use app\models\ParametrosAnalisis;
use app\models\Status;
use app\models\Usuarios;
use Yii;
use app\models\EstadoFinanciero;
use app\models\Provider;
use yii\filters\VerbFilter;
use yii\web\UploadedFile;


class FinancierosController extends GeneralController
{

    public function behaviors()
    {
        return [
            'verbs' => [
                'class' => VerbFilter::className(),
                'actions' => [
                    'delete' => ['POST','GET'],
                ],
            ],
        ];
    }


    public function actionView(){

        $modelEF = $this->findModelEF();
        $rechazo = Status::find()->getStatus($modelEF->estado_financiero_id,'estado_financiero','op');
        $modelDT = EstadoFinancieroTipo::find()->where(['provider_id' => Yii::$app->user->identity->providerid])->one();

            return $this->render('view', [
            'modelEF' => $modelEF,
            'modelCC' => $this->findModelCC(),
            'modelDA' => $this->findModelDA(),
            'rechazo' => $rechazo,
            'type' => isset($modelDT->type)?$modelDT->type:''
        ]);
    }


    public function actionViewValidar($id=null){
        $queryParams = Yii::$app->request->queryParams;
        $id = isset($queryParams['id']) ? intval( $queryParams['id'] ) : null;
        $opcionesArray = isset($queryParams['opciones']) ? $queryParams['opciones'] : null;
        $opcionesSerial = isset($opcionesArray) ? serialize($opcionesArray) : null;
        $opcionesBase64 = isset($opcionesSerial) ? base64_encode($opcionesSerial) : null;

        $modelEF = EstadoFinanciero::find()->where(['provider_id' => intval($id)])->one();
        $modelCC = CapacidadContratacion::find()->where(['provider_id' => intval($id)])->one();
        $modelDA = DeclaracionIsr::find()->where(['provider_id' => intval($id)])->one();
        if(Yii::$app->user->isGuest || $id == null || !Yii::$app->user->can(Usuarios::ROLE_VALIDADOR_CONTRATISTAS) || !$modelEF || !$modelCC){
            $this->redirect('/');
        }
        $modelos = [];
        if (isset($modelEF->provider_id) && !empty($modelEF->provider_id)) {
            $modelos[$modelEF->formName()] = $modelEF->attributes;
        }
        if (isset($modelCC->provider_id) && !empty($modelCC->provider_id)) {
            $modelos[$modelCC->formName()] = $modelCC->attributes;
        }
        if (isset($modelEF->provider_id) && !empty($modelEF->provider_id)) {
            $modelos[$modelDA->formName()] = $modelDA->attributes;
        }

        $status = new Status();
        $status->scenario = Status::SCENARIO_CREATE;
        $validados = new DatosValidados();
        $validados->scenario = DatosValidados::SCENARIO_CREATE;
        $rechazo = Status::find()->getStatus($modelEF->estado_financiero_id,'estado_financiero','op','TERMINADO PRO');

        $modelDT = EstadoFinancieroTipo::find()->where(['provider_id' => intval($id)])->one();


        return $this->renderAjax('view-validar', [
            'modelEF' => $modelEF,
            'modelCC' => $modelCC,
            'modelStatus' => $status,
            'modelValidado' => $validados,
            'modelDA' => $modelDA,
            'opciones' => $opcionesBase64,
            'modelos' => base64_encode(json_encode($modelos)),
            'rechazo' => $rechazo,
            'namePro' => $this->getNameProvider($modelEF->provider_id),
            'type' => isset($modelDT->type)?$modelDT->type:''
        ]);

    }


    public function actionUpdate(){

        if (Yii::$app->request->isAjax || Yii::$app->request->post()) {


            $modelEF = $this->findModelEF();
            $modelCC = $this->findModelCC();
            $modelDA = $this->findModelDA();


            $modelEF->archivoOLD = $modelEF->archivo;
            $modelEF->cedula_profesionalOLD = $modelEF->cedula_profesional;
            $modelDA->archivoOLD = $modelDA->archivo;
            $modelDA->acuseOLD = $modelDA->acuse;

            $modelEF->provider_id = Yii::$app->user->identity->providerid;
            $modelCC->provider_id = Yii::$app->user->identity->providerid;
            $modelDA->provider_id = Yii::$app->user->identity->providerid;
            //$transaccion = $modelEF->getDb()->beginTransaction();

            if ($modelEF->load(Yii::$app->request->post())
                && $modelCC->load(Yii::$app->request->post())
                && $modelDA->load(Yii::$app->request->post())) {

                $type = Yii::$app->request->post()['tipo_ef'];


                if (in_array($type, ['ef2', 'ef'])) {

                    if ($type == 'ef2') {
                        $type = 'ESTADO';
                    } else {
                        $type = 'DECLARACION';
                    }


                    if (($modelDT = EstadoFinancieroTipo::find()->where(['provider_id' => Yii::$app->user->identity->providerid])->one()) !== null) {
                        $modelDT->type = $type;
                    } else {
                        $modelDT = new EstadoFinancieroTipo();
                        $modelDT->provider_id = Yii::$app->user->identity->providerid;
                        $modelDT->type = $type;
                    }

                    $modelDT->save();


                }


                //$modelCC = $this->calculateCC($modelCC);

                $pathDA = 'op/datos_financieros/declaracion_isr';
                $this->makeDir($pathDA);

                $pathEF = 'op/datos_financieros/estado-financiero/';
                $this->makeDir($pathEF);


                $modelEF->status_op = Status::STATUS_ENEDICION;
                $modelCC->status_op = Status::STATUS_ENEDICION;
                $modelDA->status_op = Status::STATUS_ENEDICION;

                if (!empty($modelEF->archivo) && $modelEF->archivo != $modelEF->archivoOLD) {
                    $new_nameArc = str_replace('archivos_tmp/', $pathEF . '/', $modelEF->archivo);
                    $this->copyFile($modelEF->archivo, $new_nameArc);
                    $modelEF->archivo = $new_nameArc;
                }

                if (!empty($modelEF->cedula_profesional) && $modelEF->cedula_profesional != $modelEF->cedula_profesionalOLD) {
                    $new_nameCp = str_replace('archivos_tmp/', $pathEF . '/', $modelEF->cedula_profesional);
                    $this->copyFile($modelEF->cedula_profesional, $new_nameCp);
                    $modelEF->cedula_profesional = $new_nameCp;
                }

                if (!empty($modelDA->archivo) && $modelDA->archivo != $modelDA->archivoOLD) {
                    $new_nameDaArc = str_replace('archivos_tmp/', $pathDA . '/', $modelDA->archivo);
                    $this->copyFile($modelDA->archivo, $new_nameDaArc);
                    $modelDA->archivo = $new_nameDaArc;
                }

                if (!empty($modelDA->acuse) && $modelDA->acuse != $modelDA->acuseOLD) {
                    $new_nameAcuse = str_replace('archivos_tmp/', $pathDA . '/', $modelDA->acuse);
                    $this->copyFile($modelDA->acuse, $new_nameAcuse);
                    $modelDA->acuse = $new_nameAcuse;
                }

                $modelDA->save();
                $modelCC->save();
                $modelEF->save();
                //if () {
                  //  $transaccion->commit();

                    if (($requi_status = Status::find()->where(['and', ['register_id' => $modelEF->estado_financiero_id], ['status_op' => Status::STATUS_PENDIENTE], ['modelo' => 'estado_financiero']])->one()) != null) {
                        $requi_status->status_op = Status::STATUS_TERMINADO_PRO;
                        $requi_status->save();
                    }

                    Yii::$app->session->setFlash('success', 'Registro creado/actualizado correctamente.');

                //} else {
                  //  $transaccion->rollBack();
                //}


                return $this->redirect(['view']);
            }



            $modelDT = EstadoFinancieroTipo::find()->where(['provider_id' => Yii::$app->user->identity->providerid])->one();
            return $this->renderAjax('update', [
                'modelEF' => $modelEF,
                'modelCC' => $modelCC,
                'modelDA' => $modelDA,
                'type' => isset($modelDT->type)?$modelDT->type:''
            ]);
        }


        return $this->goHome();
    }


    public function actionTerminar(){
        $modelEF = $this->findModelEF();
        $modelCC = $this->findModelCC();
        $modelDA = $this->findModelDA();

        if($this->estaLleno($modelCC) && ($this->estaLleno($modelEF) ||  $this->estaLleno($modelDA)) &&
            (in_array($modelEF->status_op,$this->statusParaTerminar))){
            $modelEF->status_op = Status::STATUS_PORVALIDAR;
            $modelEF->save();
            Status::updateAll(['status_op' => Status::STATUS_TERMINADO_PRO],['register_id' => $modelEF->estado_financiero_id,'modelo'=>'estado_financiero']);
            $modelCC->status_op = Status::STATUS_PORVALIDAR;
            $modelCC->save();
            Status::updateAll(['status_op' => Status::STATUS_TERMINADO_PRO],['register_id' => $modelCC->capacidad_contratacion_id,'modelo'=>'capacidad_contratacion']);
            $modelDA->status_op = Status::STATUS_PORVALIDAR;
            $modelDA->save();
            Status::updateAll(['status_op' => Status::STATUS_TERMINADO_PRO],['register_id' => $modelDA->isr_id ,'modelo'=>'declaracion_isr']);
            Yii::$app->session->setFlash('success','Registro enviado a validar correctamente.');

            $correos_validadores = self::getEmailValidador(3,'op');


            $IdLastVal = $this->saveLastSendValidation($modelEF->provider_id,'mainFilterEstadoFinanciero','estado_financiero','op');
            $this->saveLastSendValidationRelation($IdLastVal,$modelEF->provider_id,'estado_financiero',$modelEF->estado_financiero_id,'op');

            self::sendEmail('/provider/correos/nuevo', null, $correos_validadores, 'Nuevo proveedor', ['modulo' => 'Datos financieros','tipo_provider' => 'op']);

            self::AllSendNotification(null,'op',null,'DATOS FINANCIEROS','Datos financieros');

            $provider = Provider::findOne(Yii::$app->user->identity->providerid);
            GeneralController::sendModuloRevisionEmail($provider->email, 'Datos financieros');

        }else{
            Yii::$app->session->setFlash('error', "Solo los registros llenados completamente, se pueden enviar a revisar.");
        }
        return $this->redirect(['view']);
    }


    protected function findModelEF()
    {
        $model = EstadoFinanciero::find()->where(['provider_id'=>Yii::$app->user->identity->providerid])->one();
        if(!$model){
            $model = new EstadoFinanciero();
        }
        return $model;
    }

    protected function findModelDA()
    {
        $model = DeclaracionIsr::find()->where(['provider_id'=>Yii::$app->user->identity->providerid])->one();
        if(!$model){
            $model = new DeclaracionIsr();
        }
        return $model;
    }


    protected function findModelCC()
    {
        $model = CapacidadContratacion::find()->where(['provider_id' => Yii::$app->user->identity->providerid])->one();
        if(!$model){
            $model = new CapacidadContratacion();
        }
        return $model;
    }


    protected function findModelPA(){
        $model = ParametrosAnalisis::find()->where(['status' =>'ACTIVO'])->one();
        if(!$model){
            $model = new ParametrosAnalisis();
        }
        return $model;
    }


    public function calculateCC($model){
        $modelPA = $this->findModelPA();

        $model->activo_circulante = str_replace(",", "", $model->activo_circulante);
        $model->pasivo_circulante = str_replace(",", "", $model->pasivo_circulante);
        $model->pasivo_largo_plazo = str_replace(",", "", $model->pasivo_largo_plazo);
        $model->pasivo_diferido = str_replace(",", "", $model->pasivo_diferido);
        $model->capital_contable = str_replace(",", "", $model->capital_contable);
        $model->ventas_totales = str_replace(",", "", $model->ventas_totales);
/*
        $liquidezDiv = 2;
        $solvenciaDiv = 2;
        $apalancamientoDiv = 0;
        $model->pasivo_total = $model->pasivo_circulante + $model->pasivo_largo_plazo + $model->pasivo_diferido;


        if($model->pasivo_circulante>0){
            $liquidezDiv = $model->activo_circulante / $model->pasivo_circulante;
        }

        if($model->pasivo_total>0){
            $solvenciaDiv = $model->activo_circulante/$model->pasivo_total;
        }

        if($model->capital_contable>0){
            $apalancamientoDiv = $model->pasivo_total/$model->capital_contable;
        }

        $model->liquidez = $liquidezDiv;// $model->activo_circulante / $model->pasivo_circulante;
        $model->solvencia = $solvenciaDiv;// $model->activo_circulante/$model->pasivo_total;
        $model->apalancamiento = $apalancamientoDiv;//$model->pasivo_total/$model->capital_contable;


        if(($model->activo_circulante - $model->pasivo_circulante)!=0 && $modelPA->anticipo){
            $model->capacidad_contratacion = ($model->activo_circulante - $model->pasivo_circulante)/$modelPA->anticipo;
        }else{
            $model->capacidad_contratacion = 0.0;
        }

        $liquidezPARAM = $model->liquidez <= 0.5 ?
            $modelPA->nivel1 : ($model->liquidez > 0.5 && $model->liquidez <= 0.75 ?
            $modelPA->nivel2 : ($model->liquidez > 0.75 && $model->liquidez <= 1 ?
            $modelPA->nivel3 : ($model->liquidez > 1 && $model->liquidez <= 1.25 ?
            $modelPA->nivel4 : $modelPA->nivel5)));

        $solvenciaPARAM = $model->solvencia  <= 0.5 ?
            $modelPA->nivel1 : ($model->solvencia  > 0.5 && $model->solvencia  <= 0.75 ?
            $modelPA->nivel2 : ($model->solvencia  > 0.75 && $model->solvencia  <= 1 ?
            $modelPA->nivel3 : ($model->solvencia  > 1 && $model->solvencia  <= 1.25 ?
            $modelPA->nivel4 : $modelPA->nivel5)));

        $apalancamientoPARAM = $model->apalancamiento <= 0.75 ?
            $modelPA->nivel5 : ($model->apalancamiento > 0.75 && $model->apalancamiento <= 1 ?
            $modelPA->nivel4 : ($model->apalancamiento > 1 && $model->apalancamiento <= 1.25 ?
            $modelPA->nivel3 : ($model->apalancamiento > 1.25 && $model->apalancamiento <= 1.75 ?
            $modelPA->nivel2 : $modelPA->nivel1)));

        $model->capacidad_contratacion_resultado = (($model->capital_contable * $liquidezPARAM * $solvenciaPARAM * $apalancamientoPARAM)*.5) + $model->ventas_totales*.5;
*/
        return $model;
    }

}
