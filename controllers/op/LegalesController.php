<?php

namespace app\controllers\op;


use app\controllers\CartaController;
use app\helpers\GeneralController;
use app\helpers\OpController;
use app\models\ActaConstitutiva;
use app\models\ActaConstitutivaSearch;
use app\models\AltaHaciendaSearch;
use app\models\CatActividades;
use app\models\CatEntidades;
use app\models\CatMunicipios;
use app\models\CatRamas;
use app\models\City;
use app\models\Curp;
use app\models\DatosValidados;
use app\models\EscrituraPublica;
use app\models\EscrituraPublicaSearch;
use app\models\Giro;
use app\models\ComprobanteDomicilio;
use app\models\IdOficial;
use app\models\ModificacionActa;
use app\models\ModificacionActaSearch;
use app\models\Porcentaje;
use app\models\RegistroImss;
use app\models\RegistroPublicoPropiedad;
use app\models\RelacionAccionistas;
use app\models\RepresentanteLegal;
use app\models\Provider;
use app\models\ProviderSearch;
use app\models\RepresentanteLegalSearch;
use app\models\State;
use app\models\Status;
use app\models\Usuarios;
use kartik\form\ActiveForm;
use Yii;
use app\models\Rfc;
use app\models\RfcSearch;
use yii\db\Query;
use yii\helpers\ArrayHelper;
use yii\filters\VerbFilter;
use yii\helpers\Html;
use yii\web\Controller;
use yii\web\NotFoundHttpException;
use yii\web\Response;
use yii\web\UploadedFile;
use app\models\RelacionAccionistasSearch;





class LegalesController extends GeneralController
{


    public function behaviors()
    {
        return [
            'verbs' => [
                'class' => VerbFilter::className(),
                'actions' => [
                    'terminar' => ['GET'],
                    'delete' => ['POST','GET'],
                ],
            ],
        ];
    }



    //GENERALES

    //Revision de funcion porque me dio tiempo
    public function actionView(){
        $provider_id = Yii::$app->user->identity->providerid;
        if($provider_id == 0 || !Provider::verifyProvider($provider_id) || !Provider::verifyEtapa2($provider_id)){
            return $this->render('/site/error',['name' => 'Lo sentimos...','message' => 'Acceso denegado']);
        }

        $rechazo = Status::find()->getStatus($provider_id,'rfc','op');

        $ras = new RelacionAccionistasSearch(); //Filtro de relacion accionistas
        $accionistas = (Object)[
            'edicion' => $ras->search(Yii::$app->request->queryParams,'edicionFilter'),
            'rechazados' => $ras->search(Yii::$app->request->queryParams,'rechazadosFilter'),
            'validados' => $ras->search(Yii::$app->request->queryParams,'validadosFilter'),
            'pendientes' => $ras->search(Yii::$app->request->queryParams,'pendientesFilter'),
            'filtro' => $ras
        ];

        $mas = new ModificacionActaSearch(); //Filtro de modificacion de acta
        $modificaciones = (Object)[
            'edicion' => $mas->search(Yii::$app->request->queryParams,'edicionFilter'),
            'rechazados' => $mas->search(Yii::$app->request->queryParams,'rechazadosFilter'),
            'validados' => $mas->search(Yii::$app->request->queryParams,'validadosFilter'),
            'pendientes' => $mas->search(Yii::$app->request->queryParams,'pendientesFilter'),
            'filtro' => $mas
        ];

        $rls = new RepresentanteLegalSearch(); //Filtro de representante legal
        $representantes = (Object)[
            'edicion' => $rls->search(Yii::$app->request->queryParams, 'edicionFilter'),
            'rechazados' => $rls->search(Yii::$app->request->queryParams, 'rechazadosFilter'),
            'validados' => $rls->search(Yii::$app->request->queryParams, 'validadosFilter'),
            'pendientes' => $rls->search(Yii::$app->request->queryParams, 'pendientesFilter'),
            'filtro' => $rls
        ];

        $modelos = (Object)[
            'acta' => $this->findModelActa($provider_id),
            'imss' => $this->findModelRegistroImss($provider_id),
            'escritura' => $this->findModelEscrituraPublica($provider_id),
            'registro' => $this->findModelRegistroPublico($provider_id),
            'curp' => $this->findModelCurp($provider_id),
            'oficial' => $this->findModelIdoficial($provider_id),
            'rfc' => $this->findModel($provider_id),
            'accionistas' => $accionistas,
            'modificaciones' => $modificaciones,
            'representantes' => $representantes
            ];


        return $this->render('view', [
            'tipo_persona' => Provider::findOne($provider_id)->tipo_persona,
            'rechazo' => $rechazo,
            'modelos' => $modelos
        ]);
        
    }


    public function actionUpdate(){
        $user_id = Yii::$app->user->getId();
        $provider_id = Yii::$app->user->identity->providerid;
        if( $provider_id == 0 || !Provider::verifyProvider($provider_id) || !Provider::verifyEtapa2($provider_id) ){
            return $this->render('/site/error',['name' => 'Lo sentimos...','message' => 'Acceso denegado']);
        }

        $models = (Object)[
            'acta' => $this->findModelActa($provider_id),
            'imss' => $this->findModelRegistroImss($provider_id),
            'escritura' => $this->findModelEscrituraPublica($provider_id),
            'registro' => $this->findModelRegistroPublico($provider_id),
            'curp' => $this->findModelCurp($provider_id),
            'oficial' => $this->findModelIdoficial($provider_id),
            'rfc' => $this->findModel($provider_id)
        ];

        $models->registro->provider_id = $provider_id;
        $models->acta->provider_id = $provider_id;
        $models->imss->provider_id = $provider_id;
        $models->escritura->provider_id = $provider_id;
        $models->curp->provider_id = $provider_id;
        $models->oficial->provider_id = $provider_id;
        $models->rfc->provider_id = $provider_id;


        if( $models->rfc->load(Yii::$app->request->post()) || $models->imss->load(Yii::$app->request->post()) || $models->escritura->load(Yii::$app->request->post())
            || $models->registro->load(Yii::$app->request->post()) || $models->curp->load(Yii::$app->request->post()) || $models->oficial->load(Yii::$app->request->post()) ) {
            
            if( $models->acta->load(Yii::$app->request->post()) ){

                if (!empty($models->acta->documento_acta) && GeneralController::str_contains($models->acta->documento_acta, "archivos_tmp") ) {
                    //*/ Pendiente de estandarizar la manera en que los archivos son renombrados y ubicados
                    $acta_path = Yii::$app->user->identity->tipo.'/datos_legales/acta_constitutiva';
                    /*/ $acta_path = "{$pathActa}/{$user_id}/"; //*/
                    $this->makeDir($acta_path);
                    $new_acta_path = str_replace('archivos_tmp/', $acta_path, $models->acta->documento_acta);
                    $this->copyFile($models->acta->documento_acta, $new_acta_path);
                    $models->acta->documento_acta = $new_acta_path;
                }

                $models->acta->save(false);

            }

            if( $models->imss->load(Yii::$app->request->post()) ){

                if (!empty($models->imss->url_registro_imss) && GeneralController::str_contains($models->imss->url_registro_imss, "archivos_tmp") ) {
                    //*/ Pendiente de estandarizar la manera en que los archivos son renombrados y ubicados
                    $imss_path = $models->imss->path.'/';
                    /*/ $imss_path = "{$models->imss->path}/{$user_id}/"; //*/
                    $this->makeDir($imss_path);
                    $new_imss_path = str_replace('archivos_tmp/', $imss_path, $models->imss->url_registro_imss);
                    $this->copyFile($models->imss->url_registro_imss, $new_imss_path);
                    $models->imss->url_registro_imss = $new_imss_path;
                }

                $models->imss->save(false);

            }

            if( $models->escritura->load(Yii::$app->request->post()) ){

                if (!empty($models->escritura->url_escritura_publica) && GeneralController::str_contains($models->escritura->url_escritura_publica, "archivos_tmp") ) {
                    //*/ Pendiente de estandarizar la manera en que los archivos son renombrados y ubicados
                    $escritura_path = $models->escritura->path.'/';
                    /*/ $escritura_path = "{$models->escritura->path}/{$user_id}/"; //*/
                    $this->makeDir($escritura_path);
                    $new_escritura_path = str_replace('archivos_tmp/', $escritura_path, $models->escritura->url_escritura_publica);
                    $this->copyFile($models->escritura->url_escritura_publica, $new_escritura_path);
                    $models->escritura->url_escritura_publica = $new_escritura_path;
                }

                $models->escritura->save(false);

            }

            if( $models->curp->load(Yii::$app->request->post()) ){

                if (!empty($models->curp->url_curp) && GeneralController::str_contains($models->curp->url_curp, "archivos_tmp") ) {
                    //*/ Pendiente de estandarizar la manera en que los archivos son renombrados y ubicados
                    $curp_path = $models->curp->path_curp.'/';
                    /*/ $curp_path = "{$models->curp->path_curp}/{$user_id}/"; //*/
                    $this->makeDir($curp_path);
                    $new_curp_path = str_replace('archivos_tmp/', $curp_path, $models->curp->url_curp);
                    $this->copyFile($models->curp->url_curp, $new_curp_path);
                    $models->curp->url_curp = $new_curp_path;
                }

                $models->curp->save(false);

            }

            if( $models->oficial->load(Yii::$app->request->post()) ){

                $tipo_identificacion = $models->oficial->tipo_identificacion;

                if($tipo_identificacion == 'IFE' && isset($models->oficial->dateIfe) && !empty($models->oficial->dateIfe)){
                    $models->oficial->expiration_date = $models->oficial->dateIfe.'-12-31';
                }else if($tipo_identificacion == 'PASAPORTE' && isset($models->oficial->datePas) && !empty($models->oficial->datePas)){
                    $models->oficial->expiration_date = $models->oficial->datePas;
                }

                if (!empty($models->oficial->url_idoficial) && GeneralController::str_contains($models->oficial->url_idoficial, "archivos_tmp") ) {
                    //*/ Pendiente de estandarizar la manera en que los archivos son renombrados y ubicados
                    $id_path = $models->oficial->path_idoficial.'/';
                    /*/ $id_path = "{$models->oficial->path_idoficial}/{$user_id}/"; //*/
                    $this->makeDir($id_path);
                    $new_id_path = str_replace('archivos_tmp/', $id_path, $models->oficial->url_idoficial);
                    $this->copyFile($models->oficial->url_idoficial, $new_id_path);
                    $models->oficial->url_idoficial = $new_id_path;
                }

                $models->oficial->save(false);

            }

            if( $models->registro->load(Yii::$app->request->post()) ){ $models->registro->save(false); }

            $models->rfc->status_op = Status::STATUS_ENEDICION;
            if($models->rfc->save(false)){
                $models->rfc->status_op= Status::STATUS_ENEDICION;
                $models->rfc->save();
                if( ( $rechazo = Status::find()->where(['and',['register_id' => $provider_id],['status_op' => Status::STATUS_PENDIENTE],['modelo'=>'rfc']])->one() ) !==null ){
                    $rechazo->status_op = Status::STATUS_TERMINADO_PRO;
                    $rechazo->save();
                }
            }

            return $this->redirect(['view']);

        }

        $estados = ArrayHelper::map(CatEntidades::find()->select(['entidad_id','nombre'])->all(),'entidad_id','nombre');

        $municipios = (Object)[
            'escritura' => ($models->escritura->ciudad_id)?
                ArrayHelper::map(CatMunicipios::find()->where(['entidad_id' => $models->escritura->estado_id])->asArray()->all(),'municipio_id','nombre'):[],
            'registro' => ($models->registro->ciudad_id)?
                ArrayHelper::map(CatMunicipios::find()->where(['entidad_id' => $models->registro->estado_id])->asArray()->all(),'municipio_id','nombre'):[],
            'acta' => ($models->acta->ciudad_id)?
                ArrayHelper::map(CatMunicipios::find()->where(['entidad_id' => $models->acta->estado_id])->asArray()->all(),'municipio_id','nombre'):[],
        ];

        if(isset($models->oficial->tipo_identificacion) && $models->oficial->tipo_identificacion == 'IFE' && isset($models->oficial->expiration_date) && !empty($models->oficial->expiration_date)){
            $models->oficial->dateIfe = substr($models->oficial->expiration_date,0,4);
        }else if(isset($models->oficial->tipo_identificacion) && $models->oficial->tipo_identificacion == 'PASAPORTE' && isset($models->oficial->expiration_date) && !empty($models->oficial->expiration_date)){
            $models->oficial->datePas = $models->oficial->expiration_date;
        }

        return $this->render('update', [
            'models' => $models,
            'municipios'=>$municipios,
            'estados'=> $estados,
            'tipo_persona' => Provider::findOne($provider_id)->tipo_persona,
            'provider_id' => $provider_id
        ]);

    }

    //Representante Legal


    public function actionUpdaterep($id = 0)
    {

        if (Yii::$app->request->isAjax || Yii::$app->request->post()) {

            $model = $this->findModelRepresentanteLegal(intval(base64_decode($id)));
            $modelOld = $model->getAttributes();
            $statusOld = $model->status_op;
            $model->scenario = RepresentanteLegal::SCENARIO_CREATE_REP_OP;
            $providerId = Yii::$app->user->identity->providerid;
            $modelActa = $this->findModelActa($providerId);

            $modelActa->documento_actaOLD = $modelActa->documento_acta;

            $model->documento_identificacionOLD = $model->documento_identificacion;
            $model->documento_actaOLD = $model->documento_acta;

            $repRfcOld = null;
            $nameOld = null;
            $firmante = null;
            $model->validado = empty($model->validado) ? false : $model->validado;
            if(isset($model->rep_bys) && !empty($model->rep_bys)){
                $repRfcOld = $model->rfc;
                $nameOld = trim($model->nombre).' '.trim($model->ap_paterno).' '.trim($model->ap_materno);
                $firmante = $model->rep_bys;
            }

            $tipo = null;

            if (isset($_POST['RepresentanteLegal']['rep_op']) && !empty($_POST['RepresentanteLegal']['rep_op'])) {
                $tipo = $_POST['RepresentanteLegal']['rep_op'];
            }

            $t = Yii::$app->db->beginTransaction();
            if (Yii::$app->request->isAjax && $model->load(Yii::$app->request->post())) {
                $result = [];
                Yii::$app->response->format = Response::FORMAT_JSON;
                if (isset($model->rfc) && !empty($model->rfc)) {
                    $count_exist = RepresentanteLegal::find()
                        ->where(['and', ['provider_id' => $providerId], ['rfc' => $model->rfc], ['activo' => true],['not in','rfc',['XEXX010101000']]])
                        ->count('1');
                    if ($count_exist > 0 && $id === 0) {
                        $result['representantelegal-rfc'] = ['Ya se encuentra el representante legal registrado.'];
                    }
                }
                return array_merge($result,ActiveForm::validate($model));
            } else if ($model->load(Yii::$app->request->post())) {
                CartaController::reactivaCartaProtesta($model);

                if($statusOld=='VALIDADO'){
                    $modelNew = $model->getAttributes();
                    $updateStatus = $this->verifyChange($modelOld,$modelNew);
                    if($updateStatus && !$model->validado){
                        $model->status_op = 'EN EDICION';
                    }
                }
                $tipoIdent = $model->tipo_identificacion;

                if($tipoIdent == 'IFE' && isset($model->dateIfe) && !empty($model->dateIfe)){
                    $model->vencimiento_identificacion = $model->dateIfe.'-12-31';
                }else if($tipoIdent == 'PASAPORTE' && isset($model->datePas) && !empty($model->datePas)){
                    $model->vencimiento_identificacion = $model->datePas;
                }

                $pathRL = 'op/actividad_economica/representante_legal';
                $this->makeDir($pathRL);
                $model->provider_id = $providerId;

                if ($tipo == 'si' || $tipo == 'no') {
                    $model->rep_op = $tipo === 'si' ? true : false;
                }

                if (!empty($model->documento_identificacion) && $model->documento_identificacion != $model->documento_identificacionOLD) {
                    $new_nameDi = str_replace('archivos_tmp/', $pathRL . '/', $model->documento_identificacion);
                    $this->copyFile($model->documento_identificacion, $new_nameDi);
                    $model->documento_identificacion = $new_nameDi;
                }

                if (!empty($model->documento_acta) && $model->documento_acta != $model->documento_actaOLD) {
                    $new_nameDocA = str_replace('archivos_tmp/', $pathRL . '/', $model->documento_acta);
                    $this->copyFile($model->documento_acta, $new_nameDocA);
                    $model->documento_acta = $new_nameDocA;
                }

                if (!empty($model->documento_relacion_select)) {
                    $model->documento_acta = $model->documento_relacion_select;
                }

                $verify_save = true;
                if ($modelActa->load(Yii::$app->request->post())) {

                    $pathA = 'op/actividad_economica/acta_constitutiva';
                    $this->makeDir($pathA);
                    $modelActa->provider_id = $providerId;

                    if (!empty($modelActa->documento_acta) && $modelActa->documento_acta != $modelActa->documento_actaOLD) {
                        $new_nameActa = str_replace('archivos_tmp/', $pathA . '/', $modelActa->documento_acta);
                        $this->copyFile($modelActa->documento_acta, $new_nameActa);
                        $modelActa->documento_acta = $new_nameActa;
                    }

                    if (!$modelActa->save()) {
                        $verify_save = false;
                    }
                }


                switch (Yii::$app->request->post()['RepresentanteLegal']['tipo_poder']) {
                    case 'ACTA':
                        $model->documento_acta = '0';
                        break;
                    case 'ACTUALIZACIÓN':
                        $model->documento_acta = Yii::$app->request->post()['RepresentanteLegal']['documento_relacion_select'];
                        break;
                }

                if ($model->save() && $verify_save) {

                    $repRfcNew = $model->rfc;
                    $nameNew = trim($model->nombre).' '.trim($model->ap_paterno).' '.trim($model->ap_materno);

                    $rfcVerify = RepresentanteLegal::find()->select('rfc')->where(['and', ['provider_id' => $providerId], ['rep_op' => true],
                        ['activo' => true],['not in','representante_legal_id',$model->representante_legal_id]])->one()['rfc'];

                    if ( (!empty($model->rfc) && $model->rfc != '' && $model->rep_op &&  $model->rfc != $rfcVerify) || ($nameOld!=null && $nameOld!=$nameNew) || (!empty($firmante) && $firmante!=$model->rep_op) ) {
                        Provider::updateAll(['status_carta_op' => 'PENDIENTE'], ['provider_id' => $model->provider_id]);
                    }

                    if($model->rep_op){
                        RepresentanteLegal::updateAll(['rep_op' => false],['and',['provider_id' => $providerId],['not in','representante_legal_id',$model->representante_legal_id]]);
                    }

                    $t->commit();
                    self::eliminarCita();
                    return $this->redirect('view');
                }
                //var_dump(json_encode($modelActa->errors)).exit();
                //var_dump(json_encode($model->errors)).exit();
                $t->rollBack();
                return $this->goHome();

            }

            $rechazo = [];
            if (!empty($model->representante_legal_id)) {
                $rechazo = Status::find()->getStatus($model->representante_legal_id, 'representante_legal', 'op');
            }

            $mods = ArrayHelper::map(
                ModificacionActa::find()->select(['modificacion_acta_id', 'nombre_documento'])
                    ->where(['and', ['provider_id' => Yii::$app->user->identity->providerid], ['activo' => TRUE], ['in','status_op',[Status::STATUS_PORVALIDAR,Status::STATUS_VALIDADO,Status::STATUS_ENEDICION]]])
                    ->asArray()->all(), 'modificacion_acta_id', 'nombre_documento');

            if(isset($model->tipo_identificacion) && $model->tipo_identificacion == 'IFE' && isset($model->vencimiento_identificacion) && !empty($model->vencimiento_identificacion)){
                $model->dateIfe = substr($model->vencimiento_identificacion,0,4);
            }else if(isset($model->tipo_identificacion) && $model->tipo_identificacion == 'PASAPORTE' && isset($model->vencimiento_identificacion) && !empty($model->vencimiento_identificacion)){
                $model->datePas = $model->vencimiento_identificacion;
            }

            return $this->renderAjax('updaterep', [
                'model' => $model,
                'modelActa' => $modelActa,
                'rechazo' => $rechazo,
                'mods' => $mods
            ]);

        }

        return $this->goHome();

    }

    public function actionViewrep($id = null)
    {

        $model = RepresentanteLegal::find()->where(['and', ['provider_id' => Yii::$app->user->identity->providerid], ['representante_legal_id' => intval(base64_decode($id))]])->one();
        if ($model) {
            return $this->renderAjax('viewrep', [
                'model_representante_legal' => $model,
                'model_registro_acta' => $this->findModelActa(Yii::$app->user->identity->providerid),
            ]);
        }
        return json_encode(['status' => 'No permitido']);
    }

    public function actionDeleterepresentante($id = 0)
    {

        $model = RepresentanteLegal::find()
            ->where(['and', ['provider_id' => Yii::$app->user->identity->providerid],
                ['representante_legal_id' => intval(base64_decode($id))],
                ['status_op' => [Status::STATUS_ENEDICION,Status::STATUS_RECHAZADO,Status::STATUS_VALIDADO]]])->one();

        if ($model) {
            $model->activo = FALSE;
            $this->deleteFile($model->documento_identificacion);
            $this->deleteFile($model->documento_acta);
            $model->save();
            Yii::$app->session->setFlash('success', 'Registro eliminado correctamente.');
            return $this->redirect('view#representante');
        }
        Yii::$app->session->setFlash('error', 'Error al eliminar el registro.');
        return $this->redirect('view#representante');
    }


    public function actionTerminarrep($id = null)
    {

        $IdLastVal = $this->saveLastSendValidation(Yii::$app->user->identity->getProvider(),'validRepresentante','representante_legal','op');

        if ($id != null) {
            $status = $this->terminarUno('RepresentanteLegal', intval(base64_decode($id)), 'representante_legal_id', 'representante_legal', 'status_op','op',$IdLastVal);
        } else {
            $status = $this->terminarTodos('RepresentanteLegal', 'representante_legal_id', 'representante_legal', 'status_op','op',$IdLastVal);
        }

        if ($status) {
            $correos_validadores = self::getEmailValidador(1, 'op');

            self::sendEmail('/provider/correos/nuevo', null, $correos_validadores, 'Nuevo proveedor', ['modulo' => 'Representante Legal', 'tipo_provider' => 'op']);

            self::AllSendNotification(null, 'op', null, 'DATOS LEGALES', 'Representante Legal');

            $provider = Provider::findOne(Yii::$app->user->identity->providerid);
            GeneralController::sendModuloRevisionEmail($provider->email, 'Legales (Representante Legal)');
        }


        return $this->redirect('view#representante');
    }



    //accionistas

    public function actionViewaccionistas($id=null){

        $model = RelacionAccionistas::find()->where(['and',['provider_id'=> Yii::$app->user->identity->providerid],['relacion_accionistas_id'=> intval(base64_decode($id))]])->one();
        if($model){
            return $this->renderAjax('viewaccionistas',[
                'model'=>$model
            ]);
        }
        //return json_encode(['status'=>'No permitido']);
    }


    public function actionViewValidaraccionistas($id=null, $user_id=null){

        $queryParams = Yii::$app->request->queryParams;
        $id = isset($queryParams['id']) ? intval( base64_decode($queryParams['id']) ) : 0;
        $user_id = isset($queryParams['user_id']) ? intval( $queryParams['user_id'] ) : 0;
        $opcionesArray = isset($queryParams['opciones']) ? $queryParams['opciones'] : null;
        $opcionesSerial = isset($opcionesArray) ? serialize($opcionesArray) : null;
        $opcionesBase64 = isset($opcionesSerial) ? base64_encode($opcionesSerial) : null;

        $model = $this->findModelAccionista($id);
        $model_status = new Status();
        $model_status->scenario = Status::SCENARIO_CREATE;
        $modelValidado = new DatosValidados();
        $modelValidado->scenario = DatosValidados::SCENARIO_CREATE;
        $modelos = [];

        if(Yii::$app->user->can(Usuarios::ROLE_VALIDADOR_PROVEEDORES)){
            if(isset($model->provider_id) && !empty($model->provider_id)){
                $modelos[$model->formName()] = $model->attributes;
            }
            $modelos = base64_encode(json_encode($modelos));

        }

        $rechazo = [];
        if(!empty($model->relacion_accionistas_id)){
            $rechazo = Status::find()->getStatus($model->relacion_accionistas_id,'relacion_accionistas','op','TERMINADO PRO');
        }

        return $this->renderAjax('view-validaraccionistas', [
            'model' => $model,
            'modelos' => $modelos,
            'modelStatus' => $model_status,
            'modelValidado' => $modelValidado,
            'rechazo' => $rechazo,
            'opciones' => $opcionesBase64,
            'namePro' => $this->getNameProvider($model->provider_id)
        ]);

    }


    public function actionUpdateaccionistas($id=0){
        $isRequest = Yii::$app->request->isAjax;
        $postRequest = Yii::$app->request->post();

        if($isRequest || $postRequest){
            
            $model = $this->findModelAccionistas(intval(base64_decode($id)));
            $model->provider_id = Yii::$app->user->identity->providerid;
            if($isRequest && $model->load($postRequest)){
                $result = [];
                Yii::$app->response->format = Response::FORMAT_JSON;
                if (!empty($model->provider_id) && !empty($model->rfc)){
                    $count_exist = RelacionAccionistas::find()
                        ->where(['and', ['provider_id' => $model->provider_id], ['rfc' => $model->rfc], ['activo' => true],['not in','rfc',['XEXX010101000']]])
                        ->count('1');
                    if($count_exist>0 && $id === 0){  $result['relacionaccionistas-rfc'] = ['Ya se encuentra el accionista registrado']; }
                }
                if(!empty($model->rfc)){
                    if(strlen($model->rfc) == 12){
                        if(empty($model->razon_social)){ $result['relacionaccionistas-razon_social'] = ['Razón Social no puede estar vacío']; }
                    }else{
                        if(empty($model->curp) && $model->rfc != 'XAXX010101000'){ $result['relacionaccionistas-curp'] = ['Curp no puede estar vacío']; }
                        if(empty($model->nombre)){ $result['relacionaccionistas-nombre'] = ['Nombre no puede estar vacío']; }
                        if(empty($model->ap_paterno)){ $result['relacionaccionistas-ap_paterno'] = ['Primer apellido no puede estar vacío']; }
                        if(empty($model->ap_materno)){ $result['relacionaccionistas-ap_materno'] = ['Segundo apellido no puede estar vacío']; }
                    }
                    if(empty($model->tipo_identificacion)){ $result['relacionaccionistas-tipo_identificacion'] = ['Debe seleccionar un tipo de identificacion']; }
                    else{ if($model->tipo_identificacion != 'CIF' && strlen($model->rfc) == 12){  $result['relacionaccionistas-tipo_identificacion'] = ['El tipo de identificacion para Persona Moral debe ser la Cedula de Identificacion Fiscal'];  } }
                    if(empty($model->url_identificacion)){ $result['relacionaccionistas-url_identificacion'] = ['El documento es requerido']; }
                    if(empty($model->url_identificacion)){ $result['relacionaccionistas-url_identificacion'] = ['El documento es requerido']; }
                }
                if(empty($model->tipo_relacion)){ $result['relacionaccionistas-tipo_relacion'] = ['El tipo de relacion es requerido']; }
                else if($model->tipo_relacion == RelacionAccionistas::RELACION_MODIFICACION && (
                    isset($postRequest['RelacionAccionistas']['documento_relacion_select']) && empty($postRequest['RelacionAccionistas']['documento_relacion_select'])
                )){ $result['relacionaccionistas-documento_relacion_select'] = ['El documento de relacion es requerido'];  }
                else if( $model->tipo_relacion == RelacionAccionistas::RELACION_OTRO && empty($model->documento_relacion) ){
                    $result['relacionaccionistas-documento_relacion'] = ['El documento de relacion es requerido'];
                }
                return array_merge($result,ActiveForm::validate($model));
            }else if($model->load($postRequest)){
                $model->status_op = Status::STATUS_ENEDICION;

                if (!empty($model->documento_relacion) && GeneralController::str_contains($model->documento_relacion, "archivos_tmp") ) {
                    $this->makeDir($model->pathOp);
                    $new_documento_path = str_replace('archivos_tmp/', $model->pathOp, $model->documento_relacion);
                    $this->copyFile($model->documento_relacion, $new_documento_path);
                    $model->documento_relacion = $new_documento_path;
                }

                if (!empty($model->url_identificacion) && GeneralController::str_contains($model->url_identificacion, "archivos_tmp") ) {
                    $this->makeDir($model->pathOp);
                    $new_identificacion_path = str_replace('archivos_tmp/', $model->pathOp, $model->url_identificacion);
                    $this->copyFile($model->url_identificacion, $new_identificacion_path);
                    $model->url_identificacion = $new_identificacion_path;
                }

                switch ($postRequest['RelacionAccionistas']['tipo_relacion']){
                    case 'ACTA':
                        $model->documento_relacion = '0';
                        break;
                    case 'MODIFICACION':
                        $model->documento_relacion = Yii::$app->request->post()['RelacionAccionistas']['documento_relacion_select'];
                        break;
                }

                if($model->save()){
                    GeneralController::changeStatus($model->relacion_accionistas_id,'relacion_accionistas', 'status_op');
                    $this->redirect(['view#accionistas']);
                }else{
                    var_dump($model->errors).exit();
                }

            }

            $mods = ArrayHelper::map(
                ModificacionActa::find()->select(['modificacion_acta_id','nombre_documento'])
                    ->where(['and',['provider_id' => Yii::$app->user->identity->providerid],['activo'=>TRUE], ['in','status_op' ,[Status::STATUS_PORVALIDAR,Status::STATUS_VALIDADO,Status::STATUS_ENEDICION]]])
                    ->asArray()->all(),'modificacion_acta_id','nombre_documento');

            $rechazo = [];
            if(!empty($model->relacion_accionistas_id)){
                $rechazo = Status::find()->getStatus($model->relacion_accionistas_id,'relacion_accionistas','op');
            }

            return $this->renderAjax('updateaccionistas',[
                'model' => $model,
                'modificaciones' => $mods,
                'rechazo' => $rechazo
            ]);

        }

        return $this->goHome();

    }


    public function actionDeleteaccionistas($id=0){

        $model = RelacionAccionistas::find()
            ->where(['and',['provider_id'=>Yii::$app->user->identity->providerid],['relacion_accionistas_id'=>intval(base64_decode($id))],['status_'.Yii::$app->user->identity->tipo=>$this->statusParaEliminar]])->one();

        if($model){
            $model->activo = FALSE;
            $this->deleteFile($model->documento_relacion);
            $model->save(false);
            Yii::$app->session->setFlash('success','Registro eliminado correctamente.');
            return $this->redirect('view#accionistas');
        }
        //return json_encode(['status'=>'No permitido']);
    }


    public function actionTerminaraccionistas($id=null){

        $IdLastVal = $this->saveLastSendValidation(Yii::$app->user->identity->getProvider(),'validRelacionAccionistas','relacion_accionistas','op');

        if($id!=null){
            $status = $this->terminarUno('RelacionAccionistas',intval(base64_decode($id)),'relacion_accionistas_id','relacion_accionistas','status_op','ACCIONISTA',$IdLastVal);
        }else{
            $status = $this->terminarTodos('RelacionAccionistas','relacion_accionistas_id','relacion_accionistas','status_op','ACCIONISTA',$IdLastVal);
        }

        if($status){
            $correos_validadores = self::getEmailValidador(1,'op');

            self::sendEmail('/provider/correos/nuevo', null, $correos_validadores, 'Nuevo proveedor', ['modulo' => 'Relación accionistas','tipo_provider' => 'op']);

            self::AllSendNotification(null,'op',null,'DATOS LEGALES','Relación accionistas');

            $provider = Provider::findOne(Yii::$app->user->identity->providerid);
            GeneralController::sendModuloRevisionEmail($provider->email, 'Legales (Relación accionistas)');
        }

        return $this->redirect('view#accionistas');
    }


    protected function findModelAccionistas($id){
        $model = RelacionAccionistas::find()->where(['and',['provider_id'=>Yii::$app->user->identity->providerid],['relacion_accionistas_id' => $id]])->one();
        if(!$model){
            $model = new RelacionAccionistas();
        }
        return $model;
    }


    public function actionRelacion($id = null){
        if($id != 'MODIFICACION'){
            return false;
        }
        $mods = ArrayHelper::map(
            ModificacionActa::find()->select(['modificacion_acta_id','nombre_documento'])
                ->where(['and',['provider_id' => Yii::$app->user->identity->providerid],['activo'=>TRUE],['in','status_op' ,[Status::STATUS_PORVALIDAR,Status::STATUS_VALIDADO,Status::STATUS_ENEDICION]]])
                ->asArray()->all(),'modificacion_acta_id','nombre_documento');

        $selector ='<option value=""></option>';
        foreach ($mods as $key=>$value){
            $selector .='<option value="'.$key.'">'.$value.'</option>';
        }
        echo $selector;

    }





    //Modificaciones


    public function actionViewmod($id=null){

        $model = ModificacionActa::find()->where(['and',['provider_id'=> Yii::$app->user->identity->providerid],['modificacion_acta_id'=> intval(base64_decode($id))]])->one();
        if($model){
            return $this->renderAjax('viewmod',[
                'model'=>$model
            ]);
        }
        //return json_encode(['status'=>'No permitido']);
    }


    public function  actionViewValidarmod($id=0){

        $model = ModificacionActa::find()->where(['modificacion_acta_id'=> intval(base64_decode($id))])->one();
        if(Yii::$app->user->isGuest || $id == null || !Yii::$app->user->can(Usuarios::ROLE_VALIDADOR_CONTRATISTAS) || !$model){
            $this->redirect('/');
        }

        $status = new Status();
        $status->scenario = Status::SCENARIO_CREATE;
        $validados = new DatosValidados();
        $validados->scenario = DatosValidados::SCENARIO_CREATE;

        return $this->renderAjax('view-validaraccionistas',[
            'modelStatus' => $status,
            'modelValidado' => $validados,
            'model'=>$model,
            'namePro' => $this->getNameProvider($model->provider_id)
        ]);

    }


    public function actionUpdatemod($id=0){

        if(Yii::$app->request->isAjax || Yii::$app->request->post()){
            $model = $this->findModelMod(intval(base64_decode($id)));

            $model->documento_actaOLD = $model->documento_acta;
            $model->provider_id = Yii::$app->user->identity->providerid;
            if(Yii::$app->request->isAjax && $model->load(Yii::$app->request->post())){
                $errors = [];
                Yii::$app->response->format = Response::FORMAT_JSON;
                if(empty($model->descripcion_cambios)){ $errors['modificacionacta-descripcion_cambios'] = ['La descripcion de inscripcion no puede estar vacia']; }
                if(empty($model->nombre_documento)){ $errors['modificacionacta-nombre_documento'] = ['El nombre del documento no puede estar vacio']; }
                return array_merge(ActiveForm::validate($model), $errors);
            }else if($model->load(Yii::$app->request->post())){
                $model->status_op = Status::STATUS_ENEDICION;
                $path = Yii::$app->user->identity->tipo.'/datos_legales/modificaciones/';

                $this->makeDir($path);

                if(!empty($model->documento_acta) && $model->documento_acta!=$model->documento_actaOLD){
                    $new_nameActa = str_replace('archivos_tmp/',$path.'/',$model->documento_acta);
                    $this->copyFile($model->documento_acta,$new_nameActa);
                    $model->documento_acta = $new_nameActa;
                }

                if($model->save()){
                    GeneralController::changeStatus($model->modificacion_acta_id,'modificacion_acta','status_op');
                    $this->redirect('view#modificaciones');
                }else{
                    return $this->goHome();
                }
            }

            $rechazo = [];
            if(!empty($model->modificacion_acta_id)){
                $rechazo = Status::find()->getStatus($model->modificacion_acta_id,'modificacion_acta','op');
            }


            return $this->renderAjax('updatemod',[
                'model' => $model,
                'rechazo' => $rechazo
            ]);
        }

        return $this->goHome();



    }


    public function actionDeletemod($id=0){


        $model = ModificacionActa::find()
            ->where(['and',['provider_id'=>Yii::$app->user->identity->providerid],['modificacion_acta_id'=>intval(base64_decode($id))],['status_'.Yii::$app->user->identity->tipo=>$this->statusParaEliminar]])->one();

        if($model){
            $model->activo = FALSE;
            $model->save();
            Yii::$app->session->setFlash('success','Registro eliminado correctamente.');
            return $this->redirect('view');
        }
        //return json_encode(['status'=>'No permitido']);
    }


    public function actionTerminarmod($id=null){

        $IdLastVal = $this->saveLastSendValidation(Yii::$app->user->identity->getProvider(),'validModificacionActa','modificacion_acta','op');

        if($id!=null){
            $status = $this->terminarUno('ModificacionActa',intval(base64_decode($id)),'modificacion_acta_id','modificacion_acta','status_op','',$IdLastVal);
        }else{
            $status = $this->terminarTodos('ModificacionActa','modificacion_acta_id','modificacion_acta','status_op','',$IdLastVal);
        }

        if($status){
            $correos_validadores = self::getEmailValidador(1,'op');

            self::sendEmail('/provider/correos/nuevo', null, $correos_validadores, 'Nuevo proveedor', ['modulo' => 'Modificaciones de acta','tipo_provider' => 'op']);

            self::AllSendNotification(null,'op',null,'DATOS LEGALES','Modificaciones de acta');

            $provider = Provider::findOne(Yii::$app->user->identity->providerid);
            GeneralController::sendModuloRevisionEmail($provider->email, 'Legales (Modificaciones de acta)');

        }
        return $this->redirect(['view#modificaciones']);
    }


    protected function findModel($id)
    {
        $model = Rfc::find()->where(['provider_id' => $id])->one();
        if($model == null || empty($model)){
            $model = new Rfc();
        }
        return $model;
    }

    protected function findModelMod($id){
        $model = ModificacionActa::find()->where(['and',['provider_id'=>Yii::$app->user->identity->providerid],['modificacion_acta_id' => $id]])->one();
        if(!$model){
            $model = new ModificacionActa();
        }
        return $model;
    }


    protected function findModelComprobanteDomicilio($id)
    {
        $model = ComprobanteDomicilio::find()->where(['provider_id' => $id])->one();
        if(!$model){
            $model = new ComprobanteDomicilio();
        }
        return $model;
    }




    protected function findModelRegistroImss($id)
    {
        $model = RegistroImss::find()->where(['provider_id' => $id])->one();
        if(!$model){
            $model = new RegistroImss();
        }
        return $model;
    }


    protected function findModelActa($id)
    {

        $model = ActaConstitutiva::find()->where(['provider_id' => $id])->one();
        if(!$model){
            $model = new ActaConstitutiva();
        }
        return $model;
    }


    protected function findModelEscrituraPublica($id){
        $model = EscrituraPublica::find()->where(['provider_id' => $id])->one();
        if(!$model){
            $model = new EscrituraPublica();
        }
        return $model;
    }


    protected function findModelRegistroPublico($id){
        $model = RegistroPublicoPropiedad::find()->where(['provider_id' => $id])->one();
        if(!$model){
            $model = new RegistroPublicoPropiedad();
        }
        return $model;
    }


    public function actionList_city($id){
        $city = ArrayHelper::map(CatMunicipios::find()->select(['municipio_id','nombre'])->where(['entidad_id' => $id])->asArray()->all(),'municipio_id','nombre');
        $con ='';
        $con .='<option value=""></option>';
        foreach ($city as $key=>$value){
            $con .='<option value="'.$key.'">'.$value.'</option>';
        }
        echo $con;

    }

//    public function actionIndexValidador()
//    {
//        $searchModel = new RfcSearch();
//        $searchPropiedad = new EscrituraPublicaSearch();
//        $searchActaConstitutiva = new ActaConstitutivaSearch();
//        $searchHacienda = new AltaHaciendaSearch();
//        $searchRelacionAccionistas = new RelacionAccionistasSearch();
//        $searchProvider = new ProviderSearch();
//        $dataProvider = $searchModel->search(Yii::$app->request->queryParams,'mainFilter');
//        $datosPropiedad = $searchPropiedad->search(Yii::$app->request->queryParams,'mainPropiedad');
//        $datosHacienda = $searchHacienda->search(Yii::$app->request->queryParams,'mainHacienda');
//        $datosRelacionAccionistas = $searchProvider->search(Yii::$app->request->queryParams,'validRelacionAccionistas');
//
//
//        return $this->render('index-validador', [
//            'searchModel' => $searchModel,
//            'searchPropiedad' => $searchPropiedad,
//            'searchActaConstitutiva' => $searchActaConstitutiva,
//            'searchHacienda' => $searchHacienda,
//            'dataProvider' => $dataProvider,
//            'datosPropiedad' => $datosPropiedad,
//            'datosHacienda' => $datosHacienda,
//            'datosRelacionAccionistas' => $datosRelacionAccionistas,
//            'searchProvider'=> $searchProvider,
//        ]);
//    }

    //Validar bien la logica de la funcion, ya que valida en fron y back y es un desperdicio de recursos

    public function actionTerminar($id=null){
        $id = Yii::$app->user->identity->providerid;
        if($id == null || !Provider::verifyProvider($id) || !Provider::verifyEtapa2($id)|| !self::verifyTerminar($id)){
            echo $this->render('/site/error',['name' => 'Lo sentimos...','message' => 'Acceso denegado']);

            return false;
        }
        $model = $this->findModel($id);
        if($model->status_op != Status::STATUS_ENEDICION){
            echo $this->render('/site/error',['name' => 'Lo sentimos...','message' => 'Acceso denegado']);

            return false;
        }

        $rfc = Yii::$app->db->createCommand();
        $rfc->update('provider.rfc',['status_op' => Status::STATUS_PORVALIDAR],'provider_id ='.$id);
        $rfc->execute();

        $correos_validadores = self::getEmailValidador(1,'op');

        $IdLastVal = $this->saveLastSendValidation($id,'mainFilterLegales','rfc','op');
        $this->saveLastSendValidationRelation($IdLastVal,$id,'rfc',$id,'op');

        self::sendEmail('/provider/correos/nuevo', null, $correos_validadores, 'Nuevo proveedor', ['modulo' => 'Datos legales','tipo_provider' => 'op']);

        self::AllSendNotification(null,'op',null,'DATOS LEGALES','Datos legales');

        $provider = Provider::findOne(Yii::$app->user->identity->providerid);
        GeneralController::sendModuloRevisionEmail($provider->email, 'Legales (Datos Legales)');

        return $this->redirect(['/provider/dashboard']);
    }

//    public function actionConfirm(){
//
//        $role = Yii::$app->user->identity->role;
//        $status_global = $role == 'VALIDADOR PROVEEDORES'?'status_bys':($role == 'VALIDADOR CONTRATISTAS'?'status_op':'');
//
//        $id = Yii::$app->request->post()['provider_id'];
//        $rfc = Yii::$app->db->createCommand();
//        $rfc->update('provider.rfc',[$status_global => 'VALIDADO'],'provider_id ='.$id);
//        $rfc->execute();
//
//        $status_id = Status::find()->select('status_id')
//            ->where(['and',['register_id' => $id],[$status_global => 'PENDIENTE']])
//            ->andWhere(['modelo'=>'RFC'])->one();
//        if(!empty($status_id['status_id'])){
//            $requi_status = Status::findOne($status_id);
//            $requi_status->$status_global = Status::STATUS_TERMINADO;
//            $requi_status->update();
//
//        }
//
//        Yii::$app->session->setFlash('success', 'Los datos se validaron correctamente');
//        return Yii::$app->getResponse()->redirect(['datosLegales/rfc/index-validador']);
//
//    }

    public function actionViewValidar($id=null){
        $queryParams = Yii::$app->request->queryParams;
        $provider_id = isset($queryParams['id']) ? intval( $queryParams['id'] ) : null;
        $opcionesArray = isset($queryParams['opciones']) ? $queryParams['opciones'] : null;
        $opcionesSerial = isset($opcionesArray) ? serialize($opcionesArray) : null;
        $opcionesBase64 = isset($opcionesSerial) ? base64_encode($opcionesSerial) : null;

        $provider = Provider::findOne($provider_id);

        $tipo_persona = $provider->tipo_persona;
        $nombre_provider = $provider->getFullName();

        $modelos = [];

        $models = (Object)[
            'acta' => $this->findModelActa($provider_id),
            'imss' => $this->findModelRegistroImss($provider_id),
            'escritura' => $this->findModelEscrituraPublica($provider_id),
            'registro' => $this->findModelRegistroPublico($provider_id),
            'rfc'  =>  $this->findModel($provider_id),
            'curp' => $this->findModelCurp($provider_id),
            'idoficial' => $this->findModelIdoficial($provider_id)
        ];


        $model_status = new Status();
        $model_status->scenario = Status::SCENARIO_CREATE;
        $model_validado = new DatosValidados();
        $model_validado->scenario = DatosValidados::SCENARIO_CREATE;
        if(isset($models->acta->provider_id) && !empty($models->acta->provider_id)){
            $modelos[$models->acta->formName()] = $models->acta->attributes;
        }
        if(isset($models->imss->provider_id) && !empty($models->imss->provider_id)){
            $modelos[$models->imss->formName()] = $models->imss->attributes;
        }
        if(isset($models->escritura->provider_id) && !empty($models->escritura->provider_id)){
            $modelos[$models->escritura->formName()] = $models->escritura->attributes;
        }
        if(isset($models->registro->provider_id) && !empty($models->registro->provider_id)){
            $modelos[$models->registro->formName()] = $models->registro->attributes;
        }

        if(isset($models->rfc->provider_id) && !empty($models->rfc->provider_id)){
            $modelos[$models->rfc->formName()] = $models->rfc->attributes;
        }

        if(isset($models->curp->provider_id) && !empty($models->curp->provider_id)){
            $modelos[$models->curp->formName()] = $models->curp->attributes;
        }

        if(isset($models->idoficial->provider_id) && !empty($models->idoficial->provider_id)){
            $modelos[$models->idoficial->formName()] = $models->idoficial->attributes;
        }

        $rfc = Provider::find()->select('rfc')->where(['provider_id' => $id])->one()->rfc;

        $rechazo = Status::find()->getStatus($id,'rfc','op','TERMINADO PRO');

        return $this->renderAjax('view-validar', [
            'rfc'=>$rfc,
            'model_status' => $model_status,
            'model_validado' => $model_validado,
            'modelos' => base64_encode(json_encode($modelos)),
            'models' => $models,
            'provider_id' => $id,
            'rechazo' => $rechazo,
            'nombre_provider' => $nombre_provider,
            'opciones' => $opcionesBase64,
            'tipo_persona' => $tipo_persona,

        ]);
    }

    public function actionViewValidarep($id = 0)
    {
        $queryParams = Yii::$app->request->queryParams;
        $id = isset($queryParams['id']) ? intval( base64_decode($queryParams['id']) ) : 0;
        $opcionesArray = isset($queryParams['opciones']) ? $queryParams['opciones'] : null;
        $opcionesSerial = isset($opcionesArray) ? serialize($opcionesArray) : null;
        $opcionesBase64 = isset($opcionesSerial) ? base64_encode($opcionesSerial) : null;

        $model = $this->findModelRepresentanteLegalVal($id);

        $model_status = new Status();
        $model_status->scenario = Status::SCENARIO_CREATE;
        $modelValidado = new DatosValidados();
        $modelValidado->scenario = DatosValidados::SCENARIO_CREATE;
        $modelos = [];

        if (Yii::$app->user->can(Usuarios::ROLE_VALIDADOR_PROVEEDORES)) {
            if (isset($model->provider_id) && !empty($model->provider_id)) {
                $modelos[$model->formName()] = $model->attributes;
            }
            $modelos = base64_encode(json_encode($modelos));

        }

        $rechazo = Status::find()->getStatus($model->representante_legal_id, 'representante_legal', 'op', 'TERMINADO PRO');


        return $this->renderAjax('view-validarep', [
            'model_representante_legal' => $model,
            'model_registro_acta' => $this->findModelActa($model->provider_id),
            'modelos' => $modelos,
            'modelStatus' => $model_status,
            'modelValidado' => $modelValidado,
            'rechazo' => $rechazo,
            'opciones' => $opcionesBase64,
            'namePro' => $this->getNameProvider($model->provider_id)
        ]);
    }

    public function actionViewValidarmodificaciones($id=null, $user_id=null){

        $queryParams = Yii::$app->request->queryParams;
        $id = isset($queryParams['id']) ? intval( base64_decode($queryParams['id']) ) : 0;
        $user_id = isset($queryParams['user_id']) ? intval( $queryParams['user_id'] ) : 0;
        $opcionesArray = isset($queryParams['opciones']) ? $queryParams['opciones'] : null;
        $opcionesSerial = isset($opcionesArray) ? serialize($opcionesArray) : null;
        $opcionesBase64 = isset($opcionesSerial) ? base64_encode($opcionesSerial) : null;

        $model = $this->findModelModificaciones($id);
        $model_status = new Status();
        $model_status->scenario = Status::SCENARIO_CREATE;
        $modelValidado = new DatosValidados();
        $modelValidado->scenario = DatosValidados::SCENARIO_CREATE;
        $modelos = [];

        if(Yii::$app->user->can(Usuarios::ROLE_VALIDADOR_PROVEEDORES)){
            if(isset($model->provider_id) && !empty($model->provider_id)){
                $modelos[$model->formName()] = $model->attributes;
            }
            $modelos = base64_encode(json_encode($modelos));

        }

        $rechazo = Status::find()->getStatus($model->modificacion_acta_id,'modificacion_acta','op','TERMINADO PRO');

        return $this->renderAjax('view-validarmodificaciones', [
            'model' => $model,
            'modelos' => $modelos,
            'modelStatus' => $model_status,
            'modelValidado' => $modelValidado,
            'rechazo' => $rechazo,
            'opciones' => $opcionesBase64,
            'namePro' => $this->getNameProvider($model->provider_id)
        ]);

    }

//    public function actionCreate()
//    {
//        $model = new Rfc();
//        $model_curp = new Curp();
//        $model_idoficial =new IdOficial();
//
//        $t = $model_curp->getDb()->beginTransaction();
//        $model->urlRfc = UploadedFile::getInstance($model,'url_rfc');
//        $model_curp->urlCurp = UploadedFile::getInstance($model_curp,'url_curp');
//        $model_idoficial->urlIdOficial = UploadedFile::getInstance($model_idoficial,'url_idoficial');
//
//        $id = Provider::find()->select('provider_id')->where(['user_id' => Yii::$app->user->id])->one()->provider_id;
//        $rfc = Yii::$app->user->can(Usuarios::ROLE_PROVIDER)?Yii::$app->user->identity->rfc:'';
//
//        if ($model->load(Yii::$app->request->post()) || $model_curp->load(Yii::$app->request->post()) || $model_idoficial->load(Yii::$app->request->post()) ) {
//
//            if(!empty($model->urlRfc)){
//                $model->provider_id = $id;
//                $model->url_rfc = !empty($model->urlRfc)?$model->path.'/'.md5($model->urlRfc->baseName).time().'.'.$model->urlRfc->extension:'';
//                if($model->save(false)) {
//
//                    if(!file_exists($model->path)){
//                        mkdir($model->path,0755,true);
//                    }
//                    !empty($model->urlRfc)?$model->urlRfc->saveAs($model->url_rfc):'';
//                }
//            }
//
//            if(!empty($model_curp->urlCurp)){
//                $model_curp->url_curp = !empty($model_curp->urlCurp)?$model_curp->path_curp.'/'.md5($model_curp->urlCurp->baseName).time().'.'.$model_curp->urlCurp->extension:'';
//                $model_curp->provider_id = $id;
//                if($model_curp->save(false)){
//
//                    if(!file_exists($model_curp->path_curp)){
//                        mkdir($model_curp->path_curp,0755,true);
//                    }
//                    !empty($model_curp->urlCurp)?$model_curp->urlCurp->saveAs($model_curp->url_curp):'';
//                }
//            }
//
//            if(!empty($model_idoficial->urlIdOficial)){
//                $model_idoficial->url_idoficial = !empty($model_idoficial->urlIdOficial)?$model_idoficial->path_idoficial.'/'.md5($model_idoficial->urlIdOficial->baseName).time().'.'.$model_idoficial->urlIdOficial->extension:'';
//                $model_idoficial->provider_id = $id;
//                if($model_idoficial->save(false)){
//
//                    if(!file_exists($model_idoficial->path_idoficial)){
//                        mkdir($model_idoficial->path_idoficial,0755,true);
//                    }
//                    !empty($model_idoficial->urlIdOficial)?$model_idoficial->urlIdOficial->saveAs($model_idoficial->url_idoficial):'';
//                }
//
//            }
//            $t->commit();
//            return $this->redirect(['view', 'id' => $id]);
//        }
//        $t->rollBack();
//
//        $curp = ArrayHelper::getColumn(Curp::find()->select('url_curp')->where(['provider_id' => $id])->all(),'url_curp');
//        $rfc_data = ArrayHelper::getColumn(Rfc::find()->select('url_rfc')->where(['provider_id' => $id])->all(),'url_rfc');
//        $id_of = ArrayHelper::getColumn(IdOficial::find()->select('url_idoficial')->where(['provider_id' => $id])->all(),'url_idoficial');
//
//        $rfc_true_false = !empty($rfc_data[0])?true:false;
//        $curp_true_false = !empty($curp[0])?true:false;
//        $idoficial_true_false = !empty($id_of[0])?true:false;
//        return $this->render('create', [
//            'model' => $model,
//            'model_curp' => $model_curp,
//            'model_idoficial' => $model_idoficial,
//            'rfc'=>$rfc,
//            'rfc_true_false' => $rfc_true_false,
//            'curp_true_false' => $curp_true_false,
//            'idoficial_true_false' => $idoficial_true_false,
//        ]);
//    }
//
//
//    public function actionDelete($id)
//    {
//        $this->findModel($id)->delete();
//
//        return $this->redirect(['index']);
//    }
//
//    protected function findModel($id)
//    {
//        $model = Rfc::find()->where(['provider_id' => $id])->one();
//        if($model == null || empty($model)){
//            $model = new Rfc();
//        }
//        return $model;
//    }
//
//    public function findModelHistorico($id)
//    {
//        $model = Historico::find()->where(['provider_id' => $id,'modelo' => 'Rfc'])->one();
//        if($model == null || empty($model)){
//            $model = new Historico();
//        }
//        return $model;
//    }
//
//    public function findModelIdoficial($id)
//    {
//        $model = IdOficial::find()->where(['provider_id' => $id])->one();
//        if($model == null || empty($model)){
//            $model = new IdOficial();
//        }
//        return $model;
//    }
//
//
//
//
//
//
//
//    public function findModelRegistroActas($id)
//    {
//        $model = RegistroActas::find()->where(['acta_id' => $id])->one();
//        if($model == null || empty($model)){
//            $model = new RegistroActas();
//        }
//        return $model;
//    }
//
//
//    public function findModelPorcentaje($id)
//    {
//        $model = Porcentaje::find()->where(['and', ['register_id' => $id], ['modelo' => 'rfc']])->one();
//        if($model == null || empty($model)){
//            $model = new Porcentaje();
//        }
//        return $model;
//    }





    public function findModelAct($id)
    {
        $model = Giro::find()->where(['and',['provider_id' => $id, 'active' => true]])->all();
        if($model == null || empty($model)){
            $model = [new Giro()];
        }
        return $model;
    }

    public function findModelCurp($id)
    {
        $model = Curp::find()->where(['provider_id' => $id])->one();
        if($model == null || empty($model)){
            $model = new Curp();
        }
        return $model;
    }

    public function findModelIdoficial($id)
    {
        $model = IdOficial::find()->where(['provider_id' => $id])->one();
        if($model == null || empty($model)){
            $model = new IdOficial();
        }
        return $model;
    }

    public function findModelActaConstitutiva($id)
    {

        $model = ActaConstitutiva::find()->where(['provider_id' => $id])->one();
        if($model == null || empty($model)){
            $model = new ActaConstitutiva();
        }
        return $model;
    }

    protected function findModelModificaciones($id)
    {
        if (($model = ModificacionActa::findOne($id)) !== null) {
            return $model;
        } else {
            throw new NotFoundHttpException('The requested page does not exist.');
        }
    }

    protected function findModelAccionista($id)
    {
        if (($model = RelacionAccionistas::findOne($id)) !== null) {
            return $model;
        } else {
            throw new NotFoundHttpException('The requested page does not exist.');
        }
    }

//    public function actionList_rama($id){
//        $rama =  ArrayHelper::map(CatRamas::find()->select(['rama_id','nombre_rama'])
//            ->where(['sector_id' => $id])
//            ->orderBy(['nombre_rama'=>SORT_ASC])
//            ->asArray()->all(),'rama_id','nombre_rama');
//
//        $con ='';
//        $con .='<option value=""></option>';
//        if (count($rama) > 0) {
//            foreach ($rama as $key => $value) $con .="<option value='" . $key . "'>" . $value . "</option>";
//        } else {
//            $con .="<option>No hay ramas</option>";
//        }
//
//        echo $con;
//    }
//
//
//    public function actionList_actividad($id){
//        $act1 =  ArrayHelper::map(CatActividades::find()->select(['actividad_id','nombre_actividad'])
//            ->where(['rama_id' => $id])
//            ->orderBy(['nombre_actividad'=>SORT_ASC])
//            ->asArray()->all(),'actividad_id','nombre_actividad');
//        $con ='';
//        $con .='<option value=""></option>';
//        if (count($act1) > 0) {
//            foreach ($act1 as $key => $value) $con .="<option value='" . $key . "'>" . $value . "</option>";
//        } else {
//            $con .="<option>No hay actividades</option>";
//        }
//
//        echo $con;
//    }


    public  function  verifyUpdate($provider){
        if($provider){
            $status = Rfc::find()->select('status_op')->where(['provider_id' => $provider])->one()->status_op;
            if($status == 'PENDIENTE'|| $status == 'RECHAZADO' || $status == 'VALIDADO'){
                return true;
            }
        }
        return false;
    }


    public  function  verifyTerminar($provider){

        if($provider){
            $tipo_pro = Provider::find()->select('tipo_persona')->where(['provider_id' => $provider])->one()['tipo_persona'];


            $model = $this->findModel($provider);
            if($tipo_pro=='Persona física'){
                $model_curp = $this->findModelCurp($provider);
                $model_idoficial = $this->findModelIdoficial($provider);

                $esta_lleno = \app\helpers\GeneralController::estaLleno($model) && \app\helpers\GeneralController::estaLleno($model_curp) &&
                    \app\helpers\GeneralController::estaLleno($model_idoficial) ;
            }else{
                $acta = $this->findModelActa($provider);
                $imss = $this->findModelRegistroImss($provider);
                $escritura = $this->findModelEscrituraPublica($provider);
                $registro = $this->findModelRegistroPublico($provider);
                $esta_lleno = \app\helpers\GeneralController::estaLleno($acta) && \app\helpers\GeneralController::estaLlenoActa($acta)
                    && \app\helpers\GeneralController::estaLleno($model) && \app\helpers\GeneralController::estaLleno($imss)
                    && \app\helpers\GeneralController::estaLleno($escritura, 'ESCRITURA') && \app\helpers\GeneralController::estaLleno($registro);
            }

            $status = $model->status_op;
            if(($status == Status::STATUS_ENEDICION || $status == Status::STATUS_RECHAZADO) && $esta_lleno && $model->url_rfc!=''){
                return true;
            }
        }

        return false;
    }


    public function findModelRepresentanteLegal($id)
    {
        $model = RepresentanteLegal::find()->where(['and', ['provider_id' => Yii::$app->user->identity->providerid], ['representante_legal_id' => $id]])->one();
        if($model == null || empty($model)){
            $model = new RepresentanteLegal();
        }
        return $model;
    }

    protected function findModelRepresentanteLegalVal($id)
    {
        if (($model = RepresentanteLegal::findOne($id)) !== null) {
            return $model;
        } else {
            throw new NotFoundHttpException('The requested page does not exist.');
        }
    }
}
