<?php

namespace app\controllers\op;

use app\helpers\GeneralController;
use app\models\CapacidadProduccion;
use app\models\CatAsentamientos;
use app\models\CatColonia;
use app\models\CatEntidad;
use app\models\CatEntidades;
use app\models\CatLocalidades;
use app\models\CatMunicipio;
use app\models\CatMunicipios;
use app\models\CatVialidad;
use app\models\DatosValidados;
use app\models\DireccionNl;
use app\models\FotografiaNegocio;
use app\models\Historico;
use app\models\Porcentaje;
use app\models\Provider;
use app\models\RelacionAccionistas;
use app\models\Status;
use app\models\Usuarios;
use kartik\form\ActiveForm;
use Yii;
use yii\db\Query;
use app\models\Ubicacion;
use app\models\UbicacionSearch;
use yii\helpers\ArrayHelper;
use yii\web\Controller;
use yii\web\NotFoundHttpException;
use yii\filters\VerbFilter;
use yii\web\Response;

if(Yii::$app->user->can(Usuarios::ROLE_PROVIDER)){
    Yii::$app->params['provider_id'] = Yii::$app->user->identity->getProvider();
}
/**
 * UbicacionController implements the CRUD actions for Ubicacion model.
 */
class UbicacionController extends GeneralController
{
    /**
     * @inheritdoc
     */
    public function behaviors()
    {
        return [
            'verbs' => [
                'class' => VerbFilter::className(),
                'actions' => [
                    'delete' => ['POST','GET'],
                ],
            ],
        ];
    }

    /**
     * Lists all Ubicacion models.
     * @return mixed
     */
    public function actionIndex($id=null)
    {
        $id = intval($id);
        if (Yii::$app->user->can(Usuarios::ROLE_PROVIDER)){
            $id=Yii::$app->params['provider_id'];
        }

        if($id == null || strlen($id)>15 || !Provider::verifyProviderExistence($id) || !Provider::verifyEtapa3($id) || !Yii::$app->user->can(Usuarios::ROLE_PROVIDER)){
            $this->msgError();
        }
        $searchModel = new UbicacionSearch();
        $dataProvider = $searchModel->search(Yii::$app->request->queryParams);
        $validados = $searchModel->search(Yii::$app->request->queryParams,'validadosFilter');
        $rechazados = $searchModel->search(Yii::$app->request->queryParams,'rechazadosFilter');
        $pendientes = $searchModel->search(Yii::$app->request->queryParams,'pendientesFilter');


        $model_direccion = $this->findModelDireccionNl($id);
        $colonia_nl = new CatAsentamientos();
        if(isset($model_direccion->colonia_fiscal) && !empty($model_direccion->colonia_fiscal)){
            $colonia_nl = CatAsentamientos::find()->select(['asentamiento_id','nombre'])
                ->where(['asentamiento_id' => $model_direccion->colonia_fiscal])->one();
        }
        $city_nl = new CatMunicipios();
        $entidad = Ubicacion::find()->select('state_fiscal')->where(['and',['provider_id' => $id],['type_address_prov' => 'DOMICILIO FISCAL']])->one()['state_fiscal'];
        //CatMunicipios::find()->select('entidad_id')->where(['municipio_id' => $city])->one()['entidad_id'];

        if(!Yii::$app->request->get()){
            $model_porcentaje = $this->findModelPorcentaje($id);
            $verify = Porcentaje::find()->select(['register_id'])->where(['and',['register_id' =>$id],['modelo'=>'ubicacion']])->one();
            if($verify){
                if (($dataProvider->getTotalCount() + $pendientes->getTotalCount() + $rechazados->getCount() + $validados->getCount()) != 0) {
                    $model_porcentaje->porcentaje = (($pendientes->getTotalCount() + $validados->getCount()) / ($dataProvider->getTotalCount() + $pendientes->getTotalCount() + $rechazados->getCount() + $validados->getCount()) * 100);
                    \Yii::$app->db->createCommand("update provider.porcentaje set porcentaje = $model_porcentaje->porcentaje where modelo='ubicacion' and register_id =:id")
                        ->bindValue(':id', $id)
                        ->execute();
                }
            }else{
                $porcen = new Porcentaje();
                $porcen->modelo = 'ubicacion';
                $porcen->register_id = $id;
                if($dataProvider->getTotalCount() + $pendientes->getTotalCount() + $validados->getCount() > 0){
                    $porcen->porcentaje = round(($pendientes->getTotalCount() + $validados->getCount())/($dataProvider->getTotalCount() + $pendientes->getTotalCount() + $rechazados->getCount() + $validados->getCount())*100);
                }else{
                    $porcen->porcentaje = 0;
                }
                $porcen->save();
            }
        }

        if($entidad!=19 && $model_direccion->city_fiscal){
            $city_nl = CatMunicipios::find()->select(['municipio_id', 'nombre'])->where(['municipio_id' => $model_direccion->city_fiscal])->one();
        }

        $rechazo = Status::find()->getStatusRechazarCotejo($id);
        $rechazoDirNl = [];
        if(isset($model_direccion->ubicacion_id) && !empty($model_direccion->ubicacion_id)){
            $rechazoDirNl = Status::find()->getStatus($model_direccion->ubicacion_id, 'direccion_nl', 'bys');
        }

        return $this->render('index', [
            'searchModel' => $searchModel,
            'dataProvider' => $dataProvider,
            'validados' => $validados,
            'rechazados' => $rechazados,
            'pendientes' => $pendientes,
            'entidad' => $entidad,
            'model_direccion' => $model_direccion,
            'colonia_nl' => $colonia_nl,
            'city_nl' => $city_nl,
            'rechazo' => $rechazo,
            'rechazoDirNl' => $rechazoDirNl
        ]);
    }

    /**
     * Displays a single Ubicacion model.
     * @param integer $id
     * @return mixed
     */
    public function actionView($id=null,$user_id=null)
    {

        $user_id = intval($user_id);
        $id = intval($id);
        if($user_id == null 
        || strlen($user_id)>15 
        || !Ubicacion::verifyUbicacion($id) 
        || !Ubicacion::verifyProviderExistenceUbi($id) 
        || (!Provider::verifyProvider($user_id) && Yii::$app->user->can(Usuarios::ROLE_PROVIDER))
        || !Provider::verifyEtapa3($user_id)){
            $this->msgError();
        }
        $models = '';
        $id_provider = Ubicacion::find()->select('provider_id')->where(['ubicacion_id'=>$id])->one()->provider_id;
        $tipo_provider = Provider::find()->select('tipo_provider')->where(['provider_id' => $id_provider])->one()->tipo_provider;
        $model_status = new Status();
        $model_status->scenario = Status::SCENARIO_CREATE;
        $rechazo = Status::find()->getStatus($id,'Ubicacion', $tipo_provider);
        $ubicacion = $this->findModel($id);
        if(Yii::$app->user->can(Usuarios::ROLE_VALIDADOR_CONTRATISTAS)){
            $modelos = [];
            if(isset($ubicacion->provider_id) && !empty($ubicacion->provider_id)){
                $modelos[$ubicacion->formName()] = $ubicacion->attributes;
            }
            $models = base64_encode(json_encode($modelos));

        }

        return $this->renderAjax('view', [
            'model' => $ubicacion,
            'model_status' => $model_status,
            'rechazo' => $rechazo,
            'modelos' => $models
        ]);
    }

     /**
     * Displays a single Ubicacion model.
     * @param integer $id
     * @return mixed
     */
     public function actionViewValidar($id=null,$user_id=null)
     {
 
        $queryParams = Yii::$app->request->queryParams;
        $id = isset($queryParams['id']) ? intval( $queryParams['id'] ) : null;
        $user_id = isset($queryParams['user_id']) ? intval( $queryParams['user_id'] ) : null;
        $opcionesArray = isset($queryParams['opciones']) ? $queryParams['opciones'] : null;
        $opcionesSerial = isset($opcionesArray) ? serialize($opcionesArray) : null;
        $opcionesBase64 = isset($opcionesSerial) ? base64_encode($opcionesSerial) : null;

         if($user_id == null 
         || strlen($user_id)>15 
         || !Ubicacion::verifyUbicacion($id) 
         || !Ubicacion::verifyProviderExistenceUbi($id) 
         || (!Provider::verifyProvider($user_id) && Yii::$app->user->can(Usuarios::ROLE_PROVIDER))
         || !Provider::verifyEtapa3($user_id)){
             $this->msgError();
         }
         $modelos = [];
         $id_provider = Ubicacion::find()->select('provider_id')->where(['ubicacion_id'=>$id])->one()->provider_id;
         $tipo_provider = Provider::find()->select('tipo_provider')->where(['provider_id' => $id_provider])->one()->tipo_provider;
         $model_status = new Status();
         $model_status->scenario = Status::SCENARIO_CREATE;
         $model_validado = new DatosValidados();
         $model_validado->scenario = DatosValidados::SCENARIO_CREATE;
         $ubicacion = $this->findModel($id);
         if(Yii::$app->user->can(Usuarios::ROLE_VALIDADOR_CONTRATISTAS)){
             if(isset($ubicacion->provider_id) && !empty($ubicacion->provider_id)){
                 $modelos[$ubicacion->formName()] = $ubicacion->attributes;
             }
             $modelos = base64_encode(json_encode($modelos));
 
         }
         $rechazo = Status::find()->getStatus($id,'ubicacion','op','TERMINADO PRO');
         return $this->renderAjax('view-validar', [
             'model' => $ubicacion,
             'model_status' => $model_status,
             'rechazo' => $rechazo,
             'modelos' => $modelos,
             'model_validado' => $model_validado,
             'id_provider' => $id_provider,
             'rechazo' => $rechazo,
             'namePro' => $this->getNameProvider($id_provider),
             'opciones' => $opcionesBase64
         ]);
     }



    public function actionViewVisit($id=null,$user_id=null)
    {

        $user_id = intval($user_id);
        $id = intval($id);
        if($user_id == null
            || strlen($user_id)>15
            || !Ubicacion::verifyUbicacion($id)
            || !Ubicacion::verifyProviderExistenceUbi($id)
            || (!Provider::verifyProvider($user_id) && Yii::$app->user->can(Usuarios::ROLE_PROVIDER))
            || !Provider::verifyEtapa3($user_id)){
            $this->msgError();
        }
        $models = '';
        $id_provider = Ubicacion::find()->select('provider_id')->where(['ubicacion_id'=>$id])->one()->provider_id;
        $tipo_provider = Provider::find()->select('tipo_provider')->where(['provider_id' => $id_provider])->one()->tipo_provider;
        $model_status = new Status();
        $model_status->scenario = Status::SCENARIO_CREATE;
        $rechazo = Status::find()->getStatus($id,'Ubicacion', $tipo_provider);
        $ubicacion = $this->findModel($id);
        if(Yii::$app->user->can(Usuarios::ROLE_VALIDADOR_PROVEEDORES)){
            $modelos = [];
            if(isset($ubicacion->provider_id) && !empty($ubicacion->provider_id)){
                $modelos[$ubicacion->formName()] = $ubicacion->attributes;
            }
            $models = base64_encode(json_encode($modelos));

        }

        return $this->renderAjax('view-visit', [
            'model' => $ubicacion,
            'model_status' => $model_status,
            'rechazo' => $rechazo,
            'modelos' => $models
        ]);
    }


    public function actionViewValidarNl($id=null,$user_id=null)
    {

        $queryParams = Yii::$app->request->queryParams;
        $id = isset($queryParams['id']) ? intval( $queryParams['id'] ) : null;
        $user_id = isset($queryParams['user_id']) ? intval( $queryParams['user_id'] ) : null;
        $opcionesArray = isset($queryParams['opciones']) ? $queryParams['opciones'] : null;
        $opcionesSerial = isset($opcionesArray) ? serialize($opcionesArray) : null;
        $opcionesBase64 = isset($opcionesSerial) ? base64_encode($opcionesSerial) : null;

        if($user_id == null
            || strlen($user_id)>15
            || !Ubicacion::verifyUbicacionNl($id)
            || !Ubicacion::verifyProviderExistenceUbiNl($id)
            || (!Provider::verifyProvider($user_id) && Yii::$app->user->can(Usuarios::ROLE_PROVIDER))
            || !Provider::verifyEtapa3($user_id)){
            $this->msgError();
        }
        $modelos = [];
        $id_provider = Ubicacion::find()->select('provider_id')->where(['ubicacion_id'=>$id])->one()['provider_id'];
        $model_status = new Status();
        $model_status->scenario = Status::SCENARIO_CREATE;
        $model_validado = new DatosValidados();
        $model_validado->scenario = DatosValidados::SCENARIO_CREATE;
        $direccionNl = $this->findModelDireccionNlId($id);
        if(Yii::$app->user->can(Usuarios::ROLE_VALIDADOR_CONTRATISTAS)){
            if(isset($direccionNl->provider_id) && !empty($direccionNl->provider_id)){
                $modelos[$direccionNl->formName()] = $direccionNl->attributes;
            }
            $modelos = base64_encode(json_encode($modelos));

        }
        $rechazo = Status::find()->getStatus($id,'direccion_nl','op','TERMINADO PRO');
        return $this->renderAjax('view-validar-nl', [
            'model' => $direccionNl,
            'model_status' => $model_status,
            'rechazo' => $rechazo,
            'modelos' => $modelos,
            'model_validado' => $model_validado,
            'id_provider' => $id_provider,
            'rechazo' => $rechazo,
            'namePro' => $this->getNameProvider($id_provider),
            'opciones' => $opcionesBase64
        ]);
    }

    public function actionTerminar($id = null)
    {

        $IdLastVal = $this->saveLastSendValidation(Yii::$app->user->identity->getProvider(),'validUbicacion','ubicacion','op');

        if ($id != null) {
            $status = $this->terminarUno('Ubicacion', intval(base64_decode($id)), 'ubicacion_id', 'ubicacion', 'status_op','',$IdLastVal);
        } else {
            $status = $this->terminarTodos('Ubicacion', 'ubicacion_id', 'ubicacion', 'status_op','',$IdLastVal);
        }
        if ($status) {
            $correos_validadores = self::getEmailValidador(2, 'op');

            self::sendEmail('/provider/correos/nuevo', null, $correos_validadores, 'Nuevo proveedor', ['modulo' => 'Ubicación', 'tipo_provider' => 'op']);

            self::AllSendNotification(null, 'op', null, 'DATOS TECNICOS', 'Ubicación');

            $provider = Provider::findOne(Yii::$app->user->identity->providerid);
            GeneralController::sendModuloRevisionEmail($provider->email, 'Domicilio');
        }
        return $this->redirect('index');
    }

    public function actionConfirm(){

        $modelos = isset(Yii::$app->request->post()['modelos'])?Yii::$app->request->post()['modelos']:[];

        $status_global = 'status_op';

        $id = Yii::$app->request->post()['ubicacion_id'];
        $model = $this->findModel($id);
        $t = Yii::$app->db->beginTransaction();
        $rfc = Yii::$app->db->createCommand();
        $rfc->update('provider.ubicacion',[$status_global => Status::STATUS_VALIDADO],'ubicacion_id ='.$id);
        if($rfc->execute()){
            $val = true;
            if(($requi_status = Status::find()->where(['and',['register_id' => $model->ubicacion_id],[$status_global => Status::STATUS_PENDIENTE],['modelo'=>'Ubicacion']])->one())!==null){
                $requi_status->status_op = Status::STATUS_TERMINADO_PRO;
                $val = $requi_status->save();
            }

            if($val){
                $rfc_pro = Provider::find()->select(['provider_id','rfc'])->where("provider_id =(select provider_id from provider.ubicacion where ubicacion_id=$id)")->one();
                $valid = true;
                if($rfc_pro['provider_id']){
                    if(!empty($modelos)){
                        $models_arr = json_decode(base64_decode($modelos),true);
                        foreach ($models_arr as $k => $value){
                            $model_historico = new Historico();
                            if(DatosValidados::Verify($k)){
                                $value[$status_global] = Status::STATUS_VALIDADO;
                            }
                            $model_historico->provider_id = $rfc_pro['provider_id'];
                            $model_historico->rfc = $rfc_pro['rfc'];
                            $model_historico->validador_id = Yii::$app->user->getId();
                            $model_historico->modelo = $k;
                            $model_historico->data = json_encode($value);
                            $valid = $valid && $model_historico->save();
                        }
                    }
                }else{$valid = false;}

                if($valid){
                    $t->commit();
                    Yii::$app->session->setFlash('success', 'Los datos se validaron correctamente');
                }else{
                    $t->rollBack();
                    Yii::$app->session->setFlash('error', 'Error al validar la información');

                }
            }
        }else{
            $t->rollBack();
            Yii::$app->session->setFlash('error', 'Error al validar la información');
        }

        return Yii::$app->getResponse()->redirect(['datosTecnicos/validador/index-validador']);

    }
    /**
     * Creates a new Ubicacion model.
     * If creation is successful, the browser will be redirected to the 'view' page.
     * @return mixed
     */
    public function actionCreate()
    {
        if (Yii::$app->request->isAjax || Yii::$app->request->post()) {
            $model = new Ubicacion();
            $colonia = [];
            $city = [];
            $state = [];
            $id = Provider::find()->select('provider_id')->where(['user_id' => Yii::$app->user->id])->one()->provider_id;
            $model->provider_id = $id;

            if ($id == null || !Provider::verifyProvider($id) || !Provider::verifyEtapa3($id)) {

                $this->msgError();
            }

            $model_catcolonia = new CatAsentamientos();
            if (Yii::$app->request->isAjax && $model->load(Yii::$app->request->post())) {
                Yii::$app->response->format = Response::FORMAT_JSON;
                return ActiveForm::validate($model);
            } else if ($model->load(Yii::$app->request->post())) {
                $model->scenario = 'op';
                $location = json_decode($model->geo_ubicacion);
                if (isset($location[0]) && !empty($location[0]) && isset($location[1]) && !empty($location[1])) {
                    $val_geo = Ubicacion::findBySql("select ST_MakePoint(" . $location[0] . "," . $location[1] . ")")->asArray()->all();
                    $model->geo_ubicacion = $val_geo[0]['st_makepoint'];
                }
                if (!$model->save()) {
                    return $this->goHome();
                }
                self::eliminarCita();
                return $this->redirect(['index']);
            }

            $state = ArrayHelper::map(CatEntidades::find()->select(['entidad_id', 'nombre'])->asArray()
                ->all(), 'entidad_id', 'nombre');

            $vialidad = ArrayHelper::map(CatVialidad::find()->select(['vialidad_id', 'descripcion'])->asArray()
                ->all(), 'vialidad_id', 'descripcion');

            return $this->renderAjax('create', [
                'model' => $model,
                'state' => $state,
                'model_catcolonia' => $model_catcolonia,
                'vialidad' => $vialidad
            ]);
        }
        return $this->goHome();
    }

    /**
     * Updates an existing Ubicacion model.
     * If update is successful, the browser will be redirected to the 'view' page.
     * @param integer $id
     * @return mixed
     */

    public function actionUpdate($user_id,$id)
    {
        if (Yii::$app->request->isAjax || Yii::$app->request->post()) {
            if ($user_id == null || !Provider::verifyProvider($user_id) || !Provider::verifyEtapa3($user_id) || !UbicacionController::verifyUpdate($user_id, $id)) {
                echo $this->render('/site/error', ['name' => 'Lo sentimos...', 'message' => 'Acceso denegado']);

                return false;
            }
            $model = $this->findModel($id);
            $colonia = [];
            $localidad = [];
            $city = [];
            $state = [];
            $model->provider_id = $user_id;
            $id_register = $model->ubicacion_id;
            $rechazo = Status::find()->getStatus($id_register, 'ubicacion', 'op');
            if (!empty($model->state_fiscal)) {
                $city = ArrayHelper::map(CatMunicipios::find()->select(['municipio_id', 'nombre'])
                    ->where(['entidad_id' => $model->state_fiscal])->asArray()->orderBy(['nombre' => 'ASC'])->all(), 'municipio_id', 'nombre');
            }
            if (!empty($model->state_fiscal) && !empty($model->city_fiscal)) {
                $colonia = ArrayHelper::map(CatAsentamientos::find()->select(['asentamiento_id', 'nombre'])
                    ->where(['and', ['entidad_id' => $model->state_fiscal], ['municipio_id' => $model->city_fiscal]])->asArray()->orderBy(['nombre' => 'ASC'])->all(), 'asentamiento_id', 'nombre');
                $localidad = ArrayHelper::map(CatLocalidades::find()->select(['localidad_id', 'nombre'])
                    ->where(['and', ['entidad_id' => $model->state_fiscal], ['municipio_id' => $model->city_fiscal]])->asArray()->orderBy(['nombre' => 'ASC'])->all(), 'localidad_id', 'nombre');

            }
            if (Yii::$app->request->isAjax && $model->load(YII::$app->request->post())) {
                Yii::$app->response->format = Response::FORMAT_JSON;
                return ActiveForm::validate($model);
            } else if ($model->load(Yii::$app->request->post())) {
                $model->scenario = 'op';
                $location = json_decode($model->geo_ubicacion);
                if (isset($location[0]) && !empty($location[0]) && isset($location[1]) && !empty($location[1])) {
                    $val_geo = Ubicacion::findBySql("select ST_MakePoint(" . $location[0] . "," . $location[1] . ")")->asArray()->all();
                    $model->geo_ubicacion = $val_geo[0]['st_makepoint'];
                }
                $model->status_op = Status::STATUS_ENEDICION;

                if(!$model->save()){
                    return $this->goHome();
                }
                self::eliminarCita();
                return $this->redirect(['index']);
            }

            $state = ArrayHelper::map(CatEntidades::find()->select(['entidad_id', 'nombre'])->asArray()->orderBy(['nombre' => 'ASC'])
                ->all(), 'entidad_id', 'nombre');

            $vialidad = ArrayHelper::map(CatVialidad::find()->select(['vialidad_id', 'descripcion'])->asArray()->orderBy(['descripcion' => 'ASC'])
                ->all(), 'vialidad_id', 'descripcion');

            return $this->renderAjax('update', [
                'model' => $model,
                'colonia' => $colonia,
                'city' => $city,
                'state' => $state,
                'rechazo' => $rechazo,
                'localidad' => $localidad,
                'vialidad' => $vialidad
            ]);
        }
        return $this->goHome();
    }

    /**
     * Deletes an existing Ubicacion model.
     * If deletion is successful, the browser will be redirected to the 'index' page.
     * @param integer $id
     * @return mixed
     */
    public function actionDelete($id)
    {
        Ubicacion::updateAll(['activo' => false], ['ubicacion_id' => intval($id)]);
        FotografiaNegocio::updateAll(['activo' => false], ['ubicacion_id' => intval($id)]);
        return $this->redirect(['index']);
    }

    /**
     * Finds the Ubicacion model based on its primary key value.
     * If the model is not found, a 404 HTTP exception will be thrown.
     * @param integer $id
     * @return Ubicacion the loaded model
     * @throws NotFoundHttpException if the model cannot be found
     */
    protected function findModel($id)
    {
        if (($model = Ubicacion::findOne($id)) !== null) {
            return $model;
        } else {
            throw new NotFoundHttpException('The requested page does not exist.');
        }
    }

    public function actionList_colpostal($id){
        $colonias =  ArrayHelper::map(CatAsentamientos::find()->select(['cv_asentamiento','nombre'])
            ->where(['cp' => $id])
            ->orderBy(['nombre'=>SORT_ASC])
            ->asArray()->orderBy(['nombre'=>'ASC'])->all(),'cv_asentamiento','nombre');
        $con = '';
        $con .='<option value=""></option>';
        if (count($colonias) > 0) {
            foreach ($colonias as $key => $value) $con .="<option value='" . $key . "'>" . $value . "</option>";
        } else {
            $con .="<option></option>";
        }
        echo $con;
    }

    public function actionList_city($id){
        $city = ArrayHelper::map(CatMunicipios::find()->select(['municipio_id', 'nombre'])
            ->where(['entidad_id' =>$id])->asArray()->orderBy(['nombre'=>'ASC'])->all(),'municipio_id', 'nombre');


        $con = '';

        $con .='<option value="">Selecciona...</option>';

        if($city){
            foreach ($city as $key => $val)
                $con .="<option value=".$key.">".$val."</option>";
        }else{
            $con .='<option value="">No hay resultados</option>';
        }
        echo $con;
    }

    public function actionList_localidades($id,$state){
        $city = ArrayHelper::map(CatLocalidades::find()->select(['localidad_id', 'nombre'])
            ->where(['and',['entidad_id' =>$state],['municipio_id' => $id]])->asArray()->orderBy(['nombre'=>'ASC'])->all(),'localidad_id', 'nombre');


        $con = '';

        $con .='<option value="">Selecciona...</option>';

        if($city){
            foreach ($city as $key => $val)
                $con .="<option value=".$key.">".$val."</option>";
        }else{
            $con .='<option value="">No hay resultados</option>';
        }
        echo $con;
    }

    public function actionList_asentamiento($id,$state){
        $asentamiento = ArrayHelper::map(CatAsentamientos::find()->select(['asentamiento_id', 'nombre'])
            ->where(['and',['entidad_id' =>$state],['municipio_id' => $id]])->asArray()->orderBy(['nombre'=>'ASC'])->all(),'asentamiento_id', 'nombre');

        $con = '';
        $con .='<option value="">Selecciona...</option>';
        if($asentamiento){
            foreach ($asentamiento as $key => $val)
                $con .="<option value=".$key.">".$val."</option>";
        }else{
            $con .='<option value="">No hay resultados</option>';
        }
        echo $con;
    }

    public function actionList_state($id){
        $state = CatColonia::find()->select(['cv_entidad'])->where(['cv_cp' =>$id])->one();

        $list_state =  CatEntidad::find()->select(['cv_entidad','nombre'])
            ->where(['cv_entidad' => $state])
            ->orderBy(['nombre'=>SORT_ASC])
            ->asArray()->all();
        return \GuzzleHttp\json_encode($list_state);

    }
    public function findModelPorcentaje($id)
    {
        $model = Porcentaje::find()->where(['register_id' => $id])->one();
        if($model == null || empty($model)){
            $model = new Porcentaje();
        }
        return $model;
    }
    public function findModelDireccionNl($id)
    {
        $model = Ubicacion::find()->where(['and',['provider_id' => $id],['type_address_prov' => 'NL']])->orderBy(['ubicacion_id' => SORT_DESC])->one();
        if($model == null || empty($model)){
            $model = new Ubicacion();
        }
        return $model;
    }

    public function findModelDireccionNlId($id)
    {
        $model = Ubicacion::find()->where(['ubicacion_id' => $id])->one();
        if($model == null || empty($model)){
            $model = new Ubicacion();
        }
        return $model;
    }
    public  function  verifyUpdate($provider, $id){
        if($provider){
            $status = Ubicacion::find()->select('status_op')->where(['and',['provider_id' => $provider],['ubicacion_id'=>$id]])->one()['status_op'];
            $rechazo = Status::find()->getStatusRechazarCotejo($provider);

            if($status == Status::STATUS_ENEDICION || $status == Status::STATUS_RECHAZADO || $status == Status::STATUS_VALIDADO || !empty($rechazo)){
                return true;
            }
        }
        return false;
    }



}
