<?php

namespace app\controllers\op;

use app\helpers\GeneralController;
use app\helpers\OpController;
use app\models\CatSubespecialidades;
use app\models\DatosValidados;
use app\models\Status;
use app\models\SubespecialidadCategory;
use app\models\Usuarios;
use kartik\form\ActiveForm;
use Yii;
use app\models\Experiencia;
use app\models\ExperienciaSearch;
use app\models\Provider;
use yii\filters\VerbFilter;
use yii\helpers\ArrayHelper;
use yii\web\Response;
use yii\web\UploadedFile;

class ExperienciaController extends GeneralController
{

    public function behaviors()
    {
        return [
            'verbs' => [
                'class' => VerbFilter::class,
                'actions' => [
                    'delete' => ['POST','GET'],
                ],
            ],
        ];
    }


    public function actionIndex()
    {
        $m = new ExperienciaSearch();
        $modelos = (Object)[
            'edicion' => $m->search(Yii::$app->request->queryParams,'edicionFilter'),
            'rechazados' => $m->search(Yii::$app->request->queryParams,'rechazadosFilter'),
            'validados' => $m->search(Yii::$app->request->queryParams,'validadosFilter'),
            'pendientes' => $m->search(Yii::$app->request->queryParams,'pendientesFilter'),
            'filtro' => $m
        ];
        return $this->render('index', [
            'modelos' => $modelos
        ]);
    }


    public function actionView($id)
    {
        $model_status = new Status();
        $model_status->scenario = Status::SCENARIO_CREATE;
        $model = Experiencia::find()->where(['experiencia_id' => intval(base64_decode($id))])->one();
        if(Yii::$app->user->can(Usuarios::ROLE_PROVIDER)){
            $model = Experiencia::find()->where(['and',['experiencia_id' => intval(base64_decode($id)), 'provider_id' => Yii::$app->user->identity->providerid]])->one();
        }
        if(!$model){
            return $this->redirect('index');
        }
        return $this->renderAjax('view', [
            'model' => $model,
            'model_status' => $model_status
        ]);
    }


    public function actionUpdate($id=0)
    {
        if(Yii::$app->request->isAjax || Yii::$app->request->post()){
            $model = $this->findModel(intval(base64_decode($id)));

            $rechazo = [];
            if($model->experiencia_id!==null){
                $rechazo = Status::find()->getStatus($model->experiencia_id,'experiencia','op');
            }


            $model->url_factura_xmlURL = UploadedFile::getInstance($model,'url_factura_xml');

            $model->url_contratoOLD = $model->url_contrato;
            $model->url_acta_entregaOLD = $model->url_acta_entrega;
            $model->url_factura_pdfOLD = $model->url_factura_pdf;
            $model->url_factura_xmlOLD = $model->url_factura_xml;

            $model->provider_id = Yii::$app->user->identity->providerid;
            if(Yii::$app->request->isAjax && $model->load(Yii::$app->request->post())){
                Yii::$app->response->format = Response::FORMAT_JSON;
                return ActiveForm::validate($model);
            }else if ($model->load(Yii::$app->request->post())) {

                if(($requi_status = Status::find()->where(['and',['register_id' => $model->experiencia_id],['status_op' => Status::STATUS_PENDIENTE],['modelo'=>'experiencia']])->one())!==null){
                    $requi_status->status_op = Status::STATUS_TERMINADO_PRO;
                    $requi_status->save();
                }
                $model->status_op = Status::STATUS_ENEDICION;
                $this->makeDir($model->path);

                if(!empty($model->url_contrato) && $model->url_contrato!=$model->url_contratoOLD){
                    $new_nameContrato = str_replace('archivos_tmp/',$model->path.'/',$model->url_contrato);
                    $this->copyFile($model->url_contrato,$new_nameContrato);
                    $model->url_contrato = $new_nameContrato;
                }

                if(!empty($model->url_acta_entrega) && $model->url_acta_entrega!=$model->url_acta_entregaOLD){
                    $new_nameAe = str_replace('archivos_tmp/',$model->path.'/',$model->url_acta_entrega);
                    $this->copyFile($model->url_acta_entrega,$new_nameAe);
                    $model->url_acta_entrega = $new_nameAe;
                }

                if(!empty($model->url_factura_pdf) && $model->url_factura_pdf!=$model->url_factura_pdfOLD){
                    $new_nameFpdf = str_replace('archivos_tmp/',$model->path.'/',$model->url_factura_pdf);
                    $this->copyFile($model->url_factura_pdf,$new_nameFpdf);
                    $model->url_factura_pdf = $new_nameFpdf;
                }
                ($model->url_factura_xmlURL) ?
                    $this->saveNewFile($model,'url_factura_xml', $model->path) :
                    $model->url_factura_xml = $model->url_factura_xmlOLD;

                if($model->save()) {
                    return $this->redirect(['index']);
                }else{
                    return $this->goHome();
                }
            }
            $tipo_obra = ArrayHelper::map(SubespecialidadCategory::find()->select(['category_id','nombre'])->all(),'category_id','nombre');
            $especialidad  = ArrayHelper::map(CatSubespecialidades::find()->select(['subespecialidad_id','nombre'])->all(),'subespecialidad_id','nombre');


            return $this->renderAjax('update', [
                'model' => $model,
                'rechazo' => $rechazo,
                'tipo_obra' => $tipo_obra,
                'especialidad' => $especialidad,
            ]);
        }
        return $this->goHome();

    }


    public function actionTerminar($id=null){

        $IdLastVal = $this->saveLastSendValidation(Yii::$app->user->identity->getProvider(),'validExperiencia','experiencia','op');

        if($id!=null){
            $status = $this->terminarUno('Experiencia',intval(base64_decode($id)),'experiencia_id','experiencia','status_op','',$IdLastVal);
        }else{
            $status = $this->terminarTodos('Experiencia','experiencia_id', 'experiencia','status_op','',$IdLastVal);
        }


        if($status){
            $correos_validadores = self::getEmailValidador(2,'op');

            self::sendEmail('/provider/correos/nuevo', null, $correos_validadores, 'Nuevo proveedor', ['modulo' => 'Experiencía','tipo_provider' => 'op']);

            self::AllSendNotification(null,'op',null,'DATOS LEGALES','Experiencía');

            $provider = Provider::findOne(Yii::$app->user->identity->providerid);
            GeneralController::sendModuloRevisionEmail($provider->email, 'Experiencia');
        }


        return $this->redirect(['index']);
    }


    public function actionDelete($id=null)
    {
        $model = Experiencia::find()
            ->where(['and',['experiencia_id' => base64_decode($id),'provider_id'=> Yii::$app->user->identity->providerid,'status_op'=>$this->statusParaEliminar]])
            ->one();
        if($model){
            $this->deleteFile($model->url_contrato);
            $this->deleteFile($model->url_acta_entrega);
            $this->deleteFile($model->url_factura_pdf);
            $this->deleteFile($model->url_factura_xml);
            $model->activo = FALSE;
            $model->save();
            return $this->redirect(['index']);
        }
        return json_encode(['status'=>'No permitido']);
    }


    protected function findModel($id)
    {
        $model = Experiencia::find()->where(['and',['provider_id'=>Yii::$app->user->identity->providerid],['experiencia_id' => intval($id)]])->one();
        if (!$model) {
            $model = new Experiencia();
        }
        return $model;
    }


    public function actionViewValidar($id=null){

        $queryParams = Yii::$app->request->queryParams;
        $id = isset($queryParams['id']) ? intval( $queryParams['id'] ) : null;
        $opcionesArray = isset($queryParams['opciones']) ? $queryParams['opciones'] : null;
        $opcionesSerial = isset($opcionesArray) ? serialize($opcionesArray) : null;
        $opcionesBase64 = isset($opcionesSerial) ? base64_encode($opcionesSerial) : null;

        $model = Experiencia::find()->where(['experiencia_id' => $id ])->one();

        if(Yii::$app->user->isGuest || $id == null || !Yii::$app->user->can(Usuarios::ROLE_VALIDADOR_CONTRATISTAS) || !$model){
            $this->redirect('/');
        }

        $status = new Status();
        $status->scenario = Status::SCENARIO_CREATE;
        $validados = new DatosValidados();
        $validados->scenario = DatosValidados::SCENARIO_CREATE;
        $rechazo = Status::find()->getStatus($model->experiencia_id,'experiencia','op','TERMINADO PRO');

        return $this->renderAjax('view-validar',[
            'modelStatus' => $status,
            'modelValidado' => $validados,
            'model' => $model,
            'rechazo' => $rechazo,
            'namePro' => $this->getNameProvider($model->provider_id),
            'opciones' => $opcionesBase64
        ]);

    }


    public function actionEspecialidades($id){

        $ciudades = ArrayHelper::map(CatSubespecialidades::find()->where(['category_id' => intval($id)])->asArray()->all(),'subespecialidad_id','nombre');
        $selector ='<option value=""></option>';
        foreach ($ciudades as $key=>$ciudad){
            $selector .='<option value="'.$key.'">'.$ciudad.'</option>';
        }
        echo $selector;
    }

}
