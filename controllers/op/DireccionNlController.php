<?php

namespace app\controllers\op;

use app\helpers\GeneralController;
use app\models\CatAsentamientos;
use app\models\CatColonia;
use app\models\CatLocalidades;
use app\models\CatMunicipio;
use app\models\CatMunicipios;
use app\models\CatVialidad;
use app\models\Provider;
use app\models\Status;
use app\models\Ubicacion;
use app\models\Usuarios;
use Yii;
use app\models\DireccionNl;
use app\models\DireccionNlSearch;
use yii\helpers\ArrayHelper;
use yii\web\Controller;
use yii\web\NotFoundHttpException;
use yii\filters\VerbFilter;


if (Yii::$app->user->can(Usuarios::ROLE_PROVIDER)) {
    Yii::$app->params['provider_id'] = Yii::$app->user->identity->getProvider();
}
/**
 * DireccionNlController implements the CRUD actions for DireccionNl model.
 */
class DireccionNlController extends GeneralController
{
    /**
     * @inheritdoc
     */
    public function behaviors()
    {
        return [
            'verbs' => [
                'class' => VerbFilter::className(),
                'actions' => [
                    'delete' => ['POST','GET'],
                ],
            ],
        ];
    }

    /**
     * Lists all DireccionNl models.
     * @return mixed
     */
    public function actionIndex()
    {
        $searchModel = new DireccionNlSearch();
        $dataProvider = $searchModel->search(Yii::$app->request->queryParams);

        return $this->render('index', [
            'searchModel' => $searchModel,
            'dataProvider' => $dataProvider,
        ]);
    }

    /**
     * Displays a single DireccionNl model.
     * @param integer $id
     * @return mixed
     */
    public function actionView($id)
    {
        return $this->render('view', [
            'model' => $this->findModel($id),
        ]);
    }

    /**
     * Creates a new DireccionNl model.
     * If creation is successful, the browser will be redirected to the 'view' page.
     * @return mixed
     */
    public function actionCreate()
    {
        $model = new Ubicacion();
        $city_nl = DireccionNl::find()->getCityNL();
        $id = Yii::$app->params['provider_id'];
        $model->provider_id = $id;
        if ($model->load(Yii::$app->request->post())) {
            if((Ubicacion::find()->where(['and',['calle_fiscal' => $model->calle_fiscal],['num_ext_fiscal' => $model->num_ext_fiscal],['num_int_fiscal'=> $model->num_int_fiscal],['colonia_fiscal'=> $model->colonia_fiscal],['cp_fiscal'=> $model->cp_fiscal],['city_fiscal'=> $model->city_fiscal],['localidad_id'=> $model->localidad_id]])->one())===null) {
                $model->encargado = 'DIRECCIÓN NUEVO LEÓN';
                $model->tipo = 'DIRECCIÓN NUEVO LEÓN';
                $model->descripcion = 'DIRECCIÓN NUEVO LEÓN';
                $model->type_address_prov = 'NL';
                $model->correo = '';
                $model->telefono = '';
                $model->state_fiscal = 19;
                $location = json_decode($model->geo_ubicacion);
                if (isset($location[0]) && !empty($location[0]) && isset($location[1]) && !empty($location[1])) {
                    $val_geo = DireccionNl::findBySql("select ST_MakePoint(" . $location[0] . "," . $location[1] . ")")->asArray()->all();
                    $model->geo_ubicacion = $val_geo[0]['st_makepoint'];
                }
                if (!$model->save()) {

                }

            }
            return $this->redirect(['bys/ubicacion/index']);

        }

        $vialidad = ArrayHelper::map(CatVialidad::find()->select(['vialidad_id', 'descripcion'])->asArray()
            ->all(), 'vialidad_id', 'descripcion');

        return $this->renderAjax('create', [
            'model' => $model,
            'colonia_nl' => [],
            'city_nl' => $city_nl,
            'vialidad' => $vialidad,
            'localidad' => [],
        ]);
    }

    /**
     * Updates an existing DireccionNl model.
     * If update is successful, the browser will be redirected to the 'view' page.
     * @param integer $id
     * @return mixed
     */
    public function actionUpdate($id = null)
    {
        if (Yii::$app->request->isAjax || Yii::$app->request->post()) {
            $provider = Provider::find()->where(['user_id' => Yii::$app->user->getId()])->one();
            $ubicacion = $this->findUbicacionNl($id);
            $ubicacion->scenario = Ubicacion::SCENARIO_OP_NL;
            $colonia_nl = [];
            $localidad = [];
            $city_nl = ArrayHelper::map(CatMunicipios::find()->select(['municipio_id','nombre'])->where(['entidad_id'=> 19])->asArray()->all(),'municipio_id','nombre');
            $vialidad = ArrayHelper::map(CatVialidad::find()->select(['vialidad_id', 'descripcion'])->asArray()->all(), 'vialidad_id', 'descripcion');
            $ubicacion->provider_id = $provider->provider_id;
            if (!empty($ubicacion->city_fiscal)) {
                $colonia_nl = ArrayHelper::map(CatAsentamientos::find()->select(['asentamiento_id', 'nombre'])->where(['and', ['entidad_id' => 19], ['municipio_id' => $ubicacion->city_fiscal]])->asArray()->orderBy(['nombre' => 'ASC'])->all(), 'asentamiento_id', 'nombre');
                $localidad = ArrayHelper::map(CatLocalidades::find()->select(['localidad_id', 'nombre'])->where(['and', ['entidad_id' => 19], ['municipio_id' => $ubicacion->city_fiscal]])->asArray()->orderBy(['nombre' => 'ASC'])->all(), 'localidad_id', 'nombre');
            }

            if ( $ubicacion->load(Yii::$app->request->post()) ) {
                $ubicacion->state_fiscal = 19;
                $ubicacion->encargado = 'DIRECCIÓN NUEVO LEÓN';
                $ubicacion->tipo = 'DIRECCIÓN NUEVO LEÓN';
                $ubicacion->descripcion = 'DIRECCIÓN NUEVO LEÓN';
                $ubicacion->type_address_prov = 'NL';
                $ubicacion->correo = '';
                $ubicacion->telefono = '';
                $location = json_decode($ubicacion->geo_ubicacion);
                if (isset($location[0]) && !empty($location[0]) && isset($location[1]) && !empty($location[1])) {
                    $val_geo = DireccionNl::findBySql("select ST_MakePoint(" . $location[0] . "," . $location[1] . ")")->asArray()->all();
                    $ubicacion->geo_ubicacion = $val_geo[0]['st_makepoint'];
                }
                $ubicacion->status_op = Status::STATUS_ENEDICION;
                if($ubicacion->save(false)){
                    if (($requi_status = Status::find()->where(['and', ['register_id' => $ubicacion->ubicacion_id], ['status_bys' => Status::STATUS_PENDIENTE], ['modelo' => 'direccion_nl']])->one()) !== null) {
                        $requi_status->status_op = Status::STATUS_TERMINADO_PRO;
                        $requi_status->save();
                    }
                    self::eliminarCita();
                }
                return $this->redirect(['op/ubicacion/index']);
            }

            return $this->renderAjax('update', [
                'model' => $ubicacion,
                'colonia_nl' => $colonia_nl,
                'city_nl' => $city_nl,
                'vialidad' => $vialidad,
                'localidad' => $localidad,

            ]);
        }
        
    }



    public function actionTerminar($id = null)
    {
        $id = intval(base64_decode($id));
        if ($id == null || !Provider::verifyProvider($id) || !Provider::verifyEtapa2($id) || !self::verifyTerminar($id)) {
            echo $this->render('/site/error', ['name' => 'Lo sentimos...', 'message' => 'Acceso denegado']);

            return false;
        }
        $model = $this->findModel($id);
        $model->scenario = 'op_nl';
        if ($model->status_op != Status::STATUS_ENEDICION) {
            echo $this->render('/site/error', ['name' => 'Lo sentimos...', 'message' => 'Acceso denegado']);

            return false;
        }

        $model->status_op = 'POR VALIDAR';
        if(!$model->save()){
        }

        $correos_validadores = self::getEmailValidador(2, 'op');

        $IdLastVal = $this->saveLastSendValidation($model->provider_id,'mainFilterUbicacionNl','direccion_nl','op');
        $this->saveLastSendValidationRelation($IdLastVal,$model->provider_id,'direccion_nl',$model->ubicacion_id,'op');

        self::sendEmail('/provider/correos/nuevo', null, $correos_validadores, 'Nuevo proveedor', ['modulo' => 'Dirección NL', 'tipo_provider' => 'op']);

        self::AllSendNotification(null, 'op', null, 'DATOS TECNICOS', 'Direccion NL');

        $provider = Provider::findOne(Yii::$app->user->identity->providerid);
        GeneralController::sendModuloRevisionEmail($provider->email, 'Domicilio (Direccion NL)');

        return $this->redirect(['op/ubicacion/index']);
    }

    /**
     * Deletes an existing DireccionNl model.
     * If deletion is successful, the browser will be redirected to the 'index' page.
     * @param integer $id
     * @return mixed
     */
    public function actionDelete($id)
    {
        $this->findModel($id)->delete();

        return $this->redirect(['index']);
    }
    public function actionList_colpostal($id){
        $colonias =  ArrayHelper::map(CatAsentamientos::find()->select(['cv_asentamiento','nombre'])
            ->where(['cp' => $id])
            ->orderBy(['nombre'=>SORT_ASC])
            ->asArray()->orderBy(['nombre'=>'ASC'])->all(),'cv_asentamiento','nombre');
        $con = '';
        $con .='<option value=""></option>';
        if (count($colonias) > 0) {
            foreach ($colonias as $key => $value) $con .="<option value='" . $key . "'>" . $value . "</option>";
        } else {
            $con .="<option></option>";
        }
        echo $con;
    }
    public function actionListAsentamiento($id){

        $asentamientos = CatAsentamientos::find()->select(['asentamiento_id','nombre'])
            ->where(['and',['municipio_id' => $id],['entidad_id' => 19]])
            ->orderBy(['nombre'=>SORT_ASC])
            ->asArray()->all();

        $con = '';
        $con .='<option value="">Selecciona...</option>';
        if($asentamientos){
            foreach ($asentamientos as $val)
                $con .="<option value=".$val['asentamiento_id'].">".$val['nombre']."</option>";
        }else{
            $con .='Sin resultados';
        }

        echo $con;

    }
    /**
     * Finds the DireccionNl model based on its primary key value.
     * If the model is not found, a 404 HTTP exception will be thrown.
     * @param integer $id
     * @return DireccionNl the loaded model
     * @throws NotFoundHttpException if the model cannot be found
     */
    protected function findModel($id)
    {
        if (($model = Ubicacion::find()->where(['and',['provider_id' => $id],['type_address_prov' => 'NL']])->orderBy(['ubicacion_id' => SORT_DESC])->one())=== null) {
            $model = new Ubicacion();
        }

        return $model;
    }

    protected function findUbicacionNl($id){
        if( $id == null ){ new Ubicacion(); }
        return ($model = Ubicacion::findOne($id) ) !== null ? $model : new Ubicacion();
    }


    public function verifyTerminar($provider)
    {
        if ($provider) {

            $model = $this->findModel($provider);


            $esta_lleno = \app\helpers\GeneralController::estaLleno($model);

            $status = $model->status_op;
            if (($status == Status::STATUS_ENEDICION || $status == Status::STATUS_RECHAZADO) && $esta_lleno) {
                return true;
            }
        }
        return false;
    }
}
