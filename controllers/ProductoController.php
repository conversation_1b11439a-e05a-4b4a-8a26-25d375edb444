<?php

namespace app\controllers;

use app\helpers\GeneralController;
use app\models\ProviderGiro;
use app\models\UnidadMedida;
use app\models\Usuarios;
use Yii;
use app\models\Producto;
use app\models\ProductoSearch;
use app\models\Provider;
use yii\data\SqlDataProvider;
use yii\web\NotFoundHttpException;
use yii\filters\VerbFilter;

/**
 * ProductoController implements the CRUD actions for Producto model.
 */
class ProductoController extends GeneralController
{
    /**
     * @inheritdoc
     */
    public function behaviors()
    {
        return [
            'verbs' => [
                'class' => VerbFilter::class,
                'actions' => [
                    'delete' => ['POST'],
                ],
            ],
        ];
    }

    /**
     * Lists all Producto models.
     * @return mixed
     */
    public function actionValidationProducto()
    {
        $params = Yii::$app->request->queryParams;
        return $this->render('validation-producto', [
            'model' => $this->getAllDataValidados($params),
            'tipo' => 'bys'
        ]);
    }

    /**
     * Displays a single Producto model.
     * @param string $id
     * @return mixed
     */
    public function actionView($id)
    {
        return $this->render('view', [
            'model' => $this->findModel($id),
        ]);
    }


    /**
     * Displays a single Producto model.
     * @param string $id
     * @return mixed
     */
    public function actionViewInfo($id)
    {
        return $this->renderAjax('view-info', [
            'modelProductos' => $this->findModelProviderGiro($id),
        ]);
    }


    public function findModelProviderGiro($id){

        $model = ProviderGiro::find()->select("p.grupo_producto_id as grupo_producto")
            ->addSelect(new \yii\db\Expression("string_agg(provider_giro.producto_id::text,',') as producto_id"))
            ->from('provider_giro')->innerJoin('producto p','p.producto_id = provider_giro.producto_id')
            ->groupBy("grupo_producto_id")->where(['provider_id' => $id])->asArray()->all();

        if($model == null || empty($model)){
            $model = [new ProviderGiro()];
        }
        return $model;
    }

    /**
     * Creates a new Producto model.
     * If creation is successful, the browser will be redirected to the 'view' page.
     * @return mixed
     */
    public function actionCreate()
    {
        $model = new Producto();

        if ($model->load(Yii::$app->request->post()) && $model->save()) {
            return $this->redirect(['view', 'id' => $model->producto_id]);
        } else {
            return $this->render('create', [
                'model' => $model,
            ]);
        }
    }

    /**
     * Updates an existing Producto model.
     * If update is successful, the browser will be redirected to the 'view' page.
     * @param string $id
     * @return mixed
     */
    public function actionUpdate($id)
    {
        $model = $this->findModel($id);

        if ($model->load(Yii::$app->request->post()) && $model->save()) {
            return $this->redirect(['view', 'id' => $model->producto_id]);
        } else {
            return $this->render('update', [
                'model' => $model,
            ]);
        }
    }

    /**
     * Deletes an existing Producto model.
     * If deletion is successful, the browser will be redirected to the 'index' page.
     * @param string $id
     * @return mixed
     */
    public function actionDelete($id)
    {
        $this->findModel($id)->delete();

        return $this->redirect(['index']);
    }

    /**
     * Finds the Producto model based on its primary key value.
     * If the model is not found, a 404 HTTP exception will be thrown.
     * @param string $id
     * @return Producto the loaded model
     * @throws NotFoundHttpException if the model cannot be found
     */
    protected function findModel($id)
    {
        if (($model = Producto::findOne($id)) !== null) {
            return $model;
        } else {
            throw new NotFoundHttpException('The requested page does not exist.');
        }
    }

    public function actionList_iva($id)
    {
        $env_iva = Yii::$app->params['IVA'];
        $iva = (Producto::find()->select('iva')->where(['producto_id' => $id])->one()->iva)/100;
        if($iva == NULL){return ($env_iva);}elseif($iva){return $iva;}else{return 0;}
    }

    public function actionList_um($id)
    {
        $um = UnidadMedida::find()->select('abreviacion')->where("unidad_medida_id in (select unidad_medida_id from producto where producto_id = $id)")->one()->abreviacion;
        if($um){return $um;}else{return '';}
    }


    public static function getAllDataValidados($params)
    {

        $sql = ' ';
        $bind = [];
        if (isset($params['r']) && !empty($params['r'])) {

            $rfc = $params['r'];

            $sql .= " where lower(unaccent(rfc)) ilike '%' || lower(unaccent(:rfc)) || '%' ";

            $bind = [':rfc' => "%$rfc%"];
        }

        if (isset($params['n']) && !empty($params['n']) && isset($params['r']) && !empty($params['r'])) {

            $nom = $params['n'];

            $sql .= " and lower(unaccent(nombre)) ilike '%' || lower(unaccent(:nom)) || '%' ";

            $bind = [':rfc' => "%$rfc%", ':nom' => "%$nom%"];
        } elseif (isset($params['n']) && !empty($params['n'])) {
            $nom = $params['n'];

            $sql .= " where lower(unaccent(nombre)) ilike '%' || lower(unaccent(:nom)) || '%' ";
            $bind = [':nom' => "%$nom%"];
        }

        $status = Yii::$app->user->can(Usuarios::ROLE_VALIDADOR_PROVEEDORES) ? 'bys' : 'op';

        $totalCount = Yii::$app->db
            ->createCommand("with ct as
                        (
                        select distinct
                        validado_id,provider_id, created_date,status_bys,
                        row_number() over (partition by provider_id order by created_date desc) rn
                        from
                        provider.datos_validados where status_bys = 'VALIDADO' and created_date::date<='2021-09-12' order by provider_id
                        )
                        select
                        count(1)
                        from
                        ct
                        join provider p using(provider_id)
                        where
                        rn = 1 and ct.status_bys = 'VALIDADO';
            
            ")
            ->queryScalar();


        $sql = [
            'sql' => "WITH rw as(with ct as
                        (
                        select distinct
                        validado_id,provider_id, created_date,status_bys,
                        row_number() over (partition by provider_id order by created_date desc) rn
                        from
                        provider.datos_validados where status_bys = 'VALIDADO' and created_date::date<='2021-09-12' order by provider_id
                        )
                        select
                        ct.validado_id,ct.status_bys,ct.provider_id,ct.created_date,p.rfc,
                        CASE WHEN (p.tipo_persona = '".Provider::PERSONA_MORAL."')
                        then p.name_razon_social else concat_ws(' ',p.pf_nombre,p.pf_ap_paterno,p.pf_ap_materno) end as nombre
                        from
                        ct
                        join provider p using(provider_id)
                        where
                        rn = 1 and ct.status_bys = 'VALIDADO' order by created_date DESC)
                        select rw.validado_id,rw.provider_id,rw.created_date::date,rw.rfc, rw.nombre from rw $sql
            
            ",
            'totalCount' => $totalCount,
            //'sort' =>false, to remove the table header sorting
            'sort' => [
                'attributes' => [
                    'rfc' => [
                        'asc' => ['rfc' => SORT_ASC],
                        'desc' => ['rfc' => SORT_DESC],
                        'default' => SORT_DESC,
                        'label' => 'RFC',
                    ],
                    'nombre' => [
                        'asc' => ['nombre' => SORT_ASC],
                        'desc' => ['nombre' => SORT_DESC],
                        'default' => SORT_DESC,
                        'label' => 'Nombre',
                    ],
                ],
            ]
        ];

        if ($bind) {
            $sql['params'] = $bind;
        }

        $dataProvider = new SqlDataProvider($sql);

        return $dataProvider;

    }
}
