<?php

namespace app\controllers;

use app\helpers\GeneralController;
use Exception;
use Yii;
use yii\helpers\Url;
use yii\web\Controller;

class FirmaController extends Controller{

    /**
     * Funcion encargada de consultar la firma del documento, el QR y barcode
     * @param Integer $provider_id Id del proveedor
     * @param String $tipo Tipo de perfil (Bys / Op)
     * @param String $filename Ruta del archivo a firmar
     * @param Strgin $signedFilename Ruta final del archivo a firmar
     * @return Array Arreglo con la cadena de firma, QR y barcode
     **/ 
    public static function generarFirmaWS($provider_id, $tipo, $filename){
        $codigo_verificacion = GeneralController::codigoVerificacion($provider_id);
        $token = GeneralController::solicitaDatosGet(Yii::$app->params['urlToken']);
        if( is_null($token) || !isset($token['token']) ){ throw new Exception("Error al autenticarse, contacte a soporte"); }
        $datosQr = GeneralController::solicitaDatosPost(Yii::$app->params['urlQr'], ['encode' => $codigo_verificacion, 'base64' => 'true', 'validador' => true]);
        if( is_null($token) ){ throw new Exception("Error al solicitar el QR, contacte a soporte"); }
        $protocol = GeneralController::isProduction()?'https':'http';
        $datosBarCode = GeneralController::solicitaBarCode(Yii::$app->params['urlBarCode'], $codigo_verificacion);
        $datosFirma = GeneralController::solicitaDatosPost(Yii::$app->params['urlFirma'], ['code' => $codigo_verificacion, 
            'token' => $token, 'type' => strtoupper($tipo), 'path' => Url::home($protocol) . $filename, 'signed_document' => Url::home($protocol) . $filename]);

        return [ 'datosQr' => $datosQr,'datosBarCode' => $datosBarCode, 'datosFirma' => $datosFirma, "datosCodigo" => $codigo_verificacion ];
    }

}

?>