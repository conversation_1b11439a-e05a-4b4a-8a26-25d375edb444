<?php

namespace app\controllers;

use app\helpers\GeneralController;
use app\models\SubespecialidadCategory;
use app\models\SubespecialidadCategorySearch;
use yii\web\NotFoundHttpException;
use yii\filters\VerbFilter;

/**
 * SubespecialidadCategoryController implements the CRUD actions for SubespecialidadCategory model.
 */
class SubespecialidadCategoryController extends GeneralController
{
    /**
     * @inheritDoc
     */
    public function behaviors()
    {
        return array_merge(
            parent::behaviors(),
            [
                'verbs' => [
                    'class' => VerbFilter::className(),
                    'actions' => [
                        'delete' => ['POST'],
                    ],
                ],
            ]
        );
    }


    public function actionIndex(){
        $searchModel = new SubespecialidadCategorySearch();
        $dataProvider = $searchModel->search($this->request->queryParams);
        return $this->render('index', [
            'searchModel' => $searchModel,
            'dataProvider' => $dataProvider,
        ]);
    }

    public function actionUpdate($id=null){
        $model = $this->findModel($id);
        if ($model->load($this->request->post()) && $model->save()) {
            return $this->redirect(['index']);
        }
        return $this->render('update', [
            'model' => $model,
        ]);
    }

    public function actionDelete($category_id){
        //$this->findModel($category_id)->delete();
        return $this->redirect(['index']);
    }

    protected function findModel($id){
        return ($model = SubespecialidadCategory::findOne($id)) !== null ? $model : new SubespecialidadCategory();
    }
}
