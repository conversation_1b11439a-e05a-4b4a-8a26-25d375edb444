<?php
namespace app\controllers;

use app\models\ActaConstitutiva;
use app\helpers\GeneralController;
use app\models\CatAsentamientos;
use app\models\CatMunicipios;
use app\models\CatSubespecialidades;
use app\models\Certificacion;
use app\models\ClientesContratos;
use app\models\Concursos;
use app\models\Curp;
use app\models\Curriculum;
use app\models\Giro;
use app\models\HistoricoCertificados;
use app\models\IdOficial;
use app\models\IntervencionBancaria;
use app\models\ModificacionActa;
use app\models\Perfil;
use app\models\Provider;
use app\models\ProviderConcurso;
use app\models\ProviderGiro;
use app\models\ProviderQuery;
use app\models\RegistroConcursos;
use app\models\RelacionAccionistas;
use app\models\RepresentanteLegal;
use app\models\SaveSearch;
use app\models\SubespecialidadCategory;
use app\models\Ubicacion;
use app\models\Usuarios;
use Yii;
use yii\filters\auth\HttpBearerAuth;
use yii\filters\VerbFilter;
use yii\helpers\ArrayHelper;
use yii\web\Controller;
use Lcobucci\JWT\Signer\Hmac\Sha256;
use yii\web\Response;
use app\models\IpRateLimiter;


class ApiController extends Controller
{

    /**
     * {@inheritdoc}
     */
    public static function allowedDomains()
    {
        return ['*'];
    }

    public function beforeAction($action)
    {
        $this->enableCsrfValidation = false;
        \Yii::$app->response->format = Response::FORMAT_JSON;
        return parent::beforeAction($action);
    }


    /**
     * @inheritdoc
     */
    public function behaviors()
    {
        return [
            'authenticator' => [
                'class' => HttpBearerAuth::className(),
                'except' => ['auth','providerbyrfc','concursoinfo','colonias','municipios','guardaconcurso','session', 'notificacionescontratos']
            ],

            'verbs' => [
                'class' => VerbFilter::className(),
                'actions' => [
                    'auth' => ['POST'],
                    'consulta' => ['POST'],
                    'consulta-op' => ['POST'],
                    'consulta-prov' => ['POST'],
                    'consulta-bys' => ['POST']
                ],
            ],
//            'corsFilter' => [
//                'class' => \yii\filters\Cors::className(),
//                'cors' => [
//                    // restrict access to
//                    'Origin' => ['*'],
//                    // Allow only POST and GET methods
//                    'Access-Control-Request-Method' => ['POST', 'GET'],
//                    // Allow only headers 'X-Wsse'
//                    'Access-Control-Request-Headers' => ['X-Wsse'],
//                    // Allow credentials (cookies, authorization headers, etc.) to be exposed to the browser
//                    'Access-Control-Allow-Credentials' => true,
//                    // Allow OPTIONS caching
//                    'Access-Control-Max-Age' => 3600,
//                    // Allow the X-Pagination-Current-Page header to be exposed to the browser.
//                    'Access-Control-Expose-Headers' => ['X-Pagination-Current-Page'],
//                ],
//
//            ],
            /*'rateLimiter' => [
                // Use class
                'class' => \yii\filters\RateLimiter::className(),
                'enableRateLimitHeaders' => true,
                'errorMessage' => 'Rate limit exceeded',
            ]*/
            'rateLimiter' => [
                // Use class
                'class' => IpRateLimiter::className(),
                'rateLimit' => 10,
                'timePeriod' => 1,
                'separateRates' => false,
                'enableRateLimitHeaders' => false,
            ]
        ];
    }


    public function actionAuth(){

        $username = Yii::$app->request->post('username');
        $pass = Yii::$app->request->post('password');
        $modelUser = Usuarios::findByUsername($username);


        if ($modelUser  && ( $modelUser->validatePassword($pass) or $modelUser->password == $pass )) {

            $rfc = Provider::find()->select('rfc')->where(['user_id' => $modelUser->user_id])->one()['rfc'];

            $data_return = self::generateToken($modelUser->username);
            $token = $data_return['token'];
            $expiracion = $data_return['expira'];
            return [
                'status' => true,
                'message' => 'Success',
                'token' => $token,
                'rfc' => $rfc,
                'fechaExpira' => $expiracion
            ];
        }
        return [
            'status' => false,
            'message' => 'Nombre de usuario y/o contraseña incorrectos',
            'token' => null,
            'fechaExpira' => null
        ];

    }


    public function actionIndex()
    {
        return $this->redirect('/site/login');
    }


    public function actionConsulta(){
        $limite = 15;
        if(\Yii::$app->request->isPost){
            $rfc = (\Yii::$app->getRequest()->getQueryParam('rfc')==null)?'nulo':\Yii::$app->getRequest()->getQueryParam('rfc');
            $nombre = (\Yii::$app->getRequest()->getQueryParam('nombre')==null)?'nulo':\Yii::$app->getRequest()->getQueryParam('nombre');
            $producto = (\Yii::$app->getRequest()->getQueryParam('producto')==null)?'nulo':\Yii::$app->getRequest()->getQueryParam('producto');
            if($rfc!='nulo' or  $nombre!='nulo' or  $producto !='nulo'){
                $sql = " select  date_part( 'year',current_date) as \"Ejercicio\" ,m.cv_municipio as \"Clave del municipio\", m.nombre as \"Nombre del municipio o delegación\", e.entidad_id as \"Clave de la Entidad Federativa\" , e.nombre as \"Entidad Federativa.\", p.cp_fiscal as \"Código postal\", r.nombre as \"Nombre(s) del representante legal\", r.ap_paterno as \"Primer Apellido del representante legal\", r.ap_materno as \"Segundo Apellido del representante legal\", r.telefono as \"Teléfono de contacto representante legal\", r.correo as \"Correo electrónico representante legal\", r.tipo_poder as \"Tipo acreditación legal representante legal\", p.pagina_web as \"Página web del proveedor o contratista\", p.telfono as \"Teléfono oficial del proveedor o contratista\", p.email as \"Correo electrónico comercial\", 'http://proveedores-test.gob.mx/' as \"Hipervínculo Registro Proveedores Contratistas\", 'http://proveedores-test.gob.mx/site/sancionados' as \"Hipervínculo proveedores contratistas sancionados\", current_date as \"Fecha de validación\", 'Jefatura de Tramite y Control de Proveedores' as \"Área(s) responsable(s) de la información\", '' as \"Nota\", date_part( 'year',current_date) as \"Año\", '' as \"Fecha de actualización\", concat_ws (' ','Del', to_char(date_trunc('month', CURRENT_DATE),'DD/MM/YYYY'), 'al',to_char(date_trunc('month', CURRENT_DATE) + interval '1 month - 1 day','DD/MM/YYYY')) as \"Periodo que se informa\", case when p.tipo_persona = 'Persona_moral' then 'PERSONA_MORAL' else 'PERSONA_FISICA' end as \"Personería Jurídica del proveedor\", p.pf_nombre as \"Nombre(s) del proveedor o contratist\", p.pf_ap_paterno as \"Primer Apellido del proveedor o contratis\", p.pf_ap_materno as \"Segundo Apellido del proveedor o contrati\", p.name_razon_social as \"Denominación o Razón social\", p.estratificacion as \"Estratificación\", case when cp.pais_nombre = 'México' then 'Nacional' else 'Internacional' end as \"Origen del proveedor\", e.nombre as \"Entidad Federativa.\", cp.pais_nombre as \"País de origen\", p.rfc as \"RFC de la persona física o moral\", p.subcontrataciones as \"Realiza subcontrataciones\", '' as \"Giro de la empresa\", v.descripcion as \"Tipo de vialidad\", p.calle_fiscal as \"Nombre vialidad\", p.numero_fiscal as \"Número Exterior.\", p.interior_fiscal as \"Número interior en su caso\", c.nombre \"Tipo de asentamiento\", a.nombre as \"Nombre del asentamiento\", l.cv_localidad as \"Clave de la localidad\",l.nombre as \"Nombre de la localidad\" from public.provider p inner join provider.representante_legal r on r.provider_id=p.provider_id inner join cat_municipios m on m.municipio_id = p.city_fiscal inner join cat_asentamientos a on a.asentamiento_id = p.tipo_asentamiento inner join cat_localidades l on l.localidad_id = p.clave_localidad inner join cat_entidades e on e.entidad_id = m.entidad_id inner join cat_paises cp on cp.pais_id = p.pais_id inner join cat_vialidad v on  v.vialidad_id = p.tipo_vialidad inner join cat_tipo_asentamientos c on c.tipo_asentamiento_id = a.tipo_asentamiento_id  where upper(p.rfc) LIKE :rfc or upper(p.name_razon_social) LIKE :nombre limit :limit ";
                $query = Provider::findBySql($sql,[ ':rfc' => '%'.strtoupper($rfc).'%', ':nombre' => '%'.strtoupper($nombre).'%',':limit'=>$limite])->asArray()->all();
                if(count($query)==0){
                    return json_encode(['Nota'=>'Su búsqueda no arroja resultado alguno. Intente nuevamente'],JSON_UNESCAPED_UNICODE);
                }else if(count($query)==$limite){
                    array_push($query, 'Su solicitud excede el número máximo de respuestas que es posible enviarle, pruebe acotando su consulta o trate de ser más específico.');
                    return json_encode($query,JSON_UNESCAPED_UNICODE);
                }else{
                    return json_encode($query,JSON_UNESCAPED_UNICODE);
                }
            }else{
                return json_encode(['Error'=>'Es necesario adjuntar al menos un filtro de busqueda. Intente nuevamente']);
            }
        }else{
            return $this->disabled();
        }
    }


    public function actionConsultaOp(){

        $request = Yii::$app->request;


            $rfc = $request->post('rfc');
            $nombre = $request->post('nombre');
            $especialidad = $request->post('especialidad');

            $allData = [];
            $rfcSearch = 'NO DATO';
            $razonSearch = 'NO DATO';
            $comercialSearch = 'NO DATO';
            $espSearch = 'NO DATO';
            if($rfc!=null ||  $nombre != null || $especialidad!=null) {
                if($rfc!=null){
                    $rfcSearch = $rfc;
                }if($nombre!=null){
                    $razonSearch = $nombre;
                    $comercialSearch = $nombre;
                }if($especialidad!=null){
                    $espSearch = $especialidad;
                }

                $params = [':rfcSearch' => $rfcSearch,':razonSearch' => $razonSearch,':comercialSearch' => $comercialSearch, ':espSearch' => $espSearch];

                $allData = ProviderQuery::getAllDataProviderOp($params);
                for ($x = 0; $x < count($allData); $x++) {
                    $allData[$x]['actividad_economica'] = $allData[$x]['actividad_economica'] ? json_decode($allData[$x]['actividad_economica'], true) : [];
                    $allData[$x]['referencias_comerciales'] = $allData[$x]['referencias_comerciales'] ? json_decode($allData[$x]['referencias_comerciales'], true) : [];
                    $allData[$x]['representante_legal'] = $allData[$x]['representante_legal'] ? json_decode($allData[$x]['representante_legal'], true) : [];
                    $allData[$x]['acta_constitutiva'] = $allData[$x]['acta_constitutiva'] ? json_decode($allData[$x]['acta_constitutiva'], true) : [];
                    $allData[$x]['registro_imms'] = $allData[$x]['registro_imms'] ? json_decode($allData[$x]['registro_imms'], true) : [];
                    $allData[$x]['comprobante_domicilio'] = $allData[$x]['comprobante_domicilio'] ? json_decode($allData[$x]['comprobante_domicilio'], true) : [];
                    $allData[$x]['escritura_publica'] = $allData[$x]['escritura_publica'] ? json_decode($allData[$x]['escritura_publica'], true) : [];
                    $allData[$x]['registro_publico_propiedad'] = $allData[$x]['registro_publico_propiedad'] ? json_decode($allData[$x]['registro_publico_propiedad'], true) : [];
                    $allData[$x]['modificacion_acta'] = $allData[$x]['modificacion_acta'] ? json_decode($allData[$x]['modificacion_acta'], true) : [];
                    $allData[$x]['relacion_accionistas'] = $allData[$x]['relacion_accionistas'] ? json_decode($allData[$x]['relacion_accionistas'], true) : [];
                    $allData[$x]['intervencion_bancaria'] = $allData[$x]['intervencion_bancaria'] ? json_decode($allData[$x]['intervencion_bancaria'], true) : [];
                    $allData[$x]['experiencia'] = $allData[$x]['experiencia'] ? json_decode($allData[$x]['experiencia'], true) : [];
                    $allData[$x]['estado_financiero'] = $allData[$x]['estado_financiero'] ? json_decode($allData[$x]['estado_financiero'], true) : [];
                    $allData[$x]['declaracion_isr'] = $allData[$x]['declaracion_isr'] ? json_decode($allData[$x]['declaracion_isr'], true) : [];
                    $allData[$x]['organigrama'] = $allData[$x]['organigrama'] ? json_decode($allData[$x]['organigrama'], true) : [];
                    $allData[$x]['personal_tecnico'] = $allData[$x]['personal_tecnico'] ? json_decode($allData[$x]['personal_tecnico'], true) : [];
                    $allData[$x]['maquinaria_equipos'] = $allData[$x]['maquinaria_equipos'] ? json_decode($allData[$x]['maquinaria_equipos'], true) : [];
                }
                return ['status' => true,'menssage' => 'success','data' => $allData];
            }
            return ['status' => false,'menssage' => 'error','data' => []];
    }


    public function actionConsultaProvOp(){


        $data = Yii::$app->db->createCommand("with ct as
                (
                select distinct
                last_send_validation_relation_id,provider_id, created_at,
                row_number() over (partition by provider_id order by created_at desc) rn
                from
                last_send_validation_relation where provider_type = 'op' order by provider_id
                )
                select
                ct.provider_id as id,ct.created_at as ultima_mod,p.rfc,p.email as correo_provider,u.email as correo_registro,
                CASE WHEN (p.name_razon_social is not null and p.name_razon_social != '') 
                then p.name_razon_social else concat_ws(' ',p.pf_nombre,p.pf_ap_paterno,p.pf_ap_materno) 
                end as nombre_razon_social
                from
                ct
                join provider p using(provider_id)
                join usuarios u on p.user_id = u.user_id
                where
                rn = 1 order by created_at DESC")->queryAll();

        return ['status' => true,'menssage' => 'success','data' => $data];


    }


    public function actionObras(){

        $data = Yii::$app->db->createCommand("select category_id as id, nombre as descripcion from provider.subespecialidad_category")->queryAll();

        return ['status' => true,'menssage' => 'success','data' => $data];
    }


    public function actionEspecialidades($id = null)
    {

        if ($id == null) {
            $data = Yii::$app->db->createCommand("select subespecialidad_id as id, nombre as descripcion from provider.cat_subespecialidades")->queryAll();
        } else {
            $data = Yii::$app->db->createCommand("select subespecialidad_id as id, nombre as descripcion from provider.cat_subespecialidades where category_id = :id", [':id' => intval($id)])->queryAll();
        }

        return ['status' => true, 'menssage' => 'success', 'data' => $data];

    }


    public function actionDescarga(){
        $file = './tmp/concentrado.csv';
        if(!file_exists('./tmp')){
            mkdir('./tmp');
        }
        if(\Yii::$app->request->isGet){
            $query = Provider::findBySql(" select  date_part( 'year',current_date) as \"Ejercicio\" ,m.cv_municipio as \"Clave del municipio\", m.nombre as \"Nombre del municipio o delegación\", e.entidad_id as \"Clave de la Entidad Federativa\" , e.nombre as \"Entidad Federativa.\", p.cp_fiscal as \"Código postal\", r.nombre as \"Nombre(s) del representante legal\", r.ap_paterno as \"Primer Apellido del representante legal\", r.ap_materno as \"Segundo Apellido del representante legal\", r.telefono as \"Teléfono de contacto representante legal\", r.correo as \"Correo electrónico representante legal\", r.tipo_poder as \"Tipo acreditación legal representante legal\", p.pagina_web as \"Página web del proveedor o contratista\", p.telfono as \"Teléfono oficial del proveedor o contratista\", p.email as \"Correo electrónico comercial\", 'http://proveedores-test.gob.mx/' as \"Hipervínculo Registro Proveedores Contratistas\", 'http://proveedores-test.gob.mx/site/sancionados' as \"Hipervínculo proveedores contratistas sancionados\", current_date as \"Fecha de validación\", 'Jefatura de Tramite y Control de Proveedores' as \"Área(s) responsable(s) de la información\", '' as \"Nota\", date_part( 'year',current_date) as \"Año\", '' as \"Fecha de actualización\", concat_ws (' ','Del', to_char(date_trunc('month', CURRENT_DATE),'DD/MM/YYYY'), 'al',to_char(date_trunc('month', CURRENT_DATE) + interval '1 month - 1 day','DD/MM/YYYY')) as \"Periodo que se informa\", case when p.tipo_persona = 'Persona_moral' then 'PERSONA_MORAL' else 'PERSONA_FISICA' end as \"Personería Jurídica del proveedor\", p.pf_nombre as \"Nombre(s) del proveedor o contratist\", p.pf_ap_paterno as \"Primer Apellido del proveedor o contratis\", p.pf_ap_materno as \"Segundo Apellido del proveedor o contrati\", p.name_razon_social as \"Denominación o Razón social\", p.estratificacion as \"Estratificación\", case when cp.pais_nombre = 'México' then 'Nacional' else 'Internacional' end as \"Origen del proveedor\", e.nombre as \"Entidad Federativa.\", cp.pais_nombre as \"País de origen\", p.rfc as \"RFC de la persona física o moral\", p.subcontrataciones as \"Realiza subcontrataciones\", '' as \"Giro de la empresa\", v.descripcion as \"Tipo de vialidad\", p.calle_fiscal as \"Nombre vialidad\", p.numero_fiscal as \"Número Exterior.\", p.interior_fiscal as \"Número interior en su caso\", c.nombre \"Tipo de asentamiento\", a.nombre as \"Nombre del asentamiento\", l.cv_localidad as \"Clave de la localidad\",l.nombre as \"Nombre de la localidad\" from public.provider p inner join provider.representante_legal r on r.provider_id=p.provider_id inner join cat_municipios m on m.municipio_id = p.city_fiscal inner join cat_asentamientos a on a.asentamiento_id = p.tipo_asentamiento inner join cat_localidades l on l.localidad_id = p.clave_localidad inner join cat_entidades e on e.entidad_id = m.entidad_id inner join cat_paises cp on cp.pais_id = p.pais_id inner join cat_vialidad v on v.vialidad_id = p.tipo_vialidad inner join cat_tipo_asentamientos c on c.tipo_asentamiento_id = a.tipo_asentamiento_id where p.creation_date between date_trunc('month', CURRENT_DATE) and date_trunc('month', CURRENT_DATE) + interval '1 month - 1 day' ")->asArray()->all();

            if(\Yii::$app->getRequest()->getQueryParam('json')){
                return json_encode($query, JSON_UNESCAPED_UNICODE);
            }else{
                $this->downloadFile($file,$query);
                return unlink($file);
            }
        }else{
            return $this->disabled();
        }
    }


    public function downloadFile($file, $sql){
        header('Pragma: public');
        header('Expires: 0');
        header('Cache-Control: must-revalidate, post-check=0, pre-check=0');
        header('Content-Description: File Transfer');
        header('Content-Type: text/csv');
        header('Content-Disposition: attachment; filename='.$file.';');
        header('Content-Transfer-Encoding: binary');

        $fileCSV = fopen($file, 'w');
        fputs($fileCSV, $bom =( chr(0xEF) . chr(0xBB) . chr(0xBF) ));
        fputcsv($fileCSV,array_keys($sql[0]),";");
        foreach ($sql as $row) {
            fputcsv($fileCSV, $row, ";");
        }
        fclose($fileCSV);
        return file_get_contents($file);
    }


    public function disabled(){
        return ['Error' => 'No Disponible'];
    }


    public static function GenerateToken($user_name){
        $expiration = time() + (2 * 24 * 60 * 60);
        $signer = new Sha256();
        $token = Yii::$app->jwt->getBuilder()->setIssuer('https://proveedores.nl.gob.mx')
            ->setAudience('https://proveedores.nl.gob.mx')
            ->setId('4f1g23a12aa', true)
            ->set('username',$user_name)
            ->setIssuedAt(time())
            ->setExpiration($expiration)
            ->set('uid', 1)
            ->sign($signer, Yii::$app->params['sign'])
            ->getToken();
        return ['token' =>(string) $token,'expira' => $expiration];
    }


    public function saveSearch($data = null){

        if($data!=null){
            $search = new SaveSearch();
            $search->user_id = Yii::$app->user->getId();
            $search->user_ip = Yii::$app->request->userIP;
            $search->user_host = Yii::$app->request->userHost;
            $search->search = $data;
            $search->save();
        }
    }


    public function actionProviderbyrfc($rfc=null){

        if(!$rfc)
            return $this->disabled();

        $model = Yii::$app->db->createCommand(" 
        
            select p.provider_id, case when p.tipo_persona = 'Persona moral' then p.name_razon_social else CONCAT_WS(' ', p.pf_nombre, p.pf_ap_paterno, p.pf_ap_materno) end AS name_razon_social, 
            curp.curp,	d.calle_fiscal as dom_calle, d.num_ext_fiscal as dom_numero_ext, 
            d.telefono as dom_telefono, d.cp_fiscal as dom_cp, d.correo as dom_correo, ent.nombre as dom_estado, 
            mun.nombre as dom_municipio, asen.nombre as dom_colonia,
            concat_ws(' ',rl.nombre,rl.ap_paterno, rl.ap_materno) as rep_nombre, esc.notario as esc_notario, 
            esc.fecha as esc_fecha, esc.url_escritura_publica as esc_pub,
            p.rfc as rfc , c.url_certificado as certif , u.tipo_persona as tipo, r.url_rfc as constancia, 
            d.url_comprobante_domicilio as comp_dom,
            o.url_idoficial as identif, rl.documento_identificacion as rep_id, rl.documento_acta as docto_poder_x ,
            rl.tipo_poder as tipo_poder_desc, case when rl.tipo_poder='ACTA' then 1  when rl.tipo_poder='PODER' then 2  when rl.tipo_poder='ACTUALIZACIÓN' then 3  else 0 end as tipo_poder,
             case when rl.tipo_poder='PODER' then rl.documento_acta when rl.tipo_poder='ACTUALIZACIÓN' then mod.documento_acta  else null end as docto_poder
            from provider p 
            left join historico_certificados c on c.provider_id = p.provider_id and c.tipo = 'CERTIFICADO' and c.provider_type = 'bys'
            left join usuarios u on u.user_id = p.user_id
            left join provider.rfc r on r.provider_id = p.provider_id
            left join provider.ubicacion d on d.provider_id = p.provider_id and d.activo is true and d.tipo = 'DOMICILIO FISCAL'
            left join provider.id_oficial o on o.provider_id = p.provider_id
            left join provider.representante_legal rl on rl.provider_id = p.provider_id and rl.activo is true and rl.rep_bys is true
            left join cat_municipios mun on mun.municipio_id = d.city_fiscal
            left join cat_entidades ent on ent.entidad_id = d.state_fiscal
            left join cat_asentamientos asen on asen.asentamiento_id = d.colonia_fiscal
            left join provider.curp curp on curp.provider_id = p.provider_id
            left join provider.escritura_publica esc on esc.provider_id = p.provider_id
            left join provider.modificacion_acta mod on mod.provider_id = p.provider_id
            where p.rfc =   :qp1  
            
        ",[':qp1'=>$rfc])->queryOne();

        if(!$model)
            return ['status'=>true,'data'=>false];
        return ['status'=>true,'data'=>$model];
    }

    public function actionConcursoinfo($id=null){
        if (!$id)
            return $this->disabled();
        return [
            'status'=>true,
            'data'=>RegistroConcursos::findOne($id)
        ];
    }

    public function actionColonias($estado=null,$municipio=null){
        if(!$estado || !$municipio)
            return ['status'=>false,'msg'=>'YouNeedSendAllParameters'];
        $data  = CatAsentamientos::find()->select(['asentamiento_id', 'nombre'])
            ->where(['and',['entidad_id' =>$estado],['municipio_id' => $municipio]])
            ->asArray()
            ->orderBy(['nombre'=>'ASC'])->all();

        return ['status'=> true, 'data'=>$data];
    }

    public function actionMunicipios($estado=null){
        if(!$estado)
            return ['status'=>false,'msg'=>'YouNeedSendAllParameters'];
        $data  = CatMunicipios::find()->select(['municipio_id', 'nombre'])
            ->where(['entidad_id' =>$estado])->asArray()
            ->orderBy(['nombre'=>'ASC'])->all();

        return ['status'=> true, 'data'=>$data];
    }

    public function actionGuardaconcurso(){
        $data = Yii::$app->request->post();
        if(!isset($data['rfc']) || !isset($data['concurso_id']) || !isset($data['data']))
            return ['status'=>false,'msg'=>'YouNeedSendAllParameters'];
        $provider = Provider::find()->where(['rfc'=>$data['rfc']])->one();
        if(!$provider)
            return ['status'=>false,'msg'=>'DataNotfoundP'];
        $model = ProviderConcurso::find()
            ->where(['provider_id'=>$provider->provider_id,'concurso_id'=>$data['concurso_id']])
            ->one();
        if(!$model)
            return ['status'=>false,'msg'=>'DataNotfoundC'];
        $model->data_api = $data['data'];
        $model->last_updated_at = date('Y-m-d H:i:s');
        $model->save(false);
        $concurso = ProviderConcurso::findOne($data['concurso_id']);
        $concurso->activo = false;
        $concurso->save(false);
        return ['status'=>true];
    }

    public function actionSession(){
        header("Access-Control-Allow-Origin: *");
        //header("Access-Control-Allow-Credentials: true");

        if (!isset($_COOKIE['_loginUser'])) {
            return ['status' => false,'message' => 'Sesión no activa'];
        }else{
            return ['status' => true,'message' => 'Sesión activa'];
        }
    }

    public function actionInfoprovider($rfc=null){
        if(!$rfc)
            return ['status'=>true,'data'=>false];
        $provider = Provider::find()->where(['rfc'=>strtoupper($rfc)])->one();
        if(!$provider)
            return ['status'=>true,'data'=>false];
        return ['status'=>true,'data'=>['id'=>$provider->provider_id,'rfc'=>$provider->rfc,'status'=>$provider->permanently_disabled,'nombre_razon_social'=>$provider->fullName]];
    }


    //api de consulta de datos generales de proveedor
    public function actionProveedor($rfc=false,$validado=false){
        $return = ['status'=>true,'data'=>[],'msg'=>false];
        $provider['data_proveedor'] = Provider::find()
            ->where(['and',['rfc'=>$rfc],['enabled'=>true]])
            ->asArray()->one();
        if(!$rfc || !$provider){
            $return['data'] = false;
            $return['msg'] = 'Proveedor inexistente o no activo en la plataforma';
            return $return;
        }
        $id = $provider['data_proveedor']['provider_id'];
        $provider['data_usuario'] = Usuarios::find()
            ->where(['user_id'=>$provider['data_proveedor']['user_id']])
            ->asArray()->all();
        $provider['data_perfil'] = Perfil::find()
            ->where(['provider_id'=>$id])
            ->asArray()->all();
        $provider['data_curriculum'] = Curriculum::find()
            ->where(['provider_id'=>$id])
            ->asArray()->all();

        if($provider['data_proveedor']['tipo_persona']=='Persona moral'){
            $provider['data_acta'] = ActaConstitutiva::find()
                ->where(['provider_id'=>$id])
                ->asArray()->all();
            $provider['data_modificacionActa'] = ModificacionActa::find()
                ->where(['and',['provider_id'=>$id],['activo'=>true]])
                ->asArray()->all();
            $provider['data_representanteLegal'] = RepresentanteLegal::find()
                ->where(['and',['provider_id'=>$id],['activo'=>true]])
                ->asArray()->all();
            $provider['data_accionistas'] = RelacionAccionistas::find()
                ->where(['and',['provider_id'=>$id],['activo'=>true]])
                ->asArray()->all();
        }else{
            $provider['data_curp'] = Curp::find()
                ->where(['provider_id'=>$id])
                ->asArray()->all();
            $provider['data_identificacion'] = IdOficial::find()
                ->where(['provider_id'=>$id])
                ->asArray()->all();
        }
        $provider['data_productos'] = ProviderGiro::find()
            ->where(['and',['provider_id'=>$id],['active'=>true]])
            ->asArray()->all();
        $provider['data_actividadEconomica'] = Giro::find()
            ->where(['and',['provider_id'=>$id],['active'=>true]])
            ->asArray()->all();
        $provider['data_clientes'] = ClientesContratos::find()
            ->where(['and',['provider_id'=>$id],['activo'=>true]])
            ->asArray()->all();
        $provider['data_certificaciones'] = Certificacion::find()
            ->where(['and',['provider_id'=>$id],['activo'=>true]])
            ->asArray()->all();

        $provider['data_ubicacion'] = Ubicacion::find()
            ->where(['and',['provider_id'=>$id],['activo'=>true]])
            ->asArray()->all();
        $provider['data_bancos'] = IntervencionBancaria::find()
            ->where(['provider_id'=>$id])
            ->asArray()->all();
        $provider['data_certificados'] = HistoricoCertificados::find()
            ->where(['provider_id'=>$id])
            ->asArray()->all();




        $return['data'] = $provider;
        return $return;
    }

    public function actionNotificacionescontratos($rfc=null, $n_requisicion=null, $status=null, $mode=null, $oc=null){
        $data = Provider::find()->select(['email', 'rfc', 'user_id'])->where(['rfc'=>$rfc])->one();

        if(!$data){

            \Yii::$app->response->format = \yii\web\Response::FORMAT_JSON;

            return [
                'status' => false,'message' => 'no hay registro'
            ];
        }else{
            $email = $data->email;
            $subject_email = 'Información';
            $message = [
                'n_requisicion' => $n_requisicion,
                'status' => $status,
                'oc' => $oc,
                'mode' => $mode,
            ];
            if ($status === 'recepcion'  ) {                                 //. 1 recepcion y primer_envio
                if ($mode === 'primer_envio'){            
                    GeneralController::pushNotification($data->user_id, 
                    "La Orden de Compra " . $message['oc'] . " ya está disponible para recolección en ventanilla de Biblioteca Central.");
                }else{                                                       //. 1 recepcion y recurrrente
                    GeneralController::pushNotification($data->user_id,     
                    "Te recordamos que la Orden de Compra " . $message['oc'] . " ya está disponible para recolección en ventanilla de Biblioteca Central.");
                }
                GeneralController::sendEmail(
                    '/provider/correos/notificaciones_recepcion', 
                    null, 
                    $email, 
                    'Notificación de Recepción', 
                    ['message' => $message]
                );

            } elseif($status === 'proveedor') {                             //. 1 proveedor
                GeneralController::sendEmail(
                    '/provider/correos/notificaciones_proveedor', 
                    null, 
                    $email, 
                    'Notificación para Proveedor', 
                    ['message' => $message]
                );
                
                GeneralController::pushNotification($data->user_id, 
                "La Orden de Compra " . $message['oc'] . " de la Requisicion "  . $message['n_requisicion'] . " ha sido entregada.");
            }
            //var_dump($sm).exit();

            return ['status' => true,'message' => 'success'];
        }
    }
}
