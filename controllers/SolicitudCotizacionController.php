<?php

namespace app\controllers;

use app\models\CondicionesPago;
use app\models\Requisicion;
use app\models\RequisicionProducto;
use app\models\RequisicionStatus;
use app\models\SolicitudCotizacionQuery;
use app\models\SolicitudDetalle;
use app\models\SolicitudStatus;
use app\models\Usuarios;
use Yii;
use app\models\SolicitudCotizacion;
use app\models\SolicitudCotizacionSearch;
use yii\helpers\ArrayHelper;
use yii\web\Controller;
use yii\web\NotFoundHttpException;
use yii\filters\VerbFilter;
use app\helpers\GeneralController;

/**
 * SolicitudCotizacionController implements the CRUD actions for SolicitudCotizacion model.
 */
class SolicitudCotizacionController extends GeneralController
{
    /**
     * @inheritdoc
     */
    public function behaviors()
    {
        return [
            'verbs' => [
                'class' => VerbFilter::className(),
                'actions' => [
                    'delete' => ['POST'],
                ],
            ],
        ];
    }
    /**
     * Lists all SolicitudCotizacion models.
     * @return mixed
     */
    public function actionIndex()
    {
        $searchModel = new SolicitudCotizacionSearch();
        $dataProvider = $searchModel->search(Yii::$app->request->queryParams);
        $rechazadas_provider = $searchModel->search(Yii::$app->request->queryParams,'rechazadasProviderFilter');
        $etiquetas = [
            'admin' => 'Por cotizar',
            'provider' => 'Por cotizar',
            'tab_id' => 'por_cotizar'
        ];
        $id = Yii::$app->user->can(Usuarios::ROLE_PROVIDER) ? 'provider' :
            'admin';
        $porcoti = [
            'admin' => 'Rechazadas',
            'rechazadas' => 'Rechazadas',
            'tab_id' => 'rechazadas'
        ];
        $id_porcoti = Yii::$app->user->can(Usuarios::ROLE_PROVIDER) ? 'rechazadas' :
            'admin';
        return $this->render('index', [
            'searchModel' => $searchModel,
            'dataProvider' => $dataProvider,
            'rechazadas_provider' => $rechazadas_provider,
            'id' => $id,
            'etiquetas' => $etiquetas,
            'porcoti' => $porcoti,
            'id_porcoti' => $id_porcoti

        ]);
    }

    /**
     * Displays a single SolicitudCotizacion model.
     * @param string $id
     * @return mixed
     */
    public function actionView($id)
    {
        $detalle_soli = SolicitudCotizacion::find()->detalle_solicitud($id);
        $solicitud_cotizacion = SolicitudCotizacion::find()->solicitud_cotizacion($id);
        $condiciones_pago = ArrayHelper::map(CondicionesPago::find()->select(['condiciones_pago_id','descripcion'])
                            ->asArray()->all(),'condiciones_pago_id','descripcion');
        return $this->render('view', [
            'model' => $this->findModel($id),
            'detalle_soli' =>$detalle_soli,
            'condiciones_pago' => $condiciones_pago,
            'solicitud_cotizacion' => $solicitud_cotizacion
        ]);
    }

    /**
     * Creates a new SolicitudCotizacion model.
     * If creation is successful, the browser will be redirected to the 'view' page.
     * @return mixed
     */
    public function actionCreate($requi_id,$tipo_adjudicacion)
    {
        $model = [new SolicitudCotizacion()];
        $model_detalle = [new SolicitudDetalle()];
        if (Yii::$app->request->post())
        {
                if($tipo_adjudicacion == 'TRES COTIZACIONES' && count(Yii::$app->request->post()['proveedor'])<3){

                    Yii::$app->session->setFlash('error', 'Debe seleccionar minimo 3 proveedores.');

                    return Yii::$app->getResponse()->redirect(['../requisicion/solicitar-cotizacion?id='.$requi_id.'&tipo_adjudicacion='.$tipo_adjudicacion]);
                }
                $model = [];
                $model_detalle= [];
                $trans = Yii::$app->db->beginTransaction();
                $array_data = Yii::$app->request->post()['proveedor'];
                $cond_pago = Yii::$app->request->post()['condiciones_pago_id'];
                $vigencia = Yii::$app->request->post()['vigencia_cotizacion'];
                $productos = RequisicionProducto::find()->getRequisicionProductoId($requi_id);
                $id_neg = Yii::$app->user->id;
                foreach($array_data as $key=>$data){
                    $model_new = new SolicitudCotizacion();
                    $model_new->requisicion_id = $requi_id;
                    $model_new->provider_id = $key;
                    $model_new->created_by = $id_neg;
                    $save = $model_new->save(false);
                    array_push($model,$model_new);
                    if($save){
                        foreach($productos as $val){
                            $model_det = new SolicitudDetalle();
                            $model_det->solicitud_id = $model_new->solicitud_id;
                            $model_det->requisicion_producto_id = $val;
                            $saved = $model_det->save(false);
                            array_push($model_detalle,$model_det);
                        }
                    }
                }
                if($saved){
                    $factual = date("Y-m-d H:i:s");
                    $requisicion = Requisicion::findOne($requi_id);
                    $requisicion->status = Requisicion::PENDIENTE_COT;
                    $requisicion->neg_cotizacion_date = $factual;
                    $requisicion->vigencia_requisicion = $vigencia;
                    $requisicion->condiciones_pago_id = $cond_pago;
                    $requisicion->update();
                    $trans->commit();
                    return $this->redirect(['../requisicion/index-negociador']);
                }
                $trans->rollBack();

        }else{
            return $this->redirect(['../requisicion/index-negociador']);
        }
    }
    /**
     * Updates an existing SolicitudCotizacion model.
     * If update is successful, the browser will be redirected to the 'view' page.
     * @param string $id
     * @return mixed
     */

    public function actionCuadroComparativo($id){
        $model_status = new RequisicionStatus();
        $requisicion = SolicitudCotizacion::find()->tipo_requisicion($id);
        $providers =SolicitudCotizacion::find()->pro_cotizacion($id);
        $detalle = SolicitudCotizacion::find()->detalle_coti($id);
        $motivo_rechazo = SolicitudCotizacion::find()->getMotivoRechazado($id);
        $menorPrecio = SolicitudCotizacion::find()->getProductosMenorPrecio($id);
        $prod_ganador = SolicitudCotizacion::find()->getProductosGanadores($id);
        return $this->render('cuadro-comparativo', [
            'requisicion' => $requisicion,
            'providers' => $providers,
            'detalle' => $detalle,
            'model_status' => $model_status,
            'motivo_rechazo' => $motivo_rechazo,
            'menorPrecio' => $menorPrecio,
            'prod_ganador' => $prod_ganador

        ]);
    }

    public function actionCotizacion($id){
        $solicitud = SolicitudCotizacion::find()->getSolicitud($id);
        $solicitud_detalle = SolicitudCotizacion::find()->detalle_por_cotizacion($id);
        $model_status = new SolicitudStatus();
        return $this->render('cotizacion', [
            'solicitud' => $solicitud,
            'model_status' => $model_status,
            'solicitud_detalle' => $solicitud_detalle
        ]);
    }

    public function actionUpdate($id)
    {
        $model = $this->findModel($id);

        if ($model->load(Yii::$app->request->post()) && $model->save()) {
            return $this->redirect(['view', 'id' => $model->solicitud_id]);
        } else {
            return $this->render('update', [
                'model' => $model,
            ]);
        }
    }


    public function actionValidar_negociador($solicitud_id)
    {
        $factual = date("Y-m-d H:i:s");
        $id = Yii::$app->user->id;
        $solicitud = SolicitudCotizacion::findOne($solicitud_id);
        $solicitud->validacion_date = $factual;
        $solicitud->status = SolicitudCotizacion::VALIDADA;
        if ($solicitud->update()) {
            return $this->redirect(['../requisicion/index-negociador']);
        }
    }

    public function actionValidar_director_material($requi_id)
    {
        $total_requi = Requisicion::find()->select('total')->where(['requisicion_id' => $requi_id])->one()->total;
        $factual = date("Y-m-d H:i:s");
        $id = Yii::$app->user->id;
        $requisicion = Requisicion::findOne($requi_id);
        $requisicion->validador_coordinador_id = $id;
        $requisicion->valid_coordinador_date = $factual;
        $requisicion->status = $total_requi<=(Requisicion::CUOTA * 2000)?Requisicion::ORDEN_COMPRA:Requisicion::VALIDAR_DIRECTOR_GENERAL;
        if ($requisicion->save()) {
            if($requisicion->status == Requisicion::ORDEN_COMPRA){
                return $this->redirect(['../compra/compra', 'id' => $requi_id,'puesto'=>'Director material']);
            }else{
                return $this->redirect(['../requisicion/index-director']);
            }

        }
    }

    public function actionValidar_director_servicio($requi_id)
    {
        $total_requi = Requisicion::find()->select('total')->where(['requisicion_id' => $requi_id])->one()->total;
        $factual = date("Y-m-d H:i:s");
        $id = Yii::$app->user->id;
        $requisicion = Requisicion::findOne($requi_id);
        $requisicion->validador_coordinador_id = $id;
        $requisicion->valid_coordinador_date = $factual;
        $requisicion->status = $total_requi<=(Requisicion::CUOTA * 2000)?Requisicion::ORDEN_COMPRA:Requisicion::VALIDAR_DIRECTOR_GENERAL;
        if ($requisicion->update()) {
            if($requisicion->status == Requisicion::ORDEN_COMPRA){
                return $this->redirect(['../compra/compra', 'id' => $requi_id,'puesto'=>'Director servicio']);
            }else{
                return $this->redirect(['../requisicion/index-director']);
            }

        }
    }

    public function actionValidar_director_general($requi_id)
    {
        $factual = date("Y-m-d H:i:s");
        $id = Yii::$app->user->id;
        $requisicion = Requisicion::findOne($requi_id);
        $requisicion->validador_director_id = $id;
        $requisicion->valid_director_date = $factual;
        $requisicion->status = Requisicion::ORDEN_COMPRA;
        if ($requisicion->update()) {
            return $this->redirect(['../compra/compra', 'id' => $requi_id,'puesto'=>'director general']);
        }
    }

    /**
     * Deletes an existing SolicitudCotizacion model.
     * If deletion is successful, the browser will be redirected to the 'index' page.
     * @param string $id
     * @return mixed
     */
    public function actionDelete($id)
    {
        $this->findModel($id)->delete();

        return $this->redirect(['index']);
    }

    /**
     * Finds the SolicitudCotizacion model based on its primary key value.
     * If the model is not found, a 404 HTTP exception will be thrown.
     * @param string $id
     * @return SolicitudCotizacion the loaded model
     * @throws NotFoundHttpException if the model cannot be found
     */
    protected function findModel($id)
    {
        $model = Requisicion::find()->where("requisicion_id in (SELECT requisicion_id from solicitud_cotizacion WHERE solicitud_id = $id)")->one();
        if ($model !== null) {
            return $model;

        } else {
            throw new NotFoundHttpException('The requested page does not exist.');
        }
    }
}
