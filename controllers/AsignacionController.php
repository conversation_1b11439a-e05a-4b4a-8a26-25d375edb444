<?php

namespace app\controllers;

use app\custom\Module;
use app\helpers\GeneralController;
use app\models\Asignacion;
use yii\data\ActiveDataProvider;
use yii\filters\VerbFilter;
use app\models\Provider;
use app\helpers\BysController;
use app\models\AsignacionSearch;
use Yii;
use yii\web\Controller;

/**
 * IntervencionBancariaController implements the CRUD actions for IntervencionBancaria model.
 */
class AsignacionController extends GeneralController
{
    /**
     * @inheritdoc
     */
    public function behaviors()
    {
        return [
            'verbs' => [
                'class' => VerbFilter::className(),
                'actions' => [
                    'delete' => ['POST', 'GET'],
                ],
            ],
        ];
    }


    public function actionIndex()
    {
        $searchModel = new AsignacionSearch();
        $dataProvider = $searchModel->search(Yii::$app->request->queryParams);

        return $this->render('index',[
            'dataProvider'=>$dataProvider,
            'searchModel' => $searchModel,
        ]);
    }

    public function actionPrioridad($id=null){

        $model = Asignacion::find()->where(['and',['id_proveedor'=>$id],['activo'=>true]])->one();
        if($model) {
            $model->prioridad = true;
            $model->save();
        }
        return $this->redirect('/provider/index-admin-provider');
    }

    public function modelName()
    {
        return Asignacion::className();
    }

    public function searchName()
    {
        return Asignacion::className();
    }
}
