<?php

namespace app\controllers;

use app\models\AdqAdquisicion;
use yii\web\Controller;
use Yii;

class ContratosAbiertosController extends Controller
{
    public function actions()
    {
        return [
            'captcha' => [
                'class' => 'yii\captcha\CaptchaAction',
                'fixedVerifyCode' => YII_ENV_TEST ? 'testme' : null,
            ],
        ];
    }

    public function actionIndex(){
        $this->layout = 'main';
        $adquisicion = new AdqAdquisicion();
        $year = date("Y");
        $dataProvider = $adquisicion->getAdquisicionByYear(2017);
        $totaldependencias = $adquisicion->getCountTotalDependencias();
        $totalProveedores = $adquisicion->getCountTotalProveedores();

        return $this->render('index', [
            'sample' => 'data',
            'dataProvider' => $dataProvider,
            'totalDependenias' => $totaldependencias,
            'totalProveedores' => $totalProveedores
        ]);
    }

    /**
     * @param string $id
     * @return mixed
     */
    public function actionView($id)
    {
        $adquisicion = new AdqAdquisicion();
        $planification = $adquisicion->getPlanificationStageById($id);
        $tender = $adquisicion->getTenderStageById($id);
        $tenders = $adquisicion->getTendersByAquisicionId($id);
        $award = $adquisicion->getAwardsStageById($id);

        return $this->render('view', [
            'planification' => $planification,
            'tender' => $tender,
            'tenders' => $tenders,
            'award' => $award
        ]);
    }

    public function actionList_total_adquisiciones(){
        $adquisicion = new AdqAdquisicion();
        $year = date("Y");
        $totalAdquisiciones = $adquisicion->getAllAdquisicionByYear(2017);
        return json_encode($totalAdquisiciones);
    }

}