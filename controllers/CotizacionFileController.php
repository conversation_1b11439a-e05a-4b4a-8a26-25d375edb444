<?php
namespace app\controllers;

use app\models\CotizacionFile;
use Yii;
use yii\web\Controller;
use yii\web\UploadedFile;

class CotizacionFileController extends Controller
{
    public function actionSubir()
    {
        $model = new CotizacionFile();
        $dir = 'cotizacion_file/';
        if (!is_dir($dir)) {
            mkdir($dir);
        }

        if (Yii::$app->request->isPost) {
            $model->archivo = UploadedFile::getInstance($model, 'archivo');
            $url = $model->upload();
            if ($url != false) {
                return $url;
            } else {
                return false;
            }
        }
    }
}