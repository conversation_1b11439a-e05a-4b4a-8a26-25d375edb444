<?php

namespace app\controllers;


use app\helpers\GeneralController;
use app\models\Authentication;
use app\models\CodeAuthentication;
use app\models\Concursos;
use app\models\ConnectionHistory;
use app\models\ErrorInesperado;
use app\models\HistoricoCertificados;
use app\models\NumberNotification;
use app\models\ProvProveedor;
use app\models\Sanciones;
use app\models\SaveNotification;
use app\models\Ubicacion;
use app\models\Usuarios;
use app\models\VerifyUserSecop;
use kartik\form\ActiveForm;
use Yii;
use yii\data\ActiveDataProvider;
use yii\data\Pagination;
use yii\filters\AccessControl;
use yii\web\Controller;
use yii\filters\VerbFilter;
use app\models\LoginForm;
use app\models\ContactForm;
use app\models\Provider;
use app\models\Token;
use yii\helpers\Url;
use app\models\ListEmail;
use app\models\ConcursosSearch;
use app\models\RegistroConcursos;
use app\models\RegistroConcursosSearch;
use Exception;
use yii\db\Expression;
use yii\helpers\ArrayHelper;
use yii\web\Response;
use yii\web\UploadedFile;

class SiteController extends Controller
{
    public function behaviors()
    {
        return [
            'access' => [
                'class' => AccessControl::className(),
                'only' => ['logout'],
                'rules' => [
                    [
                        'actions' => ['logout'],
                        'allow' => true,
                        'roles' => ['@'],
                    ],
                ],
            ],
            'verbs' => [
                'class' => VerbFilter::className(),
                'actions' => [
                    'logout' => ['post', 'get'],
                ],
            ],
        ];
    }


    public function beforeAction($action)
    {
        if (\Yii::$app->user->isGuest) {
            $excluidos = array( 'site/configuration','site/sessions','site/change-validation','site/activation-authentication','site/change-password','site/numnotificaciones','site/num-notificaciones-reset',
                'site/notificaciones','site/view-notificaciones','site/viewed-ntf');
            if (in_array($action->controller->module->requestedRoute, $excluidos)) {
                return $this->redirect("/site/login")->send();
            }

        }
        return parent::beforeAction($action);
    }

    public function actions()
    {
        return [
            'captcha' => [
                'class' => 'yii\captcha\CaptchaAction',
                'fixedVerifyCode' => YII_ENV_TEST ? 'testme' : null,
            ],
        ];
    }

    public function actionActualizando(){
        $this->layout = false;
        return $this->render('actualizando');
    }

    /**
     * Verificar si un RFC existe en la base de datos
     */
    public function actionCheckRfc($rfc = 'OASH970823LB6')
    {
        $this->layout = false;

        $rfc = strtoupper(trim($rfc));
        $results = [];

        // Buscar en tabla usuarios
        $usuario = \app\models\Usuarios::find()->where(['rfc' => $rfc])->one();
        if ($usuario) {
            $results['usuarios'] = [
                'found' => true,
                'user_id' => $usuario->user_id,
                'username' => $usuario->username,
                'email' => $usuario->email,
                'status' => $usuario->status
            ];
        } else {
            $results['usuarios'] = ['found' => false];
        }

        // Buscar en tabla provider
        $provider = \app\models\Provider::find()->where(['rfc' => $rfc])->one();
        if ($provider) {
            $results['provider'] = [
                'found' => true,
                'provider_id' => $provider->provider_id,
                'user_id' => $provider->user_id,
                'name' => $provider->getFullName()
            ];
        } else {
            $results['provider'] = ['found' => false];
        }

        // Buscar en tabla rfc
        $rfcModel = \app\models\Rfc::find()->where(['rfc' => $rfc])->one();
        if ($rfcModel) {
            $providerFromRfc = \app\models\Provider::findOne($rfcModel->provider_id);
            $results['rfc_table'] = [
                'found' => true,
                'provider_id' => $rfcModel->provider_id,
                'user_id' => $providerFromRfc ? $providerFromRfc->user_id : null,
                'name' => $providerFromRfc ? $providerFromRfc->getFullName() : null
            ];
        } else {
            $results['rfc_table'] = ['found' => false];
        }

        return $this->asJson([
            'rfc' => $rfc,
            'results' => $results,
            'summary' => [
                'found_in_usuarios' => $results['usuarios']['found'],
                'found_in_provider' => $results['provider']['found'],
                'found_in_rfc_table' => $results['rfc_table']['found'],
                'can_login' => $results['usuarios']['found'] || $results['provider']['found'] || $results['rfc_table']['found']
            ]
        ]);
    }

    public function actionDataactividades($type=null){

        $dataExperiencia = \Yii::$app->db->createCommand("
        select cs.nombre as especialidad, count(1)
        from provider.experiencia e
        join provider.subespecialidad_category cs on cs.category_id = e.id_obra
        where status_op = 'VALIDADO' AND e.activo is TRUE 
        group by especialidad order by especialidad")->queryAll();

        return $dataExperiencia;

    }

    public function actionDataconcursos(){
        $concursos_site = RegistroConcursos::findAll(['activo' => 'true']);
        $concurso_gob = Concursos::getConcursosApi(5);
        $total_data = array_merge($concursos_site, $concurso_gob);
        usort( $total_data, function($a,$b){ 
            $date_comp_a = $a['tipo'] == 'CONCURSO' ? $a['fecha_fallo_definitivo'] : $a['fecha_limite_inscripcion'];
            $date_comp_b = $b['tipo'] == 'CONCURSO' ? $b['fecha_fallo_definitivo'] : $b['fecha_limite_inscripcion'];
            return strtotime($date_comp_b) - strtotime($date_comp_a);
        });

        $response = [ 'status'=>true, 'html'=>'' ];

        $str = '';
        if ( count($total_data) > 0 ){
            $str.='<div class="concursocont  owl-carousel owl-theme">';
            $i = 0;
            foreach($total_data as $concurso){
                $date_actual = time();
                $arr_estilos = ['clase' => 'color_iniciar', 'titulo' => 'Por Iniciar', 'imagen' => 'licitacion_iniciar'];
                //var_dump($concurso['tipo']);
                if (GeneralController::str_contains($concurso['tipo'], 'LICITACION' )) {
                    $imagenTipo = 'publica';
                    if($concurso['tipo'] == 'LICITACION_NA'){ $tipo_texto =  "Licitación pública nacional"; }
                    if($concurso['tipo'] == 'LICITACION_INT'){ $tipo_texto =  "Licitación pública internacional"; }
                    if($concurso['tipo'] == 'LICITACION_INT_AB'){ $tipo_texto =  "Licitación pública internacional abierta"; }
                    $date_inicio = strtotime($concurso['fecha_inicio_inscripcion']);
                    $date_fin = strtotime($concurso['fecha_limite_inscripcion']);
                    $date_adj = strtotime($concurso['fecha_h_fallo_definitivo']);
                    if($date_actual >= $date_inicio && $date_actual <= $date_fin){
                        $arr_estilos['clase'] = 'color_abierta';
                        $arr_estilos['titulo'] = 'Abierta';
                        $arr_estilos['imagen'] = 'licitacion_abierta';
                    } else if($date_actual > $date_fin && $date_actual <= $date_adj){
                        $arr_estilos['clase'] = 'color_proceso';
                        $arr_estilos['titulo'] = 'En Proceso';
                        $arr_estilos['imagen'] = 'licitacion_proceso';
                    }else if ($date_actual > $date_adj){
                        $arr_estilos['clase'] = 'color_adjudicada';
                        $arr_estilos['titulo'] = 'Adjudicada';
                        $arr_estilos['imagen'] = 'licitacion_adjudicada';
                    }
                } else if ($concurso['tipo'] == 'CONCURSO') {
                    $imagenTipo = 'restringida';
                    $tipo_texto = 'Concurso por invitación restringida';
                    $date_inicio = strtotime($concurso['fecha_apertura_propuestas']);
                    $date_fin = strtotime($concurso['fecha_fallo_definitivo']);
                    if($date_actual >= $date_inicio && $date_actual <= $date_fin){
                        $arr_estilos['clase'] = 'color_abierta';
                        $arr_estilos['titulo'] = 'Abierta';
                        $arr_estilos['imagen'] = 'licitacion_abierta';
                    } else if ($date_actual > $date_fin){
                        $arr_estilos['clase'] = 'color_adjudicada';
                        $arr_estilos['titulo'] = 'Adjudicada';
                        $arr_estilos['imagen'] = 'licitacion_adjudicada';
                    }
                } else {
                    $imagenTipo = 'subasta';
                    $tipo_texto = 'Subasta electrónica inversa';
                    $date_inicio = strtotime($concurso['fecha_inicio_inscripcion']);
                    $date_fin = strtotime($concurso['fecha_limite_inscripcion']);
                    $date_adj = strtotime($concurso['fecha_h_fallo_definitivo']);
                    if($date_actual >= $date_inicio && $date_actual <= $date_fin){
                        $arr_estilos['clase'] = 'color_abierta';
                        $arr_estilos['titulo'] = 'Abierta';
                        $arr_estilos['imagen'] = 'licitacion_abierta';
                    } else if($date_actual > $date_fin && $date_actual <= $date_adj){
                        $arr_estilos['clase'] = 'color_proceso';
                        $arr_estilos['titulo'] = 'En Proceso';
                        $arr_estilos['imagen'] = 'licitacion_proceso';
                    }else if ($date_actual > $date_adj){
                        $arr_estilos['clase'] = 'color_adjudicada';
                        $arr_estilos['titulo'] = 'Adjudicada';
                        $arr_estilos['imagen'] = 'licitacion_adjudicada';
                    }
                }

                $url_visor = $concurso instanceof RegistroConcursos ? "/concursos/concurso/viewhome?id=".$concurso['concurso_id'] : "/concursos/concurso/view-gob?concurso_id=".$concurso['concurso_id'];
                $fecha =  $concurso['tipo'] == 'CONCURSO' ? $concurso['fecha_fallo_definitivo'] : $concurso['fecha_limite_inscripcion'];
                $display_fecha = $fecha != null ? date_format(date_create($fecha), 'd/m/Y H:i') : "(No definido)";
  
                $str .= '
                    <a  data-aos="fade-up" data-aos-delay="'.($i * 350).'" data-aos-duration="1000"  href="javascript:void(0)" value="'.$url_visor.'" title="'.$concurso['numero_proceso'].'" class="showModalButton" data-pjax="0">
                            <div class="gradient-box">

                                <div class="area_textoconcu">
                                    <div class="tituloconcu ">
                                        '.$concurso['numero_proceso'].'
                                    </div>
                                    <div class="descripcionconcu">
                                        '. $concurso['descripcion'] .'
                                    </div>
                                    <span class="icono_conc"> <br>
                                        <img src="../imgs/'.$imagenTipo.'.png">'.$tipo_texto.'
                                    </span>
                                </div>
                                <div class="area_status">
                                    <div class="img_status">
                                        '. GeneralController::GetSVG($arr_estilos["imagen"],'iconos/') .'
                                    </div>
                                    <div class="cont_status ">
                                        <div class="texto_status '. $arr_estilos['clase'] .'">'. $arr_estilos['titulo'] .'</div>
                                        <div class="fechaconc ">
                                            Finaliza <br> '. $display_fecha.'
                                            <br>
                                        </div>
                                    </div>
                                </div>
                            </div>
                    </a>
                ';

                $i++; if($i==8) break;
            }
        }else{
            $str.= '<div class="sin_concursos"> <div class="sin_concurso_mensaje">No hay concursos disponibles. </div></div> ';
        }

        $str.= '</div><script>AOS.init(); </script>';
        $response['html'] = $str;

        return json_encode($response);

    }

    public function actionDataactualidad($sa=null){

        $ret = (object)[
            'status'=>true,
            'html'=>''
        ];

        $str = '';
        if($sa){
            //actualidad Administracion
            $news = GeneralController::solicitaDatosGet('https://nl.gob.mx/api/destacados/administracion');
            $str .= '<div class="noticias">';
            if(isset($news['nodes'])){
                foreach ($news['nodes'] as $index => $new) {
                    if($index < 3) {
                        $str .= '<a  href="https://nl.gob.mx'.(isset($new['node']['Ruta']) ? $new['node']['Ruta'] : '#') .'" target="_blank">';
                        $str .= '<div data-aos="zoom-in" data-aos-duration="500" class="noticia" >';
                        $str .= '<div class="not_imagen text-center '.(($index == 0) ? 'not_alto':'not_bajo').'">';
                        $str .= '<img src="'.(isset($new['node']['Imagen principal']['src']) ? $new['node']['Imagen principal']['src'] : '#' ).'" width="100%" alt="">';
                        $str .= '</div>';
                        $str .= '<div class="not_titulo">';
                        $str .= isset($new['node']['title']) ? $new['node']['title'] : '' ;
                        $str .= '</div>';

                        $str .= isset($new['node']['Subtitulo']) ?
                            '<div class="not_subtitulo">'
                            . $new['node']['Subtitulo'] .
                            '</div>'
                            : '';

                        if(isset($new['node']['Descripción'])){
                            $texto = substr($new['node']['Descripción'],0,566);
                            $count = strlen($new['node']['Descripción']);
                            $elipsis = $count >= 566 ? ' ...' : '';
                        }

                        $str .= isset($new['node']['Descripción']) ?
                            '<div class="not_descripcion">'
                            . $texto.$elipsis .
                            '</div>'
                            : '' ;

                        $str .= "</div>";
                        $str .= "</a>";
                    }
                }
            }
            $str .= "</div>";
        }else{
            //Actualidad movilidad
            $news = GeneralController::solicitaDatosGet('https://smpu.nl.gob.mx/siasi_ws/api/noticias/listar');


            $news = array_filter($news,function($row){ return isset($row['imagen']);}); //filtra solo los registros que tienen imagenes

            usort($news,function($a,$b){ return strtotime(trim($a['fechaCreacion'])) < strtotime(trim($b['fechaCreacion'])); });

            //var_dump(json_encode($news)).exit();
            $x = 0;
            $str .= '<div class="noticias">';
            if(isset($news)){
                foreach ($news as $index => $new) {
                    if($x++ < 4) {
                        $str .= '<a target="_blank" href="https://smpu.nl.gob.mx/web/noticias/'.$new['noticiaId'].'">';
                        $str .= '<div data-aos="zoom-in" data-aos-duration="500" class="noticia" >';
                        $str .= '<div class="not_imagen text-center '.(($index == -1) ? 'not_alto_op':'not_bajo_op').'">';
                        $str .= '<img src="https://smpu.nl.gob.mx'.(isset($new['imagen']) ? $new['imagen'] : $new['video'] ).'" width="90%" alt="">';
                        $str .= '</div>';
                        $str .= '<div class="not_titulo">';
                        $str .= isset($new['tituloNoticia']) ? $new['tituloNoticia'] : '' ;
                        $str .= '</div>';


                        if(isset($new['descripcion'])){
                            $texto = substr($new['descripcion'],0,200);
                            $count = strlen($new['descripcion']);
                            $elipsis = $count >= 200 ? ' ...' : '';
                        }

                        $str .= (isset($new['descripcion']) ?
                            '<div class="not_descripcion">'
                            . $texto.$elipsis .
                            '</div>'
                            : '' );

                        $str .= "</div>";
                        $str .= "</a>";
                    }else{
                        break;
                    }
                }
            }
            $str .= "</div>";
        }
        $str.= '</div><script>AOS.init();</script>';
        $ret->html = $str;
        return json_encode($ret);


    }

    public function actionError()
    {
        if(Yii::$app->user->isGuest){
            $this->layout = 'nomain';
        }else{
            $this->layout = 'home';
        }

        return $this->render('error',['name' => 'lo sentimos...','message' => 'pagina no encontrada (#404)']); // page not found

    }

    public function actionIndex()
    {
        if(Url::home('https')=='https://quecompra.nl.gob.mx/'){
            $model = new ListEmail();
            $this->layout = 'mainMail';
            return $this->render('/list-email/create', [
                'model' => $model,
            ]); // change to "comingsoon"
        }else {

            if (Yii::$app->user->isGuest) {
                //$searchModel = new ConcursosSearch();
                //$allData = array_merge($searchModel->searchConcursosTodos(null,'total'),Concursos::getAllDataOP(3));
                //usort($allData,function($a,$b){return strtotime($b['concurso_date_fin'])-strtotime($a['concurso_date_fin']);});

                //$news = GeneralController::solicitaDatosGet('https://nl.gob.mx/api/destacados/administracion');



                return $this->render('index', [
                    //'news'=>$news,
                    'obras' => $this->actionDataactividades(),
                    //'data' => array_filter($allData,function($val){return isset($val['visible'])?$val['visible']:true;})
                ]);
            } else {

                if (Yii::$app->user->can(Usuarios::ROLE_PROVIDER)) {
                    if (!Provider::registro_completo()) {
                        return $this->redirect(['/provider/create','tipoProvider' => Yii::$app->user->identity->tipo]);
                    } else {
                        return $this->redirect(['/provider/dashboard']);
                    }
                } else if(Yii::$app->user->can(Usuarios::ROLE_VALIDADOR_PROVEEDORES)){
                    return $this->redirect(['/asignacion/index']);
                } else if(Yii::$app->user->can(Usuarios::ROLE_ADMIN_SECOP) || Yii::$app->user->can(Usuarios::ROLE_ADMIN_PROVIDER)  || Yii::$app->user->can(Usuarios::ROLE_ADMIN_CONTRATISTAS) || Yii::$app->user->identity->role == Usuarios::ROLE_DIOS){
                    return $this->redirect(['/usuarios/index']);
                } else if(Yii::$app->user->can(Usuarios::ROLE_USUARIO_CONSULTA_BYS)){
                    return $this->redirect(['/provider/index-detalle-provider-permiso']);
                } else if(Yii::$app->user->can(Usuarios::ROLE_METODO_PAGO)){
                    return $this->redirect(['/banco/index']);
                } else if(Yii::$app->user->can(Usuarios::ROLE_VALIDADOR_CONTRATISTAS)){
                    return $this->redirect(['/legales/index-validador']);
                } else if(Yii::$app->user->can(Usuarios::ROLE_USUARIO_CONSULTA_BYS)){
                    return $this->redirect(['/provider/index-detalle-provider-permiso']);
                } else if(Yii::$app->user->can(Usuarios::ROLE_USUARIO_CONSULTA_OP)){
                    return $this->redirect(['/provider/index-detalle-provider-permiso-op']);
                } else if (Yii::$app->user->can(Usuarios::ROLE_ADMIN_CONCURSOS)) {
                    return $this->redirect(['/concursos/concurso/index']);
                }else if(Yii::$app->user->can(Usuarios::ROLE_ADMIN_PROVEE)){
                    return $this->redirect(['/evento/lista']);
                } else {
                    $this->layout = 'home';
                    echo $this->render('/site/error',['name' => 'Lo sentimos...','message' => 'Acceso denegado']);
                    exit();
                }
            }
        }
    }

    public function actionConcurso($num = 100, $tipo = 'total',$ajax=false){
        $this->layout = $ajax ? 'concursos' : 'main';
        return $this->render('concurso');
    }


    /**
     * @param $pdf
     * @return string
     */
    public function actionVisor($pdf,$ext_file=null)
    {

        if (Yii::$app->request->isAjax) {
            return $this->renderAjax('visor', [
                'pdf' => $pdf,
                'ext' => $ext_file
            ]);
        }

    }

    public function actionPlayer($url){
        return $this->renderAjax('player',[
            'url' => $url
        ]);
    }

    public function actionValidateCert(){

        $body = Yii::$app->request->getBodyParams();


        $err = new ErrorInesperado();
        $err->data = $body;
        $err->user_id = 1;
        $err->save();

    }

    public function actionInformacion()
    {

        $path = Url::previous('urlprev');

        return $this->redirect([$path . '#informacion']);

    }

    public function actionDocumentos()
    {
        $path = Url::previous('urlprev');

        return $this->redirect([$path . '#documentos']);

    }

    public function actionConcursos()
    {
        $path = Url::previous('urlprev');

        return $this->redirect([$path . '#concursos']);

    }

    public function actionEventos()
    {
        $path = Url::previous('urlprev');

        return $this->redirect([$path . '#eventos']);

    }

    public function actionInfoOld($id=83434){

        $provId = (Yii::$app->request->isAjax)?$id:$provId = Yii::$app->getRequest()->getQueryParam('id') ;

        if(!is_numeric($provId) or $provId == null){
            if(Yii::$app->request->isAjax){
                return $this->renderAjax('info',['info'=>'Proveedor no válido, favor de intentar nuevamente.']);
            }else{
                return $this->render('info',['info'=>'Proveedor no válido, favor de intentar nuevamente.']);
            }
        }
        $modelProv = new ProvProveedor();
        $nombreComercial = $modelProv->getNombrecomercial($provId);

        if($nombreComercial==null){
            if(Yii::$app->request->isAjax){
                return $this->renderAjax('info',['info'=>'No se encuentra el proveedor seleccionado, favor de intentar nuevamente.']);
            }else{
                return $this->render('info',['info'=>'No se encuentra el proveedor seleccionado, favor de intentar nuevamente.']);
            }
        }
        $datosContacto = $modelProv->getDatosContacto($provId);
        $montosAnuales = $modelProv->getMontosAnualesOld($provId);
        $ultimasAdjudicaciones = $modelProv->getUltimasAdjudicaciones($provId);

        if(Yii::$app->request->isAjax){
            return $this->renderAjax('info-old',[
                'nombreComercial' => $nombreComercial,
                'datosContacto' => $datosContacto,
                'montosAnuales'=> $montosAnuales,
                'ultimasAdjudicaciones' => $ultimasAdjudicaciones
            ]);
        }else{
            return $this->render('info-old',[
                'nombreComercial' => $nombreComercial,
                'datosContacto' => $datosContacto,
                'montosAnuales'=> $montosAnuales,
                'ultimasAdjudicaciones' => $ultimasAdjudicaciones
            ]);
        }


    }

    public function actionInfo($id=0){

        $provId = (Yii::$app->request->isAjax)?$id:$provId = Yii::$app->getRequest()->getQueryParam('id') ;

        if(!is_numeric($provId) or $provId == null){
            if(Yii::$app->request->isAjax){
                return $this->renderAjax('info',['info'=>'Proveedor no válido, favor de intentar nuevamente.']);
            }else{
                return $this->render('info',['info'=>'Proveedor no válido, favor de intentar nuevamente.']);
            }
        }
        $modelProv = new ProvProveedor();
        $nombreComercial = $modelProv->getNombrecomercial($provId);

        if($nombreComercial==null){
            if(Yii::$app->request->isAjax){
                return $this->renderAjax('info',['info'=>'No se encuentra el proveedor seleccionado, favor de intentar nuevamente.']);
            }else{
                return $this->render('info',['info'=>'No se encuentra el proveedor seleccionado, favor de intentar nuevamente.']);
            }
        }
        $datosContacto = $modelProv->getDatosContacto($provId);
        $montosAnuales = $modelProv->getMontosAnuales($provId);
        $ultimasAdjudicaciones = $modelProv->getUltimasAdjudicaciones($provId);

        if(Yii::$app->request->isAjax){
            return $this->renderAjax('info',[
                'nombreComercial' => $nombreComercial,
                'datosContacto' => $datosContacto,
                'montosAnuales'=> $montosAnuales,
                'ultimasAdjudicaciones' => $ultimasAdjudicaciones
            ]);
        }else{
            return $this->render('info',[
                'nombreComercial' => $nombreComercial,
                'datosContacto' => $datosContacto,
                'montosAnuales'=> $montosAnuales,
                'ultimasAdjudicaciones' => $ultimasAdjudicaciones
            ]);
        }


    }

    public function actionInfo_local($id=null){

        if(!$id){
            $render = Yii::$app->request->isAjax ? 'renderAjax' : 'render' ;
            return $this->$render('info',['info'=>'Proveedor no válido, favor de intentar nuevamente.']);
        }

        $provider = Provider::findOne($id);
        $ubicacion = Ubicacion::findBySql("
            SELECT st_X(u.geo_ubicacion) as lat, st_y(u.geo_ubicacion) as lng,
            concat_ws(' ',u.calle_fiscal, u.num_ext_fiscal,u.num_int_fiscal,'CP',u.cp_fiscal, a.nombre) as direccion,
            l.nombre as localidad, m.nombre as municipio, e.nombre as estado 
            from provider.ubicacion u 
            inner join cat_entidades e on e.entidad_id = u.state_fiscal 
            inner join cat_municipios m on m.municipio_id = u.city_fiscal
            inner join cat_asentamientos a on a.asentamiento_id = u.colonia_fiscal
            inner join cat_localidades l on l.localidad_id = u.localidad_id
            where u.provider_id = $id and u.tipo = 'DOMICILIO FISCAL' and u.activo is true")->asArray()->one();

        if( isset($ubicacion['direccion']) ){
            if(!empty($ubicacion['localidad'])){
                if($ubicacion['localidad'] == $ubicacion['municipio']){
                    $ubicacion['direccion'] .= ' '.$ubicacion['municipio'].' '.$ubicacion['estado'];
                }else{
                    $ubicacion['direccion'] .= ' '.$ubicacion['localidad'].' '.$ubicacion['municipio'].' '.$ubicacion['estado'];
                }
            }
        }

        return $this->renderAjax('info_local',[
            'provider' => $provider,
            'ubicacion' => $ubicacion,
        ]);

    }

    public function actionSancionados(){
        //$modelSancionados = new Sanciones();
        //$dataProv = Yii::$app->getRequest()->getQueryParam('prov');
        //$sancionados = $modelSancionados->listaSancionados($dataProv);

        if(Yii::$app->request->get('api')){
            $this->layout = 'concursos';
        }

        return $this->render('sancionados', [
                'sancionados' => Sanciones::find()->where(['vigente' => true])->all()
            ]
        );
    }

    public function actionInfosan ($id=0){

        return $this->renderAjax('infoSancionados',
            ['model' => Sanciones::findOne($id)]
        );
    }

    /**
     * @return string
     */
    public function actionFind($name=null){

        //echo $this->render('/site/error',['name' => 'Lo sentimos...','message' => 'Acceso denegado']);
        /* $dataProv = Yii::$app->getRequest()->getQueryParam('id');
        if($dataProv==null){
            $dataProv = 'a';
        } */
        /* Busqueda SECOP
        $modelProv = new ProvProveedor();
        $provs = $modelProv->getProvs($dataProv);
        */

        $proveedores = Provider::find()
        ->orFilterWhere(['ilike', 'name_razon_social', $name])
        ->orFilterWhere(['ilike', 'rfc', $name])
        ->orFilterWhere(['ilike', 'name_comercial', $name])
        ->orFilterWhere(['ilike', "concat_ws(' ',pf_nombre,pf_ap_paterno,pf_ap_materno)", $name])
        ->andWhere(['not',['Clave_ProveedorSire' => null]])->andWhere(['not',['permanently_disabled' => Provider::STATUS_EN_PROCESO]])->limit(20)->asArray()->all();

        /* $id=strtoupper($id);
        $provs = Provider::find()
            ->where(['or',['like','upper(name_razon_social)',$id],['like','rfc',$id],['like','upper(name_comercial)',$id]])->limit(20)
            ->asArray()->all(); */

        return $this->render('find', [ 'proveedores'=>$proveedores ]);
    }

    public function actionLogin($token=null)
    {
        /* if((!$token || (strpos($token,'tec123')===false)) && ($_SERVER['SERVER_NAME'] == 'beta.proveedores.nl.gob.mx')){
            Yii::$app->session->setFlash('error', 'El sitio se encuentra en versión beta.');
            return $this->redirect(['/site/index']);
        } */



        if (!Yii::$app->user->isGuest) {
            return $this->goHome();
        }

        $fecha =date('Y-m-d');
        $tmp = explode('-',$fecha);
        $fechahoy =mktime(0,0,0,$tmp[1],$tmp[2],$tmp[0]);

        $model = new LoginForm();


        if(Yii::$app->request->isAjax && $model->load(Yii::$app->request->post())){
            Yii::$app->response->format = Response::FORMAT_JSON;
            $arr = [];

            $funcionario = isset($model->es_funcionario) ? filter_var($model->es_funcionario, FILTER_VALIDATE_BOOLEAN) : false;

            if(!$model->getUser($funcionario)){
                $arr['loginform-username'] = ['No pudimos encontrar tu usuario en el sistema'];
            }else if(!$model->verifyUserAndPass($funcionario)){
                $arr['loginform-password'] = ['La contraseña es incorrecta.'];
            }

            return $arr;
        }else if (($model->load(Yii::$app->request->post()) && $model->verifyUserAndPass($model->es_funcionario))||$model->isLoginFiel()) {

            if($model->save_browser == null){
                $userIdAuth = Yii::$app->db->createCommand(" SELECT a.active, u.user_id,u.email,p.tipo_provider,u.role FROM usuarios u 
                          left JOIN authentication a ON a.user_id=u.user_id
                          left JOIN provider p on p.user_id =  u.user_id 
                          WHERE u.username = :user",[':user' => $model->username])->queryOne();

                $cookie_value = null;
                if($userIdAuth['active'] && !isset($_COOKIE['_verify_login'])) {
                    $codigo = $this->getCode(6);
                    GeneralController::sendEmail('/provider/correos/code_authentication',null,$userIdAuth['email'],'Código de autenticación ',['tipo_provider' => $userIdAuth['tipo_provider'],'code' => $codigo]);

                    $cookie_name = "_verify_login";
                    $cookie_value = md5(time().$userIdAuth['user_id']);
                    setcookie($cookie_name, $cookie_value, time() + (86400 * 30), "/"); // 86400 = 1 day
                    $model->cookie_value = $cookie_value;
                    $model->user_id = base64_encode($userIdAuth['user_id']);
                    $model->generateCookie = true;

                    $this->saveCode($userIdAuth['user_id'],$codigo,$cookie_value);

                    return $this->render('code-authentication', [
                        'model' => $model
                    ]);
                }else if(!$model->generateCookie && empty($model->cookie_value) && !isset($_COOKIE['_verify_login'])){
                    $cookie_name = "_verify_login";
                    $cookie_value = md5(time().$userIdAuth['user_id']);
                    setcookie($cookie_name, $cookie_value, time() + (86400 * 30), "/"); // 86400 = 1 day
                    $model->cookie_value = $cookie_value;
                }

                $verifyCookie = false;

                if(isset($_COOKIE['_verify_login']) && (Authentication::find()->where(['and',['user_id' => $userIdAuth['user_id']],['active' => true]])->one())!==null){
                    $verCookie = CodeAuthentication::find()->where(['and',[
                        'cookie' => $_COOKIE['_verify_login']],['active' => true],['activation' => false]])->one();

                    if($verCookie!==null){
                        $verifyCookie = true;
                    }else{

                        $verCookie = ArrayHelper::getColumn(Yii::$app->db->createCommand("select cookie from connection_history where user_id = :id and cookie is not null
                                                                    and permission is TRUE  and browser_save is TRUE ",[':id' => $userIdAuth['user_id']])->queryAll(),'cookie');
                        if(!in_array($_COOKIE['_verify_login'],$verCookie) && $userIdAuth['active']){
                            $verifyCookie = true;
                        }
                    }
                }

                if((!empty($model->code) && !empty($model->cookie_value) && !empty($model->username)) || $verifyCookie){
                    $user_id_code = intval(base64_decode($model->user_id));
                    $user_id_code = $user_id_code == 0?$userIdAuth['user_id']:$user_id_code;
                    $cok = $model->cookie_value!=null?$model->cookie_value:(isset($_COOKIE['_verify_login'])?$_COOKIE['_verify_login']:$user_id_code);
                    $dataCode = CodeAuthentication::find()->where(['and',['user_id' => $user_id_code],
                        ['code' => $model->code],[
                            'cookie' => $cok],['active' => true],['activation' => false]])->one();


                    if($dataCode!==null){
                        $dataCode->active = false;
                        $dataCode->save();

                        if($dataCode->expiration<time()){
                            $codigo = $this->getCode(6);

                            $cookie_name = "_verify_login";
                            $cookie_value = md5(time().$userIdAuth['user_id']);
                            setcookie($cookie_name, $cookie_value, time() + (86400 * 30), "/"); // 86400 = 1 day
                            $model->cookie_value = $model->cookie_value;
                            $model->user_id = base64_encode($userIdAuth['user_id']);
                            $model->generateCookie = true;

                            $statusSave = $this->saveCode($userIdAuth['user_id'],$codigo,$cookie_value);

                            GeneralController::sendEmail('/provider/correos/code_authentication',null,$userIdAuth['email'],'Código de autenticación ',['tipo_provider' => $userIdAuth['tipo_provider'],'code' => $codigo]);

                            \Yii::$app->session->setFlash('error', 'El código ha expirado, se le ha enviado un nuevo código...');

                            return $this->render('code-authentication', [
                                'model' => $model
                            ]);
                        }

                    }else{
                        $msg = 'Código no encontrado...';
                        if($verifyCookie = true){
                            $generateNewCookie = false;
                            if(isset($_COOKIE['_verify_login'])){
                                if(($code_aut = CodeAuthentication::find()->where(['and',['cookie' => $_COOKIE['_verify_login']],['active' => true],['activation' => false]])->one())!==null) {
                                    if($code_aut->expiration<time()){
                                        $code_aut->active = false;
                                        $code_aut->save();
                                        $generateNewCookie = true;
                                    }else{
                                        $msg = 'Código incorrecto...';
                                    }
                                }else{
                                    if(($code_cokk = ConnectionHistory::find()->where(['and',['cookie' => $_COOKIE['_verify_login']],['browser_save' => false],['or',['permission' => false]]])->one())!==null) {
                                        $generateNewCookie = true;
                                    }else if(($code_cokk = ConnectionHistory::find()->where(['cookie' => $_COOKIE['_verify_login']])->one())===null){
                                        $generateNewCookie = true;
                                    }else if( (Authentication::find()->where(['and',['user_id' => $userIdAuth['user_id']],['active' => true]])->one())!==null){
                                        $generateNewCookie = true;
                                    }
                                }

                            }
                            if($generateNewCookie){
                                $codigo = $this->getCode(6);
                                $cookie_value = $cookie_value == null?$cookie_value = md5(time().$userIdAuth['user_id']):$cookie_value;
                                $cookie_name = "_verify_login";
                                setcookie($cookie_name, $cookie_value, time() + (86400 * 30), "/"); // 86400 = 1 day
                                $model->cookie_value = $cookie_value;
                                $model->user_id = base64_encode($userIdAuth['user_id']);
                                $model->generateCookie = true;
                                $statusSave = $this->saveCode($userIdAuth['user_id'],$codigo,$cookie_value);

                                GeneralController::sendEmail('/provider/correos/code_authentication',null,$userIdAuth['email'],'Código de autenticación ',['tipo_provider' => $userIdAuth['tipo_provider'],'code' => $codigo]);
                                $msg = 'Se han detectado credenciales inexistentes, hemos enviado un código de autenticación ha su correo...';
                            }

                        }

                        \Yii::$app->session->setFlash('error', $msg);

                        return $this->render('code-authentication', [
                            'model' => $model
                        ]);

                    }
                }

                if(($model->generateCookie == true && $model->save_browser == null) || $model->code!=null){
                    return $this->render('save-browser', [
                        'model' => $model
                    ]);
                }
            }

            $funcionario_mode = isset($model->es_funcionario) ? filter_var($model->es_funcionario, FILTER_VALIDATE_BOOLEAN) : false;

            if($funcionario_mode){

                try{

                    $url_login_ldap = Yii::$app->params['url_insumos_login_ad'];
                    $post_login = http_build_query(['user' => $model->username, 'secret' => $model->password]);
                    $response_login = GeneralController::curlPostFormData($url_login_ldap, $post_login);
                    $model->password = 'xxxxx';
                    if( is_null($response_login) ){ throw new Exception("No hubo respuesta del servico de login"); }
                    if( isset($response_login['status']) && $response_login['status'] == 'error' ){ throw new Exception( json_encode($response_login) ); }
                    if( $response_login['status'] == 1 ){ $model->password = '-'; $model->username = $model->username.'_funcionario'; }

                }catch(Exception $e){ self::setErrorLoginLdap($e->getMessage()); }

            }
            
            if($model->login()){
                $unique_id = Yii::$app->session->id;
                $browSave = $model->save_browser == null?true:$model->save_browser;
                $geCok = $model->cookie_value!=null?$model->cookie_value:(isset($_COOKIE['_verify_login'])?$_COOKIE['_verify_login']:'');
                GeneralController::getDataConnection($geCok,$browSave,$unique_id);

                $usuarioID = Yii::$app->getUser()->id;
                $modelUsuarios = Usuarios::findOne($usuarioID);


                if($modelUsuarios->status != 'ACTIVO' && $modelUsuarios->role == 'PROVIDER'){
                    $modelSanciones = Sanciones::findOne(['proveedor_id' => Provider::findOne(['user_id' => $usuarioID])->provider_id, 'activo' => 1]);
                    $dia = date($modelSanciones->f_fin);
                    $hoy = date('Y-m-d');
                    if($hoy >= $dia){
                        $modelUsuarios->status = 'ACTIVO';
                        $modelUsuarios->save(false);
                        $modelSanciones->comentario = "La inhabilitación de usuario de ha deshecho debido a que pasó la fecha fin de la sanción sobre el mismo. *Generado automáticamente.";
                        $modelSanciones->save();
                    }else{
                        Yii::$app->user->logout();
                        Yii::$app->session->setFlash('error',"Usuario sancionado, para más información contacta con un administrador. Expiración: ".$modelSanciones->f_fin);
                        return $this->redirect( ['/site/login']);
                    }
                }

                $userId = Yii::$app->user->identity->user_id;
                $ruta_redirect = "/";
                if (Yii::$app->user->identity->role == Usuarios::ROLE_DIOS) {
                    $ruta_redirect = '/usuarios/index';
                } else if (Yii::$app->user->can(Usuarios::ROLE_ADMIN_CONCURSOS)) {
                    $ruta_redirect = '/concursos/concurso/index';
                } else if (Yii::$app->user->can(Usuarios::ROLE_ADMIN_PROVIDER) ||
                    Yii::$app->user->can(Usuarios::ROLE_ADMIN_SECOP) ||
                    Yii::$app->user->can(Usuarios::ROLE_ADMIN_CONTRATISTAS) ) {
                    $ruta_redirect = '/usuarios/index';
                } else if(Yii::$app->user->can(Usuarios::ROLE_USUARIO_CONSULTA_BYS)){
                    $ruta_redirect = '/provider/index-detalle-provider-permiso';
                } else if(Yii::$app->user->can(Usuarios::ROLE_USUARIO_CONSULTA_OP)){
                    $ruta_redirect = '/provider/index-detalle-provider-permiso-op';
                } else if(Yii::$app->user->can(Usuarios::ROLE_PREGUNTAS_EVALUADOR) || Yii::$app->user->can(Usuarios::ROLE_PREGUNTAS_EVALUADOR_TIPO_1)
                    || Yii::$app->user->can(Usuarios::ROLE_PREGUNTAS_EVALUADOR_TIPO_2) || Yii::$app->user->can(Usuarios::ROLE_PREGUNTAS_REVISOR)){
                    $ruta_redirect = '/concursos/solicitud/index-evaluacion';
                }
                else if(Yii::$app->user->can(Usuarios::ROLE_CALL_CENTER)) {
                    $ruta_redirect = '/provider/index-detalle-dashboard';
                }else if(Yii::$app->user->can(Usuarios::ROLE_ADMIN_PROVEE)){
                    $ruta_redirect = '/evento/lista';
                }else if(Yii::$app->user->can(Usuarios::ROLE_CONSULTA_SANCION)){
                    $ruta_redirect = '/sanciones/';
                } else if (Yii::$app->user->can(Usuarios::ROLE_PROVIDER)) {

                    if (!Provider::registro_completo()) {
                        return $this->redirect(['/provider/create']);
                    } else if(Provider::status_provider() == Provider::PROVEEDOR_ETAPA_3 || Provider::PROVEEDOR_ETAPA_2){
                        $provider = Provider::find()->where(['user_id' => $userId])->one();
                        $tipo_pro = $provider->tipo_provider == 'op'?'fecha_certificado_op':'fecha_certificado_bys';
                        $tmp = explode('-',$provider->$tipo_pro);
                        //$fechabd1=mktime(0,0,0,$tmp[1],$tmp[2]-15,$tmp[0]+1);
                        if(!empty($provider->$tipo_pro)){
                            $fecha_certificado_db = date('Y-m-d',mktime(0,0,0,$tmp[1],$tmp[2],$tmp[0]+1));
                            $dif = date_diff(date_create($fecha), date_create($fecha_certificado_db));
                            $diferencia = $dif->format("%a");
                        } else{
                            $diferencia = 31;
                        }

                        if(1==2/*$diferencia <= 1 && Provider::status_provider() == Provider::PROVEEDOR_ETAPA_3*/){
                            $etapa = Yii::$app->db->createCommand();
                            $etapa->update('provider', ['status' => 'PROVEEDOR_ETAPA_2','status_carta_'.$provider->tipo_provider=>''], 'provider_id =' .$provider->provider_id);
                            $etapa->execute();

                            $alta_hacienda = Yii::$app->db->createCommand();
                            $alta_hacienda->update('provider.alta_hacienda',['status_bys' => 'PENDIENTE'],'provider_id =' .$provider->provider_id);
                            $alta_hacienda->execute();

                            $certificacion = Yii::$app->db->createCommand();
                            $certificacion->update('provider.certificacion',['status_bys' => 'PENDIENTE'],'provider_id =' .$provider->provider_id);
                            $certificacion->execute();

                            $clientes_contratos = Yii::$app->db->createCommand();
                            $clientes_contratos->update('provider.clientes_contratos',['status_bys' => 'PENDIENTE'],'provider_id =' .$provider->provider_id);
                            $clientes_contratos->execute();

                            $rfc = Yii::$app->db->createCommand();
                            $rfc->update('provider.rfc',['status_bys' => 'PENDIENTE'],'provider_id =' .$provider->provider_id);
                            $rfc->execute();

                            $intervencion_bancaria = Yii::$app->db->createCommand();
                            $intervencion_bancaria->update('provider.intervencion_bancaria',['status_bys' => 'PENDIENTE'],'provider_id =' .$provider->provider_id);
                            $intervencion_bancaria->execute();

                            $ubicacion = Yii::$app->db->createCommand();
                            $ubicacion->update('provider.ubicacion',['status_bys' => 'PENDIENTE'],'provider_id =' .$provider->provider_id);
                            $ubicacion->execute();

                            $ultima_declaracion = Yii::$app->db->createCommand();
                            $ultima_declaracion->update('provider.ultima_declaracion',['status_bys' => 'PENDIENTE'],'provider_id =' .$provider->provider_id);
                            $ultima_declaracion->execute();

                            $fotografia_negocio = Yii::$app->db->createCommand();
                            $fotografia_negocio->update('provider.fotografia_negocio',['status_bys' => 'PENDIENTE'],'provider_id =' .$provider->provider_id);
                            $fotografia_negocio->execute();

                            $relacion_accionistas = Yii::$app->db->createCommand();
                            $relacion_accionistas->update('provider.relacion_accionistas',['status_bys' => 'PENDIENTE'],'provider_id =' .$provider->provider_id);
                            $relacion_accionistas->execute();

                            Yii::$app->session->setFlash('error', 'Es necesario mandar nuevamente su información.');

                            if($diferencia == 0 ){
                                Yii::$app->session->setFlash('error', 'Es necesario mandar nuevamente su información.');
                                $ruta_redirect = '/provider/dashboard';
                                //return $this->redirect(['/provider/dashboard']);
                            }
                        }else if($diferencia == 1 ){
                            Yii::$app->session->setFlash("error", "Tiene $diferencia día, para la revalidación de su información.");
                        }else if($diferencia <= 30 ){
                            Yii::$app->session->setFlash("error", "Tiene $diferencia días, para la revalidación de su información.");
                            if(($diferencia == 30)||($diferencia == 15)||($diferencia == 3)){

                                $correo = Yii::$app->db->createCommand("SELECT email FROM usuarios WHERE user_id = ".$userId)->queryOne();

                                $usuario = Yii::$app->db->createCommand("SELECT pf_nombre,pf_ap_paterno,pf_ap_materno,name_razon_social,tipo_persona FROM provider WHERE user_id = ".$userId)->queryOne();

                                if($usuario['tipo_persona']=='Persona física'){
                                    $nombre = $usuario['pf_nombre'].' '.$usuario['pf_ap_paterno'].' '.$usuario['pf_ap_materno'];
                                }else{
                                    $nombre = $usuario['name_razon_social'];
                                }

                                Yii::$app->session->setFlash("error", "Tiene $diferencia días, para la revalidación de su información.");

                                GeneralController::sendEmail('/provider/correos/renovacion',null,$correo['email'],'Actualiza tu información',['nombre' => $nombre]);
                            }
                        }

                        $ruta_redirect = '/provider/dashboard';
                    } else if (Provider::registro_completo() && Provider::status_provider() == Provider::PROVEEDOR_ETAPA_2) {
                        Yii::$app->view->params['provider_id']=Yii::$app->user->identity->getProvider();
                        $ruta_redirect = '/provider/dashboard';
                    } else if (Provider::status_provider() == Provider::PROVEEDOR_SECOP) {
                        $ruta_redirect = '/solicitud-cotizacion/index';
                    }
                } else if (Yii::$app->user->can(Usuarios::ROLE_VALIDADOR_SECRETARIA)
                    || Yii::$app->user->can(Usuarios::ROLE_CAPTURISTA)
                    || Yii::$app->user->can(Usuarios::ROLE_VALIDADOR_DEPENDENCIA)
                    || Yii::$app->user->can(Usuarios::ROLE_CONTROL_OPERACIONES)
                ) {
                    $ruta_redirect = '/requisicion/index';
                } else if (Yii::$app->user->can(Usuarios::ROLE_NEGOCIADOR)) {
                    $ruta_redirect = '/requisicion/index-negociador';
                }else if(Yii::$app->user->can(Usuarios::ROLE_VALIDADOR_PROVEEDORES)){
                    $ruta_redirect = '/asignacion/index';
                }
                else if (Yii::$app->user->can(Usuarios::ROLE_NEGOCIADOR_CONTRATISTAS)) {
                    $ruta_redirect = '/contratistas/analisis-contratistas/index-analisis-experiencia';
                }
                else if (Yii::$app->user->can(Usuarios::ROLE_COORDINADOR)) {
                    $ruta_redirect = '/requisicion/index-coordinador';
                } else if (Yii::$app->user->can(Usuarios::ROLE_DIRECTOR)) {
                    $ruta_redirect = '/requisicion/index-director';
                } else if (Yii::$app->user->can(Usuarios::ROLE_VALIDADOR_CONTRATISTAS)) {
                    $resp = ArrayHelper::map(Yii::$app->user->getIdentity()->responsabilidades, 'descripcion', 'descripcion');

                    if (!empty($resp['DATOS LEGALES'])) {
                        $ruta_redirect = '/legales/index-validador';
                    } else if (!empty($resp['DATOS TECNICOS']) || !empty($resp['FOTOGRAFIA'])) {
                        $ruta_redirect = '/tecnicos/index-validador';
                    } else if (!empty($resp['DATOS FINANCIEROS'])) {
                        $ruta_redirect = '/financieros/index-validador';
                    }
                } else if (Yii::$app->user->can(Usuarios::ROLE_VENTANILLA_PROVEEDORES)) {
                    $ruta_redirect = '/provider/index-ventanilla';
                }else if (Yii::$app->user->can(Usuarios::ROLE_VALIDA_FOTO)) {
                    $ruta_redirect = '/tecnicos/index-validador';
                } if (Yii::$app->user->can(Usuarios::ROLE_METODO_PAGO)) {
                    $ruta_redirect = '/banco/index';
                }

                $this->redirect($ruta_redirect);

            }
        }

        //$searchModel = new ConcursosSearch();
        //$allData = array_merge($searchModel->searchConcursosTodos(null,'total'),Concursos::getAllDataOP(3));
        //usort($allData,function($a,$b){return strtotime($b['concurso_date_fin'])-strtotime($a['concurso_date_fin']);});

        $this->layout = 'limpio';

        return $this->render('login', [
            'model' => $model,
        ]);
    }

    /**
     * Diagnóstico de archivos FIEL
     */
    public function actionDiagnoseFiel()
    {
        Yii::$app->response->format = Response::FORMAT_JSON;

        if (!Yii::$app->request->isPost) {
            return [
                'success' => false,
                'errors' => ['Método no permitido']
            ];
        }

        try {
            // Obtener archivos
            $certFile = UploadedFile::getInstanceByName('certificado');
            $keyFile = UploadedFile::getInstanceByName('llave');
            $password = Yii::$app->request->post('password');

            if (!$certFile || !$keyFile || !$password) {
                return [
                    'success' => false,
                    'errors' => ['Todos los campos son requeridos para el diagnóstico']
                ];
            }

            // Leer contenido
            $certContent = file_get_contents($certFile->tempName);
            $keyContent = file_get_contents($keyFile->tempName);

            $diagnosis = [
                'certificate' => [
                    'size' => strlen($certContent),
                    'is_pem' => strpos($certContent, '-----BEGIN') !== false,
                    'starts_with' => substr($certContent, 0, 50),
                    'is_binary' => !mb_check_encoding($certContent, 'UTF-8')
                ],
                'key' => [
                    'size' => strlen($keyContent),
                    'is_pem' => strpos($keyContent, '-----BEGIN') !== false,
                    'starts_with' => substr($keyContent, 0, 50),
                    'is_binary' => !mb_check_encoding($keyContent, 'UTF-8')
                ],
                'openssl_tests' => []
            ];

            // Probar diferentes métodos de apertura de llave
            $methods = [
                'direct' => $keyContent,
                'as_pem_private' => "-----BEGIN PRIVATE KEY-----\n" . chunk_split(base64_encode($keyContent), 64, "\n") . "-----END PRIVATE KEY-----\n",
                'as_pem_rsa' => "-----BEGIN RSA PRIVATE KEY-----\n" . chunk_split(base64_encode($keyContent), 64, "\n") . "-----END RSA PRIVATE KEY-----\n",
                'as_pem_encrypted' => "-----BEGIN ENCRYPTED PRIVATE KEY-----\n" . chunk_split(base64_encode($keyContent), 64, "\n") . "-----END ENCRYPTED PRIVATE KEY-----\n"
            ];

            foreach ($methods as $method => $content) {
                $result = openssl_pkey_get_private($content, $password);
                $diagnosis['openssl_tests'][$method] = [
                    'success' => $result !== false,
                    'error' => $result === false ? openssl_error_string() : null
                ];
                if ($result) {
                    openssl_pkey_free($result);
                }
            }

            return [
                'success' => true,
                'diagnosis' => $diagnosis
            ];

        } catch (\Exception $e) {
            return [
                'success' => false,
                'errors' => ['Error en diagnóstico: ' . $e->getMessage()]
            ];
        }
    }

    /**
     * Login con FIEL (e.firma)
     */
    public function actionLoginFiel()
    {
        Yii::$app->response->format = Response::FORMAT_JSON;

        if (!Yii::$app->request->isPost) {
            return [
                'success' => false,
                'errors' => ['Método no permitido']
            ];
        }

        try {
            // Obtener archivos y datos
            $certFile = UploadedFile::getInstanceByName('certificado');
            $keyFile = UploadedFile::getInstanceByName('llave');
            $password = Yii::$app->request->post('password');

            if (!$certFile || !$keyFile || !$password) {
                return [
                    'success' => false,
                    'errors' => ['Todos los campos son requeridos: certificado, llave y contraseña']
                ];
            }

            // Usar nuestro componente FIEL para validar
            $fielManager = Yii::$app->fiel;
            $result = $fielManager->validateCertificate($certFile->tempName, $keyFile->tempName, $password);

            if (!$result['success']) {
                return [
                    'success' => false,
                    'errors' => [$result['error']]
                ];
            }

            $rfc = $result['rfc'];

            if (empty($rfc)) {
                return [
                    'success' => false,
                    'errors' => ['No se pudo extraer el RFC del certificado']
                ];
            }

            // Buscar usuario por RFC
            $user = $fielManager->findUserByRFC($rfc);

            if (!$user) {
                return [
                    'success' => false,
                    'errors' => ["Usuario no registrado en la plataforma con RFC: $rfc"]
                ];
            }

            // Verificar que el usuario esté activo
            if ($user->status !== 'ACTIVO') {
                return [
                    'success' => false,
                    'errors' => ['Usuario inactivo. Contacte al administrador.']
                ];
            }

            // Hacer login del usuario
            if (Yii::$app->user->login($user)) {
                // Log de auditoría
                Yii::info([
                    'fiel_login_success' => true,
                    'user_id' => $user->user_id,
                    'rfc' => $rfc,
                    'ip' => Yii::$app->request->userIP,
                    'timestamp' => date('Y-m-d H:i:s')
                ], 'fiel.login');

                // Determinar ruta de redirección según el rol
                $redirectUrl = $this->getRedirectUrlByRole($user);

                return [
                    'success' => true,
                    'message' => 'Login exitoso con FIEL',
                    'redirect' => $redirectUrl
                ];
            } else {
                return [
                    'success' => false,
                    'errors' => ['Error al iniciar sesión']
                ];
            }

        } catch (\Exception $e) {
            Yii::error("Error en login FIEL: " . $e->getMessage(), __METHOD__);
            return [
                'success' => false,
                'errors' => ['Error interno del servidor: ' . $e->getMessage()]
            ];
        }
    }

    /**
     * Obtiene la URL de redirección según el rol del usuario
     */
    protected function getRedirectUrlByRole($user)
    {
        if ($user->role == 'PROVIDER') {
            return '/provider/dashboard';
        } elseif ($user->role == 'ADMIN') {
            return '/usuarios/index';
        } else {
            return '/';
        }
    }

    public function actionSocket($socket = null){

        $this->layout = 'nomain';

        if($socket=='Mdig='){
            return $this->render('socket');
        }

        $this->redirectUser();

    }

    public function redirectUser(){

        $fecha =date('Y-m-d');
        $tmp = explode('-',$fecha);
        $fechahoy =mktime(0,0,0,$tmp[1],$tmp[2],$tmp[0]);

    }

    public function actionLogout()
    {


        if(isset($_COOKIE['_verify_login'])){
            ConnectionHistory::updateAll(['session' => null],['cookie' => $_COOKIE['_verify_login']]);
        }

        Yii::warning(Yii::$app->user->logout());
        return $this->goHome();
    }

//    public function actionCodeAuthentication()
//    {
//        Yii::warning(Yii::$app->user->logout());
//        return $this->goHome();
//    }


    public function saveCode($user_id,$codigo,$cookie,$activation = false){

        $expiration = time()+60*10;
        $model_code = new CodeAuthentication();
        $model_code->user_id = $user_id;
        $model_code->code = $codigo;
        $model_code->cookie = $cookie;
        $model_code->expiration = $expiration;
        $model_code->activation = $activation;
        return $model_code->save(false);
    }
    public function getCode($limit) {
        $codigo = '';
        $numeros = '1234567890';
        $size = strlen($numeros)-1;
        for($i=0;$i < $limit;$i++) $codigo .= $numeros[mt_rand(0,$size)];
        return $codigo;
    }


    public function actionContact()
    {
        $model = new ContactForm();
        if ($model->load(Yii::$app->request->post()) && $model->contact(Yii::$app->params['adminEmail'])) {
            Yii::$app->session->setFlash('contactFormSubmitted');

            return $this->refresh();
        }
        return $this->render('contact', [
            'model' => $model,
        ]);
    }

    public function actionChange(){

        $tipo_pro = Yii::$app->user->identity->tipo;

        $tipo_pro = $tipo_pro=='op'?'bys':'op';

        Yii::$app->db->createCommand("update provider set tipo_provider = :tipo where user_id =:id",[':tipo' => $tipo_pro,':id' => Yii::$app->user->id])->execute();
        return $this->redirect('/provider/dashboard');
    }
    public function actionChangeByNotif($tipo, $url){

        Yii::$app->db->createCommand("update provider set tipo_provider = :tipo where user_id =:id",[':tipo' => $tipo,':id' => Yii::$app->user->id])->execute();
        $url_token_decode = urldecode($url);
        return $this->redirect($url_token_decode);

    }

    public function actionAbout()
    {
        return $this->render('about');
    }

    public function actionCreate()
    {
        //  return $this->render('views\usuarios\create');
        return $this->redirect('index.php?r=usuarios%2Fcreate');
    }

    public function actionDelf($x=null){
        if($x==null || !base64_decode($x)){
            return json_encode(['status'=>'No permitido']);
        }
        $SePuedenEliminar= ['PENDIENTE','RECHAZADO'];
        $mod = "app\models\\".explode('[',base64_decode($x))[0];
        $prop = explode(']',explode('[',base64_decode($x))[1])[0];
        $model = $mod::find()->where(['and',['provider_id'=>Yii::$app->user->identity->providerid,'status_op'=>$SePuedenEliminar]])->One();

        if($model->$prop){
            if(file_exists($model->$prop)){
                try{
                    unlink($model->$prop);
                }catch(Exception $e){

                }
            }else{
                return "el archivo no existe";
            }
            $model->$prop = null;
            $model->save();
            return 'ok';
        }else{
            return 'no';
        }

    }


    public function actionConfiguration(){

        $this->layout = 'home';

        $provider_id = Yii::$app->user->identity->providerid;
        $tipo = Yii::$app->user->identity->tipo;
        $cert = HistoricoCertificados::find()->where(['and',['provider_id' => $provider_id],['tipo' => 'CERTIFICADO'],['provider_type' => $tipo]])->orderBy(['historico_certificados_id' => SORT_DESC])->one();
  
        return $this->render('configuration',[
            'model' => $this->findModelAuthentication(),
            'cert' => $cert
        ]);
    }

    public function actionSessions($closeSession = null)
    {
        $user_id = Yii::$app->user->getId();

        if($closeSession == null){
            $query = ConnectionHistory::find()->where(['and', ['user_id' => $user_id], ['permission' => true]])->orderBy(['session' => new Expression("CASE WHEN session IS NULL THEN 0 ELSE 1 END DESC"),'last_connection' => SORT_DESC]);
            $pagination = new Pagination(['totalCount'=>$query->count(),'pageSize'=>10]);
            $allData = $query->offset($pagination->offset)->limit($pagination->limit)->all();
            return $this->render('sessions', [
                'allData'=>$allData,
                'pagination' => $pagination
            ]);
        }else{
            $actualSession = isset($_COOKIE['_verify_login']) ? $_COOKIE['_verify_login'] : '000000';
            $allSession = ConnectionHistory::find()->where(['and', ['user_id' => $user_id], ['permission' => true], ['<>', 'cookie', $actualSession]])->all();
            foreach ($allSession as $session_old){
                ConnectionHistory::updateAll(['session' => null,'permission' => false],['connection_history_id' => $session_old['connection_history_id']]);
                if (!empty($session_old['session'])){
                    try{
                        session_id($session_old['session']);
                        session_start();
                        session_destroy();
                        session_abort();
                    }catch (\Exception $e){ }
                }
            }
            return $this->redirect( ['sessions']);
        }
    }


    public function findModelAuthentication(){
        $id = Yii::$app->user->id;


        if(($model = Authentication::find()->where(['user_id' => $id])->one())===null){
            $model = new Authentication();
        }

        return $model;
    }


    public function actionChangeValidation($true_false = null){
        $values = ['0','1',true,false,'true','false'];
        $verify = true;
        if(Yii::$app->request->isAjax && in_array($true_false,$values)){
            $true_false = $true_false == 'false' || $true_false == '0'?false:true;
            $id = Yii::$app->user->getId();
            if(($model = Authentication::find()->where(['user_id' => $id])->one())!==null){
                $model->active = $true_false;
                $verify = $verify && $model->save();

            }else{
                $model = new Authentication();
                $model->user_id = $id;
                $model->active = $true_false;
                $verify = $verify && $model->save();
            }
            return $verify;
        }
        return false;
    }

    public function actionActivationAuthentication($true_false=null,$code = null,$urlReq = null,$codeWriete = null){

        if(Yii::$app->request->isAjax){

            $email = Yii::$app->user->identity->email;
            $user_id = Yii::$app->user->id;
            $cookie_value = isset($_COOKIE['_verify_login'])?$_COOKIE['_verify_login']:'';
            $url = null;
            $tipo_provider = null;
            if (Yii::$app->user->can('PROVIDER')){
                $tipo_provider = Provider::find()->select('tipo_provider')->where(['user_id' => $user_id])->one()['tipo_provider'];

                $url = $tipo_provider=='bys'?'/bys/perfil/index':'/op/perfil/index';

            }

            $m = $true_false=='true'?'Código para activar autenticación en dos pasos ':'Código para desactivar autenticación en dos pasos ';

            if($urlReq!=null && $codeWriete==null){

                $dataCode = CodeAuthentication::find()->where(['and',['user_id' => $user_id],
                    ['active' => true],['activation' => true],['activation' => false]])->one();
                if($dataCode!==null){
                    if($dataCode->expiration<time()){

                        $dataCode->active = false;
                        $dataCode->save();

                        $codigo = $this->getCode(6);

                        $statusSave = $this->saveCode($user_id,$codigo,$cookie_value,true);

                        GeneralController::sendEmail('/provider/correos/code_authentication',null,$email,$m,['tipo_provider' => $tipo_provider,'code' => $codigo]);



                    }
                }else{
                    $codigo = $this->getCode(6);
                    GeneralController::sendEmail('/provider/correos/code_authentication',null,$email,$m,['tipo_provider' => $tipo_provider,'code' => $codigo]);
                    $this->saveCode($user_id,$codigo,$cookie_value,true);

                }

                $msg = ['msg' => 'Código enviado exitosamente a su correo.','status' => 'success'];

                $url_new = base64_decode($urlReq).'&msg='.base64_encode(json_encode($msg));
                return base64_encode($url_new);
            }

            if($codeWriete!=null){
                $dataCode = CodeAuthentication::find()->where(['and',['user_id' => $user_id],
                    ['code' => $codeWriete],['active' => true],['activation' => true]])->one();

                if($dataCode!==null){
                    $dataCode->active = false;
                    $dataCode->save();

                    if($dataCode->expiration<time()){
                        $codigo = $this->getCode(6);

                        $statusSave = $this->saveCode($user_id,$codigo,$cookie_value,true);

                        GeneralController::sendEmail('/provider/correos/code_authentication',null,$email,$m,['tipo_provider' => $tipo_provider,'code' => $codigo]);

                        $msg = ['msg' => 'El código a expirado, se le ha enviado un nuevo código...','status' => 'error'];
                        $url_new = base64_decode($urlReq).'&msg='.base64_encode(json_encode($msg));
                        return base64_encode($url_new);
                    }else{

                        $values = ['0','1',true,false,'true','false'];
                        $verify = true;
                        if(Yii::$app->request->isAjax && in_array($true_false,$values)){
                            $true_false = $true_false == 'false' || $true_false == '0'?false:true;

                            if(($model = Authentication::find()->where(['user_id' => $user_id])->one())!==null){
                                $model->active = $true_false;
                                $verify = $verify && $model->save();

                            }else{
                                $model = new Authentication();
                                $model->user_id = $user_id;
                                $model->active = $true_false;
                                $verify = $verify && $model->save();
                            }
                        }

                        if($verify){
                            return $this->redirect('/site/configuration');
                        }
                    }
                }else{
                    $msg = ['msg' => 'Código no encontrado...','status' => 'error'];

                    $url_new = base64_decode($urlReq).'&msg='.base64_encode(json_encode($msg));
                    return base64_encode($url_new);
                }

                }

            return $this->renderAjax('activation-authentication',[
                'email' => $email,
                'url' => $url,
                'true_false' => $true_false,
                'code' => $code,
                'urlReq' => $urlReq

            ]);

        }else{
            return $this->redirect('/site/configuration');
        }

    }


    public function actionChangePassword(){

        if(Yii::$app->request->isAjax){
            $user_id = Yii::$app->user->id;

            $model = new \yii\base\DynamicModel([
                'old_password', 'new_password','new_password_2'
            ]);

            $model->setAttributeLabels([
                'old_password' => 'Contraseña actual',
                'new_password' => 'Contraseña nueva',
                'new_password_2' => 'Repite la contraseña nueva',
            ]);

            $model->addRule(['old_password'],'required');
            $model->addRule(['old_password'],'string');
            $model->addRule(['new_password'],'required');
            $model->addRule(['new_password'],'string');
            $model->addRule(['new_password_2'],'required');
            $model->addRule(['new_password_2'],'string');


            if(Yii::$app->request->isAjax && $model->load(Yii::$app->request->post())){
                Yii::$app->response->format = Response::FORMAT_JSON;
                $result = ActiveForm::validate($model);

                if(empty($result)){
                    $usuario = Usuarios::findOne($user_id);
                    if(!Usuarios::virifyPassword($model->old_password,$usuario->password)){
                        $result['dynamicmodel-old_password'] = ['La contraseña no coincide con la contraseña actual'];
                    }else if($model->old_password == $model->new_password){
                        $result['dynamicmodel-new_password'] = ['La contraseña debe ser diferente a la contraseña actual.'];
                    }else if($model->new_password != $model->new_password_2){
                        $result['dynamicmodel-new_password_2'] = ['La nueva contraseña no coincide con la contraseña nueva repetida.'];
                    }else{
                        $usuario->pass_tmp = $model->new_password;
                        $usuario->password = $model->new_password;
                        $usuario->secop = false;
                        $secop = array(
                            "clave_sire" => "",
                            "nombre" => $usuario->nombre,
                            "usuario" => $usuario->username,
                            "contrasena" => $model->new_password,
                            "correo" => $usuario->email,
                            "rfcprov" => $usuario->rfc
                        );
                        GeneralController::insertProvSecopa($secop);
                        if(!$usuario->save()){
                            $result['dynamicmodel-new_password'] = ['La contraseña debe contener al menos una mayúscula, una minúscula y un número.'];
                        }else{
                            if((VerifyUserSecop::find()->where(['user_id' => $usuario->user_id])->one())===null){
                                $verifyUserSecop = new VerifyUserSecop();
                                $verifyUserSecop->user_id = $usuario->user_id;
                                $verifyUserSecop->save();
                            }
                            \Yii::$app->session->setFlash('success', 'Contraseña actualizada exitosamente.');

                            return $this->redirect('configuration');
                        }
                    }
                }

                return $result;

            }

            return $this->renderAjax('change-password',[
                'model' => $model
            ]);
        }

        return $this->redirect('configuration');






    }


    public function get_timeago( $ptime )
    {
        $etime = time() - $ptime;

        if( $etime < 1 )
        {
            return 'menos de 1 segundo';
        }

        $a = array( 12 * 30 * 24 * 60 * 60  =>  'año',
            30 * 24 * 60 * 60       =>  'mes',
            24 * 60 * 60            =>  'día',
            60 * 60             =>  'hora',
            60                  =>  'minuto',
            1                   =>  'segundo'
        );

        foreach( $a as $secs => $str )
        {
            $d = $etime / $secs;

            if( $d >= 1 )
            {
                $r = round( $d );
                return $r . ' ' . $str . ( $r > 1 && $str!='mes'? 's' :($r > 1 && $str=='mes'?'es':'') );
            }
        }
    }


    public function actionNumnotificaciones()
    {
        if(Yii::$app->request->isAjax){
            $user_id = Yii::$app->user->getId();

            $tot = NumberNotification::find()->select('number_notification')->where(['and',['user_id' => $user_id],['viewed' => false]])->one()['number_notification'];

            return $tot;
        }else{
            return $this->goHome();
        }
    }

    public function  actionNumNotificacionesReset(){

        if(Yii::$app->request->isAjax){
            $user_id = Yii::$app->user->getId();

            if(($numberNot = NumberNotification::find()->where(['user_id' => $user_id])->one())!==null){
                $numberNot->number_notification = 0;
                $numberNot->viewed = true;
                $numberNot->save();
            }
            return true;
        }else{
            return $this->goHome();

        }

    }

    public function actionNotificaciones()
    {
        if(Yii::$app->request->isAjax){

            $user_id = Yii::$app->user->getId();

            $data = GeneralController::getDataNotification($user_id);

            $con = '<div class="sin_notificacion">Sin notificaciones</div>';
            if($data){
                $con = '';

                foreach ($data as $val){
                    $data_conection = \app\models\Usuarios::LastConnection($val['created_date']);

                    $txt = '';
                    $msg_extra = '';
                    $linea = '';
                    $connection = '';
                    if(isset($data_conection['con'])){
                        $connection = $data_conection['con'];
                        if(!$data_conection['change']){
                            $txt = 'hace ';
                        }

                    }

                    if(!empty($val['msg_extra'])){
                        $msg_extra = strtoupper($val['msg_extra']);
                        $linea = ' - ';
                    }


                    $url = '#';

                    if($val['url_module']!=null){
                        if($val['type_user']=='PROVIDER' && $val['url_module']!='/banco/view' && $val['url_module']!='/calendar/calendar'
                            && $val['url_module']!='/calendar/documentacion-cotejo-pre' && $val['url_module']!='https://proveedores.nl.gob.mx/Notificacion.pdf' ){
                                $tipo_actual = Yii::$app->user->identity->tipo;
                                if($tipo_actual != null && ($tipo_actual != $val['op_bys'])){
                                    $tipo_nuevo = $val['op_bys'];
                                    $titulo_modal = $tipo_nuevo == 'op' ? "¿Está seguro de Cambiar a la sección de CONTRATISTAS?" : '¿Está seguro de Cambiar a la sección de PROVEEDORES?';
                                    $url_red = '/'.$val['op_bys'].$val['url_module'].'?ntf='.base64_encode($val['save_notification_id']);
                                    $url_token_encode = urlencode($url_red);
                                    $url = "/site/change-by-notif?tipo=$tipo_nuevo&url=$url_token_encode";
                                }else{
                                    $url = '/'.$val['op_bys'].$val['url_module'];
                                }
                            }else{ $url = $val['url_module']; }

                        /* $url = $val['type_user']=='PROVIDER'
                               && $val['url_module']!='/banco/view'
                               && $val['url_module']!='/calendar/calendar'
                                && $val['url_module']!='/calendar/documentacion-cotejo-pre'
                               && $val['url_module']!='https://proveedores.nl.gob.mx/Notificacion.pdf'?
                            '/'.$val['op_bys'].$val['url_module']:$val['url_module']; */

                        if($val['params']!=null){

                            /*
                             * checar las urls que requieren parametros
                             *
                             *
                             */
                        }

                        //$html .='<a href ='.$url.'>Ver</a>';
                    }

                    //$url .= strpos($url,'?')?'&ntf='.base64_encode($val['save_notification_id']):'?ntf='.base64_encode($val['save_notification_id']);

                    $color = $val['msg_extra'] =='Validado'?'#8cc63f':($val['msg_extra'] =='Rechazado'?'#ce5b5b':($val['msg_extra'] =='¡Felicidades!'?'#29abe2':''));

                    $type = $val['viewed']?'visto':'por_ver';

                    $moduleValName = in_array($val['module_validation_name'],['CURRÍCULUM','Currículum'])?'Clientes':$val['module_validation_name'];

                    $msg = str_replace(':name_mod:',$moduleValName=='Perfil'?$moduleValName.'-Informacion':$moduleValName,$val['notification']);

                    $url_icon = $val['module_notification_name'] == 'RESPUESTA DE VALIDACION' ? ( $val['op_bys'] == 'bys' ? '@web/imgs/bienes_servicios.svg' : '@web/imgs/obras_publicas.svg' ) : "@web/imgs/notificaciones/".str_replace(' ', '_',$val['module_notification_name']).".png";

                    if(GeneralController::str_contains($url, 'change-by-notif')){
                        $con.=
                            "<a class='notificacion_link sweetalert ".$type."' data-url='$url' data-title='$titulo_modal' data-icon='success' data-confirm_label='Aceptar' data-cancel_label='Cancelar' >
                                <div class='cont_notificacion'>
                                    <div class='cont_imagen_not'>
                                        <img src='".Url::to($url_icon)."' />
                                    </div>
                                    <div class='cont_texto_not'>
                                        <span class='titulo_notificacion'>".$msg."</span>
                                        <span class='subtitulo_notificacion'>".$val['module_notification_name'].$linea."<span style='color: $color'>".$msg_extra."</span></span>
                                        <span class='tiempo_notificacion'>".$txt.$connection."</span>
                                        <div class='bolita_notificiacion ".$type."'></div>
                                    </div>
                                </div>
                            </a>
                            <div class='raya_notificacion'></div>";
                    }else{
                        $url .= strpos($url,'?')?'&ntf='.base64_encode($val['save_notification_id']):'?ntf='.base64_encode($val['save_notification_id']);
                        $con .=
                            "<a class='notificacion_link ".$type."' href = '".$url."'>
                                <div class='cont_notificacion'>
                                    <div class='cont_imagen_not'>
                                        <img src='".Url::to($url_icon)."' />
                                    </div>
                                    <div class='cont_texto_not'>
                                        <span class='titulo_notificacion'>".$msg."</span>
                                        <span class='subtitulo_notificacion'>".$val['module_notification_name'].$linea."<span style='color: $color'>".$msg_extra."</span></span>
                                        <span class='tiempo_notificacion'>".$txt.$connection."</span>
                                        <div class='bolita_notificiacion ".$type."'></div>
                                    </div>
                                </div>
                            </a>
                            <div class='raya_notificacion'></div>";
                    }
                }
            }
            return $con;
        }else{
            return $this->goHome();
        }
    }

    public function actionCertificados(){
        if(Yii::$app->request->isAjax){
            $user_id = Yii::$app->user->getId();
            $response = '<div class="sin_notificacion">Sin certificados</div>';

            $provider = Provider::find()->where(['user_id' => $user_id])->one();
            if($provider == null){ return $response; }
            $data = HistoricoCertificados::find()->where(['and', ['provider_id' => $provider->provider_id, 'provider_type' => $provider->tipo_provider]])->all();
            if($data == null){ return $response; }
            else{
                $response = '';
                foreach($data as $certificado){
                    $response .= "
                        <a class='notificacion_link visto' target='_blank' href = '/".GeneralController::limpiarUrl($certificado->url_certificado)."'>
                            <div class='cont_notificacion'>
                                <div class='cont_imagen_not'>
                                    <img src='".Url::to('@web/imgs/notificaciones/CERTIFICADO.png')."'/>
                                </div>
                                <div class='cont_texto_not'>
                                    <span class='titulo_notificacion'>". ($certificado->provider_type == 'bys' ? ($certificado->tipo=='CURSO'?'Acreditación Curso':($certificado->tipo == HistoricoCertificados::TIPO_ACTUALIZACION ? 'Actualizacion' : 'Certificado') ) : ($certificado->tipo=='CURSO'?'Acreditación Curso':'Constancia')) ."</span>
                                    <span class='subtitulo_notificacion'>".date('d-M-Y H:m', strtotime($certificado->created_at))."</span>
                                </div>
                            </div>
                        </a>
                        <div class='raya_notificacion'></div>";
                }
            }

            return $response;

        }
    }

    public function actionViewNotificaciones()
    {

        $this->layout = 'home';

            $user_id = Yii::$app->user->getId();

            $data = GeneralController::getDataNotification($user_id);

            $con = '<div class="sin_notificacion">Sin notificaciones</div>';

            if($data){
                $con = '';

                foreach ($data as $val){
                    $data_conection = \app\models\Usuarios::LastConnection($val['created_date']);
                    $msg_extra = '';
                    $linea = '';
                    $txt = '';
                    $connection = '';
                    if(isset($data_conection['con'])){
                        $connection = $data_conection['con'];
                        if(!$data_conection['change']){
                            $txt = 'hace ';
                        }

                    }
                    if(!empty($val['msg_extra'])){
                        $msg_extra = strtoupper($val['msg_extra']);
                        $linea = ' - ';
                    }

                    $url = '#';
                    if($val['url_module']!=null){
                        if($val['type_user']=='PROVIDER' && $val['url_module']!='/banco/view' && $val['url_module']!='/calendar/calendar'
                            && $val['url_module']!='/calendar/documentacion-cotejo-pre' && $val['url_module']!='https://proveedores.nl.gob.mx/Notificacion.pdf' ){
                                $tipo_actual = Yii::$app->user->identity->tipo;
                                if($tipo_actual != null && ($tipo_actual != $val['op_bys'])){
                                    $tipo_nuevo = $val['op_bys'];
                                    $titulo_modal = $tipo_nuevo == 'op' ? "¿Está seguro de Cambiar a la sección de CONTRATISTAS?" : '¿Está seguro de Cambiar a la sección de PROVEEDORES?';
                                    $url_red = '/'.$val['op_bys'].$val['url_module'].'?ntf='.base64_encode($val['save_notification_id']);
                                    $url_token_encode = urlencode($url_red);
                                    $url = "/site/change-by-notif?tipo=$tipo_nuevo&url=$url_token_encode";
                                }else{
                                    $url = '/'.$val['op_bys'].$val['url_module'];
                                }
                            }else{ $url = $val['url_module']; }

                        if($val['params']!=null){

                            /*
                             * checar las urls que requieren parametros
                             *
                             *
                             */
                        }

                        //$html .='<a href ='.$url.'>Ver</a>';
                    }

                    $color = $val['msg_extra'] =='Validado'?'#8cc63f':($val['msg_extra'] =='Rechazado'?'#ce5b5b':($val['msg_extra'] =='¡Felicidades!'?'#29abe2':''));

                    $type = $val['viewed']?'visto':'por_ver';

                    $moduleValName = in_array($val['module_validation_name'],['CURRÍCULUM','Currículum'])?'Clientes':$val['module_validation_name'];

                    $msg = str_replace(':name_mod:',$moduleValName=='Perfil'?$moduleValName.'-Informacion':$moduleValName,$val['notification']);

                    $url_icon = $val['module_notification_name'] == 'RESPUESTA DE VALIDACION' ? ( $val['op_bys'] == 'bys' ? '@web/imgs/bienes_servicios.svg' : '@web/imgs/obras_publicas.svg' ) : "@web/imgs/notificaciones/".str_replace(' ', '_',$val['module_notification_name']).".png";

                    if(GeneralController::str_contains($url, 'change-by-notif')){
                        $con.=
                            "<a class='notificacion_link sweetalert ".$type."' data-url='$url' data-title='$titulo_modal' data-icon='success' data-confirm_label='Aceptar' data-cancel_label='Cancelar' >
                                <div class='cont_notificacion'>
                                    <div class='cont_imagen_not'>
                                        <img src='".Url::to($url_icon)."' />
                                    </div>
                                    <div class='cont_texto_not'>
                                        <span class='titulo_notificacion'>".$msg."</span>
                                        <span class='subtitulo_notificacion'>".$val['module_notification_name'].$linea."<span style='color: $color'>".$msg_extra."</span></span>
                                        <span class='tiempo_notificacion'>".$txt.$connection."</span>
                                        <div class='bolita_notificiacion ".$type."'></div>
                                    </div>
                                </div>
                            </a>
                            <div class='raya_notificacion'></div>";
                    }else{
                        $url .= strpos($url,'?')?'&ntf='.base64_encode($val['save_notification_id']):'?ntf='.base64_encode($val['save_notification_id']);
                        $con .=
                            "<a class='notificacion_link ".$type."' href = '".$url."'>
                                <div class='cont_notificacion'>
                                    <div class='cont_imagen_not'>
                                        <img src='".Url::to($url_icon)."' />
                                    </div>
                                    <div class='cont_texto_not'>
                                        <span class='titulo_notificacion'>".$msg."</span>
                                        <span class='subtitulo_notificacion'>".$val['module_notification_name'].$linea."<span style='color: $color'>".$msg_extra."</span></span>
                                        <span class='tiempo_notificacion'>".$txt.$connection."</span>
                                        <div class='bolita_notificiacion ".$type."'></div>
                                    </div>
                                </div>
                            </a>
                            <div class='raya_notificacion'></div>";
                    }

                }
            }

            return $this->render('view-notificaciones',[
                'notificaciones' => $con
            ]);
        
    }



    public function actionViewedNtf($ntf=null){

        if(Yii::$app->request->isAjax && $ntf!=null){
            $id = intval(base64_decode($ntf));
            if(($data = SaveNotification::find()->where(['and',['save_notification_id' => $id],['viewed' => false]])->one())!==null){
                $data->viewed = true;
                $data->viewed_date = date('Y-m-d H:i:s');
                $data->save(false);
                return true;
            }
        }else{
            return $this->goHome();
        }
    }

    public function actionDownloadCert(){


        $provider_id = Yii::$app->user->identity->providerid;
        $tipo = Yii::$app->user->identity->tipo;
        $cert = Yii::$app->db->createCommand("
        select hc.url_certificado, CASE WHEN (p.name_razon_social is not null and p.name_razon_social != '') then p.name_razon_social else concat_ws(' ',p.pf_nombre,p.pf_ap_paterno,p.pf_ap_materno) end as name_provider
        from historico_certificados hc
        join provider p using(provider_id)
        where provider_id = $provider_id and tipo = 'CERTIFICADO' and provider_type = '$tipo'
        order by hc.historico_certificados_id DESC")->queryOne();


        $nameP = $this->limpiar_cadena($cert['name_provider']);
        $name = $cert['url_certificado'];

        $size = filesize($name);
        header("Content-Type: application/pdf");
        header("Content-Disposition: attachment; filename=$nameP.pdf");
        header("Content-Transfer-Encoding: binary");
        header("Content-Length: " . $size);
        // Descargar archivo
        readfile($name);
        exit();
    }

    public function limpiar_cadena($cadena,$propio=true)
    {
        $cadena = trim($cadena);
        $cadena = str_replace(['á', 'à', 'ä', 'â', 'ª', 'Á', 'À', 'Â', 'Ä','Ã'],['a', 'a', 'a', 'a', 'a', 'A', 'A', 'A', 'A','A'],$cadena);
        $cadena = str_replace(['é', 'è', 'ë', 'ê', 'É', 'È', 'Ê', 'Ë'],['e', 'e', 'e', 'e', 'E', 'E', 'E', 'E'],$cadena);
        $cadena = str_replace(['í', 'ì', 'ï', 'î', 'Í', 'Ì', 'Ï', 'Î'],['i', 'i', 'i', 'i', 'I', 'I', 'I', 'I'],$cadena);
        $cadena = str_replace(['ó', 'ò', 'ö', 'ô', 'Ó', 'Ò', 'Ö', 'Ô'],['o', 'o', 'o', 'o', 'O', 'O', 'O', 'O'],$cadena);
        $cadena = str_replace(['ú', 'ù', 'ü', 'û', 'Ú', 'Ù', 'Û', 'Ü'],['u', 'u', 'u', 'u', 'U', 'U', 'U', 'U'],$cadena);
        $cadena = str_replace(['ñ', 'Ñ', 'ç', 'Ç'],['n', 'N', 'c', 'C',],$cadena);
        $cadena = str_replace(["¨", "º", "-", "~","#", "@", "|", "!","·", "$", "%", "&", "/","(", ")", "?", "'", "¡","¿", "[", "^", "<code>", "]",
            "+", "}", "{", "¨", "´",">", "< ", ";", ",", ":","."],'',$cadena);
        if($propio)
            $cadena = str_replace(' ','_',$cadena);
        return $cadena;
    }

    public function setErrorLoginLdap($error){
        $error = new ErrorInesperado();
        $error->tipo_error = 'LOGIN_FUNCIONARIO';
        $error->data = $error;
        $error->user_id = Yii::$app->user->getId();
        $error->save();
    }
}
