<?php

namespace app\controllers;

use app\helpers\GeneralController;
use app\helpers\SireController;
use app\models\ActaCircunstanciada;
use app\models\Asignacion;
use app\models\Calendar;
use app\models\CertificateGenerated;
use app\models\ComprobanteDomicilio;
use app\models\EstadoFinanciero;
use app\models\Experiencia;
use app\models\ExpirationDocuments;
use app\models\FirstUpdateCertificate;
use app\models\Historico;
use app\models\HistoricoCertificados;
use app\models\LastSendValidationRelation;
use app\models\Module;
use app\models\ModulesComplete;
use app\models\Movements;
use app\models\MovementsOp;
use app\models\NoLocalizados;
use app\models\NotAssist;
use app\models\Provider;
use app\models\Status;
use app\models\Rfc;
use app\models\AltaHacienda;
use app\models\IntervencionBancaria;
use app\controllers\CartaController;
use app\helpers\FunctionsHelper;
use app\models\Ubicacion;
use app\models\UltimaDeclaracion;
use app\models\UserRelationProvider;
use app\models\UsuarioConsulta;
use app\models\Usuarios;
use app\models\Visit;
use kartik\form\ActiveForm;
use Yii;
use mPDF;
use yii\helpers\ArrayHelper;
use yii\helpers\Url;
use app\models\DatosValidados;
use app\models\DatosValidadosSearch;
use app\models\RepresentanteLegal;
use yii\web\Controller;
use yii\web\NotFoundHttpException;
use yii\filters\VerbFilter;
use yii\web\Response;

/**
 * DatosValidadosController implements the CRUD actions for DatosValidados model.
 */
class ValidadorController extends GeneralController
{
    /**
     * @inheritdoc
     */
    public function behaviors()
    {
        return [
            'verbs' => [
                'class' => VerbFilter::className(),
                'actions' => [
                    'delete' => ['POST'],
                ],
            ],
        ];
    }

    public function actionTodos($isVal=0){ //Recomiendo documentar bien esta funcion y ponerle comentarios a las variables, cada que la leo me ando revolviendo xd

        $aux = 0;
        $rechazados = [];
        $mensaje = false;
        if(isset($_POST)){
            Yii::$app->db->beginTransaction();
            $provider_id = strval($_POST['id']);
            $atributos_rechazados = json_decode($_POST['atributos_rechazados'], true);
            $modulos_validados = $_POST['validado'];
            Status::updateAll(['status_bys'=>Status::STATUS_TERMINADO],['and',['provider_id'=>$provider_id],['status_bys'=>Status::STATUS_TERMINADO_PRO]]);
            foreach ($modulos_validados as $key => $val){
                $mod = Module::find()->where(['and',['model'=>$key],['provider_id'=>$provider_id]])->one();
                //var_dump($mod).exit();
                if($val == 'validado'){
                    $model = new DatosValidados();
                    $model->status_bys = Status::STATUS_VALIDADO;
                    $mod->status_bys = ($isVal==1) ? Status::STATUS_VALIDADO : Status::STATUS_REVISADO;
                    if( $isVal == 1 && $key == 'bys_legales' ){ RepresentanteLegal::updateAll(['validado' => true, 'status_bys' => Status::STATUS_VALIDADO], ['and', ['provider_id' => $provider_id], ['activo' => true] ] ); } 
                }else{
                    GeneralController::pushNotification(Provider::findOne($provider_id)->user_id, "¡Tu expediente ha sido rechazado!");
                    $aux++ ;
                    array_push($rechazados, GeneralController::getNameModuloByKey($key));
                    $model = new Status();
                    $model->status_bys = Status::STATUS_RECHAZADO;
                    $mod->status_bys = Status::STATUS_RECHAZADO;
                    $model->motivo = $_POST['rechazo'][$key];
                    if(isset($atributos_rechazados[$key])){
                        $rechazo_modulo = json_encode($atributos_rechazados[$key]);
                        $model->atributos = $rechazo_modulo;
                        if($key == 'bys_legales'){
                            foreach($atributos_rechazados[$key] as $modelo){
                                if($modelo['modelo'] == 'RepresentanteLegal'){ 
                                    $rp = RepresentanteLegal::findOne($modelo['id']);
                                    $rp->status_bys = Status::STATUS_RECHAZADO;
                                    $rp->save();
                                }
                            }
                        }
                    }
                }
                $model->created_id = Yii::$app->user->getId();
                $model->provider_id = $provider_id;
                $model->modelo = $key;
                $model->created_date = $this->getDateNow();
                $model->register_id = $mod->id_module;

                if(!$model->save(false))
                    var_dump(json_encode($model->errors));
                if(!$mod->save(false))
                    var_dump(json_encode($mod->errors));
            }
            $asig = Asignacion::find()->where(['and',['id_proveedor'=>$provider_id],['activo'=>true]])->one();
            if($asig){
                if($isVal==0)
                    $asig->date_rev = date('Y-m-d H:i:s');
                $asig->activo = false;
                $asig->last_updated_by = Yii::$app->user->getId();
                $asig->last_updated_at = date('Y-m-d H:i:s');
            }
            if($aux==0){
                $provider = Provider::findOne($provider_id);
                $provider->status_cotejar = Status::STATUS_PENDIENTE;
                $provider->save();
                $asig->activo = true;
                $asig->modo = 'VAL';
            }

            // si esta el validador y hay un rechazo, retorna todos los que no estan rechazados a revisados, para que el
            // expediente regrese al proveedor y pueda pasar directamente al revisor de nuevo al ser enviado a revision
            if($aux != 0 && $isVal==1){
                foreach ($modulos_validados as $key => $val) {
                    $mod = Module::find()->where(['and', ['model' => $key], ['provider_id' => $provider_id], ['!=','status_bys', Status::STATUS_RECHAZADO]])->one();
                    if($mod) {
                        $mod->status_bys = Status::STATUS_REVISADO;
                        $mod->save();
                    }
                }
            }

            if(count($rechazados) > 0){
                $provider = Provider::findOne($provider_id);
                $subject = "Solicitud de corrección de datos";
                GeneralController::sendEmail('/provider/correos/rechazos',null,$provider->email,$subject,['tipo_provider' => 'bys', 'rechazados' => $rechazados]);
                /* if($provider->permanently_disabled == 'ACTIVO' && date('m') == 1 && self::certExpYear($provider->vigencia)){
                    $provider->permanently_disabled = 'INACTIVO';
                    $provider->save(false);
                } */
            }

            if($aux == 0 && $isVal==1){
                $mensaje = $this->certByValidation($provider_id, $modulos_validados);


                $asig->last_updated_by = Yii::$app->user->getId();
                $asig->last_updated_at = date('Y-m-d H:i:s');
                $asig->activo = false;

                $consultaUser = ArrayHelper::getColumn(UsuarioConsulta::find()->select('user_id')->where(['type_user' => 'bys'])->asArray()->all(),'user_id');

                foreach ($consultaUser as $v){
                    if(($modUC = UserRelationProvider::find()->where(['and',['provider_id' => $provider_id],['user_id' => $v]])->one())!==null){
                        $modUC->status = 'ACTIVO';
                    }else{
                        $modUC = new UserRelationProvider();
                        $modUC->status = 'ACTIVO';
                        $modUC->user_id = $v;
                        $modUC->provider_id = $provider_id;
                        $modUC->created_by = Yii::$app->user->getId();
                    }
                    $modUC->save();
                }
            }
            $asig->save();
            ModulesComplete::deleteAll(['provider_id'=>$provider_id]);
            Yii::$app->db->transaction->commit();
        }
        if($mensaje)
            Yii::$app->session->setFlash('info',$mensaje);

        if($isVal==1)
            return $this->redirect('/validacion/index');
        else
            return $this->redirect('/asignacion/index');
    }

    //METODO DE PRUEBA DE NOTIFICACIONES(email, socket.io) AL VALIDAR
    public function actionTodosDummy(){
        $arr_validados = [];
        $arr_rechazados = [];
        $provider_id = strval($_POST['id']);
        $provider = Provider::findOne($provider_id);
        foreach ($_POST['validado'] as $key => $val){
            $mod = Module::find()->where(['and',['model'=>$key],['provider_id'=>$provider_id]])->one();
            if($val == 'validado'){
                /* $model = new DatosValidados();
                $model->status_bys = Status::STATUS_VALIDADO; */
                $mod->status_bys = Status::STATUS_VALIDADO;
                array_push($arr_validados, GeneralController::getNameModuloByKey($key));
            }else{
                /* $model = new Status();
                $model->status_bys = Status::STATUS_RECHAZADO; */
                $mod->status_bys = Status::STATUS_RECHAZADO;
                /* $model->motivo = $_POST['rechazo'][$key]; */
                array_push($arr_rechazados, GeneralController::getNameModuloByKey($key));
            }
            /* var_dump($mod); */
            /* $model->created_id = Yii::$app->user->getId();
            $model->provider_id = $provider_id;
            $model->modelo = $key;
            $model->created_date = $this->getDateNow();
            $model->register_id = $mod->id_module; */

            /* if(!$model->save(false))
                var_dump(json_encode($model->errors));
            if(!$mod->save(false))
                var_dump(json_encode($mod->errors)); */
        }
        GeneralController::notifyValidacion("<EMAIL>", $arr_validados, $arr_rechazados);
        var_dump($arr_validados);
        var_dump($arr_rechazados);
    }


    public function certByValidation($provider_id, $modulos_validados){

        $dataProv = Provider::findOne($provider_id);
        $hasSire = (HistoricoCertificados::find()->where(['and', ['provider_id' => $provider_id], ['tipo' => 'CERTIFICADO'], ['provider_type' => 'bys']])->count('1'))  >  0 ;
        $mensaje =  false;
        //si es su primer certificado y la funcionalidad de subir a sire esta activa
        if(!$hasSire && $_ENV['UP_TO_SIRE']){
            // preguntar primero a la API de sire si está el proveedor porsu RFC
            $existSire = SireController::consultaProviderByRfc($dataProv->rfc);
            // si no existe y no hay ningun problema con la api
            if(!$existSire->exist && !$existSire->error) {
                // traemos la data, en formato de insercion del proveedor en cuestion
                $data = SireController::getDataProviderRfc($dataProv->rfc);
                // se trata de insertar en la api de sire el proveedor
                $insertSire = SireController::insertProviderSire($data);
                //si se tiene un resultado exitoso se procede a actualizar el numero de sire en el padron y se notificia al usuario
                if ($insertSire->status){
                    $mensaje = 'Proveedor insertado correctamente en SIREGob, su certificado se ha generado automáticamente.';
                    $dataProv->Clave_ProveedorSire = $insertSire->num;
                }else{
                    //sino solo se notifica al usuario que hubo un problema, se registran los errores en la tabla error_inesperado
                    $mensaje = 'Ocurrió un incidente al insertar el proveedor en SIRE, favor de insertarlo manualmente.('.var_dump($insertSire->error).')';
                }
            }
            // si el proveedor ya esta dado de alta en sire, solo se actualiza el numero de provedor en el padrón
            if($existSire->exist) {
                $mensaje = 'El proveedor ya se encuentra dado de alta en SIRE, su certificado se generó automáticamente.';
                $dataProv->Clave_ProveedorSire = $existSire->num;
            }
            $dataProv->save(false);

            //Si se llegara a detectar un detalle en el api de sire, se notifica al usuario
            if($existSire->error)
                $mensaje = 'No de detecta correcto funcionamiento en la API de SIRE. No se realizó la busqueda/inserción en sire, favor de realizarla manualmente.';
        }

        if ($hasSire || ($dataProv->Clave_ProveedorSire && isset($existSire) && !$existSire->error) ) { // TODO agregar la validación concerniente, para que despues de agregado a sire, se pueda generar su certificado automáticamente
            $hcId = null;
            $mes = date('m');
            /* Esta condicion solo funciona cuando al proveedor se le debe generar un certificado */
            /* Se manejan 3 casos de acuerdo a la actualizacion descrita el dia 19/12/2024
            *   1.- Cuando el proveedor esta inactivo
            *   2.- Cuando el proveedor esta activo y realizo una modificacion
            *   3.- Cuando el proveedor activo realiza una modificacion en diciembre en el mismo a;o de vigencia
            */
            $not_date_certificate = is_null($dataProv->vigencia) || empty($dataProv->vigencia); //Despues del query, todo proveedor que tenga certificado deberia tener vigencia. Por si acaso se sigue validando xd
            $prov_inactive = $dataProv->permanently_disabled != 'ACTIVO';
            //Para considerarse actualizacion, debe estar activo, enviar a revision en Diciembre del a;o en el que expira su certificado
            $is_update_certificate = $dataProv->permanently_disabled == 'ACTIVO' && $mes == 12 && self::certExpYear($dataProv->vigencia);
            //Por tema de que no siempre se genera documento de actualizacion, se inicializa con la vigencia actual. 
            $nueva_vigencia = $dataProv->vigencia;

            //Ya no deberia haber sin fecha de vigencia, pero si lo hay o esta inactivo se va por la certificacion.
            if( $prov_inactive ){
                $nueva_vigencia = FunctionsHelper::getNuevaVigencia(); //(date("Y") + 1)."-12-31";
                $response_certificado = DocumentosController::generarCertificado($provider_id, 'bys', $nueva_vigencia);
                //$hcId = $this->documentocertificado($provider_id, 'bys');
                $hcId = $response_certificado['historico_certificado_id'];
                $old_cert_date = Yii::$app->db->createCommand("SELECT * FROM historico_certificados
                WHERE provider_id = $provider_id AND tipo = 'CERTIFICADO' and provider_type = 'bys' and historico_certificados_id not in($hcId)  
                ORDER BY historico_certificados_id DESC limit 1")->queryOne();

                $first_update_certificate = new FirstUpdateCertificate();
                $first_update_certificate->provider_id = $provider_id;
                $first_update_certificate->update_certificate = true;
                $first_update_certificate->historico_certificado_id = $hcId;
                $first_update_certificate->date_old_certificate = $old_cert_date['created_at'];
                $first_update_certificate->save();

                Movements::updateAll(['first_update_certificate_id' => $first_update_certificate->first_update_certificate_id], ['and', ['provider_id' => $provider_id], ['first_update_certificate_id' => null],['certificate' => true]]);
                self::AllSendNotification($dataProv->user_id, 'bys', null, 'CERTIFICADO', null, '¡Felicidades!', 'PROVIDER'); //Deberia haber uno de Actualizacion?
                $subject_correo = '¡Felicidades! ya formas parte del Padrón de Proveedores';
                self::sendEmail('/provider/correos/certificacion', null, $dataProv->email, $subject_correo, ['tipo_provider' => 'bys'], $response_certificado['documento']);
                //La indicacion es que si es certificacion, la vigencia solo sea al 31 de dic a 1 a;o de la validacion.
                //Ej: Validacion Febrero 2025, vigencia Diciembre 2026
                
            }else if( $is_update_certificate ){
                ///* Codigo prod/
                $nueva_vigencia = FunctionsHelper::getNuevaVigencia(true); //(date("Y") + 2)."-12-31";
                //Codigo de actualizacion, se implemento como prueba la nueva logica para la generacion de certificados
                $response_actualizacion = DocumentosController::generarCertificado($provider_id, 'bys', $nueva_vigencia, null, true);
                if(file_exists($response_actualizacion['documento'])){
                    $subject_correo = '¡Felicidades!, tu registro al Padrón de Proveedores del Gobierno del Estado de Nuevo León a sido actualizado';
                    GeneralController::sendEmail('/provider/correos/actualizacion_certificado', null, $dataProv->email, $subject_correo, ['tipo_provider' => 'bys'], $response_actualizacion['documento']);
                    //GeneralController::AllSendNotification($dataProv->user_id, 'bys', null, HistoricoCertificados::TIPO_ACTUALIZACION, null, '¡Felicidades!', 'PROVIDER');
                }
                /*/
                $nueva_vigencia = "2026-12-31";
                $response_actualizacion = DocumentosController::generarCertificado($provider_id, 'bys', $nueva_vigencia, '2024-12-31', true);
                if($response_actualizacion['historico_certificado_id']){
                    self::guardarHistorico($provider_id, $modulos_validados, $response_actualizacion['historico_certificado_id'],'bys', '2024-12-31');
                }
                if(file_exists($response_actualizacion['documento'])){
                    $subject_correo = '¡Felicidades!, tu registro al Padrón de Proveedores del Gobierno del Estado de Nuevo León a sido actualizado';
                    GeneralController::sendEmail('/provider/correos/actualizacion_certificado', null, $dataProv->email, $subject_correo, ['tipo_provider' => 'bys'], $response_actualizacion['documento']);
                    //GeneralController::AllSendNotification($dataProv->user_id, 'bys', null, HistoricoCertificados::TIPO_ACTUALIZACION, null, '¡Felicidades!', 'PROVIDER');
                }
                //*/
            } 


            /* if( ((is_null($dataProv->vigencia) || empty($dataProv->vigencia)) && ($dataProv->permanently_disabled != 'ACTIVO') || ( $dataProv->permanently_disabled == 'ACTIVO' && $mes == 12 )) ){
                $hcId = $this->documentocertificado($provider_id, 'bys');
                $hcDateOld = Yii::$app->db->createCommand("select * from historico_certificados 
                                where provider_id  = $provider_id and tipo = 'CERTIFICADO' and provider_type = 'bys' and historico_certificados_id not in($hcId)
                                order by historico_certificados_id DESC limit 1")->queryOne();
                $first_update_certificate = new FirstUpdateCertificate();
                $first_update_certificate->provider_id = $provider_id;
                $first_update_certificate->update_certificate = true;
                $first_update_certificate->historico_certificado_id = $hcId;
                $first_update_certificate->date_old_certificate = $hcDateOld['created_at'];
                $first_update_certificate->save();
                Movements::updateAll(['first_update_certificate_id' => $first_update_certificate->first_update_certificate_id], ['and', ['provider_id' => $provider_id], ['first_update_certificate_id' => null],['certificate' => true]]);
                self::AllSendNotification($dataProv->user_id, 'bys', null, 'CERTIFICADO', null, '¡Felicidades!', 'PROVIDER');
                $anio = ($mes==12) ? 2 : 1; //años que se le suman a la vigencia del certificado
                $dataProv->vigencia = (date("Y") + $anio)."-12-31";
            } */

            ///*Codigo prod/
                self::guardarHistorico($provider_id, $modulos_validados, $hcId,'bys');
            /*/
            if( !$is_update_certificate ){
                self::guardarHistorico($provider_id, $modulos_validados, $hcId,'bys');
            }
            //*/
            

            $dataProv->vigencia = $nueva_vigencia;
            $dataProv->permanently_disabled = 'ACTIVO';
            $dataProv->save(false);

            Provider::updateAll(['status_cotejar' => 'VALIDADO'], ['provider_id' => $provider_id]);
        }else{
            self::guardarHistorico($provider_id, $modulos_validados);
            $first_update_certificate = new FirstUpdateCertificate();
            $first_update_certificate->provider_id = $provider_id;
            $first_update_certificate->first_certificate = true;
            $first_update_certificate->save();
            Provider::updateAll(['permanently_disabled' => 'ACTIVO','status_cotejar' => 'VALIDADO'], ['provider_id' => $provider_id]);
        }
        return $mensaje;

    }

    public function actionValidar($modelo, $opciones = null, $register_id, $id, $id_extra = null)
    {
        $opcionesDecode = isset($opciones) ? base64_decode($opciones) : null;
        $opcionesUnserial = isset($opcionesDecode) ? unserialize($opcionesDecode) : null;
        $provider_id = $id_extra != null ? $id_extra : $id;
        $pro_data = Provider::find()->select(['rfc', 'tipo_persona', 'tipo_provider','permanently_disabled'])->where(['provider_id' => $provider_id])->one();

        $correoUsuarioUser_id = Provider::findBySql("select u.user_id, u.email from usuarios u 
                                 inner join provider p on p.user_id = u.user_id
                                 where p.provider_id = :param_provider", [':param_provider' => intval($provider_id)])->asArray()->one();

        $correoUsuario = $correoUsuarioUser_id['email'];
        $id_usuario = $correoUsuarioUser_id['user_id'];
        $role = Yii::$app->user->identity->role;
        $status_global = $role == 'VALIDADOR PROVEEDORES' ? 'status_bys' : ($role == 'VALIDADOR CONTRATISTAS' ? 'status_op' : '');
        $status_carta = $status_global == 'status_op' ? 'status_carta_op' : 'status_carta_bys';
        $tipo_provider_by_validador = $status_global == 'status_op' ? 'op' : 'bys';
        if ($modelo !== 'cotejar') {
            $model = new DatosValidados();
            $f_actual = $this->getDateNow();
            $t = Yii::$app->db->beginTransaction();

            $modelos = Yii::$app->request->post()['modelos'];


            if ($model->load(Yii::$app->request->post())) {
                $model->created_date = $f_actual;
                $model->created_id = Yii::$app->user->getId();
                $model->provider_id = $provider_id;

                $modeloUpdate = $modelo == 'direccion_nl' ? 'ubicacion' : $modelo;

                $rfc = Yii::$app->db->createCommand();
                if($modeloUpdate == 'certificacion'){
                    $paramsUpdate = [$status_global => 'VALIDADO','permissions' => true];
                }else{
                    $paramsUpdate = [$status_global => 'VALIDADO'];
                }
                $rfc->update('provider.' . $modeloUpdate,$paramsUpdate, [$register_id => $id]);
                if($modelo == 'representante_legal'){ RepresentanteLegal::updateAll(['validado' => true], ['and', [$register_id => $id], ['activo' => true] ]); }
                $rfc->execute();
                if ($model->save()) {
                    self::changeStatusExpired($provider_id, $id, $modelo);
                    if($tipo_provider_by_validador == 'bys'){

                        $modelMov = GeneralController::getModeloAndNameMovements($modelo);
                        if(isset($modelMov['modulos']) && !empty($modelMov['modulos'])){
                            Movements::updateAll(['active' => false],['and',['in','model',$modelMov['modulos']],['provider_id' => $provider_id]]);

                        }
                    }
                    LastSendValidationRelation::updateAll(['status' => 'VALIDADO'], ['and', ['provider_id' => $provider_id], ['model_name' => $modelo], ['register_id' => $id], ['provider_type' => $tipo_provider_by_validador], ['status' => 'PENDIENTE']]);

                    $valid = true;
                    $models_arr = json_decode(base64_decode($modelos), true);

                    foreach ($models_arr as $k => $value) {
                        $model_historico = new Historico();
                        if (DatosValidados::Verify($k)) {
                            $value[$status_global] = 'VALIDADO';
                        }
                        $model_historico->provider_id = $provider_id;
                        $model_historico->rfc = $pro_data['rfc'];
                        $model_historico->validador_id = $model->created_id;
                        $model_historico->modelo = $k;
                        $model_historico->data = $value;
                        $valid = $valid && $model_historico->save();
                    }
                    if ($valid) {

                        $t->commit();
                        $modelStatus = Status::find()
                            ->where(['and', ['register_id' => $id], [$status_global => 'TERMINADO PRO'], ['modelo' => $modelo]])->one();
                        if ($modelStatus) {
                            $modelStatus->$status_global = Status::STATUS_TERMINADO;
                            $modelStatus->save();
                        }


                    } else {
                        $t->rollBack();
                    }


                    $acta_firmada = Provider::find()->select($status_carta)->where(['provider_id' => $provider_id])->one()[$status_carta];


                    if ($status_global == 'status_bys') {
                        if (!Provider::find()->select('preregistro')->where(['provider_id' => $provider_id])->one()['preregistro']) {
                            if (GeneralController::verificarModulosPreregistro($status_global, $provider_id, $tipo_provider_by_validador, $acta_firmada)) {
                                Provider::updateAll(['preregistro' => true], ['provider_id' => $provider_id]);
                                $this->documentopreregistro($provider_id);
                                /* self::AllSendNotification($id_usuario, $tipo_provider_by_validador, null, 'PREREGISTRO', null, '¡Felicidades!', 'PROVIDER'); */
                            }
                        }

                        if (GeneralController::verificarModulosValidador($status_global, $provider_id, $tipo_provider_by_validador, $acta_firmada)) {
                            $statusCot = 'PENDIENTE AGENDAR CITA';
                            if ((HistoricoCertificados::find()->where(['and', ['provider_id' => $provider_id], ['tipo' => 'CERTIFICADO'], ['provider_type' => 'bys']])->count('1')) > 0) {
                                $verifyCotejar = Movements::find()->where(['and', ['provider_id' => $provider_id], ['in', 'model', ['rfc', 'rfc_acta_constitutiva',
                                     'rfc_curp', 'modificacion_acta', 'relacion_accionistas',
                                    'representante_legal','ubicacion','direccion_nl','perfil_ubicacion']], ['first_update_certificate_id' => null],['certificate' => true]])->count('1');
                                if ($verifyCotejar == 0 && $pro_data['permanently_disabled']!=='INACTIVO') {
                                    $statusCot = 'VALIDADO';
                                    if ((Movements::find()->where(['and', ['provider_id' => $provider_id], ['!=', 'model', 'fotografia_negocio'], ['first_update_certificate_id' => null],['certificate' => true]])->count('1')) > 0) {
                                        $hcId = $this->documentocertificado($provider_id, $tipo_provider_by_validador);
                                        $hcDateOld = Yii::$app->db->createCommand("select * from historico_certificados 
                                        where provider_id  = $provider_id and tipo = 'CERTIFICADO' and provider_type = 'bys' and historico_certificados_id not in($hcId)
                                            order by historico_certificados_id DESC limit 1")->queryOne();
                                        $first_update_certificate = new FirstUpdateCertificate();
                                        $first_update_certificate->provider_id = $provider_id;
                                        $first_update_certificate->update_certificate = true;
                                        $first_update_certificate->historico_certificado_id = $hcId;
                                        $first_update_certificate->date_old_certificate = $hcDateOld['created_at'];
                                        $first_update_certificate->save();
                                        Movements::updateAll(['first_update_certificate_id' => $first_update_certificate->first_update_certificate_id], ['and', ['provider_id' => $provider_id], ['first_update_certificate_id' => null],['certificate' => true]]);
                                        self::AllSendNotification($id_usuario, $tipo_provider_by_validador, null, 'CERTIFICADO', null, '¡Felicidades!', 'PROVIDER');
                                    }
                                }else if ((Movements::find()->where(['and', ['provider_id' => $provider_id], ['in','model',['ubicacion','direccion_nl','perfil_ubicacion']],['first_update_certificate_id' => null],['certificate' => true]])->count('1'))>0){
                                    $statusCot = 'PENDIENTE ACTIVAR AGENDA';
                                }
                            }else{
                                $statusCot = 'PENDIENTE ACTIVAR AGENDA';
                            }

                            if ($statusCot == 'PENDIENTE ACTIVAR AGENDA') {
                                if (($vi = Visit::find()->where(['and', ['provider_id' => $provider_id], ['status' => true]])->one()) === null) {
                                    $modVisit = new Visit();
                                    $modVisit->provider_id = $provider_id;
                                    $modVisit->save(false);
                                }
                                self::sendEmail('/provider/correos/visita', null, $correoUsuario, 'Visita', [], null);
                            }
                            Provider::updateAll(['status_cotejar' => $statusCot], ['provider_id' => $provider_id]);
                        }
                    } else {

                        if (($vi = Visit::find()->where(['and', ['provider_id' => $provider_id], ['status' => true],['provider_type' => 'op']])->one()) === null) {
                            if(($movements = MovementsOp::find()->where(['and',['model' => 'Ubicacion'],['provider_id' => $provider_id],['origin_id' => $id]])->one())!=null
                                || (Visit::find()->where(['and', ['provider_id' => $provider_id],['provider_type' => 'op']])->count(1)) === 0
                            ){

                                if($modeloUpdate == 'ubicacion'){
                                    MovementsOp::deleteAll(['and',['origin_id' => $id],['model' => 'Ubicacion']]);
                                }

                                $modVisit = new Visit();
                                $modVisit->provider_id = $provider_id;
                                $modVisit->provider_type = 'op';
                                $modVisit->save(false);
                            }
                        }else{
                            $vi->created_at = date('Y-m-d H:i:s');
                            $vi->save();
                        }

                        $consultaUser = ArrayHelper::getColumn(UsuarioConsulta::find()->select('user_id')->where(['type_user' => 'op'])->asArray()->all(),'user_id');

                        foreach ($consultaUser as $v){
                            if(($modUC = UserRelationProvider::find()->where(['and',['provider_id' => $provider_id],['user_id' => $v]])->one())!==null){
                                $modUC->status = 'ACTIVO';
                            }else{
                                $modUC = new UserRelationProvider();
                                $modUC->status = 'ACTIVO';
                                $modUC->user_id = $v;
                                $modUC->provider_id = $provider_id;
                                $modUC->created_by = Yii::$app->user->getId();
                            }

                            $modUC->save();
                        }

                    }

                    $this->sendNotification('success', 'Se ha validó un registro en el módulo <br><a href="' . Url::home(true) . self::viewRedirect($modelo) . '">' . $modelo . '</a>', $id_usuario);
                    $mod = $this->getModelo($modelo);

                    $moduloLimpio = self::limpiarCadena($mod);

                    self::sendEmail('/provider/correos/validado', null, $correoUsuario, 'Información ingresada corrrectamente', ['modulo' => $mod, 'tipo_provider' => $tipo_provider_by_validador]);

                    self::AllSendNotification($id_usuario, $tipo_provider_by_validador, $modelo, $moduloLimpio, $mod, 'Validado', 'PROVIDER',$id,$register_id,$modeloUpdate);


                    Yii::$app->session->setFlash('success', 'Los datos se validaron correctamente');

                    $this->ModulesCompleteOut($provider_id,true);
                    $urlRedirect = isset($opcionesUnserial) ? self::viewRedirect($modelo)."?".http_build_query($opcionesUnserial ) : self::viewRedirect($modelo);
                    $this->redirect([$urlRedirect]);
                }
            } else {
                return $this->render('create', [
                    'model' => $model,
                ]);
            }
        } else {


            $model = new DatosValidados();
            $model_acta_c = new ActaCircunstanciada();
            $f_actual = $this->getDateNow();
            $model->scenario = Status::SCENARIO_CREATE_COTEJAR;
            if ($model->load(Yii::$app->request->post())) {
                $model->created_date = $f_actual;
                $model->created_id = Yii::$app->user->getId();
                $model->provider_id = $provider_id;
                $this->makeDir($model->path_file);

                if (!empty($model->file)) {
                    $new_nameFile = str_replace('archivos_tmp/', $model->path_file . '/', $model->file);
                    $this->copyFile($model->file, $new_nameFile);
                    $model->file = $new_nameFile;
                }

                if ($model->save()) {
                    if($model_acta_c->load(Yii::$app->request->post())){
                        $model_acta_c->created_by = Yii::$app->user->getId();
                        $model_acta_c->acuse = $model->file;
                        $model_acta_c->model = 'datos_validados';
                        $model_acta_c->register_id = $model->validado_id;
                        $model_acta_c->save(false);
                    }
                    self::sendEmail('/provider/correos/cotejo_validado', null, $correoUsuario, 'Cotejo validado', []);
                    Provider::updateAll(['status_cotejar' => 'VALIDADO'], ['provider_id' => $provider_id]);
                    Calendar::updateAll(['status' => 'FINALIZADA'], ['and', ['status' => 'CONFIRMADA'], ['user' => $id_usuario]]);
                    NotAssist::updateAll(['number_not_assist' => 0], ['user_id' => $id_usuario]);
                    UltimaDeclaracion::updateAll(['created_date' => date('Y-m-d H:i:s')], ['provider_id' => $provider_id]);
                    ComprobanteDomicilio::updateAll(['created_date' => date('Y-m-d H:i:s')], ['provider_id' => $provider_id]);

                    if ((HistoricoCertificados::find()->where(['and', ['provider_id' => $provider_id], ['tipo' => 'CERTIFICADO'], ['provider_type' => 'bys']])->count('1')) > 0) {
                        $hcId = $this->documentocertificado($provider_id, $tipo_provider_by_validador);
                        $hcDateOld = Yii::$app->db->createCommand("select * from historico_certificados 
                                    where provider_id  = $provider_id and tipo = 'CERTIFICADO' and provider_type = 'bys' and historico_certificados_id not in($hcId)
                                            order by historico_certificados_id DESC limit 1")->queryOne();

                        $first_update_certificate = new FirstUpdateCertificate();
                        $first_update_certificate->provider_id = $provider_id;
                        $first_update_certificate->update_certificate = true;
                        $first_update_certificate->historico_certificado_id = $hcId;
                        $first_update_certificate->date_old_certificate = $hcDateOld['created_at'];
                        $first_update_certificate->save();
                        Movements::updateAll(['first_update_certificate_id' => $first_update_certificate->first_update_certificate_id], ['and', ['provider_id' => $provider_id], ['first_update_certificate_id' => null]]);
                        self::AllSendNotification($id_usuario, $tipo_provider_by_validador, null, 'CERTIFICADO', null, '¡Felicidades!', 'PROVIDER');
                    } else {
                        $first_update_certificate = new FirstUpdateCertificate();
                        $first_update_certificate->provider_id = $provider_id;
                        $first_update_certificate->first_certificate = true;
                        $first_update_certificate->save();
                        Provider::updateAll(['permanently_disabled' => 'ACTIVO'], ['provider_id' => $provider_id]);
                    }

                    /* self::AllSendNotification($id_usuario, 'bys', null, 'COTEJO', 'COTEJO', 'Validado', 'PROVIDER'); */
                }
            }

            return $this->redirect('/provider/index-provider-cotejar');


        }

    }


    public function actionValidateUpdateCert($provider_id = null,$tipo_provider_by_validador = null,$date=null){

        if($provider_id && $tipo_provider_by_validador){
            $providerData = Provider::find()->where(['provider_id' => $provider_id])->one();
            $statusCot = 'VALIDADO';
            //if ((Movements::find()->where(['and', ['provider_id' => $provider_id], ['!=', 'model', 'fotografia_negocio'], ['first_update_certificate_id' => null],['certificate' => true]])->count('1')) > 0) {
                $hcId = $this->documentocertificado($provider_id, $tipo_provider_by_validador,$date);
                $hcDateOld = Yii::$app->db->createCommand("select * from historico_certificados 
                                        where provider_id  = $provider_id and tipo = 'CERTIFICADO' and provider_type = 'bys' and historico_certificados_id not in($hcId)
                                            order by historico_certificados_id DESC limit 1")->queryOne();
                $first_update_certificate = new FirstUpdateCertificate();
                $first_update_certificate->provider_id = $provider_id;
                $first_update_certificate->update_certificate = true;
                $first_update_certificate->historico_certificado_id = $hcId;
                $first_update_certificate->date_old_certificate = $hcDateOld['created_at'];
                $first_update_certificate->save();
                Movements::updateAll(['first_update_certificate_id' => $first_update_certificate->first_update_certificate_id], ['and', ['provider_id' => $provider_id], ['first_update_certificate_id' => null],['certificate' => true]]);
                self::AllSendNotification($providerData->user_id, $tipo_provider_by_validador, null, 'CERTIFICADO', null, '¡Felicidades!', 'PROVIDER');
                Provider::updateAll(['status_cotejar' => $statusCot], ['provider_id' => $provider_id]);
           /* }else {
                $first_update_certificate = new FirstUpdateCertificate();
                $first_update_certificate->provider_id = $provider_id;
                $first_update_certificate->first_certificate = true;
                $first_update_certificate->save();
            }*/
        }
    }


    /* Ya no se utiliza */
    public function actionGenerateMultiCert(){

        /* $data = Yii::$app->db->createCommand("select * from historico_certificados where tipo = 'CERTIFICADO' and provider_id in(
            270,
            1246,
            2838,
            3098,
            4599,
            4885,
            5690,
            5692,
            5802,
            5926,
            5928,
            6031,
            6166,
            6186,
            6197,
            6205) and provider_type = 'bys' order by provider_id")->queryAll();




        foreach ($data as $v){
            $provider_id = $v['provider_id'];
            //if ((HistoricoCertificados::find()->where(['and', ['provider_id' => $provider_id], ['tipo' => 'CERTIFICADO'], ['provider_type' => 'bys']])->count('1')) > 1) {
                $hcId = $this->documentocertificado($provider_id, 'bys',base64_encode($v['created_at']));
                $hcDateOld = Yii::$app->db->createCommand("select * from historico_certificados 
                                    where provider_id  = $provider_id and tipo = 'CERTIFICADO' and provider_type = 'bys' and historico_certificados_id not in($hcId)
                                            order by historico_certificados_id DESC limit 1")->queryOne();

                $first_update_certificate = new FirstUpdateCertificate();
                $first_update_certificate->provider_id = $provider_id;
                $first_update_certificate->update_certificate = true;
                $first_update_certificate->historico_certificado_id = $hcId;
                $first_update_certificate->date_old_certificate = $hcDateOld['created_at'];
                $first_update_certificate->save();
                Movements::updateAll(['first_update_certificate_id' => $first_update_certificate->first_update_certificate_id], ['and', ['provider_id' => $provider_id], ['first_update_certificate_id' => null]]);

                $id_usuario = Provider::find()->select('user_id')->where(['provider_id' => $provider_id])->one()['user_id'];
                self::AllSendNotification($id_usuario, 'bys', null, 'CERTIFICADO', null, '¡Felicidades!', 'PROVIDER');
           } else {
                $first_update_certificate = new FirstUpdateCertificate();
                $first_update_certificate->provider_id = $provider_id;
                $first_update_certificate->first_certificate = true;
                $first_update_certificate->save();
            }

        } */
    }


    public function actionGeneratePreReg(){


        $data = Yii::$app->db->createCommand("select * from historico_certificados where tipo = 'PREREGISTRO' and created_at::date between '2021-10-25' and '2021-10-31'")->queryAll();

        foreach ($data as $v){
            $provider_id = $v['provider_id'];

            $this->documentopreregistro($provider_id);

            $id_usuario = Provider::find()->select('user_id')->where(['provider_id' => $provider_id])->one()['user_id'];

            /* self::AllSendNotification($id_usuario, 'bys', null, 'PREREGISTRO', null, '¡Felicidades!', 'PROVIDER'); */



        }
    }


    public function actionChangeStatusCotejar($provider_id=null){
        //http://secop.nl/validador/change-status-cotejar?provider_id=2476

        $pro_data = Provider::find()->select(['rfc', 'tipo_persona', 'tipo_provider','permanently_disabled','user_id'])->where(['provider_id' => intval($provider_id)])->one();

        if($pro_data){
            $statusCot = 'PENDIENTE AGENDAR CITA';
            $correoUsuarioUser_id = Provider::findBySql("select u.user_id, u.email from usuarios u 
                                 inner join provider p on p.user_id = u.user_id
                                 where p.provider_id = :param_provider", [':param_provider' => intval($provider_id)])->asArray()->one();
            $correoUsuario = $correoUsuarioUser_id['email'];
            if ((HistoricoCertificados::find()->where(['and', ['provider_id' => $provider_id], ['tipo' => 'CERTIFICADO'], ['provider_type' => 'bys']])->count('1')) > 0) {
                $verifyCotejar = Movements::find()->where(['and', ['provider_id' => $provider_id], ['in', 'model', ['rfc', 'rfc_acta_constitutiva',
                    'rfc_curp', 'modificacion_acta', 'relacion_accionistas',
                    'representante_legal','ubicacion','direccion_nl','perfil_ubicacion']], ['first_update_certificate_id' => null],['certificate' => true]])->count('1');
                if ($verifyCotejar == 0 && $pro_data['permanently_disabled']!=='INACTIVO') {
                    $statusCot = 'VALIDADO';
                    if ((Movements::find()->where(['and', ['provider_id' => $provider_id], ['!=', 'model', 'fotografia_negocio'], ['first_update_certificate_id' => null],['certificate' => true]])->count('1')) > 0) {
                        $hcId = $this->documentocertificado($provider_id, 'bys');
                        $hcDateOld = Yii::$app->db->createCommand("select * from historico_certificados 
                                        where provider_id  = $provider_id and tipo = 'CERTIFICADO' and provider_type = 'bys' and historico_certificados_id not in($hcId)
                                            order by historico_certificados_id DESC limit 1")->queryOne();
                        $first_update_certificate = new FirstUpdateCertificate();
                        $first_update_certificate->provider_id = $provider_id;
                        $first_update_certificate->update_certificate = true;
                        $first_update_certificate->historico_certificado_id = $hcId;
                        $first_update_certificate->date_old_certificate = $hcDateOld['created_at'];
                        $first_update_certificate->save();
                        Movements::updateAll(['first_update_certificate_id' => $first_update_certificate->first_update_certificate_id], ['and', ['provider_id' => $provider_id], ['first_update_certificate_id' => null],['certificate' => true]]);
                        self::AllSendNotification($pro_data['user_id'], 'bys', null, 'CERTIFICADO', null, '¡Felicidades!', 'PROVIDER');
                    }
                }else if ((Movements::find()->where(['and', ['provider_id' => $provider_id], ['in','model',['ubicacion','direccion_nl','perfil_ubicacion']],['first_update_certificate_id' => null],['certificate' => true]])->count('1'))>0){
                    $statusCot = 'PENDIENTE ACTIVAR AGENDA';
                }
            }else{
                $statusCot = 'PENDIENTE ACTIVAR AGENDA';
            }

            if ($statusCot == 'PENDIENTE ACTIVAR AGENDA') {
                if (($vi = Visit::find()->where(['and', ['provider_id' => $provider_id], ['status' => true]])->one()) === null) {
                    $modVisit = new Visit();
                    $modVisit->provider_id = $provider_id;
                    $modVisit->save(false);
                }
                self::sendEmail('/provider/correos/visita', null, $correoUsuario, 'Visita', [], null);
            }
            Provider::updateAll(['status_cotejar' => $statusCot], ['provider_id' => $provider_id]);

            NoLocalizados::updateAll(['status' => false],['and',['type_register' => 'NO_LOCALIZADOS'],['provider_id' => $provider_id]]);
        }

        return $this->redirect('/provider/no-localizados');
    }


    public function actionRechazar($modelo, $opciones = null, $register_id, $id)
    {
        $opcionesDecode = isset($opciones) ? base64_decode($opciones) : null;
        $opcionesUnserial = isset($opcionesDecode) ? unserialize($opcionesDecode) : null;
        $model = new Status();
        $model_acta_c = new ActaCircunstanciada();
        $f_actual = $this->getDateNow();
        $role = Yii::$app->user->identity->role;
        $status_global = $role == 'VALIDADOR PROVEEDORES' ? 'status_bys' : 'status_op';
        $tipo_provider = $status_global == 'status_bys' ? 'bys' : 'op';
        if ($modelo == 'cotejar') {
            $model->scenario = Status::SCENARIO_CREATE_COTEJAR;
            if (Yii::$app->request->isAjax && $model->load(Yii::$app->request->post())) {
                Yii::$app->response->format = Response::FORMAT_JSON;
                $arrErr = ActiveForm::validate($model);
                if (!empty($arrErr)) {
                    return [
                        'data' => [
                            'success' => false,
                            'error' => $arrErr,
                        ]
                    ];
                }
            }
        }

        if ($model->load(Yii::$app->request->post())) {
            $model->created_date = $f_actual;
            $model->created_id = Yii::$app->user->getId();

            if ($modelo !== 'cotejar') {
                Status::updateAll([$status_global => 'TERMINADO'], ['and', ['register_id' => $id],
                    ['modelo' => $model->modelo], [$status_global => 'TERMINADO PRO']]);


                $modeloUpdate = $modelo == 'direccion_nl' ? 'ubicacion' : $modelo;
                $rfc = Yii::$app->db->createCommand();
                if($modeloUpdate == 'certificacion'){
                    $paramsUpdate = [$status_global => 'RECHAZADO','permissions' => true];
                }else{
                    $paramsUpdate = [$status_global => 'RECHAZADO'];
                }
                $rfc->update('provider.' . $modeloUpdate, $paramsUpdate, [$register_id => $id]);
                $rfc->execute();

                LastSendValidationRelation::updateAll(['status' => 'RECHAZADO'], ['and', ['model_name' => $modelo], ['register_id' => $id], ['provider_type' => $tipo_provider], ['status' => 'PENDIENTE']]);

            } else {
                $this->makeDir($model->path_file);
                if (!empty($model->file)) {
                    $new_nameFile = str_replace('archivos_tmp/', $model->path_file . '/', $model->file);
                    $this->copyFile($model->file, $new_nameFile);
                    $model->file = $new_nameFile;
                }
            }

            if ($model->save()) {

                if($model_acta_c->load(Yii::$app->request->post())){

                    $model_acta_c->created_by = Yii::$app->user->getId();
                    $model_acta_c->acuse = $model->file;
                    $model_acta_c->model = 'status';
                    $model_acta_c->register_id = $model->status_id;
                    $model_acta_c->save(false);

                }

                $id_provider = '';
                if (in_array($modelo,['perfil','rfc','alta_hacienda','intervencion_bancaria','ultima_declaracion','cotejar'])) {
                    $id_user = Yii::$app->db->createCommand("select user_id from provider where provider_id = " . $id)->queryOne();
                    $id_proveedor = $id;
                } else if (in_array($modelo,['certificacion','ubicacion','direccion_nl','fotografia_negocio','clientes_contratos','relacion_accionistas','experiencia','personal_tecnico','maquinaria_equipos','organigrama','estado_financiero','modificacion_acta','representante_legal'])) {
                    $id_provider = Yii::$app->db->createCommand("select provider_id from provider." . $modeloUpdate . " where $register_id =" . $id)->queryOne();
                    $id_proveedor = $id_provider['provider_id'];
                    $id_user = Yii::$app->db->createCommand("select user_id from provider where provider_id = " . $id_proveedor)->queryOne();
                } else if ($modelo == 'historico_carta_protesta') {
                    $id_provider = Yii::$app->db->createCommand("select provider_id from provider." . $modeloUpdate . " where $register_id =" . $id)->queryOne();
                    $id_proveedor = $id_provider['provider_id'];
                    $id_user = Yii::$app->db->createCommand("select user_id from provider where provider_id = " . $id_proveedor)->queryOne();
                    Yii::$app->db->createCommand("update provider set rfc = NULL ,status_carta_$tipo_provider = 'PENDIENTE' where provider_id = $id_proveedor")->execute();

                }

                $model->provider_id = $id_proveedor;
                $model->save(false);
                if($tipo_provider == 'bys'){
                    $modelMov = GeneralController::getModeloAndNameMovements($modelo);
                    if(isset($modelMov['modulos']) && !empty($modelMov['modulos'])){
                        Movements::updateAll(['active' => false],['and',['in','model',$modelMov['modulos']],['provider_id' => $id_proveedor]]);

                    }
                }

                self::changeStatusExpired($id_provider, $id, $modelo);

                $motivo = $model->motivo;
                $id_usuario = $id_user['user_id'];
                $correoUsuario = Yii::$app->db->createCommand("select email from usuarios where user_id = " . $id_usuario)->queryOne();
                if ($modelo !== 'cotejar') {
                    $modulo = $this->getModelo($modelo);
                    //self::sendEmail('/provider/correos/rechazo', null, $correoUsuario['email'], 'Verifica tu información', ['modulo' => $modulo, 'motivo' => $motivo, 'tipo_provider' => $tipo_provider]);

                    $this->sendNotification('error', 'Se ha rechazado un registro en el módulo: <br><a href="' . Url::home(true) . self::viewRedirect($modelo) . '">' . $modulo . '</a>', $id_usuario);

                    $nombre_modulo = $this->getNewModelo($modelo);
                    $provider = Provider::find()->where(['user_id' => $id_usuario])->one();
                    $subject = "Solicitud de corrección de datos a módulo";
                    GeneralController::sendEmail('/provider/correos/rechazo',null,$provider->email, $subject,['tipo_provider' => 'op', 'nombre_modulo' => $nombre_modulo, 'motivo' => $motivo]);

                    $moduloLimpio = self::limpiarCadena($modulo);

                    self::AllSendNotification($id_usuario, $tipo_provider, $modelo, $moduloLimpio, $modulo, 'Rechazado', 'PROVIDER',$id,$register_id,$modeloUpdate);

                    $this->ModulesCompleteOut($id_proveedor);

                    $urlRedirect = isset($opcionesUnserial) ? self::viewRedirect($modelo)."?".http_build_query($opcionesUnserial ) : self::viewRedirect($modelo);
                    $this->redirect([$urlRedirect]);
                } else {
                    Provider::updateAll(['status_cotejar' => 'RECHAZADO'], ['provider_id' => $id_proveedor]);
                    Calendar::updateAll(['status' => 'FINALIZADA'], ['and', ['status' => 'CONFIRMADA'], ['user' => $id_usuario]]);
                    NotAssist::updateAll(['number_not_assist' => 0], ['user_id' => $id_usuario]);
                    self::sendEmail('/provider/correos/cotejo_rechazado', null, $correoUsuario['email'], 'Verifica tu información', ['modulo' => 'COTEJAMIENTO', 'motivo' => $model->motivo, 'tipo_provider' => $tipo_provider]);
                   /*  self::AllSendNotification($id_usuario, 'bys', null, 'COTEJO', 'COTEJO', 'Rechazado', 'PROVIDER'); */


                    return $this->redirect('/provider/index-provider-cotejar');

                }


            }

        } else {
            return $this->render('create', [
                'model' => $model,
            ]);
        }
    }


    public function changeStatusExpired($provider_id, $register, $modelo){

        if (($modelo == 'ultima_declaracion' || $modelo == 'rfc') && $provider_id) {
            ExpirationDocuments::updateAll(['status_revisor' => true], ['provider_id' => $provider_id, 'module' => $modelo]);
        } elseif ($register) {
            ExpirationDocuments::updateAll(['status_revisor' => true], ['register_id' => $register, 'module' => $modelo]);
        }

    }


    public function limpiarCadena($cadena){

        $cadena = trim($cadena);
        $cadena = str_replace(['á', 'à', 'ä', 'â', 'ª', 'Á', 'À', 'Â', 'Ä', 'Ã'], ['a', 'a', 'a', 'a', 'a', 'A', 'A', 'A', 'A', 'A'], $cadena);
        $cadena = str_replace(['é', 'è', 'ë', 'ê', 'É', 'È', 'Ê', 'Ë'], ['e', 'e', 'e', 'e', 'E', 'E', 'E', 'E'], $cadena);
        $cadena = str_replace(['í', 'ì', 'ï', 'î', 'Í', 'Ì', 'Ï', 'Î'], ['i', 'i', 'i', 'i', 'I', 'I', 'I', 'I'], $cadena);
        $cadena = str_replace(['ó', 'ò', 'ö', 'ô', 'Ó', 'Ò', 'Ö', 'Ô'], ['o', 'o', 'o', 'o', 'O', 'O', 'O', 'O'], $cadena);
        $cadena = str_replace(['ú', 'ù', 'ü', 'û', 'Ú', 'Ù', 'Û', 'Ü'], ['u', 'u', 'u', 'u', 'U', 'U', 'U', 'U'], $cadena);
        $cadena = strtoupper($cadena);
        return $cadena;

    }


    static function viewRedirect($modelo = null){

        $tipo = self::providerType();
        $array = [
            'perfil' => "legales/index-validador",
            'rfc' => "legales/index-validador",
            'alta_hacienda' => "legales/index-validador",
            'intervencion_bancaria' => "financieros/index-validador",
            'escritura_publica' => "legales/index-validador",
            'relacion_accionistas' => "legales/index-validador",
            'modificacion_acta' => "legales/index-validador",
            'acta_constitutiva' => "legales/index-validador",
            'representante_legal' => "legales/index-validador",
            'socio' => "socio/index-validador",
            'balance_estado' => "financieros/index-validador",
            'capacidad_contratacion' => "financieros/index-validador",
            'declaracion_iva' => "financieros/index-validador",
            'estado_financiero' => "financieros/index-validador",
            'declaracion_isr' => "financieros/index-validador",
            'ultima_declaracion' => "financieros/index-validador",
            'experiencia' => "tecnicos/index-validador",
            'personal_tecnico' => "tecnicos/index-validador",
            'organigrama' => "tecnicos/index-validador",
            'maquinaria_equipos' => "tecnicos/index-validador",
            'capacidad_produccion' => "tecnicos/index-validador",
            'certificacion' => "tecnicos/index-validador",
            'clientes_contratos' => "tecnicos/index-validador",
            'curriculum' => "tecnicos/index-validador",
            'fotografia_negocio' => "tecnicos/index-validador",
            'ubicacion' => "tecnicos/index-validador",
            'direccion_nl' => "tecnicos/index-validador",
            'historico_carta_protesta' => "carta/index-validador"
        ];

        return isset($array[$modelo]) ? $array[$modelo] : '';
    }


    public function getModelo($modelo)
    {

        $modulo = 'Modulo';

        switch ($modelo) {
            case 'perfil':
                $modulo = 'Perfil';
                break;
            case 'rfc':
                $modulo = 'Datos legales';
                break;
            case 'alta_hacienda':
                $modulo = 'Actividad económica';
                break;
            case 'relacion_accionistas':
                $modulo = 'Relación accionistas';
                break;
            case 'intervencion_bancaria':
                $modulo = 'Método de pago';
                break;
            case 'ultima_declaracion':
                $modulo = 'Datos financieros';
                break;
            case 'certificacion':
                $modulo = 'Credenciales';
                break;
            case 'ubicacion':
                $modulo = 'Ubicación';
                break;
            case 'direccion_nl':
                $modulo = 'Direccion Nl';
                break;
            case 'fotografia_negocio':
                $modulo = 'Fotografía negocio';
                break;
            case 'clientes_contratos':
                $modulo = 'Currículum';
                break;
            case 'modificacion_acta':
                $modulo = 'Modificación acta';
                break;

            case 'representante_legal':
                $modulo = 'Representante legal';
                break;


            //Contratistas
            case 'experiencia':
                $modulo = 'Experiencia comercial';
                break;
            case 'personal_tecnico':
                $modulo = 'Personal Técnico';
                break;
            case 'maquinaria_equipos':
                $modulo = 'Maquinaria y equipos';
                break;
            case 'organigrama':
                $modulo = 'Organigrama';
                break;
            case 'estado_financiero':
                $modulo = 'Estados financiero';
                break;
            case 'historico_carta_protesta':
                $modulo = 'Carta protesta';
                break;

        }

        return $modulo;

    }

    public function getNewModelo($modelo){
        $modulo = 'Modulo';

        switch ($modelo) {
            case 'perfil':
                $modulo = 'Mi Perfil (Generales)';
                break;
            case 'rfc':
                $modulo = 'Legales (Datos Legales)';
                break;
            case 'alta_hacienda':
                $modulo = 'Actividad Económica';
                break;
            case 'relacion_accionistas':
                $modulo = 'Legales (Relación accionistas)';
                break;
            case 'intervencion_bancaria':
                $modulo = 'Método pago';
                break;
            case 'ultima_declaracion':
                $modulo = 'Datos financieros';
                break;
            case 'certificacion':
                $modulo = 'Credenciales';
                break;
            case 'ubicacion':
                $modulo = 'Domicilio';
                break;
            case 'direccion_nl':
                $modulo = 'Domicilio (Direccion NL)';
                break;
            case 'fotografia_negocio':
                $modulo = 'Fotografía';
                break;
            case 'clientes_contratos':
                $modulo = 'Mi Perfil (Referencias Comerciales)';
                break;
            case 'modificacion_acta':
                $modulo = 'Legales (Modificación acta)';
                break;

            case 'representante_legal':
                $modulo = 'Legales (Representante legal)';
                break;

            case 'experiencia':
                $modulo = 'Experiencia';
                break;
            case 'personal_tecnico':
                $modulo = 'Datos Técnicos (Personal Técnico)';
                break;
            case 'maquinaria_equipos':
                $modulo = 'Maquinaria';
                break;
            case 'organigrama':
                $modulo = 'Datos Técnicos (Organigrama)';
                break;
            case 'estado_financiero':
                $modulo = 'Datos financieros';
                break;
            case 'historico_carta_protesta':
                $modulo = 'Carta protesta';
                break;

        }

        return $modulo;
    }

    /**
     * Finds the DatosValidados model based on its primary key value.
     * If the model is not found, a 404 HTTP exception will be thrown.
     * @param integer $id
     * @return DatosValidados the loaded model
     * @throws NotFoundHttpException if the model cannot be found
     */
    protected function findModel($id)
    {
        if (($model = DatosValidados::findOne($id)) !== null) {
            return $model;
        } else {
            throw new NotFoundHttpException('The requested page does not exist.');
        }
    }

    //Saca de la tabla de modulos completos al usuariio depsues de que se le hace una validavion (valido o rechazado)
    protected function ModulesCompleteOut($provider_id,$in=false){
        $model = ModulesComplete::find()->where(['provider_id'=>$provider_id])->one();
        if($model)
            $model->delete();
        if($in){
            GeneralController::allModulesComplete($provider_id);
        }
    }

    public function guardarHistorico($provider_id, $modulos, $hcid = null, $tipo = 'bys', $fecha_validacion = null){
        $provider = Provider::findOne($provider_id);
        $tipo_persona = $provider->tipo_persona;
        $modulos_opciones = [
            'bys_legales' =>[
                "ModificacionActa" => [ "opciones" => ["and", ["activo" => true]] ],
                "RepresentanteLegal" => [ "opciones" => ["and", ["activo" => true]] ],
                "RelacionAccionistas" => [ "opciones" => ["and", ["activo" => true]] ]
            ],
            "bys_experiencia" => [ 
                "ClientesContratos" =>[ "opciones" => ["and", ["activo" => true], ["tipo" => "bys"] ] ],
                "Certificacion" => [ "opciones" => [ "and", ["activo" => true] ] ]
            ],
            "bys_domicilio" => [
                "Ubicacion" =>[ "opciones" => ["and", ["activo" => true] ] ],
                "FotografiaNegocio" =>[ "opciones" => ["and", ["activo" => true]] ]
            ],
            "bys_bys"=>[
                "Giro" => [ "opciones" => ["and", ["active" => true]] ],
                "ProviderGiro" => [ "opciones" => ["and", ["active" => true]] ]
            ]
        ];
        foreach($modulos as $key => $value){
            $modulo_atributos = GeneralController::getModuloAtributos($key, $tipo_persona);
            $modulo_opciones = isset($modulos_opciones[$key]) ? $modulos_opciones[$key] : null;
            GeneralController::guardarModuloHistorico($modulo_atributos, $modulo_opciones, $provider_id, $hcid, $tipo, $fecha_validacion);
        }
    }

    /* Identifica si ya expiro o expira este a;o el certificado */
    public function certExpYear(string $dateStr){
        if(is_null($dateStr) || empty($dateStr)){ return true; }
        else{
            return intval(substr($dateStr, 0, 4)) <= intval(date("Y"));
        }
        
    }

}
