<?php

namespace app\controllers;

use app\models\AltaHacienda;
use app\models\ErrorInesperado;
use app\models\EstadoFinanciero;
use app\models\Excepcionado;
use app\models\ExcepcionadoSearch;
use app\models\Firmar;
use app\models\FirstUpdateCertificate;
use app\models\HistoricoCartaProtesta;
use app\models\HistoricoCertificados;
use app\models\IntervencionBancaria;
use app\models\Movements;
use app\models\Provider;
use app\models\ProviderCourse;
use app\models\Rfc;
use app\models\Status;
use app\models\Ubicacion;
use app\models\UltimaDeclaracion;
use app\models\Usuarios;
use app\models\RepresentanteLegal;
use app\models\VerifyUserSecop;
use app\models\Visit;
use http\Url;
use Yii;
use yii\data\ActiveDataProvider;
use yii\data\SqlDataProvider;
use yii\db\Expression;
use yii\db\Query;
use yii\filters\VerbFilter;
use app\helpers\GeneralController;
use yii\helpers\ArrayHelper;
use yii\web\NotFoundHttpException;
use mPDF;
use app\models\ProviderQuery;

/**
 * CertificacionController implements the CRUD actions for Certificacion model.
 */
class CartaController extends GeneralController
{
    /**
     * @inheritdoc
     */
    public function behaviors()
    {
        return [
            'verbs' => [
                'class' => VerbFilter::className(),
                'actions' => [
                    'delete' => ['POST'],
                ],
            ],
        ];
    }


    public function reactivaCartaProtesta($model=null,$type='op'){
        if(!$model)
            return false;

        $attributes = [
            'num_ext_fiscal','colonia_fiscal','cp_fiscal','city_fiscal','state_fiscal','vialidad_id','localidad_id', //Ubicacion
            'rfc','curp','nombre','ap_paterno','ap_materno','tipo_identificacion','rep_op',//Representante legal
            'name_razon_social','name_comercial','pf_nombre','pf_ap_paterno','pf_ap_materno',//Proveedor
        ];//los campos que se van a validar

        foreach ($attributes as $attr){

            if(isset($model->oldAttributes[$attr]) && $model->oldAttributes[$attr] != $model->$attr){

                $prov = Provider::find()->where(['user_id'=>Yii::$app->user->id])->one();
                $status = 'status_carta_'.$type;
                $prov->$status = Status::CARTA_GENERADA;
                $prov->save();
                break;
            }
        }
        return true;
    }

    /* public function actionExterno($rfc=null){
        $this->layout = 'nomain';
        if($rfc){
            $rep = RepresentanteLegal::find()->where(['and',['activo'=>true],['rfc'=>$rfc]])->one();
            if($rep)
                $prov = Provider::findOne($rep->provider_id);
            if($rep && $prov)
                return json_encode(["status"=>true,'data'=>$prov->name_razon_social]);
            else
                return json_encode(["status"=>false,'data'=>'El RFC ingresado necesita estar relacionado a una persona moral dentro del Padrón de Proveedores, verifique el registro en el módulo de legales.']);
        }else{
            return $this->render('externo');
        }
    } */


    public function actionCurso($rfc=null){

        if($rfc){
            $representante = RepresentanteLegal::find()->where(['rfc' => base64_decode(base64_decode(strval($rfc)))])->one();
            $condicion =" p.representante_id = ".$representante->representante_legal_id;
        }else{
            $condicion = "  pr.user_id = ".Yii::$app->user->id;
        }

        $data = Provider::findBySql(" 
            select p.end_date_video as fecha, p.provider_id as provider, pr.tipo_provider as tipo
            from provider_course p 
            inner join provider pr on pr.provider_id = p.provider_id  
            where $condicion
            order by p.end_date_video desc limit 1
        ")->asArray()->one();

        $nueva_fecha = base64_encode(date('Y-m-d'));

        if($data)
            $courseDoc = $this->documentocertificadocurso($data['provider'], $data['tipo'],$nueva_fecha,$rfc);

        if($rfc){
            return $this->redirect((GeneralController::isProduction()?\yii\helpers\Url::base('https'):\yii\helpers\Url::base(true)).'/'.$courseDoc);
        }


        return $this->redirect('/provider/dashboard');
    }


    public function actionValidar()
    {

        return $this->render('validar', [

        ]);

    }


    public function actionIndexValidador()
    {
        $params = Yii::$app->request->queryParams;
        $tipo = self::providerType();
        $allData = self::getAllDataCarta($params);

        return $this->render('index-validador', [
            'model' => $allData,
            'tipo' => $tipo,
        ]);
    }


    public function actionFirmar($rp = null)
    {

        $model = new Firmar();
        $id = \Yii::$app->user->getId();
        $data_prov = Provider::find()->select(['provider_id', 'tipo_provider', 'rfc', 'tipo_persona','name_razon_social'])->where(['user_id' => $id])->one();

        if($data_prov->tipo_provider == 'bys' && $data_prov->tipo_persona == 'Persona moral'){
            $data_prov->name_razon_social = ProviderController::traduce($data_prov->name_razon_social);
            $data_prov->save(false);
        }

        $verify = false;
        if ($data_prov['rfc'] != null) {
            $documento = Firmar::path . $data_prov['rfc'] . '/' . md5($data_prov['provider_id']) . '_original_' . $data_prov['tipo_provider'] . '.pdf';
            if (file_exists($documento)) {
                $verify = true;
            }
        }


        if (!$verify) {
            $documento = Firmar::path . $data_prov['provider_id'] . '/' . md5($data_prov['provider_id']) . '_original_' . $data_prov['tipo_provider'] . '.pdf';
        }

        $data = file_get_contents($documento);
        $base64 = base64_encode($data);
        $rechazo = Status::find()->getStatusCarta($data_prov['provider_id'], 'historico_carta_protesta', $data_prov['tipo_provider']);
        return $this->render('firmar', [
            'model' => $model,
            'documento' => $documento,
            'base64' => $base64,
            'rp' => $rp,
            'tipo_persona' => $data_prov['tipo_persona'],
            'rechazo' => $rechazo
        ]);

    }


    public function actionViewValidar($id = null)
    {

        $allData = self::getDataValidar(intval($id));
        $model_status = new Status();
        $model_status->scenario = Status::SCENARIO_CREATE;

        $namePro = Yii::$app->db->createCommand("select p.provider_id,CASE WHEN (p.name_razon_social is not null and p.name_razon_social != '') then p.name_razon_social else 
                    concat_ws(' ',p.pf_nombre,p.pf_ap_paterno,p.pf_ap_materno) end as nombre
                    from provider p
                    join provider.historico_carta_protesta hc on hc.provider_id = p.provider_id
                    where hc.historico_carta_protesta_id = $id")->queryOne();

        $statusBysOp = Yii::$app->user->can(Usuarios::ROLE_VALIDADOR_PROVEEDORES) ? 'bys' : 'op';
        $rechazo = Status::find()->getStatusCarta($namePro['provider_id'], 'historico_carta_protesta', $statusBysOp, 'TERMINADO PRO');

        return $this->renderAjax('view-validar', [
            'model' => $allData,
            'rechazo' => $rechazo,
            'namePro' => $namePro['nombre'],
            'register' => $id,
            'modelStatus' => $model_status
        ]);

    }


    public function actionValidarCertificado($serial, $tipo){

        if(!GeneralController::isProduction())
            return 'good';
        $serial = gmp_strval(gmp_init($serial, 16));

        $RootCA = "ocsp/ocsp.ac" . intval($tipo) . "_sat.pem"; // Points to the Root CA in PEM format.
        $iusser = "ocsp/AC" . intval($tipo) . "_SAT.pem";

        try {
            if (file_exists($RootCA) && file_exists($iusser)) {
                $host = "cfdi.sat.gob.mx";
                $OCSPUrl = 'https://cfdi.sat.gob.mx/edofiel'; //Points to the OCSP URL
                $output = shell_exec('openssl ocsp -issuer ' . $iusser . ' -serial ' . $serial . ' -url ' . $OCSPUrl . ' -VAfile ' . $RootCA . ' -header host ' . $host);
                if ($output) {
                    $output2 = preg_split('/[\r\n]/', $output);
                    if ($output2 != 'invalis stuff' and $output2 != null) {
                        $ocsp = trim(explode(':', preg_split('/[\r\n]/', $output)[0])[1]);
                        $ocsp = trim(strtolower($ocsp)) == 'warning' ? 'good' : $ocsp;
                        echo $ocsp; // will be "good", "revoked", or "unknown"
                    } else {
                        echo "invalis stuff";
                    }
                } else {
                    echo " Error inesperado";
                }
            } else {
                echo " Código de certificado no encontrado";
            }
        } catch (\Exception $e) {
            echo " Errores inesperados";
        }
    }


    public function actionGenerateFile()
    {

        //var_dump(Yii::$app->request->post()).exit();
        try {
            $id = \Yii::$app->user->getId();
            $data_pro = Provider::find()->select(['provider_id', 'tipo_persona', 'tipo_provider', 'rfc', 'name_comercial','email','permanently_disabled'])->where(['user_id' => $id])->asArray()->one();
            $provider_id = $data_pro['provider_id'];
            $tipo_persona = $data_pro['tipo_persona'];
            $tipo_provider = $data_pro['tipo_provider'];
            $name_comercial = $data_pro['name_comercial'];
            $emailProv = $data_pro['email'];
            $request = \Yii::$app->request;
            $data = $request->post();

            $datos = $data['datos'];
            $firma = $data['firma'];
            $rfc = trim($data['rfc']);
            $curp = $data['curp'];
            $rfc_rep = $data['rfc_rep'];
            $email = $data['email'];
            $org = $data['org'];
            $cn = $data['cn'];
            $rp = $data['rp'];

            $rfc_representante = $rfc;
            $tim = microtime(true) * 1000;
            $firmx = base64_encode(md5($tim) . md5($tim + 1) . md5($tim + 2) . md5($tim + 3) . md5($tim + 4) . md5($tim + 5));


            $path_file = Firmar::path;

            $rfcOri = $rfc;
            if ($rp != null) {
                $rfcOri = $data_pro['rfc'];
            }

            $path_fir = $path_file . $rfcOri . '/firma.fir';

            $date_year = date("Y");
            $date_month = date("m");

            if ((strlen($rfc) == 13 && $tipo_persona == 'Persona física' && $rp == null) || (strlen($rfc) == 12 && $tipo_persona == 'Persona moral' && $rp == null)
                || (strlen($rfc) == 13 && $tipo_persona == 'Persona moral' && $rp != null)) {
                $verify_exist = null;
                $verifyRfcExixt = null;
                if ($rp == null) {
                    $verify_exist = Provider::find()->select(['provider_id'])->where(['and', ['rfc' => $rfc], ['not in', 'user_id', [$id]]])->one()['provider_id'];
                    $verifyRfcExixt = Rfc::find()->select(['provider_id'])->where(['rfc' => $rfc])->one()['provider_id'];
                }
                if ($verify_exist != null || ($verifyRfcExixt==null && $rp == null)) {
                    if($verify_exist){
                        return json_encode(['status' => false, 'error' => 'Ya existe un documento firmado con los mismos certificados.']);
                    }
                    return json_encode(['status' => false, 'error' => 'El RFC no coincide con el de su perfil.']);

                } else {
                    if ($rp != null) {
                        $rfc = Provider::find()->select(['rfc'])->where(['user_id' => $id])->one()['rfc'];

                        if ($rfc != null) {
                            if ((RepresentanteLegal::find()->where(['and', ['rfc' => $rfc_representante], ['provider_id' => $provider_id], ['activo' => true], ['rep_' . $tipo_provider => true]])->one()) === null) {
                                return json_encode(['status' => false, 'error' => 'Los datos del certificado no corresponden al representante legal registrado']);
                            }
                        } else {
                            return json_encode(['status' => false, 'error' => 'No se ha encontrado la firma de la empresa(Persona moral).']);
                        }

                    }
                    if (!file_exists($path_file . $rfc)) {
                        rename($path_file . $provider_id, $path_file . $rfc);
                    }
                }

                if (($tipo_persona == 'Persona física' && $rp == null) || $tipo_persona == 'Persona moral' && $rp != null) {
                    $path_doc = $path_file . $rfc . '/' . md5($provider_id) . '_original_' . $tipo_provider . '.pdf';

                    $fp = fopen($path_fir, 'w');
                    fwrite($fp, $datos);
                    fclose($fp);

                    $generate_file_sign = $this->generate_pdf($rfc, $firmx, $date_year, $provider_id, $firma);
                    if ($generate_file_sign) {
                        $zipname = $path_file . $rfc . '/firma_' . $tipo_provider . '_' . $date_year . '_' . $date_month . time() . '.fiel';
                        $zip = new \ZipArchive;
                        $zip->open($zipname, \ZipArchive::CREATE);

                        $zip->addFile($path_fir, 'firma.fiel');
                        $zip->addFile($path_doc, $rfc . '_original.pdf');
                        $zip->close();

                        $userSecop = Usuarios::find()->select('secop')->where(['user_id' => $id])->one()['secop'];

                        if (!$userSecop) {
                            if ((VerifyUserSecop::find()->where(['user_id' => $id])->one()) === null) {
                                $verifyUserSecop = new VerifyUserSecop();
                                $verifyUserSecop->user_id = $id;
                                $verifyUserSecop->save();
                            }
                        }

                        if (file_exists($zipname)) {
                            $fecha = date('Y-m-d');
                            if ($rp == null) {
                                \Yii::$app->db->createCommand("update public.provider set status_carta_$tipo_provider = 'FIRMADA', url_carta_$tipo_provider = :url,
                        rfc = :rfc, curp_sat = :curp, rfc_rep_sat = :rfcrep , email_sat = :email,org_sat = :org,cn_sat = :cn,
                        status_$tipo_provider = 'PROVEEDOR_ETAPA_4',fecha_certificado_$tipo_provider = :fecha, firma_$tipo_provider = :fi,
                        firma_corta_$tipo_provider = :fic where provider_id = :id",
                                    [':url' => $generate_file_sign, ':rfc' => $rfc, ':curp' => $curp, ':rfcrep' => $rfc_rep, ':email' => $email,
                                        ':org' => $org, ':cn' => $cn, ':fecha' => $fecha, ':id' => $provider_id, ':fi' => $firma, ':fic' => $firmx])
                                    ->execute();

                                Rfc::updateAll(['rfc' => $rfc], ['provider_id' => $provider_id]);
                            } else {
                                \Yii::$app->db->createCommand("update public.provider set status_carta_$tipo_provider = 'FIRMADA', url_carta_$tipo_provider = :url,
                        status_$tipo_provider = 'PROVEEDOR_ETAPA_4',fecha_certificado_$tipo_provider = :fecha, firma_rp_$tipo_provider = :fi,
                        firma_corta_rp_$tipo_provider = :fic where provider_id = :id",
                                    [':url' => $generate_file_sign, ':fecha' => $fecha, ':id' => $provider_id, ':fi' => $firma, ':fic' => $firmx])
                                    ->execute();

                            }

                            $status_global = 'status_' . $tipo_provider;

                            if ($tipo_provider == 'bys') {

                                Provider::updateAll(['status_cotejar' => 'PENDIENTE'],['provider_id' => $provider_id]);

                                /*if (!Provider::find()->select('preregistro')->where(['provider_id' => $provider_id])->one()['preregistro']) {
                                    if (GeneralController::verificarModulosPreregistro($status_global, $provider_id, $tipo_provider, 'FIRMADA')) {
                                        Provider::updateAll(['preregistro' => true], ['provider_id' => $provider_id]);
                                        $this->documentopreregistro($provider_id);
                                        self::AllSendNotification($id, $tipo_provider, null, 'PREREGISTRO', null, '¡Felicidades!', 'PROVIDER');
                                    }
                                }
                                if (GeneralController::verificarModulosValidador($status_global, $provider_id, $tipo_provider, 'FIRMADA')) {
                                    $statusCot = 'PENDIENTE ACTIVAR AGENDA';
                                    if ((HistoricoCertificados::find()->where(['and', ['provider_id' => $provider_id], ['tipo' => 'CERTIFICADO'], ['provider_type' => 'bys']])->count('1')) > 0) {
                                        $verifyCotejar = Movements::find()->where(['and', ['provider_id' => $provider_id], ['in', 'model', ['rfc', 'rfc_acta_constitutiva','ubicacion','direccion_nl', 'rfc_curp', 'modificacion_acta', 'relacion_accionistas', 'representante_legal','perfil_ubicacion']], ['first_update_certificate_id' => null],['certificate' => true]])->count('1');
                                        if ($verifyCotejar == 0 && $data_pro['permanently_disabled']!=='INACTIVO') {
                                            $statusCot = 'VALIDADO';
                                            if ((Movements::find()->where(['and', ['provider_id' => $provider_id], ['!=', 'model', 'fotografia_negocio'], ['first_update_certificate_id' => null],['certificate' => true]])->count('1')) > 0) {
                                                $hcId = $this->documentocertificado($provider_id, 'bys');
                                                $hcDateOld = Yii::$app->db->createCommand("select * from historico_certificados 
                                            where provider_id  = $provider_id and tipo = 'CERTIFICADO' and provider_type = 'bys' and historico_certificados_id not in($hcId)
                                            order by historico_certificados_id DESC limit 1")->queryOne();
                                                $first_update_certificate = new FirstUpdateCertificate();
                                                $first_update_certificate->provider_id = $provider_id;
                                                $first_update_certificate->update_certificate = true;
                                                $first_update_certificate->historico_certificado_id = $hcId;
                                                $first_update_certificate->date_old_certificate = $hcDateOld['created_at'];
                                                $first_update_certificate->save();
                                                Movements::updateAll(['first_update_certificate_id' => $first_update_certificate->first_update_certificate_id], ['and', ['provider_id' => $provider_id], ['first_update_certificate_id' => null],['certificate' => true]]);
                                                self::AllSendNotification($id, 'bys', null, 'CERTIFICADO', null, '¡Felicidades!', 'PROVIDER');
                                            }
                                        }else if ((Movements::find()->where(['and', ['provider_id' => $provider_id], ['in','model',['ubicacion','direccion_nl','perfil_ubicacion']],['first_update_certificate_id' => null],['certificate' => true]])->count('1'))>0){
                                            $statusCot = 'PENDIENTE ACTIVAR AGENDA';
                                        }
                                    }else{
                                        $statusCot = 'PENDIENTE ACTIVAR AGENDA';
                                    }


                                    $proveedor = Provider::findOne($provider_id);
                                    $proveedor->status_cotejar = $statusCot;
                                    if ($proveedor->save()) {
                                        if ($statusCot == 'PENDIENTE ACTIVAR AGENDA') {
                                            if (($vi = Visit::find()->where(['and', ['provider_id' => $provider_id], ['status' => true],['provider_type' => 'bys']])->one()) === null) {
                                                $modVisit = new Visit();
                                                $modVisit->provider_id = $provider_id;
                                                $modVisit->provider_type = 'bys';
                                                $modVisit->save(false);
                                            }
                                            self::sendEmail('/provider/correos/visita', null, $emailProv, 'Visita', [], null);
                                        }
                                    }


                                }*/
                            } else {
                                if ($this->verificarModulosValidador($status_global, $provider_id, $tipo_provider, 'FIRMADA')) {
                                    if (($vi = Visit::find()->where(['and', ['provider_id' => $provider_id], ['status' => true],['provider_type' => 'op']])->one()) === null) {
                                        $modVisit = new Visit();
                                        $modVisit->provider_id = $provider_id;
                                        $modVisit->provider_type = 'op';
                                        $modVisit->save(false);
                                    }/*else{
                                        $this->documentocertificado($provider_id, $tipo_provider);
                                        self::AllSendNotification($id, $tipo_provider, null, 'CERTIFICADO', null, '¡Felicidades!', 'PROVIDER');
                                    }*/

                                }
                            }


                            if (file_exists($path_fir)) {
                                unlink($path_fir);
                            }
                            if (file_exists($path_doc)) {
                                unlink($path_doc);
                            }

                            $idCartaProtesta = ArrayHelper::getColumn(HistoricoCartaProtesta::find()->select('historico_carta_protesta_id')->where(['and', ['provider_id' => $provider_id], ['status_' . $tipo_provider => 'RECHAZADO']])->all(), 'historico_carta_protesta_id');
                            if ($idCartaProtesta) {
                                Status::updateAll(['status_' . $tipo_provider => 'TERMINADO PRO'], ['and', ['in', 'register_id', $idCartaProtesta], ['modelo' => 'historico_carta_protesta']]);
                            }

                        }
                        return json_encode(['status' => true, 'rp' => null]);
                        exit();
                    }
                } else {

                    \Yii::$app->db->createCommand("update public.provider set 
                        rfc = :rfc, curp_sat = :curp, rfc_rep_sat = :rfcrep , email_sat = :email,org_sat = :org,cn_sat = :cn,
                        firma_$tipo_provider = :fi,
                        firma_corta_$tipo_provider = :fic where provider_id = :id",
                        [':rfc' => $rfc, ':curp' => $curp, ':rfcrep' => $rfc_rep, ':email' => $email,
                            ':org' => $org, ':cn' => $cn, ':id' => $provider_id, ':fi' => $firma, ':fic' => $firmx])
                        ->execute();

                    Rfc::updateAll(['rfc' => $rfc], ['provider_id' => $provider_id]);

                    $error = new ErrorInesperado();
                    $error->tipo_error = 'FIRMO BIEN';
                    $error->user_id = Yii::$app->user->getId();
                    $error->save();
                    Yii::$app->session->setFlash('info', 'Para continuar ingresa ahora la e.firma del representante legal (que previamente se señaló como firmante en el módulo de “Representante Legal”)');
                    return json_encode(['status' => true, 'rp' => 'SI']);
                    exit();
                }

            } else {

                $tipo_persona = $rp != null ? 'Persona física (representante legal) ' : $tipo_persona;

                return json_encode(['status' => false, 'error' => 'El certificado no corresponde al de una ' . $tipo_persona . '.']);
            }

        } catch (\Exception $e) {
            $error = new ErrorInesperado();
            $error->tipo_error = 'FIRMAR CARTA';
            $error->data = 'Mensaje:'.$e->getMessage().' | Archivo:'.$e->getFile().' | Linea:'.$e->getLine();
            $error->user_id = Yii::$app->user->getId();
            $error->save();

            return json_encode(['status' => false, 'error' => 'Error code 504']);
        }

    }


    public function generate_pdf($rfc = null, $firma = null, $year = null, $provider_id = null, $firmaOri = null)
    {
        $model = $this->findModel($provider_id);

        $modelDir = $this->findModelDir($provider_id);
        $estado_nombre = '';
        if (isset($modelDir->city_fiscal) && !empty($modelDir->city_fiscal)) {
            $estado_nombre = ProviderQuery::getNameEst($modelDir->city_fiscal);
        }
        $colonia_nombre = '';
        if (isset($modelDir->colonia_fiscal) && !empty($modelDir->colonia_fiscal)) {
            $colonia_nombre = ProviderQuery::getNameCol($modelDir->colonia_fiscal);
        }
        $municipio_nombre = '';
        if (isset($modelDir->city_fiscal) && !empty($modelDir->city_fiscal)) {
            $municipio_nombre = ProviderQuery::getNameMun($modelDir->city_fiscal);
        }


        $montoMercantiles = 0;
        if($model->tipo_provider == 'bys'){
            $montoMercantiles = UltimaDeclaracion::find()->select('ingresos_mercantiles')->where(['provider_id' => $provider_id])->one()['ingresos_mercantiles'];
        }

        $data_provider = Provider::find()->where(['provider_id' => $provider_id])->asArray()->one();
        $path_file = Firmar::path . $rfc . '/' . $rfc . '_' . $data_provider['tipo_provider'] . '_firmado_' . $year . '_' . time() . '.pdf';
        $model_rl = $this->findModelRepresentante($model->provider_id, $model->tipo_provider);
        $fm = $model->tipo_persona == 'Persona moral' ? 'm' : 'f';

        if($model->tipo_provider == 'bys'){
            $carta_variable = 'pdf/cartaprotesta';
        }else{
            $carta_variable = 'pdf/cartaprotesta' . $model->tipo_provider . $fm;
        }

        if( /* $proveedor->tipo_provider == 'bys' && */ $model->tipo_persona == 'Persona moral'){
            $course = ProviderCourse::find()->where(['and',['provider_id'=>$provider_id,'representante_id' => $model_rl->representante_legal_id,'video'=>true]])->orderBy(['created_by' => SORT_DESC])->one();
        }else{
            $course = ProviderCourse::find()->where(['and',['provider_id'=>$provider_id,'video'=>true]])->orderBy(['created_by' => SORT_DESC])->one();
        }

        $content = $this->renderPartial($carta_variable, [
            'experiencia' => GeneralController::getClientesContratos($model->provider_id, $model->tipo_provider),
            'model' => $model,
            'tipo_persona' => $fm,
            'model_representante' => $model_rl,
            'estado_nombre' => $estado_nombre,
            'colonia_nombre' => $colonia_nombre,
            'municipio_nombre' => $municipio_nombre,
            'firma' => $firma,
            'course'=>$course,
            'firmaEmp' => $data_provider['firma_corta_bys'],
            'ubicacion' => $modelDir,
            'montoMercantiles' => $montoMercantiles
        ]);
        //$pie = $this->renderPartial('pdf/footer');
        $mpdf = new mPDF('utf-8', 'A4', 0, '', 0, 0, 5, 15, 0, 0);
        $mpdf->SetTitle('Certificado');
        $stylesheet = file_get_contents('css/pdf.css');

        $mpdf->SetHTMLHeader($this->renderPartial('pdf/header_cartaprotesta',['tipo_provider' => $model->tipo_provider]));
        $mpdf->SetHTMLFooter($this->renderPartial('pdf/footer_cartaprotesta',['tipo_provider' => $model->tipo_provider]));

        $mpdf->WriteHTML($stylesheet, 1);
        $mpdf->WriteHTML($content, 2);
        $mpdf->Output($path_file, 'F');
        $firma1 = 'firma_' . $model->tipo_provider;
        $firma2 = 'firma_corta_' . $model->tipo_provider;
        $historico_cp = new HistoricoCartaProtesta();
        $historico_cp->provider_id = $model->provider_id;
        $historico_cp->representante_legal_id = ($model->tipo_persona == 'Persona moral' && $model_rl !== null) ? $model_rl->representante_legal_id : null;
        $historico_cp->url_carta = $path_file;
        $historico_cp->$firma1 = $firmaOri;
        $historico_cp->$firma2 = $firma;
        $historico_cp->provider_type = $model->tipo_provider;
        $historico_cp->save(false);

        return $path_file;

    }


    public function generateFirm()
    {
        $tim = microtime(true) * 1000;
        $firmx = base64_encode(md5($tim) . md5($tim + 1) . md5($tim + 2) . md5($tim + 3) . md5($tim + 4) . md5($tim + 5));
        return $firmx;
    }


    protected function findModel($id)
    {
        if (($model = Provider::findOne($id)) !== null) {
            return $model;
        } else {
            throw new NotFoundHttpException('The requested page does not exist.');
        }
    }


    protected function findModelDir($id)
    {
        return Ubicacion::find()->where(['and', ['provider_id' => $id], ['type_address_prov' => 'DOMICILIO FISCAL']])->one();
    }


    public function findModelUser($id)
    {
        $model = Usuarios::find()->where(['user_id' => $id])->one();

        return $model;
    }


    public function findModelRepresentante($id, $tipo)
    {

        $model = RepresentanteLegal::find()->where(['and', ['provider_id' => $id], ['rep_' . $tipo => true],
            ['activo' => true]])->one();
        //$model = RepresentanteLegal::find()->where(['and',['provider_id' => $id],['activo' => true]])->one();

        return $model;
    }


    public function actionVideo($rfc=null){
        if($rfc)
            $this->layout = 'nomain';
        return $this->render('video',['rfc'=>$rfc]);
    }


    public function actionGeneracartaprotesta($rfc=null)
    {

        if($rfc) {
            $representante = RepresentanteLegal::find()->where(['rfc' => base64_decode(base64_decode(strval($rfc)))])->one();
            $provider_id = $representante->provider_id;
            $proveedor = Provider::findOne($provider_id);
        }else{
            $provider_id = Yii::$app->user->identity->providerid;
            $proveedor = Provider::findOne($provider_id);
            $representante = $this->findModelRepresentante($proveedor->provider_id, $proveedor->tipo_provider);
        }


        if(empty($representante) && $proveedor->tipo_persona == 'Persona moral'){
            \Yii::$app->session->setFlash('info', 'Debes seleccionar al representante firmante para poder continuar!');
            return Yii::$app->getResponse()->redirect(['/' . $proveedor->tipo_provider . '/legales/view']);
        }

        if( /* $proveedor->tipo_provider == 'bys' && */ $proveedor->tipo_persona == 'Persona moral'){
            $course = ProviderCourse::find()->where(['and',['provider_id'=>$provider_id,'representante_id' => $representante->representante_legal_id,'video'=>true]])->one();
        }else{
            $course = ProviderCourse::find()->where(['and',['provider_id'=>$provider_id,'video'=>true]])->one();
        }
        
        //$id = Yii::$app->user->identity->providerid;

        if(!$course) { $course = new ProviderCourse(); }
        if(!$course->video){
            if($rfc)
                $this->layout = 'nomain';
            return $this->redirect('/carta/video?rfc='.$rfc);
        }
        if($rfc)
            return $this->redirect('curso?rfc='.$rfc);

        $modelDir = $this->findModelDir($provider_id);
        if(!$modelDir)
            $modelDir = new Ubicacion();
        $estado_nombre = '';
        if (isset($modelDir->city_fiscal) && !empty($modelDir->city_fiscal)) {
            $estado_nombre = ProviderQuery::getNameEst($modelDir->city_fiscal);
        }
        $colonia_nombre = '';
        if (isset($modelDir->colonia_fiscal) && !empty($modelDir->colonia_fiscal)) {
            $colonia_nombre = ProviderQuery::getNameCol($modelDir->colonia_fiscal);
        }
        $municipio_nombre = '';
        if (isset($modelDir->city_fiscal) && !empty($modelDir->city_fiscal)) {
            $municipio_nombre = ProviderQuery::getNameMun($modelDir->city_fiscal);
        }


        $path = 'documentos_firmados/';
        $this->makeDir($path);
        $path_rfc = null;
        if (isset($proveedor->rfc) && !empty($proveedor->rfc)) {
            if (file_exists($path . $proveedor->rfc)) { $path_rfc = $path . $proveedor->rfc; }
        }
        if ($path_rfc == null) { $path_rfc = $path . $provider_id; }

        $this->makeDir($path_rfc);
        //$model_rl = $this->findModelRepresentante($model->provider_id, $model->tipo_provider); //d
        $fm = $proveedor->tipo_persona == 'Persona moral' ? 'm' : 'f';

        if ($fm == 'm' && empty($representante)) {
            \Yii::$app->session->setFlash('info', 'Debes seleccionar al representante firmante para poder continuar!');
            return Yii::$app->getResponse()->redirect(['/' . $proveedor->tipo_provider . '/legales/view']);
        }

        $montoMercantiles = 0;
        if($proveedor->tipo_provider == 'bys'){
            $montoMercantiles = UltimaDeclaracion::find()->select('ingresos_mercantiles')->where(['provider_id' => $provider_id])->one()['ingresos_mercantiles'];
        }

        // Condición para poder generar un solo documento único para BYS y dejar 2 documentos para OP
        if($proveedor->tipo_provider == 'bys'){
            $carta_variable = 'pdf/cartaprotesta';
        }else{
            $carta_variable = 'pdf/cartaprotesta' . $proveedor->tipo_provider . $fm;
        }

        $content = $this->renderPartial($carta_variable, [
            'experiencia' => GeneralController::getClientesContratos($proveedor->provider_id, $proveedor->tipo_provider),
            'model' => $proveedor,
            'course' => $course,
            'tipo_persona' => $fm,
            'model_representante' => $representante,
            'estado_nombre' => $estado_nombre,
            'colonia_nombre' => $colonia_nombre,
            'municipio_nombre' => $municipio_nombre,
            'firma' => '',//'9687750c2b490f96bb5addbab7e821296fa9cc5096f4c520a91351c93fc033df01c1e2675b3d363d7e9d0e93d11e91fcccd762fd4d5925a29b67daa724433a685a8c5fbd93ad5fc8c3ea705201ba2556fc41ae46f2c08783e852e7e27a0bd6c3ae64873d708ae84e56b6ade3290bdfc3555bcfe76494792adb24efb54eb198566157c44f46480ee21e5d575f25a464e1450b8185f9d5e67f709cdf2fa304c9ee080c6c99cbba76370311357bca232e67e39949bdf44dcc97675c65697d8345f8eab49285f424daf06f7cc78e13a3f894189565f386ab7d9d465d7a92300b5c8e83c4ade0d31cd401285193937906ad1f85dec7ca3ccdd438f23b93b90690a6fa'
            'ubicacion' => $modelDir,
            'montoMercantiles' => $montoMercantiles
        ]);
        $header = $this->renderPartial('pdf/header_cartaprotesta',['tipo_provider' => $proveedor->tipo_provider]);
        $footer = $this->renderPartial('pdf/footer_cartaprotesta',['tipo_provider' => $proveedor->tipo_provider]);
        $stylesheet = file_get_contents('css/pdf.css');

        $name_file_save = $path_rfc . '/' . md5($provider_id) . '_original_' . $proveedor->tipo_provider . '.pdf';

        if (!defined('_MPDF_TTFONTPATH')) {
            define('_MPDF_TTFONTPATH', './fonts/'); //cambia la ruta de las fuentes utilizadas en la creación del pdf (la configuración default puede solicitar alguans fuentes necesarias)
        }
        
        //función que agrega fuentes al array de $fontdata dentro del componente de mpdf
        //Nota: es necesario agregar la fuente R = Regular (o fuente base para que aplique los cambios correctamente)
        function add_custom_fonts_to_mpdf($mpdf) {
        
            $fontdata = [
                'poppins' => [
                    'R' => 'Poppins-Regular.ttf',          
                    'B' => 'Poppins-Bold.ttf',
                ],
                'axiforma' => [
                    'R' => 'Axiforma-Regular.ttf',
                    'B' => 'Axiforma-Heavy.ttf',
                ],
                'branding' => [
                    'R' => 'Branding-Medium.ttf',
                    'B' => 'Branding-Bold.ttf',
                ]
            ];
        
            foreach ($fontdata as $f => $fs) {

                // agrega al arreglo $fontdata
                $mpdf->fontdata[$f] = $fs;
        
                foreach (['R', 'B', 'I', 'BI'] as $style) {
                    if (isset($fs[$style]) && $fs[$style]) {
                        $mpdf->available_unifonts[] = $f . trim($style, 'R');
                    }
                }
        
            }
        
            $mpdf->default_available_fonts = $mpdf->available_unifonts;
        }

        // inicio normal del mpdf
        $mpdf = new mPDF('utf-8', 'A4', 0, '', 0, 0, 5, 15, 0, 0);

        add_custom_fonts_to_mpdf($mpdf); //

        $mpdf->SetTitle('Carta Protesta');
        $mpdf->SetHTMLHeader($header);
        $mpdf->SetHTMLFooter($footer);
        /* if(!$_ENV['VEDA_ELECTORAL']){ $mpdf->SetWatermarkImage('imgs/cartas/logo_sello.png',.8,array(100,100)); } */
        $mpdf->SetWatermarkImage('imgs/cartas/logo_watermark.svg',.1,array(100,100));
        $mpdf->showWatermarkImage = true;

        $mpdf->WriteHTML($stylesheet, 1);
        $mpdf->WriteHTML($content, 2);

        $mpdf->Output($name_file_save, 'F');

        if ($proveedor->tipo_provider == 'op') {
            $status_estapa = 'status_op';
            $status_carta = 'status_carta_op';
        } else {
            $status_estapa = 'status_bys';
            $status_carta = 'status_carta_bys';
        }

        $proveedor->$status_estapa = Provider::PROVEEDOR_ETAPA_3;
        $proveedor->$status_carta = Status::CARTA_GENERADA;
        $proveedor->save();
        $this->redirect('/carta/firmar');

    }


    public function generateAlet($length, $hm = null)
    {
        $characters = '';

        $characters .= 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
        if ($hm == 'si') {
            $characters .= '**********';
        }
        $charactersLength = strlen($characters);
        $randomString = '';
        for ($i = 0; $i < $length; $i++) {
            $randomString .= $characters[rand(0, $charactersLength - 1)];
        }
        return strtoupper($randomString);
    }


    public function generateAletN()
    {
        $fecha = date("Ymd", mt_rand(0, 500000000));
        return trim(substr($fecha, 2, strlen($fecha)));
    }


    public static function getAllDataCarta($params)
    {

        $sql = ' ';
        $bind = [];

        if( isset($params['n']) && !empty($params['n']) && isset($params['r']) && !empty($params['r']) && isset($params['estatus']) && !empty($params['estatus']) ){
            $rfc = $params['r'];
            $nombre = $params['n'];
            $estatus = $params['estatus'];

            $sql .= " where lower(unaccent(rfc)) ilike '%' || lower(unaccent(:rfc)) || '%' 
            and lower(unaccent(nombre)) ilike '%' || lower(unaccent(:nom)) || '%' 
            and lower(unaccent(permanently_disabled)) = lower(unaccent(:estatus)) ";

            $bind = [':rfc' => "%$rfc%", ':nom' => "%$nombre%", ':estatus' => "$estatus"];
        }else if(isset($params['n']) && !empty($params['n']) && isset($params['r']) && !empty($params['r'])){
            $rfc = $params['r'];
            $nombre = $params['n'];

            $sql .= " where lower(unaccent(rfc)) ilike '%' || lower(unaccent(:rfc)) || '%' 
            and lower(unaccent(nombre)) ilike '%' || lower(unaccent(:nom)) || '%' ";

            $bind = [':rfc' => "%$rfc%", ':nom' => "%$nombre%"];
        }else if(isset($params['r']) && !empty($params['r']) && isset($params['estatus']) && !empty($params['estatus'])){
            $rfc = $params['r'];
            $estatus = $params['estatus'];

            $sql .= " where lower(unaccent(rfc)) ilike '%' || lower(unaccent(:rfc)) || '%' 
            and lower(unaccent(permanently_disabled)) = lower(unaccent(:estatus)) ";

            $bind = [':rfc' => "%$rfc%", ':estatus' => "$estatus"];
        }else if(isset($params['n']) && !empty($params['n']) && isset($params['estatus']) && !empty($params['estatus'])){
            $nombre = $params['n'];
            $estatus = $params['estatus'];

            $sql .= " where lower(unaccent(nombre)) ilike '%' || lower(unaccent(:nom)) || '%' 
            and lower(unaccent(permanently_disabled)) = lower(unaccent(:estatus)) ";

            $bind = [':nom' => "%$nombre%", ':estatus' => "$estatus"];
        }else{
            if (isset($params['n']) && !empty($params['n'])) {
                $nom = $params['n'];
    
                $sql .= " where lower(unaccent(nombre)) ilike '%' || lower(unaccent(:nom)) || '%' ";
                $bind = [':nom' => "%$nom%"];
            }
            if (isset($params['r']) && !empty($params['r'])) {

                $rfc = $params['r'];
    
                $sql .= " where lower(unaccent(rfc)) ilike '%' || lower(unaccent(:rfc)) || '%' ";
    
                $bind = [':rfc' => "%$rfc%"];
            }
            if (isset($params['estatus']) && !empty($params['estatus'])) {

                $estatus = $params['estatus'];
    
                $sql .= " where lower(unaccent(permanently_disabled)) = lower(unaccent(:estatus))";
    
                $bind = [':estatus' => "$estatus"];
            }

        }

        $status = Yii::$app->user->can(Usuarios::ROLE_VALIDADOR_PROVEEDORES) ? 'bys' : 'op';

        $totalCount = Yii::$app->db
            ->createCommand("with ct as
                    (
                    select distinct 
                        provider_id, created_at,status_$status,
                        row_number() over (partition by provider_id order by created_at desc) rn
                    from
                        provider.historico_carta_protesta  where provider_type = '$status' order by provider_id
                    )
                    select
                        count(1)
                    from
                        ct
                        join provider p on p.provider_id = ct.provider_id
                    where
                       rn = 1 and ct.status_$status = 'VALIDADO' and p.enabled is true;
            
            ")
            ->queryScalar();


        $sql = [
            'sql' => "WITH rw as(with ct as
                (
                select distinct
                historico_carta_protesta_id,provider_id, created_at,status_$status,
                row_number() over (partition by provider_id order by created_at desc) rn
                from
                provider.historico_carta_protesta where provider_type = '$status' order by provider_id
                )
                select
                ct.historico_carta_protesta_id,ct.provider_id,ct.created_at,ct.status_$status,p.rfc,p.permanently_disabled,
                                        CASE WHEN (p.name_razon_social is not null and p.name_razon_social != '') then p.name_razon_social else concat_ws(' ',p.pf_nombre,p.pf_ap_paterno,p.pf_ap_materno) end as nombre
                from
                ct
                join provider p using(provider_id)
                where
                rn = 1 and ct.status_$status = 'VALIDADO' and p.enabled is true order by created_at DESC)
                select rw.historico_carta_protesta_id,rw.permanently_disabled,rw.provider_id,rw.created_at::date,rw.status_$status,rw.rfc, rw.nombre from rw $sql
            
            ",
            'totalCount' => $totalCount,
            //'sort' =>false, to remove the table header sorting
            'sort' => [
                'attributes' => [
                    'rfc' => [
                        'asc' => ['rfc' => SORT_ASC],
                        'desc' => ['rfc' => SORT_DESC],
                        'default' => SORT_DESC,
                        'label' => 'RFC',
                    ],
                    'nombre' => [
                        'asc' => ['nombre' => SORT_ASC],
                        'desc' => ['nombre' => SORT_DESC],
                        'default' => SORT_DESC,
                        'label' => 'Nombre',
                    ],
                ],
            ]
        ];

        if ($bind) {
            $sql['params'] = $bind;
        }

        $dataProvider = new SqlDataProvider($sql);

        return $dataProvider;

    }


    public function getDataValidar($id)
    {

        $s = new Query();
        $subquery = $s->select("h.url_carta,r.url_rfc,p.rfc")
            ->addSelect([new \yii\db\Expression('CASE WHEN (p.name_razon_social is not null and p.name_razon_social != \'\') then p.name_razon_social else concat_ws(\' \',p.pf_nombre,p.pf_ap_paterno,p.pf_ap_materno) end as nombre')])
            ->from("provider.historico_carta_protesta h ")
            ->innerJoin('provider p', 'p.provider_id = h.provider_id')
            ->innerJoin('provider.rfc r', 'r.provider_id = p.provider_id')
            ->where("h.historico_carta_protesta_id = :id")
            ->params([':id' => $id])
            ->one();

        return $subquery;

    }



}
