<?php

namespace app\controllers;

use app\helpers\GeneralController;
use app\models\Concursos;
use app\models\ConnectionHistory;
use app\models\PermissionViewModule;
use app\models\ProviderSearch;
use app\models\UsuarioConsulta;
use yii\helpers\Url;
use app\models\Auditoria;
use app\models\CatResponsabilidades;
use app\models\GrupoProducto;
use app\models\LoginForm;
use app\models\NegociadorGrupoProducto;
use app\models\Provider;
use app\models\Secretaria;
use app\models\TokenProvisional;
use app\models\UsuarioNegociador;
use Yii;
use app\models\Usuarios;
use app\models\UsuariosSearch;
use app\models\UsuariosResponsabilidades;
use yii\filters\AccessControl;
use yii\helpers\ArrayHelper;
use yii\web\Controller;
use yii\web\NotFoundHttpException;
use yii\filters\VerbFilter;
use app\models\Dependencia;
use app\models\ConcursosSearch;
use app\models\ErrorInesperado;
use app\models\Token;
use Exception;
use yii\data\SqlDataProvider;
use yii\helpers\Json;
use yii\web\Response;
use yii\widgets\ActiveForm;

/**
 * UsuariosController implements the CRUD actions for Usuarios model.
 */
class UsuariosController extends GeneralController
{
    /**
     * @inheritdoc
     */
    public function behaviors()
    {
        return [
            'access' => [
                'class' => AccessControl::className(),
                'only' => ['index','view','update','updateenlace','createenlace'],
                'rules' => [
                    [
                        'actions' => [],
                        'allow' => true,
                        'roles' => ['@'],
                    ],
                ],
            ],
            'verbs' => [
                'class' => VerbFilter::className(),
                'actions' => [
                    //'delete' => ['POST'], //para poder eliminar el usuario
                ],
            ],
        ];
    }



    /**
     * Lists all Usuarios models.
     * @return mixed
     */
    public function actionIndex()
    {
        $searchModel = new UsuariosSearch();
        $dataProvider = $searchModel->search(Yii::$app->request->queryParams);
        if(Yii::$app->user->identity->role != 'ADMIN' &&  Yii::$app->user->identity->role != 'ADMIN PROVIDER' && Yii::$app->user->identity->role != 'ADMIN CONTRATISTAS') {
            $id = Yii::$app->user->identity->getId();
            $responsabilidad = UsuariosResponsabilidades::findOne($id);
            if($responsabilidad){
                $responsabilidad->getResponsabilidad()->one();
                $model = Usuarios::findOne($id);
                if ($responsabilidad['responsabilidad_id'] == 1) {
                    return $this->redirect(['datosLegales/rfc/index-validador']);
                } else if ($responsabilidad['responsabilidad_id'] == 2) {
                    return $this->redirect(['datosTecnicos/validador/index-validador']);
                } else if ($responsabilidad['responsabilidad_id'] == 3) {
                    return $this->redirect(['datosFinancieros/declaracion-isr/index-validador']);
                } else if ($responsabilidad['responsabilidad_id'] == 4) {
                    return $this->redirect(['datosTecnicos/validador/index-validador']);
                }
            }

        }


        $id = Yii::$app->user->identity->getId();
        $model = Usuarios::findOne($id);

        return $this->render('index', [
            'model' => $model,
            'searchModel' => $searchModel,
            'dataProvider' => $dataProvider,
        ]);
    }

    /**
     * Displays a single Usuarios model.
     * @param string $id
     * @return mixed
     */
    public function actionView($id)
    {
        if (Yii::$app->user->isGuest) {
            return $this->redirect(['/site/login']);
        }


        $model = $this->findModel($id);

        $searchModel = [];
        $dataProvider = [];
        $dataAsignados = [];
        if($model->role == Usuarios::ROLE_USUARIO_CONSULTA_BYS || $model->role == Usuarios::ROLE_USUARIO_CONSULTA_OP ){
            $searchModel = new ProviderSearch();
            $searchModel->auditoria_id = $id;
            $dataProvider = $searchModel->search(Yii::$app->request->queryParams,'sinAsignarCon');
            $dataAsignados = $searchModel->search(Yii::$app->request->queryParams,'AsignadosCon');
        }

        return $this->render('view', [
            'model' => $model,
            'searchModel' => $searchModel,
            'dataProvider' => $dataProvider,
            'dataAsignados' => $dataAsignados
        ]);
    }

    public function actionAsigProv($id)
    {
        if(isset($_POST['prov']) && $_POST['prov'] != ''){
            if(isset($_POST['all_prov_asig']) && !empty($_POST['all_prov_asig']) && $_POST['all_prov_asig'] == 'si'){
                if(Yii::$app->user->can(Usuarios::ROLE_ADMIN_CONTRATISTAS)){
                    $typeUser = 'op';
                    $proveedores = ArrayHelper::getColumn(Yii::$app->db->createCommand("select provider_id from provider where provider.provider_id in(select DISTINCT provider_id from provider.datos_validados where status_op = 'VALIDADO')
                                and provider_id not in(select provider_id from user_relation_provider where user_id = $id and status='ACTIVO')")->queryAll(),'provider_id');
                }else{
                    $typeUser = 'bys';
                    $proveedores = ArrayHelper::getColumn(Yii::$app->db->createCommand("select provider_id from provider where provider.permanently_disabled in('ACTIVO')
                                and provider_id not in(select provider_id from user_relation_provider where user_id = $id and status='ACTIVO')")->queryAll(),'provider_id');
                }
                $conUser= new UsuarioConsulta();
                $conUser->user_id = $id;
                $conUser->type_user = $typeUser;
                $conUser->save();

            }else{
                $proveedores = explode(",",$_POST['prov']);
            }

            foreach ($proveedores as $proveedor) {
                Yii::$app->db->createCommand()->insert('user_relation_provider', [
                    'status' => 'ACTIVO',
                    'user_id' => $id,
                    'provider_id'=>$proveedor,
                    'created_by' => Yii::$app->user->getId(),
                ])->execute();
            }
        }

        return $this->redirect(['view','id' => $id]);
    }

    public function actionDeleteAsigProv($id)
    {
        if(isset($_POST['id_prov']) && $_POST['id_prov'] != ''){
            $proveedores = explode(",",$_POST['id_prov']);
            if(isset($_POST['all_prov_dasig']) && !empty($_POST['all_prov_dasig']) && $_POST['all_prov_dasig'] == 'si' && count($proveedores)>1){
                $connection = Yii::$app->db;
                $connection->createCommand()->update('user_relation_provider', ['status' => 'INACTIVO'], ['user_id' => $id])->execute();
            }else{
                $connection = Yii::$app->db;
                $connection->createCommand()->update('user_relation_provider', ['status' => 'INACTIVO'], ['and',['in','provider_id',$proveedores],['user_id' => $id]])->execute();
            }
        }

        return $this->redirect(['view','id' => $id]);
    }
    /**
    * Displays a single Usuarios model.
    * @param string $id
    * @return mixed
    */
    /* public function actionWelcome($id)
    {
        $this->layout='main';
        return $this->render('welcome', [
            'model' => $this->findModel($id),
        ]);
    } */

    /**
     * Creates a new Usuarios model.
     * If creation is successful, the browser will be redirected to the 'view' page.
     * @return mixed
     */

    public function actionIsvalid($rfc=null,$email=null){
        $data = ['status' => true,'msg'=>[],'email'=>false];
        if(Provider::find()->select('rfc')->where(['rfc'=>$rfc])->one() || Usuarios::find()->select('rfc')->where(['rfc'=>$rfc])->one()){
            $data['status'] = false;
            array_push($data['msg'],"El RFC: '$rfc' ya existe como RFC de proveedor en el sistema.");
        }
        if(Usuarios::find()->select('email')->where(['upper(email)'=>strtoupper($email)])->one()){
            $data['email'] = true;
            array_push($data['msg'],"El correo: '$email' ya se registró como usuario en la plataforma.");
        }

        return json_encode($data);
    }

    public function actionCreate($token=null)
    {
        
        //$consulta_token = TokenProvisional::find()->where(['token' => $token])->one();

        if(1 != 1 /*($token==null || $consulta_token==null || $consulta_token['user_id']!=null) && ($_SERVER['SERVER_NAME'] == 'beta.proveedores.nl.gob.mx') */){
            Yii::$app->session->setFlash('error', 'El sitio se encuentra en versión beta.');
            return $this->redirect(['/site/index']);
        }else {
            $this->layout = 'main';
            if (!Yii::$app->user->isGuest) {
                return $this->redirect(['/provider/create']);
            }
            $model = new Usuarios();
            $model->scenario = Usuarios::SCENARIO_CREATE;
            $model->role = Usuarios::ROLE_PROVIDER;

            $trans = Yii::$app->getDb()->beginTransaction();
            if(Yii::$app->request->isAjax && $model->load(Yii::$app->request->post())){
                Yii::$app->response->format = Response::FORMAT_JSON;
                return ActiveForm::validate($model);
            }else if ($model->load(Yii::$app->request->post())) {
                $model->email = strtolower($model->email);
                $model->pass_tmp = $model->password;
                $exp = explode('@', $model->email);
                $user_name = str_replace('-','_',$exp[0]);

                $user_name = $this->verifyUser($user_name);

                $model->username = $user_name;

                $model->nombre = $_POST['Usuarios']['nombre'];
                $model->fiel_data = $_POST['Usuarios']['fiel_data'];
                $model->isBeta = true;
                if ($model->username) {
                    $l = new LoginForm();
                    $l->username = $model->username;
                    $l->password = $model->password;

                    $secop = array(
                        "clave_sire" => "",
                        "nombre" => $model->nombre,
                        "usuario" => $model->username,
                        "contrasena" => $model->pass_tmp,
                        "correo" => $model->email,
                        "rfcprov" => $model->rfc
                    );
                    GeneralController::insertProvSecopa($secop);
                    if ($model->save()) {
                        $trans->commit();
                        if ($l->login()) {

                            /* GeneralController::getDataConnection();
                            $user_token = Yii::$app->db->createCommand();
                            $user_token->update('token_provisional',['user_id' => $model->user_id],'token_id='.$consulta_token->token_id);
                            $user_token->execute();
                            if($consulta_token){
                                $consulta_token->user_id = $model->user_id;
                                $consulta_token->save(false);
                            } */

                            return $this->redirect(['/provider/create']);
                        }
                    }else{
                        //var_dump(json_encode($model->errors));exit();
                    }
                }

                $trans->rollBack();
            }

            $this->layout = 'limpio';
            return $this->render('create', [
                'model' => $model,
            ]);
        }

    }


    public function verifyUser($user_name){

        $verify = true;
        while ($verify) {
            $user_name = $this->addRandUserName($user_name);

            $verifySecop = json_decode(GeneralController::verifyUserNameSecop($user_name),true);

            if ($verifySecop['existe'] && $verifySecop['status']){
                $verify = true;
                $user_name = $user_name . '' . rand(1, 4);
            }elseif (!$verifySecop['existe'] && $verifySecop['status']){
                $verify = false;
            }else{
                Yii::$app->session->setFlash('error', 'Error inesperado, intente de nuevo o contacte al administrador.');
                return $this->redirect(['/site/login']);
            }
        }

        return $user_name;
    }

    public function addRandUserName($user_name){

        while(Usuarios::findOne(['username' => $user_name])){
            $user_name = $user_name . '' . rand(1, 4);
        }
        return $user_name;
    }

    /**
     * Creates a new Usuarios model.
     * If creation is successful, the browser will be redirected to the 'view' page.
     * @return mixed
     */
    public function actionCreateenlace()
    {
        $model = new Usuarios();

        if(Yii::$app->request->isAjax && $model->load(Yii::$app->request->post())){
            Yii::$app->response->format = Response::FORMAT_JSON;
            return ActiveForm::validate($model);
        }else if ($model->load(Yii::$app->request->post())) {

            $model->es_funcionario = isset($_POST['Usuarios']['es_funcionario']) ? filter_var($_POST['Usuarios']['es_funcionario'], FILTER_VALIDATE_BOOLEAN) : false;
            $model->username = $model->es_funcionario ? $model->username."_funcionario" : $model->username;
            if($model->role == Usuarios::ROLE_USUARIO_CONSULTA_BYS && $model->perArr){
                $model->perArr = ArrayHelper::getColumn(PermissionViewModule::find()->select('permission_view_module_id')->where(['in','permission',$model->perArr])->all(),'permission_view_module_id');
            }

            $model->scenario = Usuarios::SCENARIO_CREATE_UPDATE_ENLACE;
            $model->created_by = Yii::$app->user->getId();
            if ($model->save()) {
                return $this->redirect(['/usuarios/view', 'id' => $model->user_id]);
            }
        }

        $dependencias = Dependencia::find()->getDependenciaCombo();
        $cat_resp = CatResponsabilidades::find()->getResponsabilidades();
        $cat_permission = PermissionViewModule::find()->getPermission();

        return $this->render('createenlace', [
            'model' => $model,
            'dependencias' => $dependencias,
            'cat_resp' => $cat_resp,
            'cat_permission' => $cat_permission
        ]);
    }

    /**
     * Updates an existing Usuarios model.
     * If update is successful, the browser will be redirected to the 'view' page.
     * @param string $id
     * @return mixed
     */
    public function actionUpdate($id)
    {
        $model = $this->findModel($id);

        if(Yii::$app->request->isAjax && $model->load(Yii::$app->request->post())){
            Yii::$app->response->format = Response::FORMAT_JSON;
            return ActiveForm::validate($model);
        }else if ($model->load(Yii::$app->request->post()) && $model->save()) {
            return $this->redirect(['view', 'id' => $model->user_id]);
        }

        return $this->render('update', [
            'model' => $model
        ]);
    }

    /**
     * @param $id
     * @return \yii\web\Response
     */
    public function actionConfirm($id)
    {
        $customer = Usuarios::findOne($id);
        $customer->address = 'si';
        $customer->update();
        return $this->redirect(['index']);
    }
    /**
     * Updates an existing Usuarios model.
     * If update is successful, the browser will be redirected to the 'view' page.
     * @param string $id
     * @return mixed
     */
    public function actionUpdateenlace($id)
    {
        $model = $this->findModel($id, true, true, true,true);
        $model->scenario = Usuarios::SCENARIO_CREATE_UPDATE_ENLACE;
        $model->username = ($model['es_funcionario'] && GeneralController::str_contains($model->username, '_funcionario')) ? strstr($model->username, '_funcionario', true) : $model->username;
        $factual = date("Y-m-d H:i:s");
        if ($model->load(Yii::$app->request->post())) {
            $model->es_funcionario = isset($_POST['Usuarios']['es_funcionario']) ? filter_var($_POST['Usuarios']['es_funcionario'], FILTER_VALIDATE_BOOLEAN) : false;
            $model->username = $model->es_funcionario ? $model->username."_funcionario" : $model->username;
            $model->updated_date = $factual;
            $model->updated_by = Yii::$app->user->getId();
            if ($model->save()) {
                return $this->redirect(['/usuarios/view', 'id' => $model->user_id]);
            }

        }

        $dependencias = Dependencia::find()->getDependenciaCombo();
        $grupoprod = GrupoProducto::find()->getGrupoProdCombo($id);
        $cat_resp = CatResponsabilidades::find()->getResponsabilidades();
        $cat_permission = PermissionViewModule::find()->getPermission();

        return $this->render('updateenlace', [
            'model' => $model,
            'grupoprod' => $grupoprod,
            'dependencias' => $dependencias,
            'cat_resp' => $cat_resp,
            'cat_permission' => $cat_permission
        ]);
    }

    /**
     * Deletes an existing Usuarios model.
     * If deletion is successful, the browser will be redirected to the 'index' page.
     * @param string $id
     * @return mixed
     */
    public function actionDelete($id)
    {
        //$this->findModel($id)->delete();
        $model = Usuarios::findOne($id);
        $model->status = 'INACTIVO';
        $model->save(false);

        return $this->redirect(['index']);
    }

    /**
     * Finds the Usuarios model based on its primary key value.
     * If the model is not found, a 404 HTTP exception will be thrown.
     * @param string $id
     * @return Usuarios the loaded model
     * @throws NotFoundHttpException if the model cannot be found
     */
    protected function findModel($id,$dependenciasArr = false,$grupoproductosArr = false , $responArr = false, $permArr = false)
    {
        if (($model = Usuarios::findOne($id)) !== null) {
            if($dependenciasArr){
                $model->dependenciasArr = ArrayHelper::getColumn($model->dependencias, 'dependencia_id');
            }
            if($grupoproductosArr){
                $model->grupoproductosArr = ArrayHelper::getColumn($model->grupoproductos, 'grupo_producto_id');
            }
            if($responArr){
                $model->responArr = ArrayHelper::getColumn($model->responsabilidades, 'responsabilidad_id');
            }
            if($permArr){
                $model->perArr = ArrayHelper::getColumn($model->permisos, 'permission_view_module_id');
            }
            return $model;
        } else {
            throw new NotFoundHttpException('The requested page does not exist.');
        }
    }

    public function findModelUserNeg($id,$grupoproductosArr = false)
    {
        if (($model = UsuarioNegociador::findOne($id)) !== null) {
            if($grupoproductosArr){
                $model->grupoproductosArr = ArrayHelper::getColumn($model->grupoproductos, 'grupo_producto_id');
            }
            return $model;
        } else {
            throw new NotFoundHttpException('The requested page does not exist.');
        }
    }

    function iniciales($nombre) {
        $ignorar = Array('del','de','la','los');
        $explode = explode(' ',$nombre);
        $iniciales = '';
        for($i=0;$i<count($explode);$i++){
            if(in_array($explode[$i],$ignorar)) $iniciales = $iniciales;
            else $iniciales .= substr($explode[$i],0,1);
        }
        return $iniciales;
    }

    public function actionList_grupo_prod_tipo($tipo)
    {
        $always_filter = ArrayHelper::getColumn(GrupoProducto::find()->select('grupo_producto_id')->where("nombre in ('VEHICULOS')")
        ->asArray()->all(),'grupo_producto_id');

        $grupo_productos = ArrayHelper::getColumn(NegociadorGrupoProducto::find()->select(['grupo_producto_id'])
                    ->where("user_negociador_id in( select user_id from usuarios where role ='$tipo')")
                    ->andWhere(['NOT IN','grupo_producto_id',$always_filter])
                    ->asArray()->all(),'grupo_producto_id');
        return json_encode($grupo_productos);
    }

    public function actionList_grupo_prod($tipo)
    {
        $grupo_pro = ArrayHelper::map(GrupoProducto::find()->select(['grupo_producto_id','nombre'])
                            ->where(['tipo' => $tipo])->orderBy(['nombre'=>SORT_ASC])
                            ->asArray()->all(),'grupo_producto_id','nombre');
        return json_encode($grupo_pro);
    }

    public function actionList_tipo_user($tipo){
        $con ='';
        $con .='<option></option>';
        if($tipo == 'NEGOCIADOR'){
            $con .='<option value="Material">Material</option>';
            $con .='<option value="Servicio">Servicio</option>';
        }else{
            $con .='<option value="Director material">Director De Materiales</option>';
            $con .='<option value="Director servicio">Director De Servicios</option>';
            $con .='<option value="Director general">Director General</option>';
        }

        return $con;

    }
    public function actionError()
    {
        if(Yii::$app->user->isGuest){
            $this->layout = 'nomain';
        }else{
            $this->layout = 'home';
        }

        $exception = Yii::$app->errorHandler->exception;
        if ($exception instanceof \yii\web\NotFoundHttpException) {
            return $this->render('error',['name' => 'lo sentimos...','message' => 'pagina no encontrada (#404)']); // page not found
        } else {
            return $this->render('error', ['exception' => $exception]);
        }
    }

    public function actionConnection(){
        $data = ConnectionHistory::find()->where(['user_id' => Yii::$app->user->getId()])->asArray()->all();
        return $this->render('connection',[
            'data' => $data

        ]);
    }

    public function actionBusquedaFuncionario($q){
        $arr_response = [];
        try{
            $session = Yii::$app->session;
            $token = $session->get('jwt_insumos');
            if(is_null($token)){
                $url_token = Yii::$app->params['url_insumos_jwt'];
                $credenciales = Yii::$app->params['insumos_jwt_credenciales'];
                $response = GeneralController::curlPostFormData($url_token, $credenciales);
                if( is_null($response) ){ throw new Exception("No hubo respuesta del servicio de autenticacion"); }
                if( isset($response['status']) && $response['status'] == 'error' ){ throw new Exception( json_encode($response)); }
                $token = $response['token'];
                $session->set('jwt_insumos', $token);
            }
            $url_consulta = Yii::$app->params['url_insumos_consulta'];
            $post_consulta = http_build_query(['search' => $q, 'token' => $token]);
            $response_consulta = GeneralController::curlPostFormData($url_consulta, $post_consulta);
            if( is_null($response_consulta) ){ throw new Exception("No hubo respuesta del servico de consulta"); }
            if( isset($response_consulta['status']) && $response_consulta['status'] == 'error' ){ throw new Exception( json_encode($response_consulta) ); }
            $total_arr = $response_consulta['data'][0];


            //Primero filtrar los usuarios con baja 
            foreach($total_arr as $usuario){
                $dn = GeneralController::parseLdapDn($usuario['dn']);
                if( !in_array('BajasUSR', $dn['OU']) ){
                    $usuario['dn'] = GeneralController::splitNames($dn['CN']);
                    if( !isset($usuario['sAMAccountName']) ){
                        $usuario['sAMAccountName'] = isset($usuario['mail']) ? strtok($usuario['mail'], '@') : null; 
                    }
                    array_push($arr_response, $usuario);
                }
            }
            
        }catch(Exception $e){ self::setErrorConsulta($e->getMessage()); }

        echo Json::encode($arr_response);exit();
        
    }

    public function setErrorConsulta($error){
        $error = new ErrorInesperado();
        $error->tipo_error = 'BUSQUEDA_FUNCIONARIO';
        $error->data = $error;
        $error->user_id = Yii::$app->user->getId();
        $error->save();
    }

    public function actionIndexVerificacionCorreo(){

        $params = Yii::$app->request->queryParams;
        $nr = isset($params['nr']) ?$params['nr'] : null;
        $rfc = isset($params['rfc']) ?$params['rfc'] : null;
        $email = isset($params['email']) ?$params['email'] : null;
       

        $dataQuery = Token::find()->select(['t.code', 't.type', 't.created_at', 't.user_id', 'u.email', 'CASE WHEN (p.name_razon_social is not null and p.name_razon_social != \'\')
            THEN p.name_razon_social ELSE concat_ws(\' \', p.pf_nombre, p.pf_ap_paterno, p.pf_ap_materno) END as fullname', 'r.rfc'])->alias('t')->
        leftJoin('provider p', 'p.user_id = t.user_id')->leftJoin('provider.rfc r', 'p.provider_id = r.provider_id')->leftJoin('usuarios u','t.user_id = u.user_id')->where(['t.type' => 0])
        ->andFilterWhere(['ilike', 'r.rfc', $rfc])->andFilterWhere(['ilike', 'u.email', $email])
        ->andFilterWhere([
            'or',
            ['ilike', 'unaccent(p.name_razon_social)', $nr],
            ['ilike', 'unaccent(p.pf_nombre)', $nr],
            ['ilike', 'unaccent(p.pf_ap_paterno)', $nr],
            ['ilike', 'unaccent(p.pf_ap_materno)', $nr],
        ]);

        $data = new SqlDataProvider([
            'sql' => $dataQuery->createCommand()->getRawSql(),
            'totalCount' => $dataQuery->count(),
            'sort' => [
                'defaultOrder' => ['created_at'=>SORT_DESC],
                'attributes' => [
                    'created_at' => [
                        'asc' => ['created_at' => SORT_ASC],
                        'desc' => ['created_at' => SORT_DESC],
                        'default' => SORT_DESC,
                    ]
                ],
            ]
        ]);

        return $this->render('index-verificacion-correo',[
            'dataProvider'=>$data,
        ]);
    }

    public function actionEliminarTokenCorreo($user_id){
        Token::deleteAll(['and',['user_id'=>$user_id], ['type'=>0]]);
        return $this->redirect(['index-verificacion-correo']);
    }
}
