<?php

namespace app\controllers;


use app\helpers\GeneralController;
use app\models\Course;
use app\models\LoginCourseForm;
use app\models\PermisosUser;
use kartik\form\ActiveForm;
use Yii;
use yii\filters\AccessControl;
use yii\web\Controller;
use yii\web\Response;

class CourseController extends Controller
{
    public function behaviors()
    {
        return [
            'access' => [
                'class' => AccessControl::className(),
                'user'=> \Yii::$app->course,
                'rules' => [
                    [
                        'allow' => true,
                        'roles' => ['@'],
                    ],
                    [
                        'allow' => true,
                        'actions' => ['login','create','check'],
                        'roles' => ['?'],
                    ]
                ],
            ]
        ];
    }

    public function actions()
    {
        return [
            'captcha' => [
                'class' => 'yii\captcha\CaptchaAction',
                'fixedVerifyCode' => YII_ENV_TEST ? 'testme' : null,
            ],
        ];
    }

    public function actionError()
    {
        if(Yii::$app->course->isGuest){
            $this->layout = 'nomain';
        }else{
            $this->layout = 'home';
        }

        return $this->render('error',['name' => 'lo sentimos...','message' => 'pagina no encontrada (#404)']); // page not found

    }

    public function actionCheck($token = null,$id = null)
    {
        if ($token && $id) {
            $id = base64_decode($id);
            $model = Course::find()->where(['and',['token' => $token],['active' => false],['user_id' => $id]])->one();
            if($model){
                $model->active = true;
                $model->save();
                $l = new LoginCourseForm();
                $l->username = $model->username;
                $l->password = $model->pass_tmp;
                if ($l->login()) {
                    if (Yii::$app->course->identity->role == 'COURSE') {
                        return $this->redirect(['/course-provider/course']);
                    } elseif (Yii::$app->course->identity->role == 'DIOS COURSE') {
                        return $this->redirect(['/course-provider/list-provider']);
                    }
                }
            }
        }
        return $this->goHome();
    }


    public function actionCreate(){

        $model = new Course();

        $trans = Yii::$app->getDb()->beginTransaction();
        if (Yii::$app->request->isAjax && $model->load(Yii::$app->request->post())) {
            Yii::$app->response->format = Response::FORMAT_JSON;
            return ActiveForm::validate($model);
        } else if($model->load(Yii::$app->request->post())){
            $model->email = strtolower($model->email);
            $exp = explode('@', $model->email);
            $user_name = str_replace('-','_',$exp[0]).'_course_prev';

            $user_name = $this->addRandUserName($user_name);
            $model->username = $user_name;
            $model->role = 'COURSE';
            $token = md5(Yii::$app->security->generateRandomString().'_'.time());
            if ($model->username) {
                $model->token = $token;
                if ($model->save()) {
                    Yii::$app->dbcourse->createCommand("INSERT INTO auth_assignment(
                                item_name, user_id, created_at) values('COURSE',$model->user_id,extract(epoch from current_timestamp));")->execute();
                    $trans->commit();
                    Yii::$app->session->setFlash('success', 'Registro exitoso, a tu correo electrónico se ha enviado la información para verificar tu cuenta');
                    GeneralController::sendEmail('/provider/correos/verificar_correo_course',null,$model->email,'Verificar cuenta',['token' => $token ,'id' => $model->user_id]);
                    return $this->redirect(['/course-provider/course']);
                }else{
                    $trans->rollBack();
                }
            }

        }

        return $this->renderAjax('create',[
            'model' => $model
        ]);

    }


    public function addRandUserName($user_name){

        while(Course::findOne(['username' => $user_name])){
            $user_name = $user_name . '' . rand(1, 4);
        }
        return $user_name;
    }

    /**
     * @param $pdf
     * @return string
     */
    public function actionVisor($pdf,$ext_file=null)
    {

        if (Yii::$app->request->isAjax) {
            return $this->renderAjax('visor', [
                'pdf' => $pdf,
                'ext' => $ext_file
            ]);
        }

    }


    public function actionLogin()
    {
        $this->layout = 'nomain';

        if (!Yii::$app->course->isGuest) {
            if(Yii::$app->course->identity->role == 'COURSE'){
                return $this->redirect(['/course-provider/course']);
            }elseif (Yii::$app->course->identity->role == 'DIOS COURSE'){
                return $this->redirect(['/course-provider/list-provider']);
            }elseif (Yii::$app->course->identity->role == 'INVITED'){

                $pe = PermisosUser::find()->where(['and',['user_id' => Yii::$app->course->getId()],['in','permiso',['register','finished']]])->one();

                if(isset($pe->permiso) && !empty($pe->permiso)){
                    $view = $pe->permiso == 'finished'?'list-provider':'register';
                    return $this->redirect(['/course-provider/'.$view]);
                }else{
                    return $this->redirect(['/course-provider/video']);
                }
            }
        }

        $model = new LoginCourseForm();
        if(Yii::$app->request->isAjax && $model->load(Yii::$app->request->post())){
            Yii::$app->response->format = Response::FORMAT_JSON;

            $arr = [];

            if(!$model->getUser()){
                $arr['logincourseform-username'] = ['No pudimos encontrar tu usuario en el sistema'];
            }else if(!$model->verifyUserAndPass()){
                $arr['logincourseform-password'] = ['La contraseña es incorrecta. Vuelve a intentarlo o haz clic en "¿Has olvidado tu contraseña?", para reestablecerla.'];
            }

            return $arr;
        }else if ($model->load(Yii::$app->request->post()) && $model->login()) {

            if(Yii::$app->course->identity->role == 'COURSE'){
                return $this->redirect(['/course-provider/course']);
            }elseif (Yii::$app->course->identity->role == 'DIOS COURSE'){
                return $this->redirect(['/course-provider/list-provider']);
            }elseif (Yii::$app->course->identity->role == 'INVITED'){

                $pe = PermisosUser::find()->where(['and',['user_id' => Yii::$app->course->getId()],['in','permiso',['register','finished']]])->one();

                if(isset($pe->permiso) && !empty($pe->permiso)){
                    $view = $pe->permiso == 'finished'?'list-provider':'register';
                    return $this->redirect(['/course-provider/'.$view]);
                }else{
                    return $this->redirect(['/course-provider/video']);
                }
            }
        }

        return $this->renderAjax('login', [
            'model' => $model,
        ]);
    }


    public function actionLogout()
    {
        Yii::$app->course->logout();

        return $this->goHome();
    }

    public function actionAbout()
    {
        return $this->render('about');
    }
}
