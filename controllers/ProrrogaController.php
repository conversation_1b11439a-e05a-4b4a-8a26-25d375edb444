<?php

namespace app\controllers;

use app\helpers\GeneralController;
use app\models\ProrrogaSearch;
use Yii;
use app\models\Prorroga;
use yii\web\Controller;
use yii\filters\VerbFilter;
use yii\web\NotFoundHttpException;
use app\models\HistoricoCertificados;

/**
 * ProductoController implements the CRUD actions for Prorroga model.
 */
class ProrrogaController extends GeneralController
{
    /**
     * @inheritdoc
     */
    public function behaviors()
    {
        return [
            'verbs' => [
                'class' => VerbFilter::class,
                'actions' => [
                    'delete' => ['POST'],
                ],
            ],
        ];
    }

    /**
     * @param string $id
     * @return mixed
     */
    public function actionIndex()
    {
        $searchModel = new ProrrogaSearch();
        $dataProvider = $searchModel->search($this->request->queryParams);
       
        \Yii::$app->session->setFlash('warning', 'Alerta: La modificación de algún registro repercutirá en tiempo real en el reporte SIET !');
       
        return $this->render('index', [
            'dataProvider' => $dataProvider,
        ]);
    }


    /**
     * @param string $id
     * @return mixed
     */
    public function actionView($id)
    {
        return $this->renderAjax('view', [
           'model' => Prorroga::findOne($id),

        ]);
    }

    public function actionUpdate($id = null)
    {
        $model = (($model = Prorroga::findOne(['id' => $id])) !== null) ? $model : new Prorroga();
        if ($model->load($this->request->post())) {
            $model->updated_at = date('Y-m-d H:i:s');

            if ($model->save()) {
                $vigenciaProrroga = $model->vigencia_prorroga;
                $vigenciaOriginal = $model->vigencia_original;

                $historicoCertificados = HistoricoCertificados::findAll([
                    'vigencia' => $vigenciaOriginal,
                    'tipo' => 'CERTIFICADO',
                    'provider_type' => 'bys'
                ]);
                
                foreach ($historicoCertificados as $historicoCertificado) {
                    $historicoCertificado->vigencia_prorroga = $vigenciaProrroga;
                    $historicoCertificado->save();
                }
                
                return $this->redirect(['index']);
            } else {
                var_dump($model->errors);
            }
        }

        return $this->renderAjax('update', [
            'model' => $model,
        ]);
    }

    /**
     * @param string $id
     * @return mixed
     */
    public function actionDelete($id)
    {
        $model = $this->findModel($id);
        if ($model !== null) {
            $model->delete();
        }

        return $this->redirect(['index']);
    }

   /**
     * @param string $id
     * @return Producto the loaded model
     * @throws NotFoundHttpException if the model cannot be found
     */
    protected function findModel($id)
    {
        if (($model = Prorroga::findOne($id)) !== null) {
            return $model;
        }

        throw new NotFoundHttpException('The requested page does not exist.');
    }

}
