<?php

namespace app\controllers;

use app\helpers\GeneralController;
use app\helpers\SireController;
use app\models\Giro;
use app\models\Provider;
use app\models\Usuarios;
use app\models\UsuariosResponsabilidades;
use Yii;
use app\models\FirstUpdateCertificate;
use app\models\FirstUpdateCertificateSearch;
use yii\web\Controller;
use yii\web\NotFoundHttpException;
use yii\filters\VerbFilter;

/**
 * FirstUpdateCertificateController implements the CRUD actions for FirstUpdateCertificate model.
 */
class FirstUpdateCertificateController extends GeneralController
{
    /**
     * {@inheritdoc}
     */
    public function behaviors()
    {
        return [
            'verbs' => [
                'class' => VerbFilter::className(),
                'actions' => [
                ],
            ],
        ];
    }

    public function actionTosire($id=null){
        if(!$id || Yii::$app->request->isPost)
            return false;

        $provider = Provider::findOne($id);
        $existSire = SireController::consultaProviderByRfc($provider->rfc);
        if(!$existSire->exist && !$existSire->error) {
            $data = SireController::getDataProviderRfc($provider->rfc);
            $insertSire = SireController::insertProviderSire($data);
            if ($insertSire->status){
                $mensaje = 'Proveedor insertado correctamente en SIREGob';
                $provider->Clave_ProveedorSire = $insertSire->num;
            }else{
                $mensaje = 'Ocurrió un incidente al insertar el proveedor en SIRE, favor de insertarlo manualmente.('.json_encode($insertSire->error).')';
            }
        }
        if($existSire->exist) {
            $mensaje = 'El proveedor ya se encuentra dado de alta en SIRE, se actualizó su número en el padrón.';
            $provider->Clave_ProveedorSire = $existSire->num;
        }
        if($existSire->error){
            $mensaje = 'No de detecta funcionamiento correcto en la API de SIRE, favor de intentarlo más tarde.';
        }

        $provider->save(false);

        Yii::$app->session->setFlash('info',$mensaje);
        return $this->redirect('index');

    }

    /**
     * Lists all FirstUpdateCertificate models.
     * @return mixed
     */
    public function actionIndex()
    {

        if(in_array('REPORTE ACTUALIZACIONES',GeneralController::getResponsabilidades()) || Yii::$app->user->can(Usuarios::ROLE_ADMIN_PROVIDER)){
            if (Yii::$app->request->post('hasEditable')) {

                $id = Yii::$app->request->post('editableKey');
                $model  = FirstUpdateCertificate::findOne($id);
                $posted = current($_POST['FirstUpdateCertificate']);
                if (isset($posted['n_sire']) && !empty($posted['n_sire'])) {
                    $provider = Provider::findOne($model->provider_id);
                    $provider->Clave_ProveedorSire = $posted['n_sire'];
                    $provider->save();
                }
                return '{"output":"","message":""}';
            }

            $searchModel = new FirstUpdateCertificateSearch();
            $dataProvider = $searchModel->search(Yii::$app->request->queryParams);

            return $this->render('index', [
                'searchModel' => $searchModel,
                'dataProvider' => $dataProvider,
                'permiso' => $this->verifyPermissionCert()
            ]);
        }

        return $this->goHome();

    }

    /**
     * Displays a single FirstUpdateCertificate model.
     * @param integer $id
     * @return mixed
     * @throws NotFoundHttpException if the model cannot be found
     */
    public function actionView($id)
    {
        return $this->render('view', [
            'model' => $this->findModel($id),
        ]);
    }

    /**
     * Creates a new FirstUpdateCertificate model.
     * If creation is successful, the browser will be redirected to the 'view' page.
     * @return mixed
     */
    public function actionCreate()
    {
        $model = new FirstUpdateCertificate();

        if ($model->load(Yii::$app->request->post()) && $model->save()) {
            return $this->redirect(['view', 'id' => $model->first_update_certificate_id]);
        }

        return $this->render('create', [
            'model' => $model,
        ]);
    }

    /**
     * Updates an existing FirstUpdateCertificate model.
     * If update is successful, the browser will be redirected to the 'view' page.
     * @param integer $id
     * @return mixed
     * @throws NotFoundHttpException if the model cannot be found
     */
    public function actionUpdate($id)
    {
        $model = $this->findModel($id);

        if ($model->load(Yii::$app->request->post()) && $model->save()) {
            return $this->redirect(['view', 'id' => $model->first_update_certificate_id]);
        }

        return $this->render('update', [
            'model' => $model,
        ]);
    }

    /**
     * Deletes an existing FirstUpdateCertificate model.
     * If deletion is successful, the browser will be redirected to the 'index' page.
     * @param integer $id
     * @return mixed
     * @throws NotFoundHttpException if the model cannot be found
     */
    public function actionDelete($id)
    {
        $data = $this->findModel($id);
        $data->status = true;
        $data->save();

        return $this->redirect(['index']);
    }

    /**
     * Finds the FirstUpdateCertificate model based on its primary key value.
     * If the model is not found, a 404 HTTP exception will be thrown.
     * @param integer $id
     * @return FirstUpdateCertificate the loaded model
     * @throws NotFoundHttpException if the model cannot be found
     */
    protected function findModel($id)
    {
        if (($model = FirstUpdateCertificate::findOne($id)) !== null) {
            return $model;
        }

        throw new NotFoundHttpException('The requested page does not exist.');
    }

    public function actionViewMovements($id){

        $actividadEconomica = Giro::find()->where(['and',['provider_id' => $id, 'active' => true]])->all();

        $data = Yii::$app->db->createCommand("select * from movements where first_update_certificate_id=:id order by model",[':id' => intval($id)])->queryAll();

        return $this->renderAjax('view-movements',[
           'movements' => $data
        ]);
    }

    function verifyPermissionCert(){
        return Yii::$app->db->createCommand("select d.descripcion from cat_responsabilidades d
            join usuarios_responsabilidades ur on ur.responsabilidad_id = d.responsabilidad_id
            where user_id = :id and d.descripcion = 'CERTIFICADOS'",[':id' => Yii::$app->user->getId()])->queryOne()['descripcion'];
    }

}
