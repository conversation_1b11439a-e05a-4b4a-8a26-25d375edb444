<?php

namespace app\controllers;

use app\helpers\GeneralController;
use app\models\DependenciaCourse;
use app\models\ErrorInesperado;
use app\models\ErrorInesperadoCourse;
use app\models\Firmar;
use app\models\HistoricoAcreditacionCurso;
use app\models\PermisosUser;
use app\models\Provider;
use app\models\ProviderCourse;
use app\models\Status;
use app\models\Usuarios;
use app\models\VerifyUserSecop;
use moonland\phpexcel\Excel;
use Yii;
use app\models\Course;
use app\models\CourseSearch;
use app\models\RepresentanteLegal;
use yii\data\SqlDataProvider;
use yii\helpers\ArrayHelper;
use yii\helpers\VarDumper;
use yii\web\Controller;
use yii\web\NotFoundHttpException;
use yii\filters\VerbFilter;
use yii\web\UploadedFile;

/**
 * DependenciaController implements the CRUD actions for Dependencia model.
 */
class CourseProviderController extends Controller
{
    /**
     * @inheritdoc
     */
    public function behaviors()
    {
        return [
            'verbs' => [
                'class' => VerbFilter::className(),
                'actions' => [
                    'delete' => ['POST'],
                ],
            ],
        ];
    }

    public function beforeAction($action)
    {
        if (\Yii::$app->user->isGuest) {
            if (!in_array($action->id, ['verify-user-secop'])) {
                //return $this->redirect("/site/index")->send();
            }
        }
        return parent::beforeAction($action);
    }

    /**
     * Lists all Dependencia models.
     * @return mixed
     */
    public function actionIndex()
    {
        $this->layout = 'homecourse';

        return $this->render('index', [
            'model' => Course::findOne(Yii::$app->course->getId())
        ]);
    }


    /**
     * Creates a new Course model.
     * If creation is successful, the browser will be redirected to the 'view' page.
     * @return mixed
     */
    public function actionCreateUser()
    {
        $this->layout = 'homecourse';
        $model = new Course();

        if ($model->load(Yii::$app->request->post())) {
            $view = isset(Yii::$app->request->post()['Course']['view']) ? Yii::$app->request->post()['Course']['view'] : [];
            $download = isset(Yii::$app->request->post()['Course']['download']) ? Yii::$app->request->post()['Course']['download'] : [];
            $model->role = 'INVITED';
            $model->active = true;
            $model->email = strtolower($model->email);
            $exp = explode('@', $model->email);
            $user_name = str_replace('-', '_', $exp[0]);
            $user_name = $this->addRandUserName($user_name);
            $model->username = $user_name;
            if ($model->save(false)) {
                if (!empty($view)) {
                    foreach ($view as $v) {
                        $mo = new PermisosUser();
                        $mo->user_id = $model->user_id;
                        $mo->permiso = $v;
                        $mo->save();
                    }
                }
                if (!empty($download)) {
                    $mo = new PermisosUser();
                    $mo->user_id = $model->user_id;
                    $mo->permiso = $download;
                    $mo->save();
                }
                Yii::$app->dbcourse->createCommand("INSERT INTO auth_assignment(
                                item_name, user_id, created_at) values('INVITED',$model->user_id,extract(epoch from current_timestamp));")->execute();
            }

            return $this->redirect(['index-user']);

        }

        return $this->render('create-user', [
            'model' => $model,
            'dependencia' => ArrayHelper::map(DependenciaCourse::find()->all(), 'dependencia_id', 'nombre'),
            'permisos' => ''
        ]);
    }

    /**
     * Updates an existing GrupoProducto model.
     * If update is successful, the browser will be redirected to the 'view' page.
     * @param string $id
     * @return mixed
     */
    public function actionUpdateUser($id)
    {
        $this->layout = 'homecourse';
        $model = $this->findModel($id);

        if ($model->load(Yii::$app->request->post())) {
            $view = isset(Yii::$app->request->post()['Course']['view']) ? Yii::$app->request->post()['Course']['view'] : [];
            $download = isset(Yii::$app->request->post()['Course']['download']) ? Yii::$app->request->post()['Course']['download'] : [];

            if ($model->save(false)) {
                PermisosUser::deleteAll(['and', ['user_id' => $model->user_id], ['in', 'permiso', ['register', 'finished']]]);

                if (!empty($view)) {
                    foreach ($view as $v) {
                        $mo = new PermisosUser();
                        $mo->user_id = $model->user_id;
                        $mo->permiso = $v;
                        $mo->save();
                    }
                }
                PermisosUser::deleteAll(['and', ['user_id' => $model->user_id], ['in', 'permiso', ['full', 'limited']]]);

                if (!empty($download)) {
                    $mo = new PermisosUser();
                    $mo->user_id = $model->user_id;
                    $mo->permiso = $download;
                    $mo->save();
                }
                return $this->redirect(['index-user']);
            }
        }

        return $this->render('update-user', [
            'model' => $model,
            'dependencia' => ArrayHelper::map(DependenciaCourse::find()->all(), 'dependencia_id', 'nombre'),
            'permisos' => ArrayHelper::getColumn(PermisosUser::find()->select('permiso')->where(['user_id' => $model->user_id])->all(), 'permiso')
        ]);
    }


    public function addRandUserName($user_name)
    {

        while (Course::findOne(['username' => $user_name])) {
            $user_name = $user_name . '' . rand(1, 4);
        }
        return $user_name;
    }

    /**
     * Updates an existing GrupoProducto model.
     * If update is successful, the browser will be redirected to the 'view' page.
     * @param string $id
     * @return mixed
     */
    public function actionViewUser($id)
    {
        $this->layout = 'homecourse';
        $model = $this->findModel($id);

        if ($model->load(Yii::$app->request->post()) && $model->save()) {
            return $this->redirect(['index-user']);
        }

        return $this->render('update', [
            'model' => $model,
        ]);
    }

    public function actionIndexUser()
    {

        $this->layout = 'homecourse';
        $searchModel = new CourseSearch();
        $dataProvider = $searchModel->search(Yii::$app->request->queryParams, 'mainFilterInvited');

        return $this->render('index-user', [
            'searchModel' => $searchModel,
            'dataProvider' => $dataProvider,
        ]);

    }

    /**
     * Lists all Dependencia models.
     * @return mixed
     */
    public function actionRegister()
    {
        $this->layout = 'homecourse';

        $searchModel = new CourseSearch();
        $dataProvider = $searchModel->search(Yii::$app->request->queryParams);

        return $this->render('register', [
            'searchModel' => $searchModel,
            'dataProvider' => $dataProvider,
        ]);
    }

    public function actionCourse()
    {

        $this->layout = 'homecourse';
        if (isset(Yii::$app->course->identity->video) && Yii::$app->course->identity->video) {
            if (Yii::$app->course->identity->status_acreditacion_bys == 'FIRMADA') {
                return $this->redirect('index');
            } else {
                return $this->redirect('generacartaprotesta');
            }

        }
        return $this->render('vid', [
            'data' => 'data'
        ]);

    }

    /**
     * Displays a single Dependencia model.
     * @param string $id
     * @return mixed
     */
    public function actionView()
    {
        $this->layout = 'homecourse';
        return $this->render('view', [
            'model' => $this->findModel(Yii::$app->course->getId()),
        ]);
    }


    /**
     * Displays a single Dependencia model.
     * @param string $id
     * @return mixed
     */
    public function actionViewAdmin($id)
    {
        $this->layout = 'homecourse';
        return $this->render('view-admin', [
            'model' => $this->findModel($id),
        ]);
    }


    /**
     * Displays a single Dependencia model.
     * @param string $id
     * @return mixed
     */
    public function actionVideo()
    {
        $this->layout = 'homecourse';
        return $this->render('video');
    }

    /**
     * Displays a single Dependencia model.
     * @param string $id
     * @return mixed
     */
    public function actionPerfil()
    {
        $this->layout = 'homecourse';
        return $this->render('video');
    }

    /**
     * Displays a single Dependencia model.
     * @param string $id
     * @return mixed
     */
    public function actionUpdate()
    {
        $this->layout = 'homecourse';
        $model = $this->findModel(Yii::$app->course->getId());

        $nombreSave = $model->nombre_razon_social;
        if ($model->load(Yii::$app->request->post())) {
            $model->nombre_razon_social = $nombreSave;
            $model->save();

            return $this->redirect('view');
        }

        return $this->render('update', [
            'model' => $model,
        ]);
    }


    /**
     * Displays a single Dependencia model.
     * @param string $id
     * @return mixed
     */
    public function actionUpdateAdmin($id)
    {
        $this->layout = 'homecourse';
        $model = $this->findModel($id);

        if ($model->load(Yii::$app->request->post())) {
            $model->save();

            return $this->redirect(['view-admin','id' => $model->user_id]);
        }

        return $this->render('update-admin', [
            'model' => $model,
        ]);
    }

    protected function findModel($id)
    {
        if (($model = Course::findOne($id)) !== null) {
            return $model;
        } else {
            throw new NotFoundHttpException('The requested page does not exist.');
        }
    }

    public function actionUpdateVideoStatus($currentTimeToken = null, $playerToken = null, $rfc=null)
    {
        if (!$currentTimeToken || !$playerToken)
            return json_encode(['status' => false]);


        if($rfc) {
            $representante = RepresentanteLegal::find()->where(['rfc' => base64_decode(base64_decode(strval($rfc)))])->one();
            $provider_id = $representante->provider_id;
            $proveedor = Provider::findOne($provider_id);
        }else{
            $provider_id = Yii::$app->user->identity->providerid;
            $proveedor = Provider::findOne($provider_id);
            $representante = RepresentanteLegal::find()->where(['and', ['provider_id' => $provider_id], ['rep_' . $proveedor->tipo_provider => true], ['activo' => true]])->one();
        }

        //$provider_id = Yii::$app->user->identity->providerid;
        //$proveedor = Provider::findOne($provider_id);
        //$representante = RepresentanteLegal::find()->where(['and', ['provider_id' => $proveedor->provider_id], ['rep_' . $proveedor->tipo_provider => true], ['activo' => true]])->one();

        if(empty($representante) && $proveedor->tipo_persona == 'Persona moral'){
            \Yii::$app->session->setFlash('error', 'Debes seleccionar al representante firmante!!!');
            return Yii::$app->getResponse()->redirect(['/' . $proveedor->tipo_provider . '/legales/view']);
        }

        if( /* $proveedor->tipo_provider == 'bys' && */ $proveedor->tipo_persona == 'Persona moral'){
            $course = ProviderCourse::find()->where(['and',['provider_id'=>$provider_id,'representante_id' => $representante->representante_legal_id]])->one();
        }else{ $course = ProviderCourse::find()->where(['provider_id'=>$provider_id])->one(); }
        
        $course->video = true;
        $course->end_date_video = date('Y-m-d H:i:s');
        if (!$course->save()) {
            return json_encode($course->errors);
        }
        return json_encode(['status' => true]);
    }

    public function actionVerifyUserSecop($rfc = null)
    {
        $data = ['status' => false, 'error' => 'RFC obligarotio'];
        /* if ($rfc) {
            $request = new \SoapClient(
                'http://sistemas.nl.gob.mx/wsSA_ProveedoresSU/wsProveedores.asmx?WSDL', array('exceptions' => true));

            $userResponseDecode = $request->ConsultaRFC(['rfc' => $rfc]);

            if (isset($userResponseDecode->ConsultaRFCResult) && !empty($userResponseDecode->ConsultaRFCResult)) {
                $userResponseData = json_decode($userResponseDecode->ConsultaRFCResult, true);
                if (isset($userResponseData[0]) && isset($userResponseData[0]['estatus']) && ($userResponseData[0]['estatus'] || $userResponseData[0]['estatus'] == '1')) {
                    if (isset($userResponseData[0]['Clave_ProveedorSire'])) {
                        $claveSire = $userResponseData[0]['Clave_ProveedorSire'];
                        $deP = $userResponseData[0]['Descripcion_Proveedor'];
                        $data = ['status' => true, 'sire' => $claveSire, 'np' => $deP];
                    }
                } else {
                    $data = ['status' => false, 'error' => 'no se encontraron resultados'];
                }
            } else {
                $data = ['status' => false, 'error' => 'Consultar proveedor SECOP No retorno variable esperada'];
            }
        } */

        return json_encode($data);
    }

    public function actionVerifyStartTime($rfc=null)
    {

        if($rfc) {
            $representante = RepresentanteLegal::find()->where(['rfc' => base64_decode(base64_decode(strval($rfc)))])->one();
            $provider_id = $representante->provider_id;
            $proveedor = Provider::findOne($provider_id);
        }else{
            $provider_id = Yii::$app->user->identity->providerid;
            $proveedor = Provider::findOne($provider_id);
            $representante = RepresentanteLegal::find()->where(['and', ['provider_id' => $provider_id], ['rep_' . $proveedor->tipo_provider => true], ['activo' => true]])->one();
        }

        if(empty($representante) && $proveedor->tipo_persona == 'Persona moral'){
            \Yii::$app->session->setFlash('error', 'Debes seleccionar al representante firmante!!!');
            return Yii::$app->getResponse()->redirect(['/' . $proveedor->tipo_provider . '/legales/view']);
        }


        if( /* $proveedor->tipo_provider == 'bys' && */ $proveedor->tipo_persona == 'Persona moral'){
            $course = ProviderCourse::find()->where(['and',['provider_id'=>$provider_id,'representante_id' => $representante->representante_legal_id]])->one();
        }else{ $course = ProviderCourse::find()->where(['provider_id'=>$provider_id])->one(); }
        

        if(!$course)
            $course = new ProviderCourse();

        if(!$course->start_date_video){
            $course->provider_id = $provider_id;
            $course->representante_id = !empty($representante) ? $representante->representante_legal_id : null;
            $course->created_by = 0;
            $course->start_date_video = date('Y-m-d H:i:s');

            if(!$course->save())
                return json_encode($course->errors);
        }

        return true;
    }

    public function actionFirmar($rp = null)
    {
        $this->layout = 'homecourse';
        $model = new Firmar();
        $id = \Yii::$app->course->getId();
        $data_prov = Course::find()->select(['user_id', 'rfc', 'tipo_persona'])->where(['user_id' => $id])->one();
        $verify = false;

        if ($data_prov['rfc'] != null) {
            $documento = 'documentos_firmados_course/' . $data_prov['rfc'] . '/' . md5($data_prov['user_id']) . '_original_bys.pdf';
            if (file_exists($documento)) {
                $verify = true;
            }
        }

        if (!$verify) {
            $documento = 'documentos_firmados_course/' . $data_prov['user_id'] . '/' . md5($data_prov['user_id']) . '_original_.pdf';
        }

        $data = file_get_contents($documento);
        $base64 = base64_encode($data);

        return $this->render('firmar', [
            'model' => $model,
            'documento' => $documento,
            'base64' => $base64,
            'rp' => $rp,
            'tipo_persona' => $data_prov['tipo_persona']
        ]);
    }

    public function actionGeneracartaprotesta()
    {

        $id = Yii::$app->course->getId();
        $model = Course::find()->where(['user_id' => $id])->one();

        $path = 'documentos_firmados_course/';
        $this->makeDir($path);

        $path_rfc = $path . $model->rfc;

        $this->makeDir($path_rfc);

        $fm = $model->tipo_persona == 'Persona moral' ? 'm' : 'f';


        $content = $this->renderPartial('pdf/acreditacion_curso_' . $fm, [
            'model' => $model,
            'firma' => '',
            'firmaEmpresa' => '',//'9687750c2b490f96bb5addbab7e821296fa9cc5096f4c520a91351c93fc033df01c1e2675b3d363d7e9d0e93d11e91fcccd762fd4d5925a29b67daa724433a685a8c5fbd93ad5fc8c3ea705201ba2556fc41ae46f2c08783e852e7e27a0bd6c3ae64873d708ae84e56b6ade3290bdfc3555bcfe76494792adb24efb54eb198566157c44f46480ee21e5d575f25a464e1450b8185f9d5e67f709cdf2fa304c9ee080c6c99cbba76370311357bca232e67e39949bdf44dcc97675c65697d8345f8eab49285f424daf06f7cc78e13a3f894189565f386ab7d9d465d7a92300b5c8e83c4ade0d31cd401285193937906ad1f85dec7ca3ccdd438f23b93b90690a6fa'

        ]);
        $header = $this->renderPartial('pdf/header_curso_bys');
        $footer = $this->renderPartial('pdf/footer_curso_bys');
        $name_file_save = $path_rfc . '/' . md5($id) . '_original_bys.pdf';
        $mpdf = new \mPDF('utf-8', 'A4', 0, '', 0, 0, 5, 15, 0, 0);
        $mpdf->SetTitle('Acreditación del curso');
        $mpdf->SetHTMLHeader($header);
        $mpdf->SetHTMLFooter($footer);
        $mpdf->WriteHTML(file_get_contents('css/pdf.css'), 1);
        $mpdf->WriteHTML($content, 2);
        $mpdf->Output($name_file_save, 'F');

        $model->status_acreditacion_bys = 'GENERADA';
        $model->save();
        $this->redirect('/course-provider/firmar');

    }

    public function makeDir($dir)
    {
        if (!file_exists($dir)) {
            mkdir($dir, 0777, true);
        }
    }


    public function actionGenerateFile()
    {
        date_default_timezone_set('America/Monterrey');
        try {

            $id = \Yii::$app->course->getId();
            $data_pro = Course::find()->select(['user_id', 'tipo_persona', 'rfc', 'email'])->where(['user_id' => $id])->asArray()->one();
            $user_id = $data_pro['user_id'];
            $emailUser = $data_pro['email'];
            $tipo_persona = $data_pro['tipo_persona'];
            $tipo_provider = 'bys';
            $request = \Yii::$app->request;
            $data = $request->post();

            $datos = $data['datos'];
            $firma = $data['firma'];
            $rfc = $data['rfc'];
            $curp = $data['curp'];
            $rfc_rep = $data['rfc_rep'];
            $email = $data['email'];
            $org = $data['org'];
            $cn = $data['cn'];
            $rp = $data['rp'];

            $rfc_representante = $rfc;
            $tim = microtime(true) * 1000;
            $firmx = base64_encode(md5($tim) . md5($tim + 1) . md5($tim + 2) . md5($tim + 3) . md5($tim + 4) . md5($tim + 5));


            $path_file = 'documentos_firmados_course/';

            $rfcOri = $rfc;
            if ($rp != null) {
                $rfcOri = $data_pro['rfc'];
            }

            $path_fir = $path_file . $rfcOri . '/firma.fir';

            $date_year = date("Y");
            $date_month = date("m");

            if ((strlen($rfc) == 13 && $tipo_persona == 'Persona física' && $rp == null) || (strlen($rfc) == 12 && $tipo_persona == 'Persona moral' && $rp == null) || (strlen($rfc) == 13 && $tipo_persona == 'Persona moral' && $rp != null)) {
                $verify_exist = null;
                if ($rp == null) {
                    $verify_exist = Course::find()->select(['user_id'])->where(['and', ['rfc' => $rfc], ['user_id' => $id]])->one()['user_id'];
                }
                if ($verify_exist == null && $rp == null) {
                    return json_encode(['status' => false, 'error' => 'Los datos del certificado no corresponden a la persona física']);
                } else {
                    if ($rp != null) {
                        $rfc = Course::find()->select(['rfc'])->where(['user_id' => $id])->one()['rfc'];

                        if ($rfc != null) {
                            if ((Course::find()->where(['and', ['rfc_representante' => $rfc_representante], ['user_id' => $user_id]])->one()) === null) {
                                return json_encode(['status' => false, 'error' => 'Los datos del certificado no corresponden al representante legal registrado']);
                            }
                        } else {
                            return json_encode(['status' => false, 'error' => 'No se ha encontrado la firma de la empresa(Persona moral).']);
                        }

                    }
                    if (!file_exists($path_file . $rfc)) {
                        rename($path_file . $user_id, $path_file . $rfc);
                    }
                }

                if (($tipo_persona == 'Persona física' && $rp == null) || $tipo_persona == 'Persona moral' && $rp != null) {
                    $path_doc = $path_file . $rfc . '/' . md5($user_id) . '_original_' . $tipo_provider . '.pdf';

                    $fp = fopen($path_fir, 'w');
                    fwrite($fp, $datos);
                    fclose($fp);

                    $generate_file_sign = $this->generate_pdf($rfc, $firmx, $date_year, $user_id, $firma);
                    if ($generate_file_sign) {
                        $zipname = $path_file . $rfc . '/firma_' . $tipo_provider . '_' . $date_year . '_' . $date_month . time() . '.fiel';
                        $zip = new \ZipArchive;
                        $zip->open($zipname, \ZipArchive::CREATE);

                        $zip->addFile($path_fir, 'firma.fiel');
                        $zip->addFile($path_doc, $rfc . '_original.pdf');
                        $zip->close();

                        if (file_exists($zipname)) {
                            $fecha = date('Y-m-d');
                            if ($rp == null) {
                                \Yii::$app->dbcourse->createCommand("update public.course_user set status_acreditacion_$tipo_provider = 'FIRMADA', url_acreditacion_$tipo_provider = :url,
                                 curp_sat = :curp, rfc_rep_sat = :rfcrep , email_sat = :email,org_sat = :org,cn_sat = :cn,
                                 fecha_acreditacion_$tipo_provider = :fecha, firma_$tipo_provider = :fi,
                                 firma_corta_$tipo_provider = :fic where user_id = :id",
                                    [':url' => $generate_file_sign, ':curp' => $curp, ':rfcrep' => $rfc_rep, ':email' => $email,
                                        ':org' => $org, ':cn' => $cn, ':fecha' => $fecha, ':id' => $user_id, ':fi' => $firma, ':fic' => $firmx])
                                    ->execute();
                            } else {
                                \Yii::$app->dbcourse->createCommand("update public.course_user set status_acreditacion_$tipo_provider = 'FIRMADA', url_acreditacion_$tipo_provider = :url,
                                    fecha_acreditacion_$tipo_provider = :fecha, firma_rp_$tipo_provider = :fi,
                                    firma_corta_rp_$tipo_provider = :fic where user_id = :id",
                                    [':url' => $generate_file_sign, ':fecha' => $fecha, ':id' => $user_id, ':fi' => $firma, ':fic' => $firmx])
                                    ->execute();

                            }

                            if ($tipo_provider == 'bys') {
                                GeneralController::sendEmail('/provider/correos/escrito_acreditacion', null, $emailUser, 'ESCRITO DE ACREDITACIÓN DEL CURSO', [], $generate_file_sign);
                                $this->documentocertificado($user_id, $tipo_persona);
                            }


                            if (file_exists($path_fir)) {
                                unlink($path_fir);
                            }
                            if (file_exists($path_doc)) {
                                unlink($path_doc);
                            }
                        }
                        return json_encode(['status' => true, 'rp' => null]);
                        exit();
                    }
                } else {

                    \Yii::$app->dbcourse->createCommand("update public.course_user set 
                        curp_sat = :curp, rfc_rep_sat = :rfcrep , email_sat = :email,org_sat = :org,cn_sat = :cn,
                        firma_$tipo_provider = :fi,
                        firma_corta_$tipo_provider = :fic where user_id = :id",
                        [':curp' => $curp, ':rfcrep' => $rfc_rep, ':email' => $email,
                            ':org' => $org, ':cn' => $cn, ':id' => $user_id, ':fi' => $firma, ':fic' => $firmx])
                        ->execute();

                    $error = new ErrorInesperadoCourse();
                    $error->tipo_error = 'FIRMO BIEN';
                    $error->user_id = Yii::$app->course->getId();
                    $error->save();

                    return json_encode(['status' => true, 'rp' => 'SI']);
                    exit();
                }

            } else {

                $tipo_persona = $rp != null ? 'Persona física (representante legal) ' : $tipo_persona;

                return json_encode(['status' => false, 'error' => 'El certificado no corresponde al de una ' . $tipo_persona . '.']);
            }

        } catch (\Exception $e) {
            $error = new ErrorInesperadoCourse();
            $error->tipo_error = 'FIRMAR ACREDITACION';
            $error->data = $e->getMessage();
            $error->user_id = Yii::$app->course->getId();
            $error->save();

            return json_encode(['status' => false, 'error' => 'Error code 504']);
        }

    }


    public function actionGenconst($id)
    {
        $path_file = 'documentos_firmados_course/';

        $proveedor = Course::findOne($id);
        $path_fir = $path_file . $proveedor->rfc . '/firma.fir';

        $date_year = date("Y");
        $date_month = date("m");
        $generate_file_sign = $proveedor->url_acreditacion_bys;
        if($proveedor->url_acreditacion_bys!='' && $proveedor->firma_corta_bys!='' && $proveedor->firma_bys!=''){
            $generate_file_sign = $this->generate_pdf($proveedor->rfc, $proveedor->firma_corta_bys, $date_year, $proveedor->user_id, $proveedor->firma_bys);
        }
        if ($generate_file_sign) {
            $proveedor->url_acreditacion_bys = $generate_file_sign;
            $proveedor->save();
            $path_doc = $path_file . $proveedor->rfc . '/' . md5($proveedor->user_id) . '_original_bys.pdf';
            $zipname = $path_file . $proveedor->rfc . '/firma_bys_' . $date_year . '_' . $date_month . time() . '.fiel';
            $zip = new \ZipArchive;
            $zip->open($zipname, \ZipArchive::CREATE);

            $zip->addFile($path_fir, 'firma.fiel');
            $zip->addFile($path_doc, $proveedor->rfc . '_original.pdf');
            $zip->close();

                GeneralController::sendEmail('/provider/correos/escrito_acreditacion', null, $proveedor->email, 'ESCRITO DE ACREDITACIÓN DEL CURSO', [], $generate_file_sign);
                $this->documentocertificado($id,null,null,true);
                if (file_exists($path_fir)) {
                    unlink($path_fir);
                }
                if (file_exists($path_doc)) {
                    unlink($path_doc);
                }
        }

        return $this->redirect('list-provider');
    }


    public function actionGenerateConst(){

        $data = Yii::$app->dbcourse->createCommand("select * from historico_acreditacion_curso where tipo = 'CONSTANCIA' and created_at::date between '2021-10-25' and '2021-10-31'")->queryAll();

        foreach ($data as $v){
            $user_id = $v['user_id'];
            $this->documentocertificado($user_id);
        }
    }


    public function documentocertificado($id = null, $tipo = null, $date = null,$ga = false)
    {

        $proveedor = Course::findOne($id);

        if (!$proveedor) {
            return $this->redirect('/');
        }

        $datosFirma = ['firmante' => '', 'firma' => '','cargo' => ''];

        $datosQr = ['img' => ''];

        $path_doc = 'prov_constancia_curso';
        if (!file_exists($path_doc)) {
            mkdir($path_doc, 0777, true);
        }

        $fisicaMoral = $proveedor->tipo_persona == 'Persona moral' ? 'm' : 'f';

        $content = $this->renderPartial('pdf/constancia_' . $fisicaMoral, [
            'model' => $proveedor,
            'datosFirma' => $datosFirma,
            'datosQr' => $datosQr,
            'date' => $date
        ]);

        $mpdf = new \mPDF();
        $mpdf->AddPage('L');
        $mpdf->SetTitle('Constancia');
        $mpdf->SetHTMLHeader($this->renderPartial('pdf/constancia_header'));
        $mpdf->SetHTMLFooter($this->renderPartial('pdf/constancia_footer', ['datosFirma' => $datosFirma, 'datosQr' => $datosQr]));
        $mpdf->defaultfooterline = 0;
        $mpdf->WriteHTML($content);
        //$mpdf->AddPage('L');
        $filename = $path_doc . '/' . md5($proveedor->user_id) . '_constancia_proveedor' . time() . '.pdf';
        $mpdf->Output($filename, 'F');

        chmod($filename, 0777);

        $date_year = date("Y");
        $date_month = date("m");
        $filenameFinal = 'documentos_firmados_course' . '/' . $proveedor->rfc . '/' . $proveedor->rfc . '_constancia_' . $date_year . '_' . $date_month . '_' . $fisicaMoral . time() . '.pdf';


        $codigoV = GeneralController::codigoVerificacion($proveedor->user_id);
        $token = GeneralController::solicitaDatosGet(\Yii::$app->params['urlToken'])['token'];
        $datosQr = GeneralController::solicitaDatosPost(\Yii::$app->params['urlQr'], ['encode' => $codigoV, 'base64' => 'true', 'validador' => true]);

        $datosBarCode = GeneralController::solicitaBarCode(\Yii::$app->params['urlBarCode'], $codigoV);
        $datosFirma = GeneralController::solicitaDatosPost(\Yii::$app->params['urlFirma'], ['code' => $codigoV, 'token' => $token, 'type' => 'BYS', 'path' => \yii\helpers\Url::home('https') . $filename, 'signed_document' => \yii\helpers\Url::home('https') . $filenameFinal]);

        unlink($filename);

        $content = $this->renderPartial('pdf/constancia_' . $fisicaMoral, [
            'model' => $proveedor,
            'datosFirma' => $datosFirma,
            'datosQr' => $datosQr,
            'date' => $date
        ]);


        $mpdf = new \mPDF();
        $mpdf->AddPage('L');
        $mpdf->SetTitle('Constancia');
        $mpdf->SetHTMLHeader($this->renderPartial('pdf/constancia_header'));
        $mpdf->SetHTMLFooter($this->renderPartial('pdf/constancia_footer', ['datosFirma' => $datosFirma, 'datosQr' => $datosQr, 'barCode' => $datosBarCode, 'code' => $codigoV]));
        $mpdf->defaultfooterline = 0;
        $mpdf->WriteHTML($content);

        $mpdf->Output($filenameFinal, 'F');
        chmod($filenameFinal, 0777);

        $historicoCerId = null;
        if ($datosFirma['status'] != 'Error' && file_exists($filenameFinal)) {
            $hc = new HistoricoAcreditacionCurso();
            $hc->user_id = $proveedor->user_id;
            $hc->codigo_verificacion = $codigoV;
            $hc->url_documento = $filenameFinal;
            $hc->tipo = 'CONSTANCIA';
            $hc->provider_type = 'bys';
            $hc->generate_admin = $ga;
            if (!empty($date)) {
                $date = base64_decode($date);
                $hc->created_at = $date;
            }
            $hc->save();
            $historicoCerId = $hc->historico_certificados_id;
            GeneralController::sendEmail('/provider/correos/constancia', null, $proveedor['email'], 'CONSTANCIA DEL CURSO', [], $filenameFinal);
        } else {
            $error = new ErrorInesperadoCourse();
            $error->tipo_error = 'FIRMAR CONSTANCIA';
            $error->data = $datosFirma;
            $error->user_id = Yii::$app->course->getId();
            $error->save(false);
        }
        return $historicoCerId;
    }

    public function generate_pdf($rfc = null, $firma = null, $year = null, $user_id = null, $firmaOri = null)
    {
        $model = $this->findModel($user_id);
        $path_file = 'documentos_firmados_course/' . $rfc . '/' . $rfc . '_bys_firmado_' . $year . '_' . time() . '.pdf';
        $fm = $model->tipo_persona == 'Persona moral' ? 'm' : 'f';
        $content = $this->renderPartial('pdf/acreditacion_curso_' . $fm, [
            'model' => $model,
            'firma' => $firma,
            'firmaEmp' => $model->firma_corta_bys,
        ]);
        //$pie = $this->renderPartial('pdf/footer');
        $mpdf = new \mPDF('utf-8', 'A4', 0, '', 0, 0, 5, 15, 0, 0);
        $mpdf->SetTitle('Acreditación del curso');
        $stylesheet = file_get_contents('css/pdf.css');
        $mpdf->SetHTMLHeader($this->renderPartial('pdf/header_curso_bys'));
        $mpdf->SetHTMLFooter($this->renderPartial('pdf/footer_curso_bys'));
        $mpdf->WriteHTML($stylesheet, 1);
        $mpdf->WriteHTML($content, 2);
        $mpdf->Output($path_file, 'F');
        $firma1 = 'firma_bys';
        $firma2 = 'firma_corta_bys';
        $historico_cp = new HistoricoAcreditacionCurso();
        $historico_cp->user_id = $model->user_id;
        $historico_cp->url_documento = $path_file;
        $historico_cp->$firma1 = $firmaOri;
        $historico_cp->$firma2 = $firma;
        $historico_cp->tipo = 'ACREDITACION';
        $historico_cp->provider_type = 'bys';
        $historico_cp->save(false);

        return $path_file;

    }

    public function generateFirm()
    {
        $tim = microtime(true) * 1000;
        $firmx = base64_encode(md5($tim) . md5($tim + 1) . md5($tim + 2) . md5($tim + 3) . md5($tim + 4) . md5($tim + 5));
        return $firmx;
    }

    public function actionValidarCertificado($serial, $tipo)
    {
        $serial = gmp_strval(gmp_init($serial, 16));

        $RootCA = "ocsp/ocsp.ac" . intval($tipo) . "_sat.pem"; // Points to the Root CA in PEM format.
        $iusser = "ocsp/AC" . intval($tipo) . "_SAT.pem";

        try {
            if (file_exists($RootCA) && file_exists($iusser)) {
                $host = "cfdi.sat.gob.mx";
                $OCSPUrl = 'https://cfdi.sat.gob.mx/edofiel'; //Points to the OCSP URL
                $output = shell_exec('openssl ocsp -issuer ' . $iusser . ' -serial ' . $serial . ' -url ' . $OCSPUrl . ' -VAfile ' . $RootCA . ' -header host ' . $host);
                if ($output) {
                    $output2 = preg_split('/[\r\n]/', $output);
                    if ($output2 != 'invalis stuff' and $output2 != null) {
                        $ocsp = trim(explode(':', preg_split('/[\r\n]/', $output)[0])[1]);
                        $ocsp = trim(strtolower($ocsp)) == 'warning' ? 'good' : $ocsp;
                        echo $ocsp; // will be "good", "revoked", or "unknown"
                    } else {
                        echo "invalis stuff";
                    }
                } else {
                    echo " Error inesperado";
                }
            } else {
                echo " Código de certificado no encontrado";
            }
        } catch (\Exception $e) {
            echo " Errores inesperados";
        }
    }


    public function actionListProvider($fi = null, $ff = null)
    {
        $this->layout = 'homecourse';
        $params = Yii::$app->request->queryParams;
        $dataSiet = $this->dataSiet($fi, $ff, $params);

        return $this->render('list-provider', [
            'data' => $dataSiet,
            'fi' => $fi,
            'ff' => $ff
        ]);

    }

    public function actionUpload()
    {
        date_default_timezone_set('America/Monterrey');
        $this->layout = 'homecourse';


        $modelImport = new \yii\base\DynamicModel([
            'fileImport' => 'File Import',
            'user_id' => 'Proveedor',
        ]);
        $modelImport->addRule(['fileImport'], 'file',['extensions'=>'pdf']);
        $modelImport->addRule(['user_id'], 'integer');

        if (\Yii::$app->request->post()) {
            $t = Yii::$app->db->beginTransaction();
            $userId = Yii::$app->request->post()['DynamicModel']['user_id'];
            $model = Course::findOne($userId);
            $model->urlAcre = UploadedFile::getInstance($modelImport,'fileImport');

            if(!empty($model->urlAcre)){
                $year = date("Y");

                GeneralController::makeDir('documentos_firmados_course/' . $model->rfc);

                $model->url_acreditacion_bys = 'documentos_firmados_course/' . $model->rfc . '/' . $model->rfc . '_bys_firmado' . $year . '_' . time() . '.pdf';

                $model->status_acreditacion_bys = 'FIRMADA';
                $model->fecha_acreditacion_bys = date('Y-m-d');
                if($model->save()){
                    $model->urlAcre->saveAs($model->url_acreditacion_bys);
                    $historico_cp = new HistoricoAcreditacionCurso();
                    $historico_cp->user_id = $model->user_id;
                    $historico_cp->url_documento = $model->url_acreditacion_bys;
                    $historico_cp->tipo = 'ACREDITACION';
                    $historico_cp->provider_type = 'bys';
                    $historico_cp->generate_admin = true;
                    if($historico_cp->save(false)){
                        $t->commit();
                        return $this->redirect(['list-provider']);
                    }
                    $t->rollBack();
                }
            }
        }

        return $this->render('upload', [
            'prov' => ArrayHelper::map(Yii::$app->dbcourse->createCommand("select user_id, concat(nombre_razon_social,' - ',nombre,' ',primer_apellido,' ',segundo_apellido) as proveedor
            from course_user where video is true and (status_acreditacion_bys!='FIRMADA' or status_acreditacion_bys is null)
            ")->queryAll(), 'user_id', 'proveedor'),
            'modelImport' => $modelImport
        ]);

    }


    public function dataSiet($fi, $ff, $params)
    {

        if ($fi == null || $ff == null) {
            $fi = '2021-01-01';
            $ff = date('Y-m-d');
        }

        $sqlSearchNp = '';
        $sqlSearchR = '';
        $sqlSearchP = '';
        $sqlSearchT = '';
        $sqlSearchF = '';
        $bind = [];
        //r,rs,tp,f
        if (isset($params['np']) && !empty($params['np'])) {

            $np = $params['np'];

            $sqlSearchNp = " and numero_proveedor = :np";

            $bind[':np'] = "$np";
        }

        if (isset($params['r']) && !empty($params['r'])) {

            $r = $params['r'];

            $sqlSearchR = " and lower(unaccent(rfc)) ilike '%' || lower(unaccent(:r)) || '%' ";

            $bind[':r'] = "%$r%";
        }

        if (isset($params['rs']) && !empty($params['rs'])) {

            $r = $params['rs'];

            $sqlSearchP = " and lower(unaccent(proveedor)) ilike '%' || lower(unaccent(:rs)) || '%' ";

            $bind[':rs'] = "%$r%";
        }

        $bind = !empty($bind) ? $bind : '';

        $totalCount = Yii::$app->dbcourse->createCommand("with a as(select user_id,numero_proveedor,rfc,
                                       nombre_razon_social as proveedor,
                                        tipo_persona,fecha_acreditacion_bys::date as fecha_acreditacion_bys,status_acreditacion_bys from course_user where status_acreditacion_bys ='FIRMADA' and fecha_acreditacion_bys::date between '$fi' and '$ff')
                                    select count(1) from a where status_acreditacion_bys ='FIRMADA' and fecha_acreditacion_bys::date between '$fi' and '$ff'
                                        $sqlSearchNp  $sqlSearchR $sqlSearchP $sqlSearchT $sqlSearchF", $bind)
            ->queryScalar();


        $sql = [
            'sql' =>
                "with a as(select user_id,numero_proveedor,rfc,
                                        nombre_razon_social as proveedor,
                                        tipo_persona,fecha_acreditacion_bys::date as fecha_acreditacion_bys,status_acreditacion_bys from course_user where status_acreditacion_bys ='FIRMADA' and fecha_acreditacion_bys::date between '$fi' and '$ff')
select * from a where status_acreditacion_bys ='FIRMADA'
                                        and fecha_acreditacion_bys::date between '$fi' and '$ff' $sqlSearchNp  $sqlSearchR $sqlSearchP $sqlSearchT $sqlSearchF order by fecha_acreditacion_bys DESC",
            'totalCount' => $totalCount,
            'db' => 'dbcourse'
            //'sort' =>false, to remove the table header sorting
        ];

        if ($bind) {
            $sql['params'] = $bind;
        }

        $dataProvider = new SqlDataProvider($sql);

        return $dataProvider;
    }


    public function actionDownloadData($fi = null, $ff = null)
    {

        date_default_timezone_set('America/Monterrey');

        if ($fi && $ff) {
            $interval = " fecha_acreditacion_bys::DATE between '$fi' and  '$ff'";
        } else {
            $fi = '2021-03-01';
            $ff = date('Y-m-d');
            $interval = " fecha_acreditacion_bys::DATE between '$fi' and  '$ff'";
        }

        $pe = PermisosUser::find()->select('permiso')->where(['and', ['user_id' => Yii::$app->course->getId()], ['permiso' => 'full']])->one()['permiso'];

        if (Yii::$app->course->identity->role == 'DIOS COURSE' || $pe) {
            $data_all_final = Yii::$app->dbcourse->createCommand("select numero_proveedor as \"Número de proveedor\",rfc as \"RFC\",tipo_persona as \"TIPO PERSONA\",
                                        nombre_razon_social as \"NOMBRE O RAZÓN SOCIAL\",
                                        rfc_representante as \"RFC DEL REPRESENTANTE LEGAL\",
                                        nombre as \"NOMBRE DE PERSONA QUE TOMO EL CURSO\",primer_apellido as \"PRIMER APELLIDO DE PERSONA QUE TOMO EL CURSO\",segundo_apellido as \"SEGUNDO APELLIDO DE PERSONA QUE TOMO EL CURSO\",cargo as \"CARGO QUE OCIPA EN EL NEGOCIO O EMPRESA\",telefono as \"TELEFONO\",email as \"CORREO ELECTRÓNICO\",
                                        creation_date AS \"FECHA DE REGISTRO\",fecha_acreditacion_bys::date as \"FECHA EN LA QUE COMPLETÓ EL CURSO\" from course_user where status_acreditacion_bys ='FIRMADA' and $interval")->queryAll();

        } else {
            $data_all_final = Yii::$app->dbcourse->createCommand("select numero_proveedor as \"Número de proveedor\",rfc as \"RFC\",tipo_persona as \"TIPO PERSONA\",
                                        nombre_razon_social as \"NOMBRE O RAZÓN SOCIAL\",
                                        fecha_acreditacion_bys::date as \"FECHA EN LA QUE COMPLETÓ EL CURSO\" from course_user where status_acreditacion_bys ='FIRMADA' and $interval")->queryAll();

        }
        if ($data_all_final) {
            $columnsData = array_keys($data_all_final[0]);

            $headersData = array_combine($columnsData, $columnsData);

            foreach ($headersData as $index => $header) {
                $headersData[$index] = str_replace("_", " ", strtoupper($header));
            }

            $now = date("Y-m-d");
            $fi_name = 'proveedores_curso' . "_" . $now . ".xlsx";
            Excel::widget([
                'models' => $data_all_final,
                'mode' => 'export',
                'fileName' => $fi_name,
                'columns' => $columnsData,
                'headers' => $headersData,
            ]);
            exit();
            return true;
        } else {
            return $this->redirect('/course-provider/list-provider');
        }


    }
}
