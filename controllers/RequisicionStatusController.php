<?php

namespace app\controllers;

use app\models\Requisicion;
use yii\filters\AccessControl;
use app\models\Usuarios;
use Yii;
use app\models\RequisicionStatus;
use app\models\RequisicionStatusSearch;
use yii\web\Controller;
use yii\web\NotFoundHttpException;
use yii\filters\VerbFilter;

/**
 * RequisicionStatusController implements the CRUD actions for RequisicionStatus model.
 */
class RequisicionStatusController extends Controller
{
    /**
     * @inheritdoc
     */
    public function behaviors()
    {
        return [
            'verbs' => [
                'class' => VerbFilter::className(),
                'actions' => [
                    'delete' => ['POST'],
                ],
            ],
        ];
    }

    /**
     * Lists all RequisicionStatus models.
     * @return mixed
     */
    public function actionIndex()
    {
        $searchModel = new RequisicionStatusSearch();
        $dataProvider = $searchModel->search(Yii::$app->request->queryParams);

        return $this->render('index', [
            'searchModel' => $searchModel,
            'dataProvider' => $dataProvider,
        ]);
    }

    /**
     * Displays a single RequisicionStatus model.
     * @param string $id
     * @return mixed
     */
    public function actionView($id)
    {
        return $this->render('view', [
            'model' => $this->findModel($id),
        ]);
    }

    /**
     * Creates a new RequisicionStatus model.
     * If creation is successful, the browser will be redirected to the 'view' page.
     * @return mixed
     */
    public function actionCreate()
    {
        $model = new RequisicionStatus();
        if ($model->load(Yii::$app->request->post())) {
            $id = Yii::$app->user->id;
            $factual = date("Y-m-d H:i:s");
            $model->created_date = $factual;
            $model->user_rechazo_id = $id;
            $requisicion = Requisicion::findOne($model->requisicion_id);
            $requisicion->status = Requisicion::RECHAZADO;
            $requisicion->update();
            if($model->save()){
                return $this->redirect(['../requisicion/']);
            }
        }
            return $this->render('create', [
                'model' => $model,
            ]);

    }

    public function actionValidar_director_servicio()
    {
        $model = new RequisicionStatus();
        if ($model->load(Yii::$app->request->post())) {
            $id = Yii::$app->user->id;
            $factual = date("Y-m-d H:i:s");
            $model->created_date = $factual;
            $model->user_rechazo_id = $id;
            $requisicion = Requisicion::findOne($model->requisicion_id);
            $requisicion->status = Requisicion::RECHAZADO_DIRECTOR_SERVICIO;
            $requisicion->update();
            if($model->save()){
                return $this->redirect(['../requisicion/index-director']);
            }
        }
        return $this->render('create', [
            'model' => $model,
        ]);

    }

    public function actionValidar_director_material()
    {
        $model = new RequisicionStatus();
        if ($model->load(Yii::$app->request->post())) {
            $id = Yii::$app->user->id;
            $factual = date("Y-m-d H:i:s");
            $model->created_date = $factual;
            $model->user_rechazo_id = $id;
            $requisicion = Requisicion::findOne($model->requisicion_id);
            $requisicion->status = Requisicion::RECHAZADO_DIRECTOR_MATERIAL;
            $requisicion->update();
            if($model->save()){
                return $this->redirect(['../requisicion/index-director']);
            }
        }
        return $this->render('create', [
            'model' => $model,
        ]);

    }

    public function actionValidar_director_general()
    {
        $model = new RequisicionStatus();
        if ($model->load(Yii::$app->request->post())) {
            $id = Yii::$app->user->id;
            $factual = date("Y-m-d H:i:s");
            $model->created_date = $factual;
            $model->user_rechazo_id = $id;
            $requisicion = Requisicion::findOne($model->requisicion_id);
            $requisicion->status = Requisicion::RECHAZADO_DIRECTOR_GENERAL;
            $requisicion->update();
            if($model->save()){
                return $this->redirect(['../requisicion/index-director']);
            }
        }
        return $this->render('create', [
            'model' => $model,
        ]);

    }

    /**
     * Updates an existing RequisicionStatus model.
     * If update is successful, the browser will be redirected to the 'view' page.
     * @param string $id
     * @return mixed
     */
    public function actionUpdate($id)
    {
        $model = $this->findModel($id);

        if ($model->load(Yii::$app->request->post()) && $model->save()) {
            return $this->redirect(['view', 'id' => $model->status_requisicion_id]);
        } else {
            return $this->render('update', [
                'model' => $model,
            ]);
        }
    }


    /**
     * Deletes an existing RequisicionStatus model.
     * If deletion is successful, the browser will be redirected to the 'index' page.
     * @param string $id
     * @return mixed
     */
    public function actionDelete($id)
    {
        $this->findModel($id)->delete();

        return $this->redirect(['index']);
    }

    /**
     * Finds the RequisicionStatus model based on its primary key value.
     * If the model is not found, a 404 HTTP exception will be thrown.
     * @param string $id
     * @return RequisicionStatus the loaded model
     * @throws NotFoundHttpException if the model cannot be found
     */
    protected function findModel($id)
    {
        if (($model = RequisicionStatus::findOne($id)) !== null) {
            return $model;
        } else {
            throw new NotFoundHttpException('The requested page does not exist.');
        }
    }
}
