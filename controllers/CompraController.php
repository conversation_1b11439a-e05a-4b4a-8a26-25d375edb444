<?php
namespace app\controllers;

use app\models\Provider;
use app\models\Requisicion;
use app\models\SolicitudDetalle;
use app\models\Usuarios;
use Yii;
use yii\web\Controller;
use mPDF;

class CompraController extends Controller
{
    public function actionCompra($id, $puesto)
    {
        $requisicion = Requisicion::findOne($id);
        $iva_porcentaje = Yii::$app->params['IVA'];
        $dependencia = $requisicion->dependencia;
        $directivo = Usuarios::findBySql("select concat_ws(' ',nombre, primer_apellido, segundo_apellido) as nombre
                                              from usuarios u
                                              where material_servicio = '$puesto'")->one()->nombre;


        $negociador = $requisicion->negociador->nombre . ' ' . $requisicion->negociador->primer_apellido . ' ' . $requisicion->negociador->segundo_apellido;

        $proveedores = Provider::findBySql("select p.provider_id,p.name_comercial, p.rfc, sc.solicitud_id,
                                    df.calle_fiscal, df.num_ext_fiscal as numero_fiscal, df.num_int_fiscal as interior_fiscal, df.colonia_fiscal, df.cp_fiscal, c.nombre as ciudad, s.nombre as estado
                                    from solicitud_cotizacion sc
                                    join solicitud_detalle sd using(solicitud_id)
                                    join provider p on p.provider_id = sc.provider_id
                                    join provider.ubicacion df on df.provider_id = p.provider_id                          
                                    join cat_municipios c on c.municipio_id = df.city_fiscal
                                    join cat_entidades s on c.entidad_id = s.entidad_id
                                    where sd.ganador=true and sc.requisicion_id = :id and df.type_address_prov = 'DOMICILIO FISCAL'",[':id' => $id])->asArray()->all();

        $paths = array();
        if (!file_exists('ordenesCompra/' . $id)) {
            $this->actionCrear($id);
        }

        foreach ($proveedores as $proveedor) {
            $productos = SolicitudDetalle::findBySql("select  r.fecha_deseada, r.requisicion_id, rp.descripcion_corta, rp.cantidad, rp.um, sd.costo_unitario, sd.importe, p.iva
                                                    from solicitud_detalle sd
                                                    join requisicion_producto rp using(requisicion_producto_id)
                                                    join producto p using(producto_id)
                                                    join requisicion r using(requisicion_id)
                                                    where sd.solicitud_id = " . $proveedor['solicitud_id'] . " and sd.ganador = true")
                ->asArray()->all();

            $html = $this->renderPartial("compra", ['proveedor' => $proveedor, 'dependencia' => $dependencia,
                'productos' => $productos, 'negociador' => $negociador,
                'directivo' => $directivo, 'puesto' => $puesto,
                'iva_pocentaje' => $iva_porcentaje]);

            $pathFile = "ordenesCompra/" . "$id/" . $proveedor['provider_id'].'.pdf';
            $mpdf = @new mPDF();
            @$mpdf->allow_output_buffering = true;
            @$mpdf->writeHtml($html);
            @$mpdf->Output($pathFile, 'F');

            array_push($paths, $pathFile);
        }

        if ($paths) {
            Yii::$app->session->setFlash('success', 'Se han generado las ordenes de compra.');
        } else {
            $requisicion = Requisicion::findOne($id);
            $requisicion->status = ($puesto == 'Director material') ? Requisicion::VALIDAR_DIRECTOR_MATERIAL :
                (($puesto == 'Director servicio')?Requisicion::VALIDAR_DIRECTOR_SERVICIO:Requisicion::VALIDAR_DIRECTOR_GENERAL);
            $requisicion->update();
            Yii::$app->session->setFlash('error', 'Error al generarse las ordenes de compra.');
        }
        return Yii::$app->getResponse()->redirect(['../requisicion/index-director']);

    }

    public function actionCrear($id)
    {
        mkdir('ordenesCompra/' . $id, 0777, true);
    }

    public function actionBorrar()
    {

        if (!is_dir($dirPath = "ordenesCompra")) {
            throw new InvalidArgumentException("$dirPath must be a directory");
        }
        if (substr($dirPath, strlen($dirPath) - 1, 1) != '/') {
            $dirPath .= '/';
        }
        $files = glob($dirPath . '*', GLOB_MARK);
        foreach ($files as $file) {
            if (is_dir($file)) {
                self::deleteDir($file);
            } else {
                unlink($file);
            }
        }
        rmdir($dirPath);
        exit;

    }
}