<?php

/*
 * This file is part of the Dektrium project.
 *
 * (c) Dektrium project <http://github.com/dektrium/>
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace app\controllers;

use app\helpers\GeneralController;
use app\models\CatEntidades;
use app\models\CatSubespecialidades;
use app\models\ErrorInesperado;
use app\models\Historico;
use app\models\Lista69b;
use app\models\PadronProveedoresSireSiet;
use app\models\Provider;
use app\models\SubespecialidadCategory;
use app\models\Usuarios;
use moonland\phpexcel\Excel;
use mPDF;
use Yii;
use app\models\RecoveryForm;
use app\models\Token;
use app\traits\AjaxValidationTrait;
use app\traits\EventTrait;
use yii\data\SqlDataProvider;
use yii\db\Expression;
use yii\filters\AccessControl;
use yii\helpers\ArrayHelper;
use yii\web\Controller;
use yii\web\NotFoundHttpException;
use app\models\ConcursosSearch;

/**
 * RecoveryController manages password recovery process.
 *
 *
 * <AUTHOR> <<EMAIL>>
 */
class ReportsController extends GeneralController
{

    public function actionValidated()
    {
        $this->layout = 'home';
        $report = new \app\reports\Validated();
        $report->run();
        return $this->render('validated',array(
            "report"=>$report
        ));

    }

    public function actionExport(){
        $report = new \app\reports\Validated();
        $report->run();
        return $this->render('export',array(
            "report"=>$report
        ));
    }

    public function actionSiet($periodo=null){

        if(in_array('REPORTE SIET',GeneralController::getResponsabilidades()) || Yii::$app->user->can(Usuarios::ROLE_ADMIN_PROVIDER)){
            $this->layout = 'home';
            $periodsArray = $this->apiPeriodos();
            # A partir del Julio del 2022 es cuando comienzan a tener vigencia los certificados
            # La API envia a partir del Enero del 2020, por este motivo se quitan los primeros 30
            $periodsArray = array_slice($periodsArray, 30);

            $periodo = in_array($periodo, $periodsArray) ? $periodo : null;            

            $dataSiet = $this->dataSietValidados($periodo);
            return $this->render('siet',[
                'data' => $dataSiet,
                'periodos' => array_reverse($periodsArray,true)
            ]);
        }

        return $this->goHome();

    }
    /**
     * se obtiene la coleccción de datos de los proveedores activos
     * para su posterior descarga
     * @param string $periodo
     * <AUTHOR> González Estrada
     * @since  29/01/24
    */
    public function dataSietDownload($periodo=null){
        if($periodo==null){
            $periodo = date('Y-m');
            $exp = explode('-',$periodo);
            $namePer = self::namePeriodo($exp[1]);
            $periodo = $namePer.'-'.$exp[0];
        }

        #obtener las fechas de un periodo determinado, ej Enero-2025
        $fechasSearch = self::compareMesAnio($periodo);

        $fechaInicio = $fechasSearch['fi'];
        $fechaFin = $fechasSearch['ff'];

        $query =  Yii::$app->db->createCommand("
            select
            date_part('year', '$fechaFin'::date) AS  \"Ejercicio\",
            to_char(date_trunc('month', '$fechaFin'::date), 'DD/MM/YYYY') AS \"Fecha de inicio del periodo que se informa\",
            to_char((date_trunc('month', '$fechaInicio'::date) + INTERVAL '1 month') - INTERVAL '1 day', 'DD/MM/YYYY') AS \"Fecha de término del periodo que se informa\",
            p.tipo_persona AS \"Personería Jurídica del proveedor o contratista (catálogo)\",
            p.pf_nombre AS \"Nombre(s) del proveedor o contratista\",
            p.pf_ap_paterno AS \"Primer apellido del proveedor o contratista\",
            p.pf_ap_materno AS \"Segundo apellido del proveedor o contratista\",
            p.name_razon_social AS \"Denominación o razón social del proveedor o contratista\",
            p.estratificacion AS \"Estratificación\",
            CASE WHEN p.pais_id = 165 THEN 'Nacional'
                WHEN p.pais_id IS NULL THEN ''
                ELSE 'Internacional'
            END AS \"Origen del proveedor o contratista (catálogo)\",
            cp.pais_nombre AS \"País de origen, si la empresa es una filial extranjera\",
            p.rfc AS \"RFC de la persona física o moral con homoclave incluida\",
            ent.name_siet AS \"Entidad federativa de la persona física o moral (catálogo)\",
            'NO' AS \"Realiza subcontrataciones (catálogo)\",
            pg.giro AS \"Actividad económica de la empresa\",
            vl.name_siet AS \"Domicilio fiscal: Tipo de vialidad (catálogo)\",
            ubc.calle_fiscal as \"Domicilio fiscal: Nombre de la vialidad\",
            ubc.num_ext_fiscal AS \"Domicilio fiscal: Número exterior\",
            ubc.num_int_fiscal AS \"Domicilio fiscal: Número interior, en su caso\",
            tase.name_siet AS \"Domicilio fiscal: Tipo de asentamiento (catálogo)\",
            ase.nombre AS \"Domicilio fiscal: Nombre del asentamiento\",
            '1' AS \"Domicilio fiscal: Clave de la localidad\",
            mun.nombre AS \"Domicilio fiscal: Nombre de la localidad\",
            mun.cv_municipio::integer AS \"Domicilio fiscal: Clave del municipio\",
            mun.nombre AS \"Domicilio fiscal: Nombre del municipio o delegación\",
            ent.entidad_id AS \"Domicilio fiscal: Clave de la Entidad Federativa\",
            ent.name_siet AS \"Domicilio fiscal: Entidad Federativa (catálogo)\",
            ubc.cp_fiscal AS \"Domicilio fiscal: Código postal\",
            '' AS \"País del domicilio en el extranjero, en su caso\",
            '' AS \"Ciudad del domicilio en el extranjero, en su caso\",
            '' AS \"Calle del domicilio en el extranjero, en su casa\",
            '' AS \"Número del domicilio en el extranjero, en su caso\",
            CASE 
                WHEN repl.nombre != '' THEN repl.nombre 
                ELSE '' 
            END AS \"Nombre(s) del representante legal de la empresa\",
            CASE 
                WHEN repl.ap_paterno != '' THEN repl.ap_paterno 
                ELSE '' 
            END AS \"Primer apellido del representante legal de la empresa\",
            CASE 
                WHEN repl.ap_materno != '' THEN repl.ap_materno 
                ELSE '' 
            END AS \"Segundo apellido del representante legal de la empresa\",
            CASE 
                WHEN repl.telefono != '' THEN repl.telefono 
                ELSE '' 
            END AS \"Teléfono de contacto representante legal de la empresa\",
                    CASE 
                WHEN repl.correo != '' THEN repl.correo 
                ELSE '' 
            END AS \"Correo electrónico representante legal, en su caso\",
            CASE 
                WHEN p.tipo_persona = 'Persona moral' THEN 'PODER' 
                ELSE 'INE' 
            END AS \"Tipo de acreditación legal representante legal\",
            CASE 
                WHEN p.pagina_web != '' AND p.protocolo != '' THEN concat_ws('', p.protocolo, p.pagina_web) 
                WHEN p.pagina_web != '' THEN concat_ws('', 'http://', p.pagina_web) 
                ELSE '' 
            END AS \"Página web del proveedor o contratista\",
            ubc.telefono AS \"Teléfono oficial del proveedor o contratista\",
            lower(p.email) AS \"Correo electrónico comercial del proveedor o contratista\",
            'https://proveedores.nl.gob.mx/' AS \"Hipervínculo Registro Proveedores Contratistas, en su caso\",
            'https://secop.nl.gob.mx/prov_sancionados.html' AS \"hiper_sancionados\",
                'Dirección de Administración' AS \"área(s) responsable(s) que genera(n), posee(n), publica(n) y actualizan la información\",
            '$fechaFin'::date AS \"Fecha de validación\",
            '$fechaFin'::date AS \"Fecha de actualización\",
            'Lo relativo a si el proveedor realiza subcontrataciones se ha reportado NO, sin embargo en términos del artículo 49 de la Ley de Adquisiciones, Arrendamientos y Contratación de Servicios del Estado de Nuevo León, La subcontratación puede proceder si la convocatoria lo permite y quienes deseen usar esta modalidad lo incluyen en su propuesta y presentan una justificación por escrito en la que fundamenten la imposibilidad de solventar una propuesta sin realizar una subcontratación' AS \"Nota\",
                CASE 
                WHEN p.tipo_persona = 'Persona física' THEN curp.curp 
                ELSE '' 
            END AS \"CURP\",
            CASE 
                WHEN curp.curp != '' AND p.tipo_persona = 'Persona física' THEN (
                    CASE
                        WHEN SUBSTRING(curp.curp FROM 11 FOR 1) = 'H' THEN 'Hombre'
                        WHEN SUBSTRING(curp.curp FROM 11 FOR 1) = 'M' THEN 'Mujer'
                        ELSE 'Género no especificado'
                    END
                ) 
                ELSE '' 
                    END AS \"Sexo\"
        from public.provider as p inner join             
            (
            /*
            Se usó subconsulta ya que la tabla de certificicados tienen multiples
            y se debe de traer el más actualizado, así mismo el where dentro de la subconsulta
            para mantener la integridad de los datos
            */
            select distinct on (provider_id) provider_id, created_at, tipo, provider_type
            from public.historico_certificados as h
            where
            
                h.tipo in ('CERTIFICADO', 'ACTUALIZACION')
                and h.provider_type = 'bys' 
                and (h.vigencia >= '$fechaInicio' or h.vigencia_prorroga >= '$fechaInicio')
                and date(h.created_at) <= '$fechaFin'
                -- Se excluye el proveedor de prueba
                and h.provider_id  not in (6131)
            order by provider_id, created_at desc) 
            as hc on p.provider_id=hc.provider_id
        left join provider.curp as curp ON curp.provider_id = p.provider_id
        left join provider.representante_legal as repl ON repl.provider_id = p.provider_id AND repl.rep_bys IS TRUE AND repl.activo IS TRUE
        left join (
			select max(ubicacion_id),
                provider_id,
                vialidad_id,
                calle_fiscal,
                state_fiscal,city_fiscal,
                localidad_id,
                colonia_fiscal,
                num_ext_fiscal,
                num_int_fiscal,
                cp_fiscal,
                telefono
			from provider.ubicacion 
		    where type_address_prov = 'DOMICILIO FISCAL' and activo = true
			group by
                provider_id,
                calle_fiscal,
                vialidad_id,
                state_fiscal,
                city_fiscal ,
                localidad_id,
                colonia_fiscal,
                num_ext_fiscal,
                num_int_fiscal,
                cp_fiscal,
			telefono ) as ubc on  ubc.provider_id = p.provider_id 
        left join public.cat_vialidad vl ON vl.vialidad_id = ubc.vialidad_id
        left join public.cat_paises as cp ON cp.pais_id = p.pais_id
        left join public.cat_entidades as ent on ent.entidad_id = ubc.state_fiscal
        left join public.cat_municipios as mun on mun.municipio_id = ubc.city_fiscal 
        left join public.cat_localidades as loc on loc.localidad_id = ubc.localidad_id
        left join public.cat_asentamientos as ase on ase.asentamiento_id = ubc.colonia_fiscal
        left join public.cat_tipo_asentamientos as tase ON tase.tipo_asentamiento_id = ase.tipo_asentamiento_id
        -- no se usa el join y duplica datos
        -- left join provider.asignacion as asg on asg.id_proveedor = p.provider_id and asg.activo = true
        left join (SELECT STRING_AGG(DISTINCT 
                CASE 
                    WHEN pg.catalogo = 'CAT2' AND pg.producto_id < 630 THEN psg.concepto
                    ELSE cla.descripcion
                END, ', ') giro,pg.provider_id
            FROM provider_giro pg
            left join productos_servicios.concepto_linea psl ON psl.concepto_linea_id = pg.producto_id
            left join productos_servicios.concepto_grupo psg ON psg.concepto_grupo_id = psl.concepto_grupo_id
            left join productos.producto prod ON prod.producto_id = pg.producto_id
            left join productos.clase cla ON cla.clase_id = prod.clase_id
                group by pg.provider_id
            ) pg on pg.provider_id = p.provider_id

        where
        p.provider_id not in (
            select proveedor_id from public.sanciones where f_fin >= '$fechaInicio'::date
        )"
    );
        return $query->queryAll();
    }

    /**
     * se obtiene el listado de los proveedores activos. Actualmente los
     * proveedores activos son los que tienen un certificado vigente.
     * 
     * @param string $periodo
     * <AUTHOR> González Estrada
     * @since  29/01/24
    */

    public function dataSietValidados($periodo)
    {
        
        if($periodo==null){
            $periodo = date('Y-m');
            $exp = explode('-',$periodo);
            $namePer = self::namePeriodo($exp[1]);
            $periodo = $namePer.'-'.$exp[0];
        }

        #obtener las fechas de un periodo determinado
        $fechasSearch = self::compareMesAnio($periodo);

        $fechaInicio = $fechasSearch['fi'];
        $fechaFin = $fechasSearch['ff'];

        $query =  ['sql' => "
        select 
        date_part('year', '$fechaFin'::date) AS  \"Ejercicio\",
        to_char(date_trunc('month', '$fechaFin'::date), 'DD/MM/YYYY') AS \"Fecha de inicio del periodo que se informa\",
        to_char((date_trunc('month', '$fechaInicio'::date) + INTERVAL '1 month') - INTERVAL '1 day', 'DD/MM/YYYY') AS \"Fecha de término del periodo que se informa\",
        p.rfc as \"RFC de la persona física o moral con homoclave incluida\",
        p.name_razon_social AS \"Denominación o razón social del proveedor o contratista\"
        from  public.provider as p inner join 
        -- Consulta el último certificado emitido por proveedor 
        (select distinct on (provider_id) provider_id, created_at, tipo, provider_type, vigencia
        from public.historico_certificados as h 
        where
        h.tipo in ('CERTIFICADO', 'ACTUALIZACION' )
        and h.provider_type = 'bys'
        and (h.vigencia >= '$fechaInicio' or h.vigencia_prorroga >= '$fechaInicio')
        and date(h.created_at) <= '$fechaFin'
        -- Se excluye el proveedor de prueba
        and h.provider_id  not in (6131)
        order by provider_id, created_at desc) 
        as hc on p.provider_id=hc.provider_id
        where
        p.provider_id not in (
            select proveedor_id from public.sanciones where f_fin >= '$fechaInicio'::date
        )"
    ];
        $dataProvider = new SqlDataProvider($query);

        return $dataProvider;
    }

    public function disabled(){
        return json_encode(['Error' => 'No Disponible']);
    }


    public function apiPeriodos(){

        //local
        //$url = 'siet.nl/configuracion/api-periodos';

        //prod
        $url = 'https://transparencia.nl.gob.mx/ApiSIET/api/configuracion/periodos';
        $curl = curl_init();
        curl_setopt($curl, CURLOPT_URL, $url);
        curl_setopt($curl, CURLOPT_HEADER, false);
        curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, FALSE);
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($curl, CURLOPT_ENCODING, "");
        $curlData = curl_exec($curl);
        curl_close($curl);

        $data = json_decode($curlData,true);

        return $data['data'];
    }

    public function actionPeriodos(){

        //local
        //$url = 'siet.nl/configuracion/api-periodos';

        //prod
        $url = 'https://transparencia.nl.gob.mx/ApiSIET/api/configuracion/periodos';
        $curl = curl_init();
        curl_setopt($curl, CURLOPT_URL, $url);
        curl_setopt($curl, CURLOPT_HEADER, false);
        curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, FALSE);
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($curl, CURLOPT_ENCODING, "");
        $curlData = curl_exec($curl);
        curl_close($curl);

        $data = json_decode($curlData,true);

        return $data['data'];
    }

    public function apiSheetExcel(){

        //local
        //$url = 'siet.nl/configuracion/api-data-excel';

        //prod
        $url = 'https://transparencia.nl.gob.mx/ApiSIET/api/configuracion/data-excel';
        $curl = curl_init();
        curl_setopt($curl, CURLOPT_URL, $url);
        curl_setopt($curl, CURLOPT_HEADER, false);
        curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, FALSE);
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($curl, CURLOPT_ENCODING, "");
        $curlData = curl_exec($curl);
        curl_close($curl);

        $data = json_decode($curlData,true);

        return $data;
    }


    public function actionDownloadExcel($periodo=null)
    {
        $dataApi = self::apiSheetExcel();
        $idFormato = $dataApi['idFormato'];
        $info = $dataApi['info'];

        $nombreFor = $dataApi['nombre'];
        $tituloFor = $dataApi['titulo'];
        $descFor = $dataApi['descripcion'];
        $camposFor = $dataApi['campos'];
        $countCampos = $dataApi ['totalItems'];

        $dataProvider = self::dataSietDownload($periodo);

        $ejer = $dataProvider[0]['Ejercicio'];
        $fini = $dataProvider[0]['Fecha de inicio del periodo que se informa'];
        $ffin = $dataProvider[0]['Fecha de término del periodo que se informa'];
        $fval = $dataProvider[0]['Fecha de validación'];
        $fact = $dataProvider[0]['Fecha de actualización'];

        PadronProveedoresSireSiet::updateAll(['ejercicio' => $ejer,'fecha_inicio_periodo_informa' => $fini,'fecha_termino_periodo_informa' => $ffin
            ,'fecha_validacion' => $fval, 'fecha_actualizacion' => $fact]);


        /*
        $dataSire = Yii::$app->db->createCommand('
        select  
                ejercicio as "Ejercicio",
                fecha_inicio_periodo_informa as "Fecha de inicio del periodo que se informa",
                fecha_termino_periodo_informa as "Fecha de término del periodo que se informa",
                personeria_juridica_proveedor_contratista_catalogo as "Personería Jurídica del proveedor o contratista (catálogo)",
                nombre_proveedor_contratista  as "Nombre(s) del proveedor o contratista",
                primer_apellido_proveedor_contratista as "Primer apellido del proveedor o contratista",
                segundo_apellido_proveedor_contratista as "Segundo apellido del proveedor o contratista",
                denominacion_razon_social_proveedor_contratista as "Denominación o razón social del proveedor o contratista",
                a.estratificacion as "Estratificación",
                origen_proveedor_contratista_catalogo as "Origen del proveedor o contratista (catálogo)",
                pais_origen_si_empresa_es_una_filial_extranjera as "País de origen, si la empresa es una filial extranjera",
                rfc_persona_fisica_moral_con_homoclave_incluida as "RFC de la persona física o moral con homoclave incluida",
                entidad_federativa_persona_fisica_moral_catalogo as "Entidad federativa de la persona física o moral (catálogo)",
                realiza_subcontrataciones_catalogo as "Realiza subcontrataciones (catálogo)",
                actividad_economica_empresa as "Actividad económica de la empresa",
                domicilio_fiscal_tipo_vialidad_catalogo as "Domicilio fiscal: Tipo de vialidad (catálogo)",
                domicilio_fiscal_nombre_vialidad as "Domicilio fiscal: Nombre de la vialidad",
                domicilio_fiscal_numero_exterior as "Domicilio fiscal: Número exterior",
                domicilio_fiscal_numero_interiorsu_caso as "Domicilio fiscal: Número interior, en su caso",
                domicilio_fiscal_tipo_asentamiento_catalogo "Domicilio fiscal: Tipo de asentamiento (catálogo)",
                domicilio_fiscal_nombre_asentamiento as "Domicilio fiscal: Nombre del asentamiento",
                domicilio_fiscal_clave_localidad as "Domicilio fiscal: Clave de la localidad",
                domicilio_fiscal_nombre_localidad as "Domicilio fiscal: Nombre de la localidad",
                domicilio_fiscal_clave_municipio as "Domicilio fiscal: Clave del municipio",
                domicilio_fiscal_nombre_municipio_delegacion as "Domicilio fiscal: Nombre del municipio o delegación",
                domicilio_fiscal_clave_entidad_federativa as "Domicilio fiscal: Clave de la Entidad Federativa" , 
                domicilio_fiscal_entidad_federativa_catalogo as "Domicilio fiscal: Entidad Federativa (catálogo)" , 
                domicilio_fiscal_codigo_postal as "Domicilio fiscal: Código postal",
                pais_domicilio_el_extranjerosu_caso as "País del domicilio en el extranjero, en su caso",
                ciudad_domicilio_el_extranjerosu_caso as "Ciudad del domicilio en el extranjero, en su caso",
                calle_domicilio_el_extranjerosu_caso as "Calle del domicilio en el extranjero, en su caso",
                numero_domicilio_el_extranjerosu_caso as "Número del domicilio en el extranjero, en su caso",
                nombre_representante_legal_empresa  as "Nombre(s) del representante legal de la empresa",
                primer_apellido_representante_legal_empresa  as "Primer apellido del representante legal de la empresa",
                segundo_apellido_representante_legal_empresa  as "Segundo apellido del representante legal de la empresa",
                telefono_contacto_representante_legal_empresa  as "Teléfono de contacto representante legal de la empresa", 
                correo_electronico_representante_legalsu_caso  as "Correo electrónico representante legal, en su caso",
                tipo_acreditacion_legal_representante_legal as "Tipo de acreditación legal representante legal", 
                pagina_web_proveedor_contratista as "Página web del proveedor o contratista",
                telefono_oficial_proveedor_contratista as "Teléfono oficial del proveedor o contratista",
                correo_electronico_comercial_proveedor_contratista as "Correo electrónico comercial del proveedor o contratista",
                hipervinculo_registro_proveedores_contratistassu_c as "Hipervínculo Registro Proveedores Contratistas, en su caso", 
                hipervinculo_directorio_proveedores_contratistas_s as "hiper_sancionados",
                area_responsable_genera_posee_publica_actualizan_i as "area_responsable", 
                fecha_validacion as "Fecha de validación",
                fecha_actualizacion as "Fecha de actualización",
                case when pagina_web_proveedor_contratista is null then \'El proveedor no reporta página Web\' else \'\' end as "Nota"
                from padron_proveedores_sire_siet a left join provider p on p.rfc = a.rfc_persona_fisica_moral_con_homoclave_incluida where p.rfc is null  ')->queryAll();


        $dataProvider = array_merge($dataProviderSiet,$dataSire);
        */

        /*echo '<pre>';
        foreach ($info['formato']['Reporte de Formatos'] as $d){
            if ($d['tipoDato'] == 'Catálogo') {
                var_dump('"'.implode(',',  array_column($d['valor'], 'descripcion')).'"');
            }
        }

        exit();*/

        $abc = ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z',
            'AA', 'AB', 'AC', 'AD', 'AE', 'AF', 'AG', 'AH', 'AI', 'AJ', 'AK', 'AL', 'AM', 'AN', 'AO', 'AP', 'AQ', 'AR', 'AS', 'AT', 'AU', 'AV', 'AW', 'AX', 'AY', 'AZ', 'BA', 'BB',
            'BC', 'BD', 'BE', 'BF', 'BG', 'BH', 'BI', 'BJ', 'BK', 'BL', 'BM', 'BN', 'BO', 'BP', 'BQ', 'BR', 'BS', 'BT', 'BU', 'BV', 'BW', 'BX', 'BY', 'BZ'];

        header('Content-Type: application/vnd.ms-excel');
        header('Content-Disposition: attachment;filename=' . $nombreFor . '.xlsx');
        header('Cache-Control: max-age=0');

        $objPHPExcel = new \PHPExcel();
        $objPHPExcel->getProperties()->setCreator("Gobierno de Nuevo Léon");
        $objPHPExcel->getProperties()->setLastModifiedBy("Gobierno de Nuevo Léon");
        $objPHPExcel->getProperties()->setTitle($nombreFor);
        $objPHPExcel->getProperties()->setSubject($nombreFor);

        $arrSheet = ['hidden','formato','tablas'];


        $style = array('alignment' => array('horizontal' => \PHPExcel_Style_Alignment::HORIZONTAL_CENTER), 'fill' =>
            array('type' => \PHPExcel_Style_Fill::FILL_SOLID, 'color' =>
                array('rgb' => '292929')
            ), 'font' => array(
            'bold' => true,
            'color' => array('rgb' => 'FFFFFF'),
            'name' => 'Arial'
        ), 'borders' => array('allborders' => array(
            'style' => \PHPExcel_Style_Border::BORDER_THIN,
            'color' => array('rgb' => '000000')
        ))
        );

        $styleColumn = [
            'fill' =>
                ['type' => \PHPExcel_Style_Fill::FILL_SOLID, 'color' =>
                    ['rgb' => 'D8D8D8']
                ], 'borders' => ['allborders' => [
                'style' => \PHPExcel_Style_Border::BORDER_THIN,
                'color' => ['rgb' => '000000']
            ]]
        ];

        $styleLeft= ['alignment' => ['horizontal' => \PHPExcel_Style_Alignment::HORIZONTAL_LEFT]
        ];

        $objPHPExcel->createSheet()->setTitle('Reporte de Formatos');

        $hiddenFor = 1;
        $hiddenTab = 1;
        foreach ($arrSheet as $aS) {
            if (isset($info[$aS]) && !empty($info[$aS])) {
                foreach ($info[$aS] as $index => $dataTipoSheet) {

                    if ($aS == 'hidden' || $aS == 'formato') {
                        if($index != 'Reporte de Formatos'){
                            $objPHPExcel->createSheet()->setTitle($index);
                        }else{
                            $curp =[ "idCampo" => 1000002, "etiqueta" => "CURP", "tipoDato" => "Texto corto", "idTipoDato" => 1, "requerido" => false, "posicion" => 43, "longitudCampo" => ["longitudMaxTxt" => 20, "caracteresEspeciales" => true, "alfaNum" => true ],  "valor" => null];
                            $sexo_campo =[ "idCampo" => 1000001, "etiqueta" => "Sexo", "tipoDato" => "Texto corto", "idTipoDato" => 1, "requerido" => false, "posicion" => 44, "longitudCampo" => ["longitudMaxTxt" => 20, "caracteresEspeciales" => true, "alfaNum" => true ],  "valor" => null];
                            array_push($dataTipoSheet, $curp, $sexo_campo);
                        }
                        $objPHPExcel->setActiveSheetIndexByName($index);
                        $fila = $aS == 'formato' ? 7 : ($aS == 'hidden' ? 1 : 3);

                        if ($aS == 'formato') {

                            $objPHPExcel->getActiveSheet()->setCellValue('A1', $idFormato)->getStyle('A1')->applyFromArray($styleLeft);
                            $objPHPExcel->getActiveSheet()->getRowDimension(1)->setCollapsed(true);
                            $objPHPExcel->getActiveSheet()->getRowDimension(1)->setVisible(false);
                            $objPHPExcel->getActiveSheet()->setCellValue('A2', 'TÍTULO');
                            $objPHPExcel->getActiveSheet()->mergeCells('A2:C2')->getStyle('A2:C2')->applyFromArray($style);


                            $objPHPExcel->getActiveSheet()->setCellValue('A3', $tituloFor);
                            $objPHPExcel->getActiveSheet()->mergeCells('A3:C3')->getStyle('A3:C3')->applyFromArray($styleColumn);

                            $objPHPExcel->getActiveSheet()->setCellValue('D3', $nombreFor);
                            $objPHPExcel->getActiveSheet()->mergeCells('D3:F3')->getStyle('D3:F3')->applyFromArray($styleColumn);

                            $objPHPExcel->getActiveSheet()->setCellValue('G3', $descFor);
                            $objPHPExcel->getActiveSheet()->mergeCells('G3:I3')->getStyle('G3:I3')->applyFromArray($styleColumn);



                            foreach ($camposFor as $cTdNc =>  $tDNc){
                                $objPHPExcel->getActiveSheet()->setCellValue($abc[$cTdNc].'4', $tDNc['idTipoDato'])->getStyle($abc[$cTdNc].'4', $tDNc['idTipoDato'])->applyFromArray($styleLeft);
                                $objPHPExcel->getActiveSheet()->setCellValue($abc[$cTdNc].'5', $tDNc['idCampo'])->getStyle($abc[$cTdNc].'5', $tDNc['idTipoDato'])->applyFromArray($styleLeft);
                            }

                            $objPHPExcel->getActiveSheet()->getRowDimension(4)->setCollapsed(true);
                            $objPHPExcel->getActiveSheet()->getRowDimension(4)->setVisible(false);

                            $objPHPExcel->getActiveSheet()->getRowDimension(5)->setCollapsed(true);
                            $objPHPExcel->getActiveSheet()->getRowDimension(5)->setVisible(false);


                            $objPHPExcel->getActiveSheet()->setCellValue('D2', 'NOMBRE CORTO');
                            $objPHPExcel->getActiveSheet()->mergeCells('D2:F2')->getStyle('D2:F2')->applyFromArray($style);

                            $objPHPExcel->getActiveSheet()->setCellValue('G2', 'DESCRIPCIÓN');
                            $objPHPExcel->getActiveSheet()->mergeCells('G2:I2')->getStyle('G2:I2')->applyFromArray($style);

                            $objPHPExcel->getActiveSheet()->setCellValue('A6', 'Tabla Campos');
                            $objPHPExcel->getActiveSheet()->mergeCells('A6:'.$abc[$countCampos-1].'6')->getStyle('A6:'.$abc[$countCampos].'6')->applyFromArray($style);

                            $objPHPExcel->getActiveSheet()->getRowDimension(1)->setCollapsed(true);
                            $objPHPExcel->getActiveSheet()->getRowDimension(4)->setCollapsed(true);
                            $objPHPExcel->getActiveSheet()->getRowDimension(5)->setCollapsed(true);

                        }else if ($aS == 'hidden') {
                            $objPHPExcel->getActiveSheet()->getRowDimension(1)->setCollapsed(true);
                            $objPHPExcel->getActiveSheet()->getRowDimension(2)->setCollapsed(true);
                        }
                        foreach ($dataTipoSheet as $i => $col) {
                            if ($aS == 'hidden' || $aS == 'formato') {
                                if ($aS != 'hidden') {
                                    $conText = '';
                                    if($col['tipoDato'] == 'Tabla'){
                                        $conText = ' Tabla_'.$col['idCampo'];
                                    }
                                    $objPHPExcel->getActiveSheet()->setCellValue($abc[$i] . $fila, trim($col['etiqueta'].$conText));
                                    $objPHPExcel->getActiveSheet()->getColumnDimension($abc[$i])->setAutoSize(true);
                                    $objPHPExcel->getActiveSheet()->getStyle($abc[$i] . $fila)->applyFromArray($styleColumn);

                                    //->getFill()->setFillType(\PHPExcel_Style_Fill::FILL_SOLID)->getStartColor()->setRGB('D8D8D8');

                                    $etiqueta = $col['etiqueta'] == 'Área(s) responsable(s) que genera(n), posee(n), publica(n) y actualizan la información'?'area_responsable':
                                        ($col['etiqueta'] == 'Hipervínculo al Directorio de Proveedores y Contratistas Sancionados'?'hiper_sancionados':$col['etiqueta']);

                                    //if(!empty($dataProvider[0][$etiqueta])){

                                        $dataCol = ArrayHelper::getColumn($dataProvider,$etiqueta);

                                        $row = 8;
                                        for ($x = 0,$xcount = count($dataCol);$x < $xcount; $x++) {
                                            if($abc[$i]=='I')//homologa estratificación a mayusculas
                                                $dataCol[$x] = strtoupper($dataCol[$x]);
                                            if($abc[$i]=='AM'){
                                                $dataCol[$x] = str_replace( ' ','',$dataCol[$x]);
                                                $dataCol[$x] = str_replace('http://http://','http://',str_replace('https://https://','https://',$dataCol[$x]));
                                                $dataCol[$x] = strtolower(str_replace('http://https://','https://',str_replace('https://http://','http://',$dataCol[$x])));
                                            } // si el campo es la pagina web (columna AM) reemplaza  http(s)

                                            $objPHPExcel->getActiveSheet()->SetCellValue($abc[$i].$row, $dataCol[$x]);
                                            $row++;

                                        }
                                    //}
                                    if ($col['tipoDato'] == 'Catálogo') {
                                        $xCat = $aS == 'hidden'?4:8;
                                        $counCat = count(array_column($col['valor'], 'descripcion'));
                                        $objPHPExcel->addNamedRange(
                                            new \PHPExcel_NamedRange(
                                                'Hidden_'.$hiddenFor,
                                                $objPHPExcel->getSheetByName('Hidden_'.$hiddenFor),
                                                'A1:A'.$counCat
                                            )
                                        );
                                        for ($x = $xCat; $x <= 1000; $x++) {

                                            $objValidation = $objPHPExcel->getActiveSheet()->getCell($abc[$i] . '' . $x)->getDataValidation();
                                            $objValidation->setType(\PHPExcel_Cell_DataValidation::TYPE_LIST);

                                            $objValidation->setAllowBlank(false);
                                            $objValidation->setShowInputMessage(true);
                                            $objValidation->setShowDropDown(true);
                                            $objValidation->setErrorTitle('Input error');
                                            $objValidation->setError('El valor no esta en la lista.');
                                            $objValidation->setPromptTitle('Lista de opciones');
                                            $objValidation->setPrompt('Selecciona una opcion.');
                                            $objValidation->setFormula1('=Hidden_'.$hiddenFor);

                                        }
                                        $hiddenFor++;
                                    }

                                } else {
                                    $fila = 1;
                                    $hoja = array_column($dataTipoSheet['valor'], 'descripcion');
                                    foreach ($hoja as $campo) {
                                        $objPHPExcel->getActiveSheet()->setCellValue('A' . $fila, $campo);
                                        next($hoja);
                                        $fila++;
                                    }
                                    $objPHPExcel->addNamedRange(new \PHPExcel_NamedRange($index, $objPHPExcel->getSheetByName($index), 'A1:A' . count($hoja)));
                                }
                            }
                        }
                    }else{
                        $nameSheet = $dataTipoSheet['tablaName'];

                        if (isset($dataTipoSheet['datos']['hidden']) && !empty($dataTipoSheet['datos']['hidden'])){
                            foreach ($dataTipoSheet['datos']['hidden'] as $i => $dat) {

                                $objPHPExcel->createSheet()->setTitle($i);
                                $objPHPExcel->setActiveSheetIndexByName($i);
                                $fila = 1;
                                $hoja = array_column($dat['valor'], 'descripcion');
                                foreach ($hoja as $campo) {
                                    $objPHPExcel->getActiveSheet()->setCellValue('A' . $fila, $campo);
                                    next($hoja);
                                    $fila++;
                                }
                                $objPHPExcel->addNamedRange(new \PHPExcel_NamedRange($i, $objPHPExcel->getSheetByName($i), 'A1:A' . count($hoja)));


                            }
                        }

                        if ($dataTipoSheet['datos']['datos']){

                            $objPHPExcel->createSheet()->setTitle($nameSheet);
                            $objPHPExcel->setActiveSheetIndexByName($nameSheet);

                            $fila = 3;

                            foreach ($dataTipoSheet['datos']['datos'] as $cTdNc =>  $tDNc){
                                $objPHPExcel->getActiveSheet()->setCellValue($abc[$cTdNc+1].'1', $tDNc['idTipoDato'])->getStyle($abc[$cTdNc].'1', $tDNc['idTipoDato'])->applyFromArray($styleLeft);
                                $objPHPExcel->getActiveSheet()->setCellValue($abc[$cTdNc+1].'2', $tDNc['idCampo'])->getStyle($abc[$cTdNc].'2', $tDNc['idTipoDato'])->applyFromArray($styleLeft);
                            }


                            $objPHPExcel->getActiveSheet()->getRowDimension(1)->setCollapsed(true);
                            $objPHPExcel->getActiveSheet()->getRowDimension(1)->setVisible(false);

                            $objPHPExcel->getActiveSheet()->getRowDimension(2)->setCollapsed(true);
                            $objPHPExcel->getActiveSheet()->getRowDimension(2)->setVisible(false);

                            $objPHPExcel->getActiveSheet()->setCellValue($abc[0] . $fila, 'ID');
                            $objPHPExcel->getActiveSheet()->getColumnDimension($abc[0])->setAutoSize(true);
                            $objPHPExcel->getActiveSheet()->getStyle($abc[0] . $fila)->applyFromArray($style);

                            foreach ($dataTipoSheet['datos']['datos'] as $i=> $col ){
                                $i++;
                                $objPHPExcel->getActiveSheet()->setCellValue($abc[$i] . $fila, trim($col['etiqueta']));
                                $objPHPExcel->getActiveSheet()->getColumnDimension($abc[$i])->setAutoSize(true);
                                $objPHPExcel->getActiveSheet()->getStyle($abc[$i] . $fila)->applyFromArray($style);


                                if ($col['tipoDato'] == 'Catálogo') {
                                    $counCat = count(array_column($col['valor'], 'descripcion'));
                                    $objPHPExcel->addNamedRange(
                                        new \PHPExcel_NamedRange(
                                            'Hidden_'.$hiddenTab.'_'.$nameSheet,
                                            $objPHPExcel->getSheetByName('Hidden_'.$hiddenTab.'_'.$nameSheet),
                                            'A1:A'.$counCat
                                        )
                                    );


                                    for ($x = 4; $x <= 100; $x++) {
                                        $objValidation = $objPHPExcel->getActiveSheet()->getCell($abc[$i] . '' . $x)->getDataValidation();
                                        $objValidation->setType(\PHPExcel_Cell_DataValidation::TYPE_LIST);
                                        $objValidation->setAllowBlank(false);
                                        $objValidation->setShowInputMessage(true);
                                        $objValidation->setShowDropDown(true);
                                        $objValidation->setErrorTitle('Input error');
                                        $objValidation->setError('El valor no esta en la lista.');
                                        $objValidation->setPromptTitle('Lista de opciones');
                                        $objValidation->setPrompt('Selecciona una opcion.');
                                        $objValidation->setFormula1('=Hidden_'.$hiddenTab.'_'.$nameSheet);

                                        //'"Persona física,Persona moral"'

                                    }
                                    $hiddenTab++;
                                }
                            }

                        }
                    }

                }
            }
        }

        $objPHPExcel->removeSheetByIndex($objPHPExcel->getIndex(
            $objPHPExcel->getSheetByName('Worksheet')));
        $objPHPExcel->setActiveSheetIndexByName('Reporte de Formatos');

        ob_clean();
        $objWriter = \PHPExcel_IOFactory::createWriter($objPHPExcel, 'Excel2007');
        $objWriter->save('php://output');
        exit();
    }


    public static function compareMesAnio($mes = null)
    {

        $arr_mes = ['01' => 'Enero', '02' => 'Febrero', '03' => 'Marzo', '04' => 'Abril', '05' => 'Mayo', '06' => 'Junio', '07' => 'Julio', '08' => 'Agosto',
            '09' => 'Septiembre', '10' => 'Octubre', '11' => 'Noviembre', '12' => 'Diciembre'];

        $date = date('Y-m-d');
        $explode_mes = explode('-', $mes);
        $arr_flip = array_flip(self::namePeriodo());
        $fecha = $explode_mes[1] . '-' . $arr_flip[$explode_mes[0]] . '-01';

        $a_date = $fecha;
        $fFin = date("Y-m-t", strtotime($a_date));

        return ['fi' => $fecha, 'ff' => $fFin];
    }


    public function namePeriodo($mes=null){

        $arr_mes = ['01' => 'Enero', '02' => 'Febrero', '03' => 'Marzo', '04' => 'Abril', '05' => 'Mayo', '06' => 'Junio', '07' => 'Julio', '08' => 'Agosto',
            '09' => 'Septiembre', '10' => 'Octubre', '11' => 'Noviembre', '12' => 'Diciembre'];

        if($mes==null){
            return $arr_mes;
        }else{
            return $arr_mes[$mes];
        }
    }


    public function actionIndicadores(){
        return $this->render('indicadores');
    }




    public function actionDataexample(){
        $data = ['data'=>[rand(50,500),rand(50,500),rand(50,500)]];
        return json_encode($data);
    }


    public function actionAdmonreport($id=1){
        $admonLimit = '2021-10-04';
        $data = ['data'=>[]];
        if($id==1){//empresasRegistradas
            $info = Provider::findBySql("
                select sum(case when date(creation_date)<'$admonLimit' then 1 else 0 end) as b, count(1)as a ,
                sum(case when date(creation_date)>='$admonLimit' then 1 else 0 end) as c from provider 
                where (tipo_provider = 'op' and enabled is true) or provider_id in(select provider_id from provider.datos_validados where status_op ='VALIDADO' )
            ")->asArray()->one();

            $data['data'] = array_values($info);
        }elseif ($id==2){//ConstanciasDeRegistroExpedidas
            $info = Provider::findBySql("
                select sum(case when date(created_at)<'$admonLimit' then 1 else 0 end) as b, count(1)as a ,
                sum(case when date(created_at)>='$admonLimit' then 1 else 0 end) as c from historico_certificados 
                where provider_type = 'op' and tipo = 'CERTIFICADO' 
            ")->asArray()->one();

            $data['data'] = array_values($info);
        }elseif ($id==3){//VisitasRealizadas
            $info = Provider::findBySql("
                select sum(case when date(d.fecha_inspeccion)<'$admonLimit' then 1 else 0 end) as b, count(1)as a ,
                sum(case when date(d.fecha_inspeccion)>='$admonLimit' then 1 else 0 end) as c 
                from visit v inner join visit_details d on d.visit_id = v.visit_id
                where v.provider_type = 'op'
            ")->asArray()->one();

            $data['data'] = array_values($info);
        }elseif ($id==4){//Documentos revisados
            $info = Provider::findBySql("
                select sum(case when date(fecha)<'$admonLimit' then 1 else 0 end) as b,
                sum(case when date(fecha)>='$admonLimit' then 1 else 0 end) as c, count(1)as a
                from (
                select u.nombre as nombre,b.* from (
                select * from (
                select created_id as usuario, created_Date as fecha
                from provider.datos_validados where created_id in (
                select user_id from usuarios where role = 'VALIDADOR CONTRATISTAS'
                ) 
                ) a union(
                select created_id as usuario, created_Date as fecha
                from provider.status where created_id in (
                select user_id from usuarios where role = 'VALIDADOR CONTRATISTAS'
                ) 
                )) b  
                inner join usuarios u on u.user_id = b.usuario
                ) x

            ")->asArray()->one();

            $data['data'] = array_values($info);
        }
        return json_encode($data);
    }


    public function actionRevsreport($id=1){

        $data = ['data'=>[]];
        if($id==1){//DocumentosRevisadosPorPersona
            $info = Provider::findBySql("
                select nombre, count(1) from (
                select u.nombre as nombre,b.* from (
                select * from (
                select created_id as usuario, created_Date as fecha
                from provider.datos_validados where created_id in (
                select user_id from usuarios where role = 'VALIDADOR CONTRATISTAS'
                ) 
                ) a union(
                select created_id as usuario, created_Date as fecha
                from provider.status where created_id in (
                select user_id from usuarios where role = 'VALIDADOR CONTRATISTAS'
                ) 
                )) b  
                inner join usuarios u on u.user_id = b.usuario
                ) x group by nombre, usuario

            ")->asArray()->all();

            $data['labels'] = array_values(array_column($info,'nombre'));
            $data['data'] = array_values(array_column($info,'count'));
        }elseif ($id==2){//VisitasPorPersona
            $info = Provider::findBySql("
                select nombre, count(1) from (
                select v.created_by as usuario, u.nombre
                from visit v inner join visit_details d on d.visit_id = v.visit_id
                inner join usuarios u on u.user_id = v.created_by
                where v.provider_type = 'op'
                ) x group by nombre, usuario
            ")->asArray()->all();

            $data['labels'] = array_values(array_column($info,'nombre'));
            $data['data'] = array_values(array_column($info,'count'));
        }
        return json_encode($data);
    }


    public function actionZonanlreport($id=1,$z=0){
        $admonLimit = '2021-10-04';
        //Apodaca, Cadereyta Jiménez, El Carmen,
        //García, San Pedro Garza García, General
        //Escobedo, Guadalupe, Juárez, Monterrey,
        //Salinas Victoria, San Nicolás de los Garza,
        //Santa Catarina y Santiago.
        $zmm = [952,955,962,964,994,966,971,976,987,992,993,995,996];
        $cond1= $z ? ' u.city_fiscal in  ('.implode(',',$zmm).') ' : ' u.state_fiscal = 19 ';
        $cond2= $z ? ' u.city_fiscal not in  ('.implode(',',$zmm).') ' : ' u.state_fiscal != 19 ';
        $esNl=  $z ? ' and u.state_fiscal = 19 ' : ' ';


        $data = ['data'=>[]];
        if($id==1){//empresasPorZonaGeografica
            $info = Provider::findBySql("
                select sum(case when $cond1 then 1 else 0 end) as b,
                sum(case when $cond2 then 1 else 0 end) as c, count(1)as a
                from provider p inner join provider.ubicacion u on u.provider_id = p.provider_id 
                where p.tipo_provider = 'op' and u.tipo = 'DOMICILIO FISCAL'
                and u.activo is true and u.status_op='VALIDADO' $esNl
            ")->asArray()->one();

            $data['data'] = array_values($info);
        }elseif ($id==2){//ConstanciasDeRegistroExpedidas_zona
            $info = Provider::findBySql("
                select sum(case when $cond1 then 1 else 0 end) as b,
                sum(case when $cond2 then 1 else 0 end) as c, count(1)as a
                from historico_certificados c inner join provider p on p.provider_id = c.provider_id 
                inner join provider.ubicacion u on u.provider_id = p.provider_id 
                where c.provider_type = 'op' and c.tipo = 'CERTIFICADO'  and u.tipo = 'DOMICILIO FISCAL'
                and u.activo is true and u.status_op='VALIDADO' $esNl
            ")->asArray()->one();

            $data['data'] = array_values($info);
        }elseif ($id==3){//VisitasRealizadas_zona
            $info = Provider::findBySql("
                select sum(case when $cond1 then 1 else 0 end) as b,
                sum(case when $cond2 then 1 else 0 end) as c, count(1)as a
                from visit v inner join visit_details d on d.visit_id = v.visit_id
                inner join provider.ubicacion u on u.ubicacion_id = d.ubicacion_id
                where v.provider_type = 'op' $esNl
            ")->asArray()->one();

            $data['data'] = array_values($info);
        }
        return json_encode($data);
    }


    public function actionAdmonreport2($id=1){
        $admonLimit = '2021-10-04';
        $data = ['data'=>[]];
        if($id==1){//empresasRegistradas
            $info = Provider::findBySql("
                select sum(case when date(creation_date)<'$admonLimit' then 1 else 0 end) as b,
                sum(case when date(creation_date)>='$admonLimit' then 1 else 0 end) as c, count(1)as a  from provider 
                where (tipo_provider = 'op' and enabled is true) or provider_id in(select provider_id from provider.datos_validados where status_op ='VALIDADO' )
            ")->asArray()->one();

            $data['data'] = array_values($info);
        }elseif ($id==2){//ConstanciasDeRegistroExpedidas
            $info = Provider::findBySql("
                select sum(case when date(created_at)<'$admonLimit' then 1 else 0 end) as b ,
                sum(case when date(created_at)>='$admonLimit' then 1 else 0 end) as c, count(1)as a from historico_certificados 
                where historico_certificados_id in (select id  from (
                    select historico_certificadoS_id as id, ROW_NUMBER() over( partition by provider_id order by created_at desc) as total 
                    FROM historico_certificados where provider_type = 'op' and tipo = 'CERTIFICADO'
                    )x  where total = 1
                )
            ")->asArray()->one();

            $data['data'] = array_values($info);
        }elseif ($id==3){//VisitasRealizadas
            $info = Provider::findBySql("
                select sum(case when date(d.fecha_inspeccion)<'$admonLimit' then 1 else 0 end) as b ,
                sum(case when date(d.fecha_inspeccion)>='$admonLimit' then 1 else 0 end) as c , count(1)as a
                from visit v inner join visit_details d on d.visit_id = v.visit_id
                where v.provider_type = 'op'
            ")->asArray()->one();

            $data['data'] = array_values($info);
        }elseif ($id==4){//Documentos revisados
            $info = Provider::findBySql("
                select sum(case when date(fecha)<'$admonLimit' then 1 else 0 end) as b,
                sum(case when date(fecha)>='$admonLimit' then 1 else 0 end) as c, count(1)as a
                from (
                select u.nombre as nombre,b.* from (
                select * from (
                select created_id as usuario, created_Date as fecha
                from provider.datos_validados where created_id in (
                select user_id from usuarios where role = 'VALIDADOR CONTRATISTAS'
                ) 
                ) a union(
                select created_id as usuario, created_Date as fecha
                from provider.status where created_id in (
                select user_id from usuarios where role = 'VALIDADOR CONTRATISTAS'
                ) 
                )) b  
                inner join usuarios u on u.user_id = b.usuario
                ) x

            ")->asArray()->one();

            $data['data'] = array_values($info);
        }
        return json_encode($data);
    }


    public function actionRevsreport2($id=1){

        $data = ['data'=>[]];
        if($id==1){//DocumentosRevisadosPorPersona
            $info = Provider::findBySql("

                select 
                    nombre, count(1) 
                from (
                    select 
                    case when u.status = 'ACTIVO' then concat_ws(' ',nombre, primer_apellido, segundo_apellido) else 'OTRO' end as nombre
                    from (
                        select * 
                        from (
                        select 
                            created_id as usuario, created_Date as fecha
                        from 
                            provider.datos_validados 
                        where 
                            created_id in (
                                select user_id 
                                from usuarios 
                                where role = 'VALIDADOR CONTRATISTAS'
                            ) 
                        ) a union(
                        select 
                            created_id as usuario, created_Date as fecha
                            from 
                                provider.status 
                            where 
                                created_id in (
                                    select user_id 
                                    from usuarios 
                                    where role = 'VALIDADOR CONTRATISTAS'
                                ) 
                            )
                        ) b  
                    inner join 
                        usuarios u on u.user_id = b.usuario
                ) x group by nombre

            ")->asArray()->all();

            $data['labels'] = array_values(array_column($info,'nombre'));
            $data['data'] = array_values(array_column($info,'count'));
        }elseif ($id==2){//VisitasPorPersona
            $info = Provider::findBySql("

                select 
                    nombre, count(1) 
                from (
                    select 
                        v.created_by as usuario, 
                        case when u.status = 'ACTIVO' then 
                        concat_ws(' ',nombre, primer_apellido, segundo_apellido)  
                        else 'OTRO' end as nombre, u.status
                    from 
                        visit v 
                    inner join 
                        visit_details d on d.visit_id = v.visit_id
                    inner join 
                        usuarios u on u.user_id = v.created_by
                    where 
                        v.provider_type = 'op'
                ) x 
                group by 
                    nombre, usuario

            ")->asArray()->all();

            $data['labels'] = array_values(array_column($info,'nombre'));
            $data['data'] = array_values(array_column($info,'count'));
        }
        return json_encode($data);
    }


    public function actionZonanlreport2($id=1,$z=0){
        $admonLimit = '2021-10-04';
        //Apodaca, Cadereyta Jiménez, El Carmen,
        //García, San Pedro Garza García, General
        //Escobedo, Guadalupe, Juárez, Monterrey,
        //Salinas Victoria, San Nicolás de los Garza,
        //Santa Catarina y Santiago.
        $zmm = [952,955,962,964,994,966,971,976,987,992,993,995,996];
        $cond1= $z ? ' u.city_fiscal in  ('.implode(',',$zmm).') ' : ' u.state_fiscal = 19 ';
        $cond2= $z ? ' u.city_fiscal not in  ('.implode(',',$zmm).') ' : ' u.state_fiscal != 19 ';
        $cond3= $z ? ' u.city_fiscal is null ' : ' u.state_fiscal is null ';
        $esNl=  $z ? ' and u.state_fiscal = 19 ' : ' ';


        $data = ['data'=>[]];
        if($id==1){//empresasPorZonaGeografica
            $info = Provider::findBySql("
                select sum(case when $cond1 then 1 else 0 end) as a,
                sum(case when $cond2 then 1 else 0 end) as b,
                sum(case when $cond3 then 1 else 0 end) as c, 
                count(1)as d
                from (select * from (
								select p.provider_id, u.state_fiscal,u.city_fiscal, ROW_NUMBER() over (partition  by p.provider_id order by p.provider_id) as total
                from provider p left join provider.ubicacion u on u.provider_id = p.provider_id and u.tipo = 'DOMICILIO FISCAL'
                and u.activo is true and u.status_op = 'VALIDADO'
                where p.provider_id in (	
								select provider_id from provider where (tipo_provider = 'op' and enabled is true) or provider_id in 
									(	select provider_id from provider.datos_validados where status_op ='VALIDADO' )
									)
									
									)x where x.total = 1 )u  where 1=1 $esNl
            ")->asArray()->one();

            $data['data'] = array_values($info);
        }elseif ($id==2){//ConstanciasDeRegistroExpedidas_zona
            $info = Provider::findBySql("
                select sum(case when $cond1 then 1 else 0 end) as b,
                sum(case when $cond2 then 1 else 0 end) as c, count(1)as a
                from historico_certificados c left join provider p on p.provider_id = c.provider_id 
                left join provider.ubicacion u on u.provider_id = p.provider_id and u.tipo = 'DOMICILIO FISCAL'
                and u.activo is true and u.status_op='VALIDADO'
                where historico_certificados_id in (select id  from (
                    select historico_certificadoS_id as id, ROW_NUMBER() over( partition by provider_id order by created_at desc) as total 
                    FROM historico_certificados where provider_type = 'op' and tipo = 'CERTIFICADO'
                    )x  where total = 1
                )
                 $esNl
            ")->asArray()->one();

            $data['data'] = array_values($info);
        }elseif ($id==3){//VisitasRealizadas_zona
            $info = Provider::findBySql("
                select sum(case when $cond1 then 1 else 0 end) as b,
                sum(case when $cond2 then 1 else 0 end) as c, count(1)as a
                from visit v inner join visit_details d on d.visit_id = v.visit_id
                inner join provider.ubicacion u on u.ubicacion_id = d.ubicacion_id
                where v.provider_type = 'op' $esNl
            ")->asArray()->one();

            $data['data'] = array_values($info);
        }
        return json_encode($data);
    }


    public function actionVisitspending($z=0){
        $admonLimit = '2021-10-04';
        //Apodaca, Cadereyta Jiménez, El Carmen,
        //García, San Pedro Garza García, General
        //Escobedo, Guadalupe, Juárez, Monterrey,
        //Salinas Victoria, San Nicolás de los Garza,
        //Santa Catarina y Santiago.
        $zmm = [952,955,962,964,994,966,971,976,987,992,993,995,996];
        $cond1= $z ? ' u.city_fiscal in  ('.implode(',',$zmm).') ' : ' u.state_fiscal = 19 ';
        $cond2= $z ? ' u.city_fiscal not in  ('.implode(',',$zmm).') ' : ' u.state_fiscal != 19 ';
        $esNl=  $z ? ' and u.state_fiscal = 19 ' : ' ';


        $data = ['data'=>[]];
        $info = Provider::findBySql("

                Select sum(case when $cond1 then 1 else 0 end) as b,
                sum(case when $cond2 then 1 else 0 end) as c, count(1)as a
                from visit v inner join provider p on p.provider_id = v.provider_id 
                inner join provider.ubicacion u on p.provider_id = u.provider_id and u.tipo = 'DOMICILIO FISCAL' --and u.activo is true
                where v.status is true and v.provider_type = 'op' $esNl

            ")->asArray()->one();

        $data['data'] = array_values($info);
        return json_encode($data);
    }


    public function actionLista69(){
        $url = "http://omawww.sat.gob.mx/cifras_sat/Documents/Definitivos.csv";
        //$data = $this->file_get_contents_utf8($url);
        $x = 0;
        $fileR = fopen($url,'r');

        if($fileR){
            $model = Lista69b::find()
                ->where(['not',['id'=>null]])->one();
            $firstLine = explode(',',utf8_encode(fgets($fileR)))[0];
            if(!$model || $firstLine != $model->id){
                $path = 'op/69b';
                if(!is_dir($path)){
                    mkdir($path,0770,true);
                }
                $fileW = fopen($path."/".date('Y-m-d').".csv","w");
                fwrite($fileW,file_get_contents($url));
                fclose($fileW);
                while(!feof($fileR)){
                    $data = utf8_encode(fgets($fileR));
                    $rfc = explode(',',$data)[1];
                    $mod = new Lista69b();
                    if($x++ == 0)
                        $mod->id = $firstLine;
                    $mod->rfc = $rfc;
                    $mod->save();
                }
            }else{
                return "Archivo no actualizado aun";
            }
        }else{
            $model = new ErrorInesperado();
            $model->data = "No existe docuumento lista 69b";
            $model->tipo_error = "69b";
            $model->save();
        }
        fclose($fileR);
        return "Actualizacion ok";
    }


    public function actionAnaliticsop($p=null){
        if(!$p)
            return $this->goHome();

        $provs = explode('_',$p);
        /*$data = Historico::find()
            ->where(['and',['in','provider_id',$provs],['tipo'=>'op']])
            ->asArray()->all();
        */
        $data = Historico::findBySql(" select * from (
            select * , ROW_NUMBER() over (partition by concat(modelo,provider_id) order by fecha_validacion desc)
            from provider.historico where provider_id in (".implode($provs,',').") and tipo = 'op' order by modelo ) 
            x where row_number = 1 ")->asArray()->all();

        $ubicaciones = [];

        /* Parte solamente de ubicacion */
        foreach($provs as $provider_id){
            $aux_ubicacion = [];
            //Fecha del ultimo certificado
            $ultimo_certificado = Yii::$app->db->createCommand("SELECT date FROM ( SELECT DISTINCT(created_at::date) as date FROM historico_certificados 
                WHERE provider_id = $provider_id AND tipo = 'CERTIFICADO' AND provider_type = 'op' ) x ORDER BY date DESC LIMIT 1 ")->queryOne();
            if(is_null($ultimo_certificado) || $ultimo_certificado == false){
                $aux_ubicacion = Historico::find()->where(['and', ['provider_id' => $provider_id], ['tipo' => 'op'], ['modelo' => 'Ubicacion']])
                    ->orderBy(['fecha_validacion' => SORT_DESC])->asArray()->all();
            }else{
                $ultimo_certificado = $ultimo_certificado['date'];
                $filtro_certificados = Yii::$app->db->createCommand("SELECT date FROM ( SELECT DISTINCT(created_at::date) as date FROM historico_certificados 
                    WHERE provider_id = $provider_id AND tipo = 'CERTIFICADO' AND provider_type = 'op' ) x WHERE date < '$ultimo_certificado' ORDER BY date DESC ")->queryAll();
                $fechas_periodos = ArrayHelper::getColumn($filtro_certificados,'date');
                $aux_ubicacion = self::getHistoricoUbicaciones($provider_id, $ultimo_certificado, $fechas_periodos);
            }         
            
            $ubicaciones = array_merge($ubicaciones, $aux_ubicacion);
        }        


        $state = ArrayHelper::map(
            CatEntidades::find()
                ->select(['entidad_id', 'nombre'])->asArray()->all(),
            'entidad_id', 'nombre');

        $tipo_obra = ArrayHelper::map(
            SubespecialidadCategory::find()
                ->select(['category_id','nombre'])->all(),
            'category_id','nombre');
        $especialidad  = ArrayHelper::map(
            CatSubespecialidades::find()
                ->select(['subespecialidad_id','nombre'])->all(),
            'subespecialidad_id','nombre');
        $provs = ArrayHelper::map(
            Provider::find()->select(new Expression(" provider_id, case when tipo_persona = 'Persona moral' then name_razon_social else concat_ws(' ',pf_nombre, pf_ap_paterno,pf_ap_materno) end as prov  "))
                ->where(['in','provider_id',$provs])
                ->asArray()->all(),
        'provider_id','prov');

        return $this->render('analiticsop',[
            'provs'=>$provs,
            'data'=>$data,
            'estados'=>$state,
            'tipo_obra'=>$tipo_obra,
            'especialidad'=>$especialidad,
            'ubicaciones'=>$ubicaciones
        ]);
    }


    public function actionPrintsop($p=null){
        if(!$p)
            return $this->goHome();

        $provs = explode('_',$p);
        $path = getcwd().'/op/zip/';

        if(!file_exists($path))
            mkdir($path,0775,true);

        $zipDataName = $path.$p.'_CONCURSO.zip';

        $zipData = new \ZipArchive();
        $zipData->open($zipDataName, \ZIPARCHIVE::CREATE);
        $unlinkF = [];

        foreach ($provs as $prov){
            $zipProv = $this->createZipByProvider($prov);
            $zipData->addFile($path.$zipProv,$zipProv);
            array_push($unlinkF,$path.$zipProv);
        }
        $zipData->close();
        foreach ($unlinkF as $file)
            unlink($file);
        return $this->downloadZip($zipDataName);
    }


    private function createZipByProvider($prov){
        /*$data = Historico::find()
            ->where(['and',['provider_id'=>$prov],['tipo'=>'op']])
            ->asArray()->all();
        */

        $data = Historico::findBySql(" select * from (
            select * , ROW_NUMBER() over (partition by modelo order by fecha_validacion desc)
            from provider.historico where provider_id = $prov and tipo = 'op' order by modelo ) 
            x where row_number = 1 ")->asArray()->all();

        $ubicaciones = [];

        /* Parte solamente de ubicacion */
        //Fecha del ultimo certificado
        $ultimo_certificado = Yii::$app->db->createCommand("SELECT date FROM ( SELECT DISTINCT(created_at::date) as date FROM historico_certificados 
            WHERE provider_id = $prov AND tipo = 'CERTIFICADO' AND provider_type = 'op' ) x ORDER BY date DESC LIMIT 1 ")->queryOne();
        if(is_null($ultimo_certificado) || $ultimo_certificado == false){
            $ubicaciones = Historico::find()->where(['and', ['provider_id' => $prov], ['tipo' => 'op'], ['modelo' => 'Ubicacion']])
                ->orderBy(['fecha_validacion' => SORT_DESC])->asArray()->all();
        }else{
            $ultimo_certificado = $ultimo_certificado['date'];
            $filtro_certificados = Yii::$app->db->createCommand("SELECT date FROM ( SELECT DISTINCT(created_at::date) as date FROM historico_certificados 
                WHERE provider_id = $prov AND tipo = 'CERTIFICADO' AND provider_type = 'op' ) x WHERE date < '$ultimo_certificado' ORDER BY date DESC ")->queryAll();
            $fechas_periodos = ArrayHelper::getColumn($filtro_certificados,'date');
            $ubicaciones = self::getHistoricoUbicaciones($prov, $ultimo_certificado, $fechas_periodos);
        }         
   

        $provider = Provider::find()->where(['provider_id'=>$prov])->one();
        $state = ArrayHelper::map(
            CatEntidades::find()
                ->select(['entidad_id', 'nombre'])->asArray()->all(),
            'entidad_id', 'nombre');

        $tipo_obra = ArrayHelper::map(
            SubespecialidadCategory::find()
                ->select(['category_id','nombre'])->all(),
            'category_id','nombre');
        $especialidad  = ArrayHelper::map(
            CatSubespecialidades::find()
                ->select(['subespecialidad_id','nombre'])->all(),
            'subespecialidad_id','nombre');

        $view = $this->renderPartial('printsop',[
            'provider'=>$provider,
            'data'=>$data,
            'estados'=>$state,
            'tipo_obra'=>$tipo_obra,
            'especialidad'=>$especialidad,
            'ubicaciones' => $ubicaciones
        ]);

        //var_dump($view).exit();
        $path = getcwd().'/op/zip/';
        $nameFileProv = $prov.'_'.$provider->rfc.'.pdf';

        $mpdf = new mPDF('utf-8', 'A4');
        $mpdf->SetTitle('Certificado');

        $stylesheet = file_get_contents('css/pdfo.css');

        $mpdf->WriteHTML($stylesheet, 1);
        $mpdf->WriteHTML($view, 2);
        $mpdf->Output($path.$nameFileProv,'F');
        //var_dump($nameFileProv).exit();
        return $nameFileProv;
    }


    public function downloadZip($filename){
        if(file_exists($filename)) {

            header('Content-Description: File Transfer');
            header('Content-Type: application/octet-stream');
            header("Cache-Control: no-cache, must-revalidate");
            header("Expires: 0");
            header('Content-Disposition: attachment; filename="'.basename($filename).'"');
            header('Content-Length: ' . filesize($filename));
            header('Pragma: public');
            flush();
            readfile($filename);
        }
        else{
            echo "File ($filename) does not exist.";
        }
    }

    public function getHistoricoUbicaciones($provider_id, $fecha, $fechas_certificados){
        $response = null;
        if( !empty($fechas_certificados) ){
            foreach($fechas_certificados as $fecha_filtro){
                $response = self::findHistoricoUbicacion($provider_id, $fecha, $fecha_filtro);
                if(!is_null($response) && !empty($response)){ continue; }
            }
        }
        return is_null($response) || empty($response) ? self::findHistoricoUbicacion($provider_id, $fecha) : $response;
    }

    public function findHistoricoUbicacion($provider_id, $fecha_consulta, $fecha_limite = null){
        $direcciones = [];
        $condicion = ['and', ['provider_id' => $provider_id],['modelo' => 'Ubicacion'], ['<=','date(fecha_validacion)', $fecha_consulta]];
        !is_null($fecha_limite) && array_push($condicion, ['>','date(fecha_validacion)', $fecha_limite]);
        $direcciones = Historico::find()->where($condicion)->asArray()->all();
        return $direcciones;
    }

}
