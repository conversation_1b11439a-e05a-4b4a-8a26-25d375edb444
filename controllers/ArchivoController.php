<?php
namespace app\controllers;

use app\helpers\GeneralController;
use app\models\Provider;
use app\models\Rfc;
use app\models\Usuarios;
use app\models\UsuariosSearch;
use Yii;
use yii\web\Controller;
use app\models\SubirArchivo;
use yii\web\UploadedFile;

class ArchivoController extends Controller
{

    /*
     * Funcion relizada para pruebas ya que se
     * necesita en ocaciones borrar
     * el RFC para realiza rpuebras de nuevo o para
     * firmar con rfc que ya se tienen para pruebas
     *
     * */
    public function actionRfc($rfc=null,$nuevo=null){
        if(GeneralController::isProduction() || !$rfc)
            return false;

        if(!in_array(strlen($rfc),[12,13]) || !in_array(strlen($nuevo),[0,12,13]))
            return "Los RFC no son correctos, por favor valide...";

        $user = Usuarios::find()->Where(['rfc'=>$rfc])->one();
        $prov = Provider::find()->Where(['rfc'=>$rfc])->one();
        $mod = Rfc::find()->Where(['rfc'=>$rfc])->one();

        if(!$user && !$prov && !$mod)
            return "El RFC ($rfc) no existe en esta version.";

        $let = chr(rand(ord('A'), ord('Z')));
        $let2 = chr(rand(ord('A'), ord('Z')));

        if($nuevo){
            //cambiar $rfc por $nuevo en las tablas de RFC,PROVIDER y Usuarios
            if($user){
                $user->rfc = $nuevo;
                $user->username = $user->username.$let;
                $user->email = $user->email.$let;
                $user->save(false);
            }
            if($prov){
                $prov->rfc = $nuevo;
                $prov->email = $prov->email.$let;
                $prov->save(false);
            }
            if($mod){
                $mod->rfc = $nuevo;
                $mod->save(false);
            }
            return "Se cambio el RFC: ($rfc) por ($nuevo)";
        }else{
            //cambiar $rfc en las tablas de RFC,PROVIDER y Usuarios
            $dato = $rfc;
            $dato[0] = $let;
            $dato[strlen($dato)-1] = $let2;

            if($user){
                $user->rfc = $dato;
                $user->username = $user->username.$let;
                $user->email = $user->email.$let;
                $user->save(false);
            }
            if($prov){
                $prov->rfc = $dato;
                $prov->email = $prov->email.$let;
                $prov->save(false);
            }
            if($mod){
                $mod->rfc = $dato;
                $mod->save(false);
            }
            return "Se eliminó el RFC: ($rfc) se cambió por uno aleatorio ($dato)";
        }
    }

    public function actionSubir()
    {
        $model = new SubirArchivo();
        $dir = 'ficha_tecnica/';
        if (!is_dir($dir)) {
            mkdir($dir);
        }

        if (Yii::$app->request->isPost) {
            $model->archivo = UploadedFile::getInstance($model, 'archivo');
            $url = $model->upload();
            if ($url != false) {
                return $url;
            } else {
                return false;
            }
        }
    }


    public function actionSendemail($name){
        ini_set('max_execution_time', '0');
        $fp = fopen("emails.list", "r");
        while (!feof($fp)){
            $linea = fgets($fp);
            if($linea){
                $correo = preg_replace("/\s+/","",$linea);
                GeneralController::sendEmail('/provider/correos/'.$name,null,trim($correo),'AVISO DE ESTATUS INACTIVO ',[]);
                echo "\nEnviando correo a: -".$correo."-";
                sleep(1);
            }

        }
        fclose($fp);

    }


    public function actionSendemail2(){

        ini_set('max_execution_time', '0');
        $fp = fopen("emails2.list", "r");
        while (!feof($fp)){
            $linea = fgets($fp);
            if($linea){
                $correo = preg_replace("/\s+/","",$linea);
                GeneralController::sendEmail('/provider/correos/notificacion2',null,trim($correo),'AVISO DE ESTATUS INACTIVO ',[]);
                echo "\nEnviando correo a: -".$correo."-";
                sleep(1);
            }

        }
        fclose($fp);

    }

    public function actionSendemail3(){

        ini_set('max_execution_time', '0');
        $fp = fopen("emails3.list", "r");
        while (!feof($fp)){
            $linea = fgets($fp);
            if($linea){
                $correo = preg_replace("/\s+/","",$linea);
                GeneralController::sendEmail('/provider/correos/notificacion2',null,trim($correo),'AVISO DE ESTATUS INACTIVO ',[]);
                echo "\nEnviando correo a: -".$correo."-";
                sleep(1);
            }

        }
        fclose($fp);

    }
}
