<?php

namespace app\controllers;

use app\helpers\GeneralController;
use app\models\Provider;
use app\models\Status;
use Yii;
use app\models\UltimaDeclaracion;
use app\models\UltimaDeclaracionSearch;
use yii\web\Controller;
use yii\web\NotFoundHttpException;
use yii\filters\VerbFilter;

/**
 * UltimaDeclaracionController implements the CRUD actions for UltimaDeclaracion model.
 */
class UltimaDeclaracionController extends GeneralController
{
    /**
     * {@inheritdoc}
     */
    public function behaviors()
    {
        return [
            'verbs' => [
                'class' => VerbFilter::className(),
                'actions' => [
                 //   'delete' => ['POST'],
                ],
            ],
        ];
    }

    /**
     * Lists all UltimaDeclaracion models.
     * @return mixed
     */
    public function actionIndex()
    {
        $searchModel = new UltimaDeclaracionSearch();
        $dataProvider = $searchModel->search(Yii::$app->request->queryParams,'mainFilterUltimaDeclaracionA');

        return $this->render('index', [
            'searchModel' => $searchModel,
            'dataProvider' => $dataProvider,
        ]);
    }

    /**
     * Displays a single UltimaDeclaracion model.
     * @param integer $id
     * @return mixed
     * @throws NotFoundHttpException if the model cannot be found
     */
    public function actionView($id)
    {
        return $this->render('view', [
            'model' => $this->findModel($id),
        ]);
    }

    /**
     * Creates a new UltimaDeclaracion model.
     * If creation is successful, the browser will be redirected to the 'view' page.
     * @return mixed
     */
    public function actionCreate()
    {
        $model = new UltimaDeclaracion();

        if ($model->load(Yii::$app->request->post()) && $model->save()) {
            return $this->redirect(['view', 'id' => $model->ultima_declaracion_id]);
        }

        return $this->render('create', [
            'model' => $model,
        ]);
    }

    /**
     * Updates an existing UltimaDeclaracion model.
     * If update is successful, the browser will be redirected to the 'view' page.
     * @param integer $id
     * @return mixed
     * @throws NotFoundHttpException if the model cannot be found
     */
    public function actionUpdate($id)
    {
        $model = $this->findModel($id);

        if ($model->load(Yii::$app->request->post()) && $model->save()) {
            return $this->redirect(['index']);
        }

        return $this->render('update', [
            'model' => $model,
        ]);
    }

    /**
     * Deletes an existing UltimaDeclaracion model.
     * If deletion is successful, the browser will be redirected to the 'index' page.
     * @param integer $id
     * @return mixed
     * @throws NotFoundHttpException if the model cannot be found
     */
    public function actionDelete($id)
    {
        $mod = $this->findModel($id);

        if($mod){
            $mod->status_bys = 'RECHAZADO';
            if($mod->save(false)){
                $rech = new Status();
                $rech->modelo = 'ultima_declaracion';
                $rech->register_id = $mod->provider_id;
                $rech->created_id = 168;
                $rech->motivo = "ESTIMADO INTERESADO:
                            CON EL FIN DE MANTENER SU REGISTRO ACTIVO EN EL PADRON DE PROVEEDORES, LO INVITAMOS A ACTUALIZAR LA SIGUIENTE INFORMACIÓN,
                            DECLARACION ANUAL";
                $rech->status_bys = 'PENDIENTE';
                $rech->expiration = true;
                $rech->provider_id = $mod->provider_id;
                if($rech->save()){
                    $email = Provider::find()->select('email')->where(['provider_id' => $rech->provider_id])->one()['email'];
                    self::sendEmail('/provider/correos/rechazo', null, $email, 'Verifica tu información', ['modulo' => 'Datos financieros', 'motivo' => $rech->motivo, 'tipo_provider' => 'bys']);

                }
            }
        }

        return $this->redirect(['index']);

    }

    /**
     * Finds the UltimaDeclaracion model based on its primary key value.
     * If the model is not found, a 404 HTTP exception will be thrown.
     * @param integer $id
     * @return UltimaDeclaracion the loaded model
     * @throws NotFoundHttpException if the model cannot be found
     */
    protected function findModel($id)
    {
        if (($model = UltimaDeclaracion::findOne($id)) !== null) {
            return $model;
        }

        throw new NotFoundHttpException('The requested page does not exist.');
    }
}
