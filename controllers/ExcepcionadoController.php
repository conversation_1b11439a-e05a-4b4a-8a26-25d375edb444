<?php

namespace app\controllers;

use app\models\Excepcionado;
use app\models\ExcepcionadoSearch;
use Yii;
use mPDF;
use app\helpers\GeneralController;
use app\models\Provider;
use yii\filters\VerbFilter;

/**
 * ExcepcionadoController implements the CRUD actions for Excepcionado model.
 */
class ExcepcionadoController extends GeneralController
{
    /**
     * @inheritDoc
     */
    public function behaviors()
    {
        return array_merge(
            parent::behaviors(),
            [
                'verbs' => [
                    'class' => VerbFilter::className(),
                    'actions' => [
                        //'delete' => ['POST'],
                    ],
                ],
            ]
        );
    }


    public function actionIndex()
    {
        $searchModel = new ExcepcionadoSearch();
        $dataProvider = $searchModel->search($this->request->queryParams);

        return $this->render('index', [
            'searchModel' => $searchModel,
            'dataProvider' => $dataProvider,
        ]);
    }


    public function actionView($id)
    {
        return $this->renderAjax('view', [
            'model' => Excepcionado::findOne($id),
        ]);
    }


    public function actionUpdate($id=null)
    {
        $model = (($model = Excepcionado::findOne(['id' => $id])) !== null) ? $model : new Excepcionado();
        $extra_document = $model->extra_document;
        $record_old = $model->record;
        GeneralController::makedir($model->path);
        if ($model->load($this->request->post())) {
            $model->token = !$model->token ? 'a' : $model->token;
            $model->created_by = !$model->created_by ? Yii::$app->user->id : $model->created_by;
            if (!empty($model->extra_document) && $model->extra_document != $extra_document) {
                $new_nameRfc = str_replace('archivos_tmp/', $model->path . '/', $model->extra_document);
                $this->copyFile($model->extra_document, $new_nameRfc);
                $model->extra_document = $new_nameRfc;
            }
            if (!empty($model->record) && $model->record != $record_old) {
                $record_new = str_replace('archivos_tmp/', $model->path . '/', $model->record);
                $this->copyFile($model->record, $record_new);
                $model->record = $record_new;
            }
            if ($model->save()) {
                if ($model->token == 'a') {
                    $t = microtime(true) * 10000;
                    $model->token = md5($t) . '_' . base64_encode($model->id);
                    $model->save();
                }
                return $this->redirect(['index']);
            } else {
                var_dump($model->errors);
            }
        }
        return $this->renderAjax('update', [
            'model' => $model,
        ]);
    }


    public function actionDelete($id = null)
    {
        if (!$id)
            return $this->redirect(['index']);

        $model = Excepcionado::findOne($id);
        $model->active = false;
        $model->last_updated_by = Yii::$app->user->id;
        $model->save();
        return $this->redirect(['index']);
    }


    public function getModelByToken($tokenex){

        if (!$tokenex || $tokenex=='') {
            $this->redirect(['/']);
            Yii::$app->end();
        }
        $id = isset(explode('_', $tokenex)[1]) ? base64_decode(explode('_', $tokenex)[1]) : null;
        $model = Excepcionado::findOne($id);

        if (!$model){
            $this->redirect(['/']);
            Yii::$app->end();
        }


        return $model;
    }


    public function actionExcepcionado($tokenex = null)
    {
        $model = $this->getModelByToken($tokenex);

        if ($model->document)
            return $this->redirect([$model->document]);

        if(!$model->time_end)
            return $this->redirect('video?tokenex='.$tokenex);

        $url = $this->certificadocurso($model);
        return $this->redirect([$url]);
    }


    public function actionVideo($tokenex){
        $this->layout = 'nomain';
        return $this->render('video',['tokenex'=>$tokenex]);
    }


    public function actionSend($id = null)
    {
        if (!$id)
            return $this->redirect(['index']);

        $model = Excepcionado::findOne($id);

        GeneralController::sendEmail('/provider/correos/curso', null, $model->email, 'Curso prevención y concientización sobre faltas administrativas y hechos de
            corrupción',  ['token'=>$model->token]);

        return $this->redirect(['index']);
    }


    public function actionDocument($id=null){
        if (!$id)
            return $this->redirect(['index']);

        $model = Excepcionado::findOne($id);
        if (!$model)
            return $this->redirect(['index']);

        $this->certificadocurso($model);
        Yii::$app->session->setFlash('success','Documento generado con exito!');
        return $this->redirect('index');

    }


    public function certificadocurso($model){

        $datosFirma = ['firmante' => '', 'cargo' => '', 'firma' => ''];
        $datosQr = ['img' => ''];

        $path_doc = $model->path;
        if (!file_exists($path_doc)) {
            mkdir($path_doc, 0777, true);
        }

        //var_dump($model->time_end).exit();

        $proveedor = new Provider();
        $proveedor->rfc = $model->rfc_empresa;
        $proveedor->name_razon_social = $model->razon_social;
        $proveedor->pf_nombre = $model->razon_social;
        $proveedor->pf_ap_paterno = '';
        $proveedor->pf_ap_materno = '';
        $proveedor->tipo_persona = ($model->tipo_persona == 'PF') ? 'Persona física' : 'Persona moral';
        $proveedor->firmante = $model->name_signatory;

        /* $proveedor = (Object)[
            'rfc'=>$model->rfc_empresa,
            'name_razon_social' => $model->razon_social ,
            'pf_nombre' => $model->razon_social ,
            'pf_ap_paterno' => '' ,
            'pf_ap_materno' => '' ,
            'tipo_persona' => ($model->tipo_persona == 'PF')?'Persona física':'Persona moral' ,
            'firmante' => $model->name_signatory ,
            ]; */

        $content = $this->renderPartial('/carta/certificadocurso', [
            'proveedor' => $proveedor,
            'date' => base64_encode($model->time_end)
        ]);

        define('_MPDF_TTFONTPATH', './fonts/'); //cambia la ruta de las fuentes utilizadas en la creación del pdf (la configuración default puede solicitar alguans fuentes necesarias)
        $stylesheet = file_get_contents('css/pdf.css');

        $mpdf = new \mPDF();
        $mpdf->SetTitle('Certificado');
        $mpdf->SetHTMLHeader($this->renderPartial('/carta/header', ['tipo_provider' => 'bys']));
        $mpdf->SetHTMLFooter($this->renderPartial('/carta/certificado_footer', ['datosFirma' => $datosFirma, 'datosQr' => $datosQr, 'tipo_provider' => 'bys']));
        $mpdf->defaultfooterline = 0;
        $mpdf->WriteHTML($content);

        $filename = $path_doc . '/' . md5($model->id) . '_certificado_proveedor_curso' . time() . '.pdf';
        $mpdf->Output($filename, 'F');


        chmod($filename, 0777);

        $date_year = date("Y");
        $date_month = date("m");
        $carpetaRfc = 'documentos_firmados' . '/' . $model->rfc_empresa;
        if(!file_exists($carpetaRfc)){
            mkdir($carpetaRfc,0777,true);
        }
        $filenameFinal = 'documentos_firmados' . '/' . $model->rfc_empresa . '/' . $model->rfc_empresa . '_certificado_curso_' . $date_year . '_' . $date_month . '_bys'  . time() . '.pdf';

        $codigoV = $this->codigoVerificacion($model->id);
        $token = $this->solicitaDatosGet(\Yii::$app->params['urlToken'])['token'];
        $datosQr = $this->solicitaDatosPost(\Yii::$app->params['urlQr'], ['encode' => $codigoV, 'base64' => 'true', 'validador' => true]);

        $datosBarCode = $this->solicitaBarCode(\Yii::$app->params['urlBarCode'], $codigoV);
        $datosFirma = $this->solicitaDatosPost(\Yii::$app->params['urlFirma'], ['code' => $codigoV, 'token' => $token, 'type' => 'BYS', 'path' => \yii\helpers\Url::home('https') . $filename, 'signed_document' => \yii\helpers\Url::home('https') . $filenameFinal]);

        // Firma prueba para generar certificado en local
        //
        // $json_prueba =
        //     '{
        //         "status": "ok",
        //         "firma": "ZDYyMmE0YjMwNTMyMDQwODMxZDdmMDA4Mjk2YzNkMGY5NWFiZTBiOTAyNTRlY2NhNzY0ZjJlYTZiZTRhYmJhZjg4MzQyZWJjYmM0Y2VhZTRiNmZmODgwZDcyYTY3YTVkODRlNGU2ODAzN2I3N2VjMzc3NWYyM2MwZWRmM2NkYzQ0NTliOGMwMmU5OWRmMTVmZjg1MzJhN2Q5YWMxODkyZjFjNjdjOWEwYjFkNzkyMTIzMjNhYTY5MzYyMzEzOGZi",
        //         "firmaLarga": "ZTEyM2VhNTA2NTlkYTVmMGU3YjM1NGQwMzViZTY4NjUyMGE2MzBlNjZmZDM0YTYxMTcwNjgxMDIyZjFlNDIwODRlYmY0NTc3ZDUzOWY5NjJjODRjNGViYjRhYzY5YTZhMTQwZDJlZDY4ZWRiMTU0NDliYzcwYTI2YzA4OTM0NzMzZDMxZTFkY2RjZjMwMzBiOWQzNmJlMWFjNGQwODdiYTkxY2M0NjU3M2JkNDBmYTQ4ZjI3OWVkMDQ0ZDhiMjk4Y2Y5Y2Y3N2ZmYzQwMjM5Yjg1M2Y0OTQxY2EzMDlhZjliZWVkODUyYTlhMGQxYjhmZmIzMWYxYWFkYjgzZTU3NmJhYjhlMjU2OTRmOTQzNzc5MjY5ODUzZThhYjhhNzIxMDc3YTVmMWU2MzBlODQ3ZmE4YzNlODg0MDYyMzU3OWQ3Nzc1MGU3ZDk3Yjc5NzcxNGYwOWQ2OGYzZTJhZTc4ZDhhYTQyYjc0YzUwZTM2Njk1MTViZTMwNWEwNGFkNmFkYWVjMzE0OGY5ZTMxNTMxNjg5OGNmOThlNGUyZjgzZTEyYjM2NzMyZDMxYTBkZGEyN2EwZjI1NzQzZjFmYTdjYzE3NzU2ZmVlNDZjNjdiZjFiNDE4N2ZhNWM1OThkMjg4MmM2NmU5ZGIzNDkwMjczZDYyNzg5YjAwZDY1YzM0MDc=",
        //         "firmante": "Ing. Juanito Perez Rodriguez",
        //         "cargo": "Encargado General de Area"
        //     }';
        // $datosFirma = json_decode($json_prueba,true);

        unlink($filename);

        $content = $this->renderPartial('/carta/certificadocurso', [
            'proveedor' => $proveedor,
            'date' => base64_encode($model->time_end)
        ]);

        $mpdf = new mPDF('utf-8', 'A4', 0, '', 0, 0, 5, 15, 0, 0);

        $this->add_custom_fonts_to_mpdf($mpdf); //

        $mpdf->SetTitle('Certificado');
        $mpdf->SetHTMLHeader($this->renderPartial('/carta/header', []));
        $mpdf->SetHTMLFooter($this->renderPartial('/carta/certificado_footer', ['datosFirma' => $datosFirma, 'datosQr' => $datosQr, 'tipo_provider' => 'bys', 'barCode' => $datosBarCode, 'code' => $codigoV]));
        /* if(!$_ENV['VEDA_ELECTORAL']){ $mpdf->SetWatermarkImage('imgs/cartas/logo_sello.png',.8,array(100,100)); } */
        $mpdf->SetWatermarkImage('imgs/cartas/logo_watermark.svg',.1,array(100,100));
        $mpdf->showWatermarkImage = true;
        $mpdf->defaultfooterline = 0;
        $mpdf->WriteHTML($content);
        $mpdf->WriteHTML($stylesheet, 1);

        $mpdf->Output($filenameFinal, 'F');
        chmod($filenameFinal, 0777);

        $model->document = $filenameFinal;
        $model->save();
        return $filenameFinal;
    }


    public function actionStarttime($tokenex=null){
        $model = $this->getModelByToken($tokenex);
        $model->time_start = date('Y-m-d H:i:s');
        $model->save();
        return true;
    }

    public function actionEndtime($tokenex=null){
        $model = $this->getModelByToken($tokenex);
        $model->time_end = date('Y-m-d H:i:s');
        $model->video = true;
        $model->save();
        return json_encode(['status'=>true]);
    }

}
