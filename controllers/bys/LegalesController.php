<?php

namespace app\controllers\bys;

use app\helpers\GeneralController;
use app\models\ActaConstitutiva;
use app\models\ActaConstitutivaSearch;
use app\models\AltaHaciendaSearch;
use app\models\CatActividades;
use app\models\CatEntidades;
use app\models\CatMunicipios;
use app\models\CatRamas;
use app\models\Curp;
use app\models\DatosValidados;
use app\models\EscrituraPublica;
use app\models\EscrituraPublicaSearch;
use app\models\ExpirationDocuments;
use app\models\Giro;
use app\models\IdOficial;
use app\models\ComprobanteDomicilio;
use app\models\ModificacionActa;
use app\models\ModificacionActaSearch;
use app\models\Module;
use app\models\Porcentaje;
use app\models\RegistroImss;
use app\models\RegistroPublicoPropiedad;
use app\models\RepresentanteLegal;
use app\models\Provider;
use app\models\ProviderSearch;
use app\models\RepresentanteLegalSearch;
use app\models\Status;
use app\models\RegistroActas;
use app\models\Historico;
use kartik\form\ActiveForm;
use Yii;
use app\models\Rfc;
use app\models\RfcSearch;
use app\models\Usuarios;
use yii\data\ActiveDataProvider;
use yii\helpers\ArrayHelper;
use yii\web\NotFoundHttpException;
use yii\filters\VerbFilter;
use yii\web\Response;
use yii\web\UploadedFile;
use app\models\RelacionAccionistas;
use app\models\RelacionAccionistasSearch;

if (Yii::$app->user->can(Usuarios::ROLE_PROVIDER)) {
    Yii::$app->params['provider_id'] = Yii::$app->user->identity->getProvider();
}

/**
 * LegalesController implements the CRUD actions for Rfc model.
 */
class LegalesController extends GeneralController
{
    /**
     * @inheritdoc
     */
    public function behaviors()
    {
        return [
            'verbs' => [
                'class' => VerbFilter::className(),
                'actions' => [
                    'delete' => ['POST'],
                    /* 'deletemod' => ['POST'], */
                    //'deleterepresentante' => ['POST'],
                    //'deleteaccionistas' => ['POST'],
                ],
            ],
        ];
    }

    /**
     * Lists all Rfc models.
     * @return mixed
     */
    public function actionIndex()
    {
        $this->msgError();
//        $searchModel = new RfcSearch();
//        $dataProvider = $searchModel->search(Yii::$app->request->queryParams);
//        $id_pro = Provider::find()->select('provider_id')->where(['user_id' => Yii::$app->user->getId()])->one()->provider_id;
//        $rfc_status = Rfc::find()->select('url_rfc')->where(['provider_id' => $id_pro])->one();
//        $curp_status = Curp::find()->select('url_curp')->where(['provider_id' => $id_pro])->one();
//        $idoficial_status = IdOficial::find()->select('url_idoficial')->where(['provider_id' => $id_pro])->one();
//
//        $button = !empty($rfc_status) && !empty($curp_status) && !empty($idoficial_status)?'Actualizar':'Crear';
//
//        return $this->render('index', [
//            'searchModel' => $searchModel,
//            'dataProvider' => $dataProvider,
//            'button' => $button
//        ]);
    }

    /**
     * Displays a single Rfc model.
     * @param integer $id
     * @return mixed
     */
    public function actionView()
    {
        $id = Yii::$app->user->identity->providerid;
        /* if ($id == null || !Provider::verifyProvider($id) || !Provider::verifyEtapa2($id)) {
            echo $this->render('/site/error', ['name' => 'Lo sentimos...', 'message' => 'Acceso denegado']);

            return false;
        } */
        $provider = Provider::find()->where(['user_id' => Yii::$app->user->getId()])->one();

        //Rechazos
        $modelStatus = Module::getstatusbymodule('bys_legales',$provider->provider_id);
        $rechazo = $modelStatus->status_bys == Status::STATUS_RECHAZADO || $modelStatus->status_bys == Status::STATUS_ENEDICION  ?
            Status::find()->getRechazo( 'bys_legales', $provider->provider_id) : null;

        $modelos = (Object)[
            'accionistas' => $this->getDataProvider('RelacionAccionistas',$provider->provider_id),
            'modificaciones' =>  $this->getDataProvider('ModificacionActa',$provider->provider_id),
            'representante' => $this->getDataProvider('RepresentanteLegal',$provider->provider_id),
        ];

        $model = (Object)[
            'model' => $this->findModel($id),
            'curp' =>  $this->findModelCurp($id),
            'id' => $this->findModelIdoficial($id),
            'acta' => $this->findModelActaConstitutiva($id),
        ];

        return $this->render('view', [
            'provider'=>$provider,
            'model' => $model,
            'modelos' => $modelos,
            'modelStatus'=>$modelStatus,
            'rechazo'=>$rechazo
        ]);
    }

    private function getDataProvider($model,$id){
        $model_ = '\app\\models\\'.$model;
        $query =  $model_::find()
            ->where(['and',['activo'=>true],['provider_id'=>$id]]);

        if($model == 'ModificacionActa'){
            $query->orderBy(['fecha_acta' => SORT_DESC]);
        }
            //->orderBy([$model_::primaryKey()=>SORT_ASC]);
        return new ActiveDataProvider([
                'query'=>$query
            ]);
    }

    public function actionCreateAccionista()
    {
        $model = new RelacionAccionistas();
        $id = Provider::find()->select('provider_id')->where(['user_id' => Yii::$app->user->id])->one()->provider_id;
        $model->provider_id = $id;
        $model->urlActaAccionistas = UploadedFile::getInstance($model, 'url_acta_2');
        if ($model->load(Yii::$app->request->post())) {
            if (!empty($model->urlActaAccionistas)) {
                $model->url_acta = $model->pathBys . '/' . md5($model->urlActaAccionistas->baseName) . time() . '.' . $model->urlActaAccionistas->extension;
                $model->urlActaAccionistas->saveAs($model->url_acta);
            }
            $porcentaje = 0;
            if (isset($_POST['porcentaje_total_m']) && $_POST['porcentaje_total_m'] != '') {
                $porcentaje = round($_POST['porcentaje_total_m']);
            }
            $model->porcentaje = $porcentaje;
            $model->save();

            return $this->redirect(['view', 'id' => base64_encode($id)]);
        } else {
            $find_acta = RelacionAccionistas::findActa($id);
            return $this->renderAjax('create-accionista', [
                'model' => $model,
                'find_acta' => $find_acta,
            ]);
        }
    }


    /**
     * Updates an existing Rfc model.
     * If update is successful, the browser will be redirected to the 'view' page.
     * @param integer $id
     * @return mixed
     */


    public function actionUpdate()
    {

        if (Yii::$app->user->can(Usuarios::ROLE_PROVIDER)) {
            $id = Yii::$app->params['provider_id'];
        }
        /* if ($id == null || !Provider::verifyProvider($id) || !Provider::verifyEtapa2($id) || !self::verifyUpdate($id) || self::cotejoPendiente() == 'CITA NO MOD') {
            $msg = self::cotejoPendiente() == 'CITA NO MOD' ? 'No puede realizar modificaciones, cuenta con cita pendiente' : 'Acceso denegado';
            return $this->render('/site/error', ['name' => 'Lo sentimos...', 'message' => $msg]);
        } */

        $tipo = Provider::getDataProvider()['tipo_provider'];

        $model = $this->findModel($id);
        $model_curp = $this->findModelCurp($id);
        $model_curpOld = $model_curp->attributes;
        $model_idoficial = $this->findModelIdoficial($id);
        $model_idoficialOld = $model_idoficial->attributes;
        $model_acta_constitutiva = $this->findModelActaConstitutiva($id);
        $model_actaOld = $model_acta_constitutiva->attributes;
       // $date_id = new \DateTime($model_idoficial->expiration_date);
        //$model_idoficial->expiration_date = $date_id->format('Y-m-d');

        $urlCurpOld = $model_curp->url_curp;
        $urlIdOficialOld = $model_idoficial->url_idoficial;
        $model_acta_constitutiva->documento_actaOLD = $model_acta_constitutiva->documento_acta;
        $urlRfcOld = $model->url_rfc;

        $rfc = Yii::$app->user->can(Usuarios::ROLE_PROVIDER) ? Yii::$app->user->identity->rfc : '';

        $modelStatus = Module::getstatusbymodule('bys_legales',$id);
        $rechazo = $modelStatus->status_bys == Status::STATUS_RECHAZADO || $modelStatus->status_bys == Status::STATUS_ENEDICION  ?
            Status::find()->getRechazo( 'bys_legales', $id) : null;

        if (
            $model->load(Yii::$app->request->post())
            || $model_curp->load(Yii::$app->request->post())
            || $model_idoficial->load(Yii::$app->request->post())
            || $model_acta_constitutiva->load(Yii::$app->request->post())
        ) {
            if (($requi_status = Status::find()->where(['and', ['register_id' => $id], ['status_bys' => Status::STATUS_PENDIENTE], ['modelo' => 'rfc']])->one()) !== null) {
                $requi_status->status_bys = Status::STATUS_TERMINADO_PRO;
                $requi_status->save();
            }
            $verify_save_all = true;
            $transaction = \Yii::$app->db->beginTransaction();
           // try {
                $idx = '';

                if ($model_curp->load(Yii::$app->request->post())) {
                    $this->makeDir($model_curp->path_curp);

                    if (!empty($model_curp->url_curp) && $model_curp->url_curp != $urlCurpOld) {
                        $new_nameCurp = str_replace('archivos_tmp/', $model_curp->path_curp . '/', $model_curp->url_curp);
                        $this->copyFile($model_curp->url_curp, $new_nameCurp);
                        $model_curp->url_curp = $new_nameCurp;
                    }

                    $model_curp->provider_id = $id;
                    if (!$model_curp->save(false)) {
                        $verify_save_all = false;
                    }
                }

                if ($model_idoficial->load(Yii::$app->request->post())) {
                    $tipoIdent = $model_idoficial->tipo_identificacion;

                    if($tipoIdent == 'IFE' && isset($model_idoficial->dateIfe) && !empty($model_idoficial->dateIfe)){
                        $model_idoficial->expiration_date = $model_idoficial->dateIfe.'-12-31';
                    }else if($tipoIdent == 'PASAPORTE' && isset($model_idoficial->datePas) && !empty($model_idoficial->datePas)){
                        $model_idoficial->expiration_date = $model_idoficial->datePas;
                    }

                    $this->makeDir($model_idoficial->path_idoficial);

                    if (!empty($model_idoficial->url_idoficial) && $model_idoficial->url_idoficial != $urlIdOficialOld) {
                        $new_nameId = str_replace('archivos_tmp/', $model_idoficial->path_idoficial . '/', $model_idoficial->url_idoficial);
                        $this->copyFile($model_idoficial->url_idoficial, $new_nameId);
                        $model_idoficial->url_idoficial = $new_nameId;
                    }
                    $model_idoficial->provider_id = $id;
                    if (!$model_idoficial->save(false)) {
                        $verify_save_all = false;
                    }
                }

                $model->status_bys = Status::STATUS_ENEDICION;
                $model->provider_id = $id;
                if (!$model->save(false)) {
                    $verify_save_all = false;
                }
                if ($model_acta_constitutiva->load(Yii::$app->request->post())) {
                    $pathA = 'bys/actividad_economica/acta_constitutiva';
                    $model_acta_constitutiva->provider_id = $id;

                    $this->makeDir($pathA);

                    if (!empty($model_acta_constitutiva->documento_acta) && $model_acta_constitutiva->documento_acta != $model_acta_constitutiva->documento_actaOLD) {
                        $new_nameAc = str_replace('archivos_tmp/', $pathA . '/', $model_acta_constitutiva->documento_acta);
                        $this->copyFile($model_acta_constitutiva->documento_acta, $new_nameAc);
                        $model_acta_constitutiva->documento_acta = $new_nameAc;
                    }
                    if (!$model_acta_constitutiva->save(false)) {
                        $verify_save_all = false;
                    }

                }

                if ($verify_save_all) {
                    $countValCert = $this->verifyProviderCert($model->provider_id, 'bys');
                    $countValRec = self::verifyValRec('rfc',$model->provider_id);

                    if ($countValCert > 0 || $countValRec>0) {
                        $trueFalse = $countValCert>0?true:false;
                        $this->compareModels($model_actaOld, $model_acta_constitutiva->attributes, $model->provider_id, 'rfc_acta_constitutiva',$model->provider_id,$trueFalse);
                        $this->compareModels($model_idoficialOld, $model_idoficial->attributes, $model->provider_id, 'rfc_id_oficial',$model->provider_id,$trueFalse);
                        $this->compareModels($model_curpOld, $model_curp->attributes, $model->provider_id, 'rfc_curp',$model->provider_id,$trueFalse);
                    }
                    self::eliminarCita();

                    self::updateModulesBys($model->provider_id,'bys_legales');
                    $transaction->commit();
                    return $this->redirect(['view']);
                } else {
                    $transaction->rollBack();
                }
           // } catch (\Exception $e) {
             //   $transaction->rollBack();
            //}
        }

        $city_state = [];
        if (isset($model_acta_constitutiva->estado_id) && !empty($model_acta_constitutiva->estado_id)) {
            $city_state = ArrayHelper::map(CatMunicipios::find()->select(['municipio_id', 'nombre'])->where(['entidad_id' => $model_acta_constitutiva->estado_id])->asArray()->all(), 'municipio_id', 'nombre');
        }
        $state_id = ArrayHelper::map(CatEntidades::find()->select(['entidad_id', 'nombre'])->all(), 'entidad_id', 'nombre');


        if(isset($model_idoficial->tipo_identificacion) && $model_idoficial->tipo_identificacion == 'IFE' && isset($model_idoficial->expiration_date) && !empty($model_idoficial->expiration_date)){
            $model_idoficial->dateIfe = substr($model_idoficial->expiration_date,0,4);
        }else if(isset($model_idoficial->tipo_identificacion) && $model_idoficial->tipo_identificacion == 'PASAPORTE' && isset($model_idoficial->expiration_date) && !empty($model_idoficial->expiration_date)){
            $model_idoficial->datePas = $model_idoficial->expiration_date;
        }

        return $this->renderAjax('update', [
            'model' => $model,
            'model_curp' => $model_curp,
            'model_idoficial' => $model_idoficial,
            'model_acta_constitutiva' => $model_acta_constitutiva,
            'rfc' => $rfc,
            'state' => $state_id,
            'city_state' => $city_state,
            'provider_id' => $id,
            'tipo' => $tipo,
            'rechazo' => $rechazo
        ]);
    }
    
    /* Metodo de creacion y edicion */
    public function actionUpdaterep($id=0){
        #Reescribir metodo
        $isRequest = Yii::$app->request->isAjax;
        $postRequest = Yii::$app->request->post();
        $provider_id = Yii::$app->user->identity->providerid; //Provider::find()->where(['user_id'=>$user_id])->one();
        $representante_id = base64_decode($id,true) == false ? $id : intval(base64_decode($id));
        $representante = $this->findModelRepresentanteLegal($representante_id);
        $acta_constitutiva = $this->findModelActa($provider_id); //El modelo de ActaConstitutiva puede ser consultado si existe o creado si no
        $representante->scenario = RepresentanteLegal::SCENARIO_CREATE_REP_BYS;
        $representante->validado = empty($representante->validado) ? false : $representante->validado;
        $representante->provider_id = $provider_id;
        $acta_constitutiva->provider_id = $provider_id;

        $createMode = GeneralController::isNullOrEmpty($representante->representante_legal_id);

        $modelStatus = Module::getstatusbymodule('bys_legales', $provider_id);
        $rechazo = $modelStatus->status_bys == Status::STATUS_RECHAZADO  || $modelStatus->status_bys == Status::STATUS_ENEDICION  ?
            Status::find()->getRechazo( 'bys_legales', $provider_id) : null;

        /* Configuracion de fechas por el documento proporcionado */
        if( !self::isNullOrEmpty($representante->tipo_identificacion) && !self::isNullOrEmpty($representante->vencimiento_identificacion) ) {
            if( $representante->tipo_identificacion == 'IFE' ) $representante->dateIfe = substr($representante->vencimiento_identificacion,0,4);
            else if( $representante->tipo_identificacion == 'PASAPORTE' ) $representante->datePas = $representante->vencimiento_identificacion;
        }

        //Copia del modelo representante legal
        $modelo_old = $representante->getOldAttributes();

        if ( $isRequest && $representante->load($postRequest) ) {

            Yii::$app->response->format = Response::FORMAT_JSON;
            $result = [];
            if ( !self::isNullOrEmpty($representante->rfc) ) {
                $count_exist = RepresentanteLegal::find()->where(['and', ['provider_id' => $provider_id], ['rfc' => $representante->rfc], 
                    ['activo' => true],['not in','rfc',['XEXX010101000']]])->count('1');
                if ($count_exist > 0 && $id === 0) {
                    $result['representantelegal-rfc'] = ['Ya se encuentra el representante legal registrado.'];
                }
            }
            return array_merge($result,ActiveForm::validate($representante));

        } else if ( $representante->load($postRequest) ) {
            
            /* SET de fecha de vencimiento por el documento seleccionado */
            if($representante->tipo_identificacion == 'IFE' && !self::isNullOrEmpty($representante->dateIfe)){
                $representante->vencimiento_identificacion = $representante->dateIfe.'-12-31';
            }else if($representante->tipo_identificacion == 'PASAPORTE' && !self::isNullOrEmpty($representante->datePas)){
                $representante->vencimiento_identificacion = $representante->datePas;
            }

            /* Verificacion si es o no representante legal */
            if ( isset($_POST['RepresentanteLegal']['rep_bys']) && !self::isNullOrEmpty($_POST['RepresentanteLegal']['rep_bys']) ) {
                $representante->rep_bys = $_POST['RepresentanteLegal']['rep_bys'] === 'si' ? true : false;
            }

            $path_rl = 'bys/actividad_economica/representante_legal';
            $this->makeDir($path_rl);

            /* En este bloque se valida que en la peticion POST se este haciendo un cambio de documentos */
            if ( !self::isNullOrEmpty($representante->documento_identificacion) && GeneralController::str_contains($representante->documento_identificacion, "archivos_tmp") ) {
                $new_doc_id = str_replace('archivos_tmp/', $path_rl . '/', $representante->documento_identificacion);
                $this->copyFile($representante->documento_identificacion, $new_doc_id);
                $representante->documento_identificacion = $new_doc_id;
            }

            if ( !self::isNullOrEmpty($representante->documento_acta) && GeneralController::str_contains($representante->documento_acta, "archivos_tmp") ) {
                $new_doc_acta = str_replace('archivos_tmp/', $path_rl . '/', $representante->documento_acta);
                $this->copyFile($representante->documento_acta, $new_doc_acta);
                $representante->documento_acta = $new_doc_acta;
            }

            if ( !self::isNullOrEmpty($representante->documento_relacion_select) ) {
                $representante->documento_acta = $representante->documento_relacion_select;
            }

            /* Este bloque valida si se hizo un cambio en el modelo ActaConstitutiva, esto por si la instancia no habia sido
            creada previamente, se crea en este formulario */
            if ($acta_constitutiva->load($postRequest)) {

                $path_ac = 'bys/actividad_economica/acta_constitutiva';
                $this->makeDir($path_ac);

                if ( !self::isNullOrEmpty($acta_constitutiva->documento_acta) && GeneralController::str_contains($acta_constitutiva->documento_acta, "archivos_tmp") ) {
                    $new_nameActa = str_replace('archivos_tmp/', $path_ac . '/', $acta_constitutiva->documento_acta);
                    $this->copyFile($acta_constitutiva->documento_acta, $new_nameActa);
                    $acta_constitutiva->documento_acta = $new_nameActa;
                }

                $acta_constitutiva->save();
            }

            switch ($postRequest['RepresentanteLegal']['tipo_poder']) {
                case 'ACTA':
                    $representante->documento_acta = '0';
                    break;
                case 'ACTUALIZACIÓN':
                    $representante->documento_acta = $postRequest['RepresentanteLegal']['documento_relacion_select'];
                    break;
            }

            /* Se verifica si hubo un cambio con el modelo inicial y el modelo resultante de su actualizacion */
            $cambio_rfc = isset($modelo_old['rfc']) ? $modelo_old['rfc'] != $representante->rfc : true;
            $cambio_nombre = isset($modelo_old['nobre']) ? ( (trim($modelo_old['nombre']).' '.trim($modelo_old['ap_paterno']).' '.trim($modelo_old['ap_materno'])) != 
                ( trim($representante->nombre).' '.trim($representante->ap_paterno).' '.trim($representante->ap_materno) ) ) : true;
            

            if( $representante->save() ){
                /* Verifica si existe un firmante diferente al nuevo registro actual */
                $rfc_firmante = RepresentanteLegal::find()->select('rfc')->where(['and', ['provider_id' => $provider_id], ['rep_bys' => true],
                    ['activo' => true],['not in','representante_legal_id',$representante->representante_legal_id]])->one()['rfc'];
                
                //Si existe un firmante se toma el valor, de lo contrario el actual modelo se toma como firmante
                $rfc_firmante = !empty($rfc_firmante) ? $rfc_firmante : $representante->rfc ;

                /* 
                Condiciones:
                    - Si el representante es firmante y existe un firmante ya inscrito
                    - Si el representante es firmante y cambio algun dato en su nombre
                    - Si el representante es firmante y cambio su RFC
                    - (Nuevo) Si el representante dejo de serlo
                */
                if( ($representante->rep_bys && ($representante->rfc != $rfc_firmante || $cambio_nombre || $cambio_rfc)) || (isset($modelo_old['rep_bys']) && $representante->rep_bys != $modelo_old['rep_bys']) ){
                    Provider::updateAll(['status_carta_bys' => Status::CARTA_GENERADA], ['provider_id' => $provider_id]);
                }

                //Al ser el registro firmante, todos los otros registros cambian a no firmante, pues solo puede existir 1 por expediente
                if($representante->rep_bys){
                    RepresentanteLegal::updateAll(['rep_bys' => false],['and',['provider_id' => $provider_id],
                        ['not in','representante_legal_id',$representante->representante_legal_id]]);
                }

                $arr_moviments = [
                    'model' => 'representante_legal',
                    'action' => $createMode ? 'Crear Representante Legal' : 'Modificacion de Representante Legal',
                    'origin_id' => $representante->representante_legal_id,
                    'provider_id' => $representante->provider_id,
                    'column' => null,
                    'column_data_old' => null,
                    'column_date_new' => null,
                    'full_model' => true,
                    'data_model' => $modelo_old
                ];

                $this->insertAllMovements($arr_moviments);

                if( !$representante->validado ) { self::updateModulesBys($representante->provider_id,'bys_legales'); }
                return $this->redirect('view');

            }

        }

        /* Mapeo y filtrado de ModificacionActa disponibles */
        $mod_actas = ArrayHelper::map( ModificacionActa::find()->select(['modificacion_acta_id', 'nombre_documento'])
            ->where(['and', ['provider_id' => $provider_id], ['activo' => true] ])
            ->asArray()->all(), 'modificacion_acta_id', 'nombre_documento');

        return $this->renderAjax('updaterep', [
            'model' => $representante,
            'modelActa' => $acta_constitutiva,
            'rechazo' => $rechazo,
            'mods' => $mod_actas
        ]);

    }

    /* public function actionUpdaterep($id = 0)
    {

        if (Yii::$app->request->isAjax || Yii::$app->request->post()) {

            $model = $this->findModelRepresentanteLegal(intval(base64_decode($id)));
            $modelOld = $model->getAttributes();
            $modelOldMov = $model->attributes;
            $statusOld = $model->status_bys;
            $model->scenario = RepresentanteLegal::SCENARIO_CREATE_REP_BYS;
            $providerId = Yii::$app->user->identity->providerid;
            $modelActa = $this->findModelActa($providerId);
            $repRfcOld = null;
            $nameOld = null;
            $firmante = null;
            $model->validado = empty($model->validado) ? false : $model->validado;
            if(isset($model->rep_bys) && !empty($model->rep_bys)){
                $repRfcOld = $model->rfc;
                $nameOld = trim($model->nombre).' '.trim($model->ap_paterno).' '.trim($model->ap_materno);
                $firmante = $model->rep_bys;
            }

            $modelActa->documento_actaOLD = $modelActa->documento_acta;

            $model->documento_identificacionOLD = $model->documento_identificacion;
            $model->documento_actaOLD = $model->documento_acta;

            $tipo = null;

            if (isset($_POST['RepresentanteLegal']['rep_bys']) && !empty($_POST['RepresentanteLegal']['rep_bys'])) {
                $tipo = $_POST['RepresentanteLegal']['rep_bys'];
            }

            $t = Yii::$app->db->beginTransaction();
            if (Yii::$app->request->isAjax && $model->load(Yii::$app->request->post())) {
                Yii::$app->response->format = Response::FORMAT_JSON;
                $result = [];
                if (isset($model->rfc) && !empty($model->rfc)) {
                    $count_exist = RepresentanteLegal::find()
                        ->where(['and', ['provider_id' => $providerId], ['rfc' => $model->rfc], ['activo' => true],['not in','rfc',['XEXX010101000']]])
                        ->count('1');
                    if ($count_exist > 0 && $id === 0) {
                        $result['representantelegal-rfc'] = ['Ya se encuentra el representante legal registrado.'];
                    }
                }
                return array_merge($result,ActiveForm::validate($model));
            } else if ($model->load(Yii::$app->request->post())) {
                $modelNew = $model->getAttributes();
                if($statusOld=='VALIDADO'){
                    $updateStatus = $this->verifyChange($modelOld,$modelNew);
                    if($updateStatus && !$model->validado){
                        $model->status_bys = 'EN EDICION';
                    }
                }

                $tipoIdent = $model->tipo_identificacion;

                if($tipoIdent == 'IFE' && isset($model->dateIfe) && !empty($model->dateIfe)){
                    $model->vencimiento_identificacion = $model->dateIfe.'-12-31';
                }else if($tipoIdent == 'PASAPORTE' && isset($model->datePas) && !empty($model->datePas)){
                    $model->vencimiento_identificacion = $model->datePas;
                }

                $pathRL = 'bys/actividad_economica/representante_legal';
                $this->makeDir($pathRL);
                $model->provider_id = $providerId;

                if ($tipo == 'si' || $tipo == 'no') {
                    $model->rep_bys = $tipo === 'si' ? true : false;
                }

                if (!empty($model->documento_identificacion) && $model->documento_identificacion != $model->documento_identificacionOLD) {
                    $new_nameDi = str_replace('archivos_tmp/', $pathRL . '/', $model->documento_identificacion);
                    $this->copyFile($model->documento_identificacion, $new_nameDi);
                    $model->documento_identificacion = $new_nameDi;
                }

                if (!empty($model->documento_acta) && $model->documento_acta != $model->documento_actaOLD) {
                    $new_nameDocA = str_replace('archivos_tmp/', $pathRL . '/', $model->documento_acta);
                    $this->copyFile($model->documento_acta, $new_nameDocA);
                    $model->documento_acta = $new_nameDocA;
                }

                if (!empty($model->documento_relacion_select)) {
                    $model->documento_acta = $model->documento_relacion_select;
                }

                $verify_save = true;
                if ($modelActa->load(Yii::$app->request->post())) {

                    $pathA = 'bys/actividad_economica/acta_constitutiva';
                    $this->makeDir($pathA);
                    $modelActa->provider_id = $providerId;

                    if (!empty($modelActa->documento_acta) && $modelActa->documento_acta != $modelActa->documento_actaOLD) {
                        $new_nameActa = str_replace('archivos_tmp/', $pathA . '/', $modelActa->documento_acta);
                        $this->copyFile($modelActa->documento_acta, $new_nameActa);
                        $modelActa->documento_acta = $new_nameActa;
                    }

                    if (!$modelActa->save()) {
                        $verify_save = false;
                    }
                }


                switch (Yii::$app->request->post()['RepresentanteLegal']['tipo_poder']) {
                    case 'ACTA':
                        $model->documento_acta = '0';
                        break;
                    case 'ACTUALIZACIÓN':
                        $model->documento_acta = Yii::$app->request->post()['RepresentanteLegal']['documento_relacion_select'];
                        break;
                }

                if ($model->save() && $verify_save) {
                    $repRfcNew = $model->rfc;
                    $nameNew = trim($model->nombre).' '.trim($model->ap_paterno).' '.trim($model->ap_materno);

                    $rfcVerify = RepresentanteLegal::find()->select('rfc')->where(['and', ['provider_id' => $providerId], ['rep_bys' => true],
                        ['activo' => true],['not in','representante_legal_id',$model->representante_legal_id]])->one()['rfc'];

                    $rfcVerify = !empty($rfcVerify) ? $rfcVerify : $model->rfc ;

                    if ( (!empty($model->rfc) && $model->rfc != '' && $model->rep_bys &&  $model->rfc != $rfcVerify) || ($nameOld!=null && $nameOld!=$nameNew) || (!empty($firmante) && $firmante!=$model->rep_bys) ) {
                        Provider::updateAll(['status_carta_bys' => 'PENDIENTE'], ['provider_id' => $model->provider_id]);
                    }

                    if($model->rep_bys){
                        RepresentanteLegal::updateAll(['rep_bys' => false],['and',['provider_id' => $providerId],['not in','representante_legal_id',$model->representante_legal_id]]);
                    }

                    $modelNewMov = $model->attributes;
                    $countValCert = $this->verifyProviderCert($model->provider_id, 'bys');
                    $countValRec = self::verifyValRec('representante_legal',$model->representante_legal_id);

                    if ($countValCert > 0 || $countValRec>0) {
                        $trueFalse = $countValCert>0?true:false;
                        if($id && $id!==0){
                            $this->compareModels($modelOldMov, $modelNewMov, $model->representante_legal_id, 'representante_legal',$model->provider_id,$trueFalse);
                        }else{
                            $arr_moviments = [
                                'model' => 'representante_legal',
                                'action' => 'Crear Representante Legal',
                                'origin_id' => $model->representante_legal_id,
                                'provider_id' => $model->provider_id,
                                'column' => null,
                                'column_data_old' => null,
                                'column_date_new' => null,
                                'full_model' => true,
                                'data_model' => $model->attributes
                            ];
                            $this->insertAllMovements($arr_moviments);
                        }
                    }


                    $t->commit();
                    self::eliminarCita();
                    if( !$model->validado ) { self::updateModulesBys($model->provider_id,'bys_legales'); }
                    return $this->redirect('view');
                }
                $t->rollBack();
                return $this->goHome();

            }

            $modelStatus = Module::getstatusbymodule('bys_legales',$providerId);
            $rechazo = $modelStatus->status_bys == Status::STATUS_RECHAZADO || $modelStatus->status_bys == Status::STATUS_ENEDICION  ?
                Status::find()->getRechazo( 'bys_legales', $providerId) : null;

            $mods = ArrayHelper::map(
                ModificacionActa::find()->select(['modificacion_acta_id', 'nombre_documento'])
                    ->where(['and', ['provider_id' => Yii::$app->user->identity->providerid], ['activo' => TRUE], ['in','status_bys',[Status::STATUS_PORVALIDAR,Status::STATUS_VALIDADO,Status::STATUS_ENEDICION]]])
                    ->asArray()->all(), 'modificacion_acta_id', 'nombre_documento');

            if(isset($model->tipo_identificacion) && $model->tipo_identificacion == 'IFE' && isset($model->vencimiento_identificacion) && !empty($model->vencimiento_identificacion)){
                $model->dateIfe = substr($model->vencimiento_identificacion,0,4);
            }else if(isset($model->tipo_identificacion) && $model->tipo_identificacion == 'PASAPORTE' && isset($model->vencimiento_identificacion) && !empty($model->vencimiento_identificacion)){
                $model->datePas = $model->vencimiento_identificacion;
            }

            return $this->renderAjax('updaterep', [
                'model' => $model,
                'modelActa' => $modelActa,
                'rechazo' => $rechazo,
                'mods' => $mods
            ]);

        }

        return $this->goHome();

    } */

    public function actionViewrep($id = null)
    {

        $model = RepresentanteLegal::find()->where(['and', ['provider_id' => Yii::$app->user->identity->providerid], ['representante_legal_id' => intval(base64_decode($id))]])->one();
        if ($model) {
            return $this->renderAjax('viewrep', [
                'model_representante_legal' => $model,
                'model_registro_acta' => $this->findModelActa(Yii::$app->user->identity->providerid),
            ]);
        }
        return json_encode(['status' => 'No permitido']);
    }

    public function actionDeleterepresentante($id = 0) {

        $representante_id = base64_decode($id,true) == false ? $id : intval(base64_decode($id));
        $representante = RepresentanteLegal::findOne(['representante_legal_id' => $representante_id]);
        
        if($representante){
            if ($this->verifyProviderCert($representante->provider_id, 'bys') > 0) {
                $arr_moviments = [
                    'provider_id' => $representante->provider_id,
                    'model' => 'representante_legal',
                    'action' => 'Eliminó Representante Legal',
                    'origin_id' => $representante->representante_legal_id,
                    'column' => null,
                    'column_data_old' => null,
                    'column_date_new' => null,
                    'full_model' => true,
                    'data_model' => $representante->attributes
                ];
                $this->insertAllMovements($arr_moviments);
            }

            if($representante->rep_bys){
                Provider::updateAll(['status_carta_bys' => Status::CARTA_GENERADA], ['provider_id' => $representante->provider_id]);
            }

            $representante->rep_bys = false;
            $representante->activo = false;
            $representante->save(false);

            Yii::$app->session->setFlash('success', 'Registro eliminado correctamente.');

        }else{
            Yii::$app->session->setFlash('error', 'Error al eliminar registro.');
        }

        return $this->redirect(['view']);

    }


    public function actionTerminarrep($id = null)
    {

        $IdLastVal = $this->saveLastSendValidation(Yii::$app->user->identity->getProvider(),'validRepresentante','representante_legal','bys');

        if ($id != null) {
            $status = $this->terminarUno('RepresentanteLegal', intval(base64_decode($id)), 'representante_legal_id', 'representante_legal', 'status_bys','bys',$IdLastVal);
        } else {
            $status = $this->terminarTodos('RepresentanteLegal', 'representante_legal_id', 'representante_legal', 'status_bys','bys',$IdLastVal);
        }

        if ($status) {
            $correos_validadores = self::getEmailValidador(1, 'bys');

            self::sendEmail('/provider/correos/nuevo', null, $correos_validadores, 'Nuevo proveedor', ['modulo' => 'Representante Legal', 'tipo_provider' => 'bys']);

            self::AllSendNotification(null, 'bys', null, 'DATOS LEGALES', 'Representante Legal');

            GeneralController::allModulesComplete(Yii::$app->user->identity->getProvider());

        }


        return $this->redirect('view#representante');
    }

    public function actionUpdateAccionista($id, $tipo)
    {
        //$id_provider = Provider::find()->select('provider_id')->where(['user_id' => Yii::$app->user->id])->one()->provider_id;
        $model = $this->findModelAccionista($id);
        $id = Provider::find()->select('provider_id')->where(['user_id' => Yii::$app->user->id])->one()->provider_id;
        $model->provider_id = $id;
        $id_register = $model->relacion_accionistas_id;
        /* $rechazo = [];
        if (!empty($id_register)) {
            $rechazo = Status::find()->getStatus($id_register, 'relacion_accionistas', $tipo);
        } */

        $modelStatus = Module::getstatusbymodule('bys_legales',$id);
        $rechazo = $modelStatus->status_bys == Status::STATUS_RECHAZADO || $modelStatus->status_bys == Status::STATUS_ENEDICION  ?
            Status::find()->getRechazo( 'bys_legales', $id) : null;

        if (Yii::$app->request->isAjax && $model->load(Yii::$app->request->post())) {
            Yii::$app->response->format = Response::FORMAT_JSON;

            $result = [];
            if(isset($model->provider_id) && !empty($model->provider_id) &&
                isset($model->curp) && !empty($model->curp)){
                $count_exist = RelacionAccionistas::find()
                    ->where(['and',['provider_id' => $model->provider_id],['curp' => $model->curp],['activo' => true],['not in','rfc',['XEXX010101000']]])
                    ->count('1');
                if($count_exist>0){
                    $result['relacionaccionistas-rfc'] = ['Ya se encuentra el accionista registrado'];
                }
            }
            return array_merge($result,ActiveForm::validate($model));
        } else if ($model->load(Yii::$app->request->post())) {
            $porcentaje = 0;
            if (isset($_POST['porcentaje_total_m']) && $_POST['porcentaje_total_m'] != '') {
                $porcentaje = round($_POST['porcentaje_total_m']);
            }
            $model->porcentaje = $porcentaje;
            if ($model->save()) {
                if (($requi_status = Status::find()->where(['and', ['register_id' => $id_register], ['status_bys' => Status::STATUS_PENDIENTE], ['modelo' => 'relacion_accionistas']])->one()) !== null) {
                    $requi_status->status_bys = Status::STATUS_TERMINADO_PRO;
                    $requi_status->save();
                }
            }
            self::updateModulesBys($model->provider_id,'bys_legales');
            return $this->redirect(['view', 'id' => base64_encode($id)]);
        } else {
            $find_acta = RelacionAccionistas::findActa($id);
            return $this->renderAjax('update', [
                'model' => $model,
                'rechazo' => $rechazo,
                'find_acta' => $find_acta
            ]);
        }
    }

    public function actionIndexValidador()
    {
        $searchModel = new RfcSearch();
        $searchPropiedad = new EscrituraPublicaSearch();
        $searchActaConstitutiva = new ActaConstitutivaSearch();
        $searchHacienda = new AltaHaciendaSearch();
        $searchRelacionAccionistas = new RelacionAccionistasSearch();
        $searchProvider = new ProviderSearch();
        $dataProvider = $searchModel->search(Yii::$app->request->queryParams, 'mainFilter');
        $datosPropiedad = $searchPropiedad->search(Yii::$app->request->queryParams, 'mainPropiedad');
        $datosHacienda = $searchHacienda->search(Yii::$app->request->queryParams, 'mainHacienda');
        $datosRelacionAccionistas = $searchProvider->search(Yii::$app->request->queryParams, 'validRelacionAccionistas');


        return $this->render('index-validador', [
            'searchModel' => $searchModel,
            'searchPropiedad' => $searchPropiedad,
            'searchActaConstitutiva' => $searchActaConstitutiva,
            'searchHacienda' => $searchHacienda,
            'dataProvider' => $dataProvider,
            'datosPropiedad' => $datosPropiedad,
            'datosHacienda' => $datosHacienda,
            'datosRelacionAccionistas' => $datosRelacionAccionistas,
            'searchProvider' => $searchProvider,
        ]);
    }

    public function actionTerminar($id = null)
    {
        $id = intval(base64_decode($id));
        if ($id == null || !Provider::verifyProvider($id) || !Provider::verifyEtapa2($id) || !self::verifyTerminar($id)) {
            echo $this->render('/site/error', ['name' => 'Lo sentimos...  Debe completar todo el formulario para poder enviar a revisar', 'message' => 'Acceso denegado']);

            return false;
        }
        $model = $this->findModel($id);
        if ($model->status_bys != Status::STATUS_ENEDICION) {
            echo $this->render('/site/error', ['name' => 'Lo sentimos...', 'message' => 'Acceso denegado']);

            return false;
        }

        $status = 'status_bys';

        $rfc = Yii::$app->db->createCommand();
        $rfc->update('provider.rfc', [$status => Status::STATUS_PORVALIDAR], 'provider_id =' . $id);
        $rfc->execute();

        ExpirationDocuments::updateAll(['status' => true],['provider_id' => $id, 'module' => 'rfc']);


        $IdLastVal = $this->saveLastSendValidation($id,'mainFilterLegales','rfc','bys');
        $this->saveLastSendValidationRelation($IdLastVal,$id,'rfc',$id,'bys');


        $correos_validadores = self::getEmailValidador(1, 'bys');

        self::sendEmail('/provider/correos/nuevo', null, $correos_validadores, 'Nuevo proveedor', ['modulo' => 'Datos legales', 'tipo_provider' => 'bys']);

        self::AllSendNotification(null, 'bys', null, 'DATOS LEGALES', 'Datos legales');

        GeneralController::allModulesComplete($id);

        return $this->redirect('../../provider/dashboard');
    }

    public function actionTerminarAccionista($id)
    {
        $id_user = Provider::find()->select('provider_id')->where(['user_id' => Yii::$app->user->id])->one()->provider_id;
        $status_global = 'status_bys';
        $rfc = Yii::$app->db->createCommand();
        $rfc->update('provider.relacion_accionistas', [$status_global => Status::STATUS_PORVALIDAR], 'relacion_accionistas_id =' . $id);
        $rfc->execute();

        $correos_validadores = self::getEmailValidador(1, 'bys');

        self::sendEmail('/provider/correos/nuevo', null, $correos_validadores, 'Nuevo proveedor', ['modulo' => 'Relación accionistas', 'tipo_provider' => 'bys']);


        return $this->redirect(['view', 'id' => base64_encode($id_user)]);
    }


    public function actionConfirm()
    {

        $id = Yii::$app->request->post()['provider_id'];
        $rfc = Yii::$app->db->createCommand();
        $rfc->update('provider.rfc', ['status_bys' => Status::STATUS_VALIDADO], 'provider_id =' . $id);
        $rfc->execute();

        if (($requi_status = Status::find()->where(['and', ['register_id' => $id], ['status_bys' => Status::STATUS_PENDIENTE], ['modelo' => 'RFC']])->one()) !== null) {
            $requi_status->status_bys = Status::STATUS_TERMINADO_PRO;
            $requi_status->save();

        }

        Yii::$app->session->setFlash('success', 'Los datos se validaron correctamente');
        return Yii::$app->getResponse()->redirect(['datosLegales/rfc/index-validador']);

    }

    public function actionViewValidar($id = null)
    {
        $queryParams = Yii::$app->request->queryParams;
        $id = isset($queryParams['id']) ? intval( $queryParams['id'] ) : null;
        $opcionesArray = isset($queryParams['opciones']) ? $queryParams['opciones'] : null;
        $opcionesSerial = isset($opcionesArray) ? serialize($opcionesArray) : null;
        $opcionesBase64 = isset($opcionesSerial) ? base64_encode($opcionesSerial) : null;

        if ($id == null || strlen($id) > 15 || !Provider::verifyProviderExistence($id)) {
            $this->msgError();
        }
        $modelos = [];
        $model = $this->findModel($id);
        $model_curp = $this->findModelCurp($id);
        $model_idoficial = $this->findModelIdoficial($id);
        $acta_constitutiva = $this->findModelActaConstitutiva($id);
        $model_status = new Status();
        $model_status->scenario = Status::SCENARIO_CREATE;
        $model_validado = new DatosValidados();
        $model_validado->scenario = DatosValidados::SCENARIO_CREATE;

        if (isset($model->provider_id) && !empty($model->provider_id)) {
            $modelos[$model->formName()] = $model->attributes;
        }
        if (isset($model_curp->provider_id) && !empty($model_curp->provider_id)) {
            $modelos[$model_curp->formName()] = $model_curp->attributes;
        }
        if (isset($model_idoficial->provider_id) && !empty($model_idoficial->provider_id)) {
            $modelos[$model_idoficial->formName()] = $model_idoficial->attributes;
        }
        if (isset($acta_constitutiva->provider_id) && !empty($acta_constitutiva->provider_id)) {
            $modelos[$acta_constitutiva->formName()] = $acta_constitutiva->attributes;
        }

        $rfc = Provider::find()->select('rfc')->where(['provider_id' => $id])->one()->rfc;
        $rechazo = Status::find()->getStatus($id, 'rfc', 'bys', 'TERMINADO PRO');

        $movements = $this->countAllMovementRfc($id);
        return $this->renderAjax('view-validar', [
            'model' => $model,
            'model_curp' => $model_curp,
            'model_idoficial' => $model_idoficial,
            'rfc' => $rfc,
            'model_status' => $model_status,
            'model_validado' => $model_validado,
            'modelos' => base64_encode(json_encode($modelos)),
            'model_acta_constitutiva' => $acta_constitutiva,
            'rechazo' => $rechazo,
            'namePro' => $this->getNameProvider($id),
            'movements' => $movements,
            'id' => $id,
            'opciones' => $opcionesBase64,
            'moduleName' => 'rfc'
        ]);

    }


    public function actionViewValidarep($id = 0)
    {
        $queryParams = Yii::$app->request->queryParams;
        $id = isset($queryParams['id']) ? intval( base64_decode($queryParams['id']) ) : 0;
        $opcionesArray = isset($queryParams['opciones']) ? $queryParams['opciones'] : null;
        $opcionesSerial = isset($opcionesArray) ? serialize($opcionesArray) : null;
        $opcionesBase64 = isset($opcionesSerial) ? base64_encode($opcionesSerial) : null;
        $model = $this->findModelRepresentanteLegalVal($id);

        $model_status = new Status();
        $model_status->scenario = Status::SCENARIO_CREATE;
        $modelValidado = new DatosValidados();
        $modelValidado->scenario = DatosValidados::SCENARIO_CREATE;
        $modelos = [];

        if (Yii::$app->user->can(Usuarios::ROLE_VALIDADOR_PROVEEDORES) || Yii::$app->user->can(Usuarios::ROLE_ADMIN_PROVIDER)) {
            if (isset($model->provider_id) && !empty($model->provider_id)) {
                $modelos[$model->formName()] = $model->attributes;
            }
            $modelos = base64_encode(json_encode($modelos));

        }

        $rechazo = Status::find()->getStatus($model->representante_legal_id, 'representante_legal', 'bys', 'TERMINADO PRO');

        $rpSearch = new RepresentanteLegalSearch();
        $rpSearch->provIdVal = $model->provider_id;
        $dataRpSearch = $rpSearch->search(Yii::$app->request->queryParams,'validadosFilter');
        $movements = $this->countAllMovements($id,'representante_legal');
        return $this->renderAjax('view-validarep', [
            'model_representante_legal' => $model,
            'model_registro_acta' => $this->findModelActa($model->provider_id),
            'modelos' => $modelos,
            'modelStatus' => $model_status,
            'modelValidado' => $modelValidado,
            'rechazo' => $rechazo,
            'namePro' => $this->getNameProvider($model->provider_id),
            'movements' => $movements,
            'id' => $id,
            'opciones' => $opcionesBase64,
            'moduleName' => 'representante_legal',
            'dataRpSearch' => $dataRpSearch
        ]);
    }

    public function actionViewValidaraccionistas($id = null, $user_id = null)
    {
        $queryParams = Yii::$app->request->queryParams;
        $id = isset($queryParams['id']) ? intval( base64_decode($queryParams['id']) ) : 0;
        $user_id = isset($queryParams['user_id']) ? intval( $queryParams['user_id'] ) : 0;
        $opcionesArray = isset($queryParams['opciones']) ? $queryParams['opciones'] : null;
        $opcionesSerial = isset($opcionesArray) ? serialize($opcionesArray) : null;
        $opcionesBase64 = isset($opcionesSerial) ? base64_encode($opcionesSerial) : null;

        $model = $this->findModelAccionista($id);
        $model_status = new Status();
        $model_status->scenario = Status::SCENARIO_CREATE;
        $modelValidado = new DatosValidados();
        $modelValidado->scenario = DatosValidados::SCENARIO_CREATE;
        $modelos = [];
        $rechazo = [];
        if (!empty($model->relacion_accionistas_id)) {
            $rechazo = Status::find()->getStatus($model->relacion_accionistas_id, 'relacion_accionistas', 'bys', 'TERMINADO PRO');
        }

        if (Yii::$app->user->can(Usuarios::ROLE_VALIDADOR_PROVEEDORES) || Yii::$app->user->can(Usuarios::ROLE_ADMIN_PROVIDER)) {
            if (isset($model->provider_id) && !empty($model->provider_id)) {
                $modelos[$model->formName()] = $model->attributes;
            }
            $modelos = base64_encode(json_encode($modelos));

        }

        $movements = $this->countAllMovements($id,'relacion_accionistas');
        $accSearch = new RelacionAccionistasSearch();
        $accSearch->provIdVal = $model->provider_id;
        $dataAccSearch = $accSearch->search(Yii::$app->request->queryParams,'validadosFilter');
        return $this->renderAjax('view-validaraccionistas', [
            'model' => $model,
            'modelos' => $modelos,
            'modelStatus' => $model_status,
            'modelValidado' => $modelValidado,
            'rechazo' => $rechazo,
            'namePro' => $this->getNameProvider($model->provider_id),
            'movements' => $movements,
            'id' => $id,
            'opciones' => $opcionesBase64,
            'moduleName' => 'relacion_accionistas',
            'dataAccSearch' => $dataAccSearch
        ]);

    }


    public function actionViewAccionista($id)
    {
        return $this->renderAjax('viewaccionista', [
            'model' => $this->findModelAccionista($id),
            'tipo' => 'bys',
        ]);
    }

    public function actionEnviar($user_id)
    {
        $status_global = 'status_bys';

        RelacionAccionistas::updateAll([$status_global => Status::STATUS_PORVALIDAR], ['and', [$status_global => Status::STATUS_ENEDICION], ['provider_id' => $user_id], ['porcentaje' => 100]]);
        return $this->redirect(['view', 'id' => base64_encode($user_id)]);

    }


    //accionistas

    public function actionViewaccionistas($id = null)
    {

        $model = RelacionAccionistas::find()->where(['and', ['provider_id' => Yii::$app->user->identity->providerid], ['relacion_accionistas_id' => intval(base64_decode($id))]])->one();
        if ($model) {
            return $this->renderAjax('viewaccionistas', [
                'model' => $model
            ]);
        }
        //return json_encode(['status' => 'No permitido']);
        return $this->goHome();
    }


    public function actionUpdateaccionistas($id = 0)
    {
        if (Yii::$app->request->isAjax || Yii::$app->request->post()) {

            $model = $this->findModelAccionistas(intval(base64_decode($id)));
            $modelOld = $model->attributes;
            $model->documento_relacionOLD = $model->documento_relacion;
            $model->provider_id = Yii::$app->user->identity->providerid;
            $model->scenario = RelacionAccionistas::SCENARIO_BYS;
            if (Yii::$app->request->isAjax && $model->load(Yii::$app->request->post())) {
                Yii::$app->response->format = Response::FORMAT_JSON;
                $result = [];
                if (!empty($model->provider_id) && !empty($model->rfc)) {
                    $count_exist = RelacionAccionistas::find()
                        ->where(['and', ['provider_id' => $model->provider_id], ['rfc' => $model->rfc], ['activo' => true],['not in','rfc',['XEXX010101000']]])
                        ->count('1');
                    if ($count_exist > 0 && $id === 0) {
                        $result['relacionaccionistas-rfc'] = ['Ya se encuentra el accionista registrado.'];
                    }
                }
                if(!empty($model->rfc)){
                    if(strlen($model->rfc) == 12 || (strlen($model->rfc) == 13 && Yii::$app->request->post('optradio') == 'M' )){
                        if(empty($model->razon_social)){ $result['relacionaccionistas-razon_social'] = ['Razón Social no puede estar vacío']; }
                    }else{
                        //if(empty($model->curp) && $model->rfc != 'XAXX010101000'){ $result['relacionaccionistas-curp'] = ['Curp no puede estar vacío']; }
                        if(empty($model->nombre)){ $result['relacionaccionistas-nombre'] = ['Nombre no puede estar vacío']; }
                        if(empty($model->ap_paterno)){ $result['relacionaccionistas-ap_paterno'] = ['Primer apellido no puede estar vacío']; }
                        if(empty($model->ap_materno)){ $result['relacionaccionistas-ap_materno'] = ['Segundo apellido no puede estar vacío']; }
                    }
                }
                return array_merge($result,ActiveForm::validate($model));
            } else if ($model->load(Yii::$app->request->post())) {

                $model->status_bys = Status::STATUS_ENEDICION;
                $path = Yii::$app->user->identity->tipo . '/datos_legales/accionistas/';

                $this->makeDir($path);

                if (!empty($model->documento_relacion) && $model->documento_relacion != $model->documento_relacionOLD) {
                    $new_nameRelacion = str_replace('archivos_tmp/', $path . '/', $model->documento_relacion);
                    $this->copyFile($model->documento_relacion, $new_nameRelacion);
                    $model->documento_relacion = $new_nameRelacion;
                }

                switch (Yii::$app->request->post()['RelacionAccionistas']['tipo_relacion']) {
                    case 'ACTA':
                        $model->documento_relacion = '0';
                        break;
                    case 'MODIFICACION':
                        $model->documento_relacion = Yii::$app->request->post()['RelacionAccionistas']['documento_relacion_select'];
                        break;
                }

                if (!$model->save()) {
                    return $this->goHome();
                }

                $countValCert = $this->verifyProviderCert($model->provider_id, 'bys');
                $countValRec = self::verifyValRec('relacion_accionistas',$model->relacion_accionistas_id);

                if ($countValCert > 0 || $countValRec>0) {
                    $trueFalse = $countValCert>0?true:false;
                    if($id && $id!==0){
                        $this->compareModels($modelOld, $model->attributes, $model->relacion_accionistas_id, 'relacion_accionistas',$model->provider_id,$trueFalse);
                    }else{
                        $arr_moviments = [
                            'provider_id' => $model->provider_id,
                            'model' => 'relacion_accionistas',
                            'action' => 'Creacion Relación accionistas',
                            'origin_id' => $model->relacion_accionistas_id,
                            'column' => null,
                            'column_data_old' => null,
                            'column_date_new' => null,
                            'full_model' => true,
                            'data_model' => $model->attributes
                        ];
                        $this->insertAllMovements($arr_moviments);
                    }
                }

                self::eliminarCita();
                self::updateModulesBys($model->provider_id,'bys_legales');
                $this->redirect('view#accionistas');
            }

            $mods = ArrayHelper::map(
                ModificacionActa::find()->select(['modificacion_acta_id', 'nombre_documento'])
                    ->where(['and', ['provider_id' => Yii::$app->user->identity->providerid], ['activo' => TRUE], ['in','status_bys',[Status::STATUS_PORVALIDAR,Status::STATUS_VALIDADO,Status::STATUS_ENEDICION]]])
                    ->asArray()->all(), 'modificacion_acta_id', 'nombre_documento');

            $modelStatus = Module::getstatusbymodule('bys_legales',$model->provider_id);
            $rechazo = $modelStatus->status_bys == Status::STATUS_RECHAZADO || $modelStatus->status_bys == Status::STATUS_ENEDICION  ?
                Status::find()->getRechazo( 'bys_legales', $model->provider_id) : null;


            return $this->renderAjax('updateaccionistas', [
                'model' => $model,
                'modificaciones' => $mods,
                'rechazo' => $rechazo
            ]);
        }

        return $this->goHome();

    }


    public function actionDeleteaccionistas($id = 0)
    {

        $model = RelacionAccionistas::find()
            ->where(['and', ['provider_id' => Yii::$app->user->identity->providerid], ['relacion_accionistas_id' => intval(base64_decode($id))]])->one();

        if ($model) {
            if($this->verifyProviderCert($model->provider_id,'bys')>0){
                $arr_moviments = [
                    'provider_id' => $model->provider_id,
                    'model' => 'relacion_accionistas',
                    'action' => 'Eliminó Accionistas',
                    'origin_id' => $model->relacion_accionistas_id,
                    'column' => null,
                    'column_data_old' => null,
                    'column_date_new' => null,
                    'full_model' => true,
                    'data_model' => $model->attributes
                ];
                $this->insertAllMovements($arr_moviments);
            }
            $model->activo = false;
            if(!$model->save(false)){ return json_encode($model->errors); }
            Yii::$app->session->setFlash('success', 'Registro eliminado correctamente.');
            return $this->redirect(['view']);
        }
        Yii::$app->session->setFlash('error', 'Error al eliminar registro.');
        return $this->redirect(['view']);
       // return json_encode(['status' => 'No permitido']);
    }

/*
    public function actionTerminaraccionistas($id = null)
    {

        $IdLastVal = $this->saveLastSendValidation(Yii::$app->user->identity->getProvider(),'validRelacionAccionistas','relacion_accionistas','bys');


        if ($id != null) {
            $status = $this->terminarUno('RelacionAccionistas', intval(base64_decode($id)), 'relacion_accionistas_id', 'relacion_accionistas', 'status_bys','',$IdLastVal);
        } else {
            $status = $this->terminarTodos('RelacionAccionistas', 'relacion_accionistas_id', 'relacion_accionistas', 'status_bys','',$IdLastVal);
        }

        if ($status) {
            $correos_validadores = self::getEmailValidador(1, 'bys');

            self::sendEmail('/provider/correos/nuevo', null, $correos_validadores, 'Nuevo proveedor', ['modulo' => 'Relación accionistas', 'tipo_provider' => 'bys']);

            self::AllSendNotification(null, 'bys', null, 'DATOS LEGALES', 'Relación accionistas');

            GeneralController::allModulesComplete(Yii::$app->user->identity->getProvider());
        }


        return $this->redirect('view#accionistas');
    }
*/

    protected function findModelAccionistas($id)
    {
        $model = RelacionAccionistas::find()->where(['and', ['provider_id' => Yii::$app->user->identity->providerid], ['relacion_accionistas_id' => $id]])->one();
        if (!$model) {
            $model = new RelacionAccionistas();
        }
        return $model;
    }


    public function actionRelacion($id = null)
    {
        if ($id != 'MODIFICACION') {
            return false;
        }
        $mods = ArrayHelper::map(
            ModificacionActa::find()->select(['modificacion_acta_id', 'nombre_documento'])
                ->where(['and', ['provider_id' => Yii::$app->user->identity->providerid], ['activo' => TRUE], ['in','status_' . Yii::$app->user->identity->tipo ,[Status::STATUS_PORVALIDAR,Status::STATUS_VALIDADO,Status::STATUS_ENEDICION]]])
                ->asArray()->all(), 'modificacion_acta_id', 'nombre_documento');

        $selector = '<option value=""></option>';
        foreach ($mods as $key => $value) {
            $selector .= '<option value="' . $key . '">' . $value . '</option>';
        }
        echo $selector;

    }


    //Modificaciones


    public function actionViewmod($id = null)
    {

        $model = ModificacionActa::find()->where(['and', ['provider_id' => Yii::$app->user->identity->providerid], ['modificacion_acta_id' => intval(base64_decode($id))]])->one();
        if ($model) {
            return $this->renderAjax('viewmod', [
                'model' => $model
            ]);
        }
        //return json_encode(['status' => 'No permitido']);
    }


//    public function actionViewValidarmod($id = 0)
//    {
//
//        $model = ModificacionActa::find()->where(['modificacion_acta_id' => intval(base64_decode($id))])->one();
//        if (Yii::$app->user->isGuest || $id == null || !Yii::$app->user->can(Usuarios::ROLE_VALIDADOR_CONTRATISTAS) || !$model) {
//            $this->redirect('/');
//        }
//        $status = new Status();
//        $status->scenario = Status::SCENARIO_CREATE;
//        $validados = new DatosValidados();
//        $validados->scenario = DatosValidados::SCENARIO_CREATE;
//        return $this->renderAjax('view-validaraccionistas', [
//            'modelStatus' => $status,
//            'modelValidado' => $validados,
//            'model' => $model
//        ]);
//
//    }


    public function actionUpdatemod($id = 0)
    {
        if (Yii::$app->request->isAjax || Yii::$app->request->post()) {

            $model = $this->findModelMod(intval(base64_decode($id)));
            $modelOld = $model->attributes;
            $model->documento_actaOLD = $model->documento_acta;
            $model->provider_id = Yii::$app->user->identity->providerid;

            $modelStatus = Module::getstatusbymodule('bys_legales',$model->provider_id);
            $rechazo = $modelStatus->status_bys == Status::STATUS_RECHAZADO || $modelStatus->status_bys == Status::STATUS_ENEDICION  ?
                Status::find()->getRechazo( 'bys_legales', $model->provider_id) : null;

            if (Yii::$app->request->isAjax && $model->load(Yii::$app->request->post())) {
                Yii::$app->response->format = Response::FORMAT_JSON;
                return ActiveForm::validate($model);
            } else if ($model->load(Yii::$app->request->post())) {
                $model->status_bys = Status::STATUS_ENEDICION;
                $path = Yii::$app->user->identity->tipo . '/datos_legales/modificaciones/';

                $this->makeDir($path);

                if (!empty($model->documento_acta) && $model->documento_acta != $model->documento_actaOLD) {
                    $new_nameActa = str_replace('archivos_tmp/', $path . '/', $model->documento_acta);
                    $this->copyFile($model->documento_acta, $new_nameActa);
                    $model->documento_acta = $new_nameActa;
                }

                if (!$model->save()) {
                    return $this->goHome();
                }

                $countValCert = $this->verifyProviderCert($model->provider_id, 'bys');
                $countValRec = self::verifyValRec('modificacion_acta',$model->modificacion_acta_id);

                if ($countValCert > 0 || $countValRec>0) {
                    $trueFalse = $countValCert>0?true:false;
                    if($id && $id!==0){
                        $this->compareModels($modelOld, $model->attributes, $model->modificacion_acta_id, 'modificacion_acta',$model->provider_id,$trueFalse);
                    }else{
                        $arr_moviments = [
                            'provider_id' => $model->provider_id,
                            'model' => 'modificacion_acta',
                            'action' => 'Creacion Modificación acta',
                            'origin_id' => $model->modificacion_acta_id,
                            'column' => null,
                            'column_data_old' => null,
                            'column_date_new' => null,
                            'full_model' => true,
                            'data_model' => $model->attributes
                        ];
                        $this->insertAllMovements($arr_moviments);
                    }
                }

                self::eliminarCita();
                self::updateModulesBys($model->provider_id,'bys_legales');
                $this->redirect('view#modificaciones');
            }

            /*$rechazo = [];
            if (!empty($model->modificacion_acta_id)) {
                $rechazo = Status::find()->getStatus($model->modificacion_acta_id, 'modificacion_acta', 'bys');

            }*/


            return $this->renderAjax('updatemod', [
                'model' => $model,
                'rechazo' => $rechazo
            ]);
        }

        return $this->goHome();

    }

    public function actionViewValidarmodificaciones($id = null, $user_id = null)
    {
        $queryParams = Yii::$app->request->queryParams;
        $id = isset($queryParams['id']) ? intval( base64_decode($queryParams['id']) ) : 0;
        $user_id = isset($queryParams['user_id']) ? intval( $queryParams['user_id'] ) : 0;
        $opcionesArray = isset($queryParams['opciones']) ? $queryParams['opciones'] : null;
        $opcionesSerial = isset($opcionesArray) ? serialize($opcionesArray) : null;
        $opcionesBase64 = isset($opcionesSerial) ? base64_encode($opcionesSerial) : null;

        $model = $this->findModelModificaciones($id);
        $model_status = new Status();
        $model_status->scenario = Status::SCENARIO_CREATE;
        $modelValidado = new DatosValidados();
        $modelValidado->scenario = DatosValidados::SCENARIO_CREATE;
        $modelos = [];

        if (Yii::$app->user->can(Usuarios::ROLE_VALIDADOR_PROVEEDORES) || Yii::$app->user->can(Usuarios::ROLE_ADMIN_PROVIDER)) {
            if (isset($model->provider_id) && !empty($model->provider_id)) {
                $modelos[$model->formName()] = $model->attributes;
            }
            $modelos = base64_encode(json_encode($modelos));

        }

        $rechazo = Status::find()->getStatus($model->modificacion_acta_id, 'modificacion_acta', 'bys', 'TERMINADO PRO');

        $movements = $this->countAllMovements($id,'modificacion_acta');


        $modSearch = new ModificacionActaSearch();
        $modSearch->provIdVal = $model->provider_id;
        $dataModSearch = $modSearch->search(Yii::$app->request->queryParams,'validadosFilter');

        return $this->renderAjax('view-validarmodificaciones', [
            'model' => $model,
            'modelos' => $modelos,
            'modelStatus' => $model_status,
            'modelValidado' => $modelValidado,
            'rechazo' => $rechazo,
            'namePro' => $this->getNameProvider($model->provider_id),
            'movements' => $movements,
            'moduleName' => 'modificacion_acta',
            'id' => $id,
            'opciones' => $opcionesBase64,
            'dataModSearch' => $dataModSearch
        ]);
    }


    public function actionDeletemod($id = 0)
    {

        $model = ModificacionActa::find()
            ->where(['and', ['provider_id' => Yii::$app->user->identity->providerid], ['modificacion_acta_id' => intval(base64_decode($id))], ['status_' . Yii::$app->user->identity->tipo => $this->statusParaEliminar]])->one();

        if ($model) {
            if($this->verifyProviderCert($model->provider_id,'bys')>0){
                $arr_moviments = [
                    'provider_id' => $model->provider_id,
                    'model' => 'modificacion_acta',
                    'action' => 'Eliminó Modificación Acta',
                    'origin_id' => $model->modificacion_acta_id,
                    'column' => null,
                    'column_data_old' => null,
                    'column_date_new' => null,
                    'full_model' => true,
                    'data_model' => $model->attributes
                ];
                $this->insertAllMovements($arr_moviments);
            }
            $model->activo = FALSE;
            $model->save(false);
            Yii::$app->session->setFlash('success', 'Registro eliminado correctamente.');
            return $this->redirect('view');
        }
        //return json_encode(['status' => 'No permitido']);
    }

    public function actionDeleteRelacion($id){
        $acta_id = intval(base64_decode($id));
        $model = ModificacionActa::find()->where( [ 'modificacion_acta_id' => $acta_id ])->one();
        if ($model) {
            $resultado = RelacionAccionistas::find()->where(['and', [ 'tipo_relacion' => 'MODIFICACION', 'documento_relacion' => $acta_id ]])->all();
            foreach($resultado as $accionista){
                $accionista->documento_relacion = null;
                $accionista->tipo_relacion = null;
                $accionista->save(false);
            }

            if($this->verifyProviderCert($model->provider_id,'bys')>0){
                $arr_moviments = [
                    'provider_id' => $model->provider_id,
                    'model' => 'modificacion_acta',
                    'action' => 'Eliminó Modificación Acta',
                    'origin_id' => $model->modificacion_acta_id,
                    'column' => null,
                    'column_data_old' => null,
                    'column_date_new' => null,
                    'full_model' => true,
                    'data_model' => $model->attributes
                ];
                $this->insertAllMovements($arr_moviments);
            }
            $model->activo = FALSE;
            $model->save();
            Yii::$app->session->setFlash('success', 'Registro eliminado correctamente.');
            return $this->redirect('view');
        }
    }


    public function actionTerminarmod($id = null)
    {

        $IdLastVal = $this->saveLastSendValidation(Yii::$app->user->identity->getProvider(),'validModificacionActa','modificacion_acta','bys');

        if ($id != null) {
            $status = $this->terminarUno('ModificacionActa', intval(base64_decode($id)), 'modificacion_acta_id', 'modificacion_acta', 'status_bys','',$IdLastVal);
        } else {
            $status = $this->terminarTodos('ModificacionActa', 'modificacion_acta_id', 'modificacion_acta', 'status_bys','',$IdLastVal);
        }

        if ($status) {
            $correos_validadores = self::getEmailValidador(1, 'bys');

            self::sendEmail('/provider/correos/nuevo', null, $correos_validadores, 'Nuevo proveedor', ['modulo' => 'Modificaciones de acta', 'tipo_provider' => 'bys']);

            self::AllSendNotification(null, 'bys', null, 'DATOS LEGALES', 'Modificaciones de acta');

            GeneralController::allModulesComplete(Yii::$app->user->identity->getProvider());

        }
        return $this->redirect('view#modificaciones');
    }


    protected function findModelMod($id)
    {
        $model = ModificacionActa::find()->where(['and', ['provider_id' => Yii::$app->user->identity->providerid], ['modificacion_acta_id' => $id]])->one();
        if (!$model) {
            $model = new ModificacionActa();
        }
        return $model;
    }


    /**
     * Deletes an existing Rfc model.
     * If deletion is successful, the browser will be redirected to the 'index' page.
     * @param integer $id
     * @return mixed
     */
    public function actionDelete($id)
    {
        $this->findModel($id)->delete();

        return $this->redirect(['index']);
    }

    public function actionDeleteAccionista($id)
    {
        $this->findModelAccionista($id)->delete();

        return $this->redirect(['view', 'id' => base64_encode($id)]);
    }

    /**
     * Finds the Rfc model based on its primary key value.
     * If the model is not found, a 404 HTTP exception will be thrown.
     * @param integer $id
     * @return Rfc the loaded model
     * @throws NotFoundHttpException if the model cannot be found
     */
    protected function findModel($id)
    {
        $model = Rfc::find()->where(['provider_id' => $id])->one();
        if ($model == null || empty($model)) {
            $model = new Rfc();
        }
        return $model;
    }

    protected function findModelAccionista($id)
    {
        if (($model = RelacionAccionistas::findOne($id)) !== null) {
            return $model;
        } else {
            throw new NotFoundHttpException('The requested page does not exist.');
        }
    }

    protected function findModelModificaciones($id)
    {
        if (($model = ModificacionActa::findOne($id)) !== null) {
            return $model;
        } else {
            throw new NotFoundHttpException('The requested page does not exist.');
        }
    }

    public function findModelCurp($id)
    {
        $model = Curp::find()->where(['provider_id' => $id])->one();
        if ($model == null || empty($model)) {
            $model = new Curp();
        }
        return $model;
    }

    public function findModelHistorico($id)
    {
        $model = Historico::find()->where(['provider_id' => $id, 'modelo' => 'Rfc'])->one();
        if ($model == null || empty($model)) {
            $model = new Historico();
        }
        return $model;
    }

    public function findModelIdoficial($id)
    {
        $model = IdOficial::find()->where(['provider_id' => $id])->one();
        if ($model == null || empty($model)) {
            $model = new IdOficial();
        }
        return $model;
    }

    public function findModelComprobanteDomicilio($id)
    {
        $model = ComprobanteDomicilio::find()->where(['provider_id' => $id])->one();
        if ($model == null || empty($model)) {
            $model = new ComprobanteDomicilio();
        }
        return $model;
    }

    public function findModelRegistroImss($id)
    {
        $model = RegistroImss::find()->where(['provider_id' => $id])->one();
        if ($model == null || empty($model)) {
            $model = new RegistroImss();
        }
        return $model;
    }

    public function findModelRepresentanteLegal($id)
    {

        $model = RepresentanteLegal::find()->where(['and', ['provider_id' => Yii::$app->user->identity->providerid], ['representante_legal_id' => $id],['activo' => true]])->one();

        if ($model == null || empty($model)) {
            $model = new RepresentanteLegal();
        }
        return $model;
    }

    public function findModelActaConstitutiva($id)
    {

        $model = ActaConstitutiva::find()->where(['provider_id' => $id])->one();
        if ($model == null || empty($model)) {
            $model = new ActaConstitutiva();
        }
        return $model;
    }


    public function findModelRegistroActas($id)
    {
        $model = new RegistroActas();
        return $model;
        $model = ModificacionActa::find()->where(['acta_constitutiva_id' => $id])->one();
        if ($model == null || empty($model)) {
            $model = new RegistroActas();
        }
        return $model;
    }

    public function findModelPorcentaje($id)
    {
        $model = Porcentaje::find()->where(['and', ['register_id' => $id], ['modelo' => 'rfc']])->one();
        if ($model == null || empty($model)) {
            $model = new Porcentaje();
        }
        return $model;
    }

    public function actionList_city($id = null)
    {

        $city = ArrayHelper::map(CatMunicipios::find()->select(['municipio_id', 'nombre'])->where(['entidad_id' => $id])->asArray()->all(), 'municipio_id', 'nombre');

        $con = '';
        $con .= '<option value=""></option>';
        if (Yii::$app->request->isAjax && $id != null && !empty($id)) {
            if (count($city) > 0) {
                foreach ($city as $key => $value) {
                    $con .= '<option value="' . $key . '">' . $value . '</option>';
                }
            } else {
                $con .= '<option value="">No hay ciudades</option>';
            }
        }
        echo $con;


    }

    public function findModelAct($id)
    {
        $model = Giro::find()->where(['and',['provider_id' => $id, 'active' => true]])->all();
        if ($model == null || empty($model)) {
            $model = [new Giro()];
        }
        return $model;
    }

    public function actionList_rama($id = null)
    {

        $con = '';
        $con .= '<option value=""></option>';

        if (Yii::$app->request->isAjax && $id != null && !empty($id)) {
            $rama = ArrayHelper::map(CatRamas::find()->select(['rama_id', 'nombre_rama'])
                ->where(['sector_id' => $id])
                ->orderBy(['nombre_rama' => SORT_ASC])
                ->asArray()->all(), 'rama_id', 'nombre_rama');

            if (count($rama) > 0) {
                foreach ($rama as $key => $value) $con .= "<option value='" . $key . "'>" . $value . "</option>";
            } else {
                $con .= "<option>No hay ramas</option>";
            }
        }
        echo $con;
    }

    public function actionList_actividad($id = null)
    {
        $con = '';
        $con .= '<option value=""></option>';
        if (Yii::$app->request->isAjax && $id != null && !empty($id)) {
            $act1 = ArrayHelper::map(CatActividades::find()->select(['actividad_id', 'nombre_actividad'])
                ->where(['rama_id' => $id])
                ->orderBy(['nombre_actividad' => SORT_ASC])
                ->asArray()->all(), 'actividad_id', 'nombre_actividad');

            if (count($act1) > 0) {
                foreach ($act1 as $key => $value) $con .= "<option value='" . $key . "'>" . $value . "</option>";
            } else {
                $con .= "<option>No hay actividades</option>";
            }
        }
        echo $con;
    }

    //valida si puede entrar a action update
    public function verifyUpdate($provider)
    {
        if ($provider) {
            $status = Rfc::find()->select('status_bys')->where(['provider_id' => $provider])->one()['status_bys'];
            if ($status == Status::STATUS_ENEDICION || $status == Status::STATUS_RECHAZADO || $status == Status::STATUS_VALIDADO) {
                return true;
            }
        }
        return false;
    }

    //valida si puede entrar a action terminar
    public function verifyTerminar($provider)
    {
        if ($provider) {
            $tipo_pro = Provider::find()->select('tipo_persona')->where(['provider_id' => $provider])->one()['tipo_persona'];


            $model = $this->findModel($provider);
            if ($tipo_pro == 'Persona física') {
                $model_curp = $this->findModelCurp($provider);
                $model_idoficial = $this->findModelIdoficial($provider);

                $esta_lleno = \app\helpers\GeneralController::estaLleno($model) && \app\helpers\GeneralController::estaLleno($model_curp) &&
                    \app\helpers\GeneralController::estaLleno($model_idoficial) ;
            } else {
                $acta = $this->findModelActaConstitutiva($provider);
                /* $esta_lleno =  \app\helpers\GeneralController::estaLlenoActa($acta); */
                $esta_lleno =  \app\helpers\GeneralController::estaLlenoActaConstitutiva($acta);
            }

            $status = $model->status_bys;
            if (($status == Status::STATUS_ENEDICION || $status == Status::STATUS_RECHAZADO) && $esta_lleno && $model->url_rfc!='') {
                return true;
            }
        }
        return false;
    }


    protected function findModelEscrituraPublica()
    {
        $model = EscrituraPublica::find()->where(['provider_id' => Yii::$app->user->identity->providerId])->one();
        if (!$model) {
            $model = new EscrituraPublica();
        }
        return $model;
    }

    public function findModelRegistroPublico()
    {
        $model = RegistroPublicoPropiedad::find()->where(['provider_id' => Yii::$app->user->identity->providerId])->one();
        if (!$model) {
            $model = new RegistroPublicoPropiedad();
        }
        return $model;
    }


    public function findModelPorcentajeEP($id)
    {
        $model = Porcentaje::find()->where(['and', ['register_id' => $id], ['modelo' => 'escritura_publica']])->one();
        if ($model == null || empty($model)) {
            $model = new Porcentaje();
        }
        return $model;
    }


    public function findModelActa($id)
    {
        $model = ActaConstitutiva::find()->where(['provider_id' => $id])->one();
        if(!$model){
            $model = new ActaConstitutiva();
        }
        return $model;
    }

    protected function findModelRepresentanteLegalVal($id)
    {
        if (($model = RepresentanteLegal::findOne($id)) !== null) {
            return $model;
        } else {
            throw new NotFoundHttpException('The requested page does not exist.');
        }
    }


}
