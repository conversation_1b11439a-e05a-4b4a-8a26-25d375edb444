<?php

namespace app\controllers\bys;

use app\helpers\GeneralController;
use app\models\DatosValidados;
use app\models\ExpirationDocuments;
use app\models\Module;
use app\models\Porcentaje;
use app\models\Provider;
use app\models\RepresentanteLegal;
use app\models\RepresentanteLegalSearch;
use app\models\Status;
use app\models\Usuarios;
use Yii;
use yii\db\Query;
use yii\helpers\ArrayHelper;
use app\models\UltimaDeclaracion;
use app\models\UltimaDeclaracionSearch;
use yii\web\Controller;
use yii\web\NotFoundHttpException;
use yii\filters\VerbFilter;
use yii\web\UploadedFile;

if(Yii::$app->user->can(Usuarios::ROLE_PROVIDER)){
    Yii::$app->params['provider_id'] = Yii::$app->user->identity->getProvider();
}
/**
 * UltimaDeclaracionController implements the CRUD actions for UltimaDeclaracion model.
 */
class FinancierosController extends GeneralController
{
    /**
     * @inheritdoc
     */
    public function behaviors()
    {
        return [
            'verbs' => [
                'class' => VerbFilter::className(),
                'actions' => [
                    'delete' => ['POST','GET'],
                ],
            ],
        ];
    }

    /**
     * Lists all UltimaDeclaracion models.
     * @return mixed
     */
    public function actionIndex()
    {
        $this->msgError();
        $searchModel = new UltimaDeclaracionSearch();
        $dataProvider = $searchModel->search(Yii::$app->request->queryParams);

        return $this->render('index', [
            'searchModel' => $searchModel,
            'dataProvider' => $dataProvider,
        ]);
    }

    /**
     * Displays a single UltimaDeclaracion model.
     * @param integer $id
     * @return mixed
     */
    public function actionView()
    {
        if (Yii::$app->user->can(Usuarios::ROLE_PROVIDER)){
            $id=Yii::$app->params['provider_id'];
        }
        if($id == null || !Provider::verifyProvider($id) || !Provider::verifyEtapa3($id)){
            $this->msgError();
        }

        $provider = Provider::find()->where(['user_id' => Yii::$app->user->getId()])->one();

        //Rechazos
        $modelStatus = Module::getstatusbymodule('bys_economica',$provider->provider_id);
        $rechazo = $modelStatus->status_bys == Status::STATUS_RECHAZADO  || $modelStatus->status_bys == Status::STATUS_ENEDICION  ?
            Status::find()->getRechazo( 'bys_economica', $provider->provider_id) : null;


        $ultima_declaracion = $this->findModel($id);
        return $this->render('view', [
            'model' => $ultima_declaracion,
            'provider'=>$provider,
            'modelStatus'=>$modelStatus,
            'rechazo'=>$rechazo
        ]);
    }


    /**
     * Displays a single UltimaDeclaracion model.
     * @param integer $id
     * @return mixed
     */
    public function actionViewValidar($id=null)
    {
        $id = intval($id);
        if($id == null || !Provider::verifyProvider($id) || !Provider::verifyEtapa3($id)){
            $this->msgError();
        }
        $model_status = new Status();
        $model_status->scenario = Status::SCENARIO_CREATE;
        $model_validado = new DatosValidados();
        $model_validado->scenario = DatosValidados::SCENARIO_CREATE;
        $ultima_declaracion = $this->findModel($id);

        $modelos = [];
        if(Yii::$app->user->can(Usuarios::ROLE_VALIDADOR_PROVEEDORES) || Yii::$app->user->can(Usuarios::ROLE_ADMIN_PROVIDER)){
            if(isset($ultima_declaracion->provider_id) && !empty($ultima_declaracion->provider_id)){
                $modelos[$ultima_declaracion->formName()] = $ultima_declaracion->attributes;
            }
            $modelos = base64_encode(json_encode($modelos));

        }

        $status_global = 'status_bys';

        $id_serial = UltimaDeclaracion::find()->select('ultima_declaracion_id')->where(['and',['provider_id' => $id],[$status_global => Status::STATUS_PORVALIDAR]])->one()['ultima_declaracion_id'];
        $rechazo = Status::find()->getStatus($id,'ultima_declaracion','bys','TERMINADO PRO');
        $movements = $this->countAllMovements($id,'ultima_declaracion');



        $rpSearch = new RepresentanteLegalSearch();
        $rpSearch->provIdVal = $id;
        $dataRpSearch = $rpSearch->search(Yii::$app->request->queryParams,'validadosFilter');

        return $this->renderAjax('view-validar', [
            'model' => $ultima_declaracion,
            'model_status' => $model_status,
            'rechazo' => $rechazo,
            'model_porcentaje' => $this->findModelPorcentaje($id),
            'model_validado' => $model_validado,
            'id_serial' => $id_serial,
            'modelos' => $modelos,
            'namePro' => $this->getNameProvider($id),
            'movements' => $movements,
            'id' => $id,
            'moduleName' => 'ultima_declaracion',
            'representateFirmante' => RepresentanteLegal::find()->where(['and',['provider_id' => $id],['rep_bys' => true]])->one(),
            'rpSearch' => $rpSearch,
            'dataRpSearch' => $dataRpSearch
        ]);
    }

    /**
     * Creates a new UltimaDeclaracion model.
     * If creation is successful, the browser will be redirected to the 'view' page.
     * @return mixed
     */
//    public function actionCreate()
//    {
//        $model = new UltimaDeclaracion();
//
//        if ($model->load(Yii::$app->request->post()) && $model->save()) {
//            return $this->redirect(['view', 'id' => $model->ultima_declaracion_id]);
//        } else {
//            return $this->render('create', [
//                'model' => $model,
//            ]);
//        }
//    }

    /**
     * Updates an existing UltimaDeclaracion model.
     * If update is successful, the browser will be redirected to the 'view' page.
     * @param integer $id
     * @return mixed
     */
    public function actionUpdate($id=null)
    {
        if (Yii::$app->user->can(Usuarios::ROLE_PROVIDER)){
            $id=Yii::$app->params['provider_id'];
        }
        /* if($id == null || !Provider::verifyProvider($id) || !Provider::verifyEtapa3($id)|| !self::verifyUpdate($id)  || self::cotejoPendiente() == 'CITA NO MOD'){
            $msg = self::cotejoPendiente() == 'CITA NO MOD'?'No puede realizar modificaciones, cuenta con cita pendiente':'Acceso denegado';
            return $this->render('/site/error', ['name' => 'Lo sentimos...', 'message' => $msg]);
        } */
        $model = $this->findModel($id);
        $modelOld = $model->attributes;
        $model_porcentaje = $this->findModelPorcentaje($id);
        $model->provider_id = $id;
        $data_isn = $model->resultado_consulta_isn;
        $urlComprobanteReciboOld = $model->comprobante_pago;
        $urlCE = $model->cumplimiento_estado;
        $urlBalanceGeneralOld = $model->balance_general;
        $urlEstadoResultadoOld = $model->estado_resultado;
        $urlBalanceAnteriorOld = $model->balance_general_anterior;
        $urlEstadoResultadoAnteriorOld = $model->estado_resultado_anterior;
        $urlJustificanteOld = $model->justificante_isn;
        $urlComprobantes_de_pagoOld = $model->comprobantes_de_pago;
        $urlEjercicioFisOld = $model->declaracion_ejercicio_fiscal;
        $urlPagoProvOld = $model->ultima_declaracion_pago_prov;

        //Rechazos
        $modelStatus = Module::getstatusbymodule('bys_economica',$id);
        $rechazo = $modelStatus->status_bys == Status::STATUS_RECHAZADO  || $modelStatus->status_bys == Status::STATUS_ENEDICION  ?
            Status::find()->getRechazo( 'bys_economica', $id) : null;

        if ($model->load(Yii::$app->request->post())) {
            $model->h = isset($model->h) ? (int)$model->h : 0;
            $model->m = isset($model->m) ? (int)$model->m : 0;
            $model->x = isset($model->x) ? (int)$model->x : 0;
    
            $model->personal = $model->x + $model->h + $model->m;   
            $model->gender = json_encode(['H'=>$model->h , 'M'=> $model->m ,'X'=> $model->x]);

            $model->resultado_consulta_isn = $data_isn;
            $verify = Porcentaje::find()->select(['register_id'])->where(['and',['register_id' =>$id],['modelo'=>'ultima_declaracion']])->one();

            if($verify){
                $porcentajeOld = $model_porcentaje->porcentaje;

                if(isset($_POST['porcentaje_total']) && $_POST['porcentaje_total'] != ''){
                    $model_porcentaje->porcentaje = round($_POST['porcentaje_total']);
                }else{
                    $model_porcentaje->porcentaje = $porcentajeOld;
                }
                \Yii::$app->db->createCommand("update provider.porcentaje set porcentaje = $model_porcentaje->porcentaje where modelo='ultima_declaracion' and register_id =:id")
                    ->bindValue(':id', $id)
                    ->execute();
            }else{
                $porcen = new Porcentaje();
                $porcen->modelo = 'ultima_declaracion';
                $porcen->register_id = $id;
                if(isset($_POST['porcentaje_total']) && $_POST['porcentaje_total'] != ''){
                    $porcen->porcentaje = round($_POST['porcentaje_total']);
                }else{
                    $porcen->porcentaje = 0;
                }
                $porcen->save();
            }
            $status_global='status_bys';

            $model->$status_global= Status::STATUS_ENEDICION;

            if(($requi_status = Status::find()->where(['and',['register_id' => $id],['status_bys' => Status::STATUS_PENDIENTE],['modelo'=>'ultima_declaracion']])->one())!==null){
                $requi_status->status_bys = Status::STATUS_TERMINADO_PRO;
                $requi_status->save();
            }

            $model->provider_id = $id;
            $pathF = 'bys/'.$model->path;
            $this->makeDir($pathF);

            /*if(!empty($model->comprobante_pago) && $model->comprobante_pago!=$urlComprobanteReciboOld){
                $new_nameCp= str_replace('archivos_tmp/',$pathF.'/',$model->comprobante_pago);
                $this->copyFile($model->comprobante_pago,$new_nameCp);
                $model->comprobante_pago = $new_nameCp;
            }

            if(!empty($model->balance_general) && $model->balance_general!=$urlBalanceGeneralOld){
                $new_nameBg= str_replace('archivos_tmp/',$pathF.'/',$model->balance_general);
                $this->copyFile($model->balance_general,$new_nameBg);
                $model->balance_general = $new_nameBg;
            }

            if(!empty($model->estado_resultado) && $model->estado_resultado!=$urlEstadoResultadoOld){
                $new_nameEr= str_replace('archivos_tmp/',$pathF.'/',$model->estado_resultado);
                $this->copyFile($model->estado_resultado,$new_nameEr);
                $model->estado_resultado = $new_nameEr;
            }*/

            if(!empty($model->balance_general_anterior) && $model->balance_general_anterior!=$urlBalanceAnteriorOld){
                $new_nameBga= str_replace('archivos_tmp/',$pathF.'/',$model->balance_general_anterior);
                $this->copyFile($model->balance_general_anterior,$new_nameBga);
                $model->balance_general_anterior = $new_nameBga;
            }

            if(!empty($model->estado_resultado_anterior) && $model->estado_resultado_anterior!=$urlEstadoResultadoAnteriorOld){
                $new_nameEra= str_replace('archivos_tmp/',$pathF.'/',$model->estado_resultado_anterior);
                $this->copyFile($model->estado_resultado_anterior,$new_nameEra);
                $model->estado_resultado_anterior = $new_nameEra;
            }

            if(!empty($model->justificante_isn) && $model->justificante_isn!=$urlJustificanteOld){
                $new_nameJi= str_replace('archivos_tmp/',$pathF.'/',$model->justificante_isn);
                $this->copyFile($model->justificante_isn,$new_nameJi);
                $model->justificante_isn = $new_nameJi;
            }

            if(!empty($model->comprobantes_de_pago) && $model->comprobantes_de_pago!=$urlComprobantes_de_pagoOld){
                $new_nameCompPago= str_replace('archivos_tmp/',$pathF.'/',$model->comprobantes_de_pago);
                $this->copyFile($model->comprobantes_de_pago,$new_nameCompPago);
                $model->comprobantes_de_pago = $new_nameCompPago;
            }


            if(!empty($model->ultima_declaracion_pago_prov) && $model->ultima_declaracion_pago_prov!=$urlPagoProvOld){
                $new_namePagoProv= str_replace('archivos_tmp/',$pathF.'/',$model->ultima_declaracion_pago_prov);
                $this->copyFile($model->ultima_declaracion_pago_prov,$new_namePagoProv);
                $model->ultima_declaracion_pago_prov = $new_namePagoProv;
            }


            /*if(!empty($model->declaracion_ejercicio_fiscal) && $model->declaracion_ejercicio_fiscal!=$urlEjercicioFisOld){
                $new_nameCompEjerFis= str_replace('archivos_tmp/',$pathF.'/',$model->declaracion_ejercicio_fiscal);
                $this->copyFile($model->declaracion_ejercicio_fiscal,$new_nameCompEjerFis);
                $model->declaracion_ejercicio_fiscal = $new_nameCompEjerFis;
            }*/

            if(!empty($model->cumplimiento_estado) && $model->cumplimiento_estado!=$urlCE){
                $n_ce= str_replace('archivos_tmp/',$pathF.'/',$model->cumplimiento_estado);
                $this->copyFile($model->cumplimiento_estado,$n_ce);
                $model->cumplimiento_estado = $n_ce;
            }

            //cambia estratificacion
            $modelProvider = Provider::findOne($model->provider_id);
            $formula = (((intval($model->personal) * 10 )/100) + (((floatval(str_replace(',','',($model->ingresos_mercantiles)))/1000000) * 90)/100));
            $anterior = strtoupper($modelProvider->estratificacion);
            //var_dump(floatval(str_replace(',','',($model->ingresos_mercantiles)))/1000000);
            //var_dump($formula).exit();
            $nueva = ( $formula <= 4.6 ) ? 'MICRO' : (( $formula <= 95 ) ? 'PEQUEÑA' :(( $formula <= 250 ) ? 'MEDIANA' : 'GRANDE' ));

            if($anterior != $nueva && $model->personal != ''){
                $modelProvider->estratificacion = $nueva;
                $modelProvider->save(false);
                Yii::$app->session->setFlash('success','De acuerdo a los datos ingresados su estratificación es: '.$nueva);
            }

            if(!$model->save(false)){
                return $this->goHome();
            }

            $countValCert = $this->verifyProviderCert($model->provider_id, 'bys');
            $countValRec = self::verifyValRec('ultima_declaracion',$model->provider_id);

            if ($countValCert > 0 || $countValRec>0) {
                $trueFalse = $countValCert>0?true:false;
                $this->compareModels($modelOld, $model->attributes, $model->provider_id, 'ultima_declaracion',$model->provider_id,$trueFalse);
            }

            //self::eliminarCita();
            self::updateModulesBys($model->provider_id,'bys_economica');



            return $this->redirect(['view']);
        } else {
            if($model->gender){
                $model->gender = json_decode($model->gender);
                $model->m = $model->gender->M;
                $model->h = $model->gender->H;
                $model->x = $model->gender->X;
            }


            return $this->renderAjax('update', [
                'model' => $model,
                'rechazo' => $rechazo
            ]);
        }
    }

    public function actionTerminar(){
        $id = Yii::$app->params['provider_id'];

        if($id == null || !Provider::verifyProvider($id) || !Provider::verifyEtapa3($id)|| !self::verifyTerminar($id)){
            echo $this->render('/site/error',['name' => 'Lo sentimos...','message' => 'Acceso denegado']);

            return false;
        }
        $status='status_bys';

        $rfc = Yii::$app->db->createCommand();
        $rfc->update('provider.ultima_declaracion',[$status => Status::STATUS_PORVALIDAR],'provider_id ='.$id);
        $rfc->execute();

        ExpirationDocuments::updateAll(['status' => true],['provider_id' => $id, 'module' => 'ultima_declaracion']);

        $IdLastVal = $this->saveLastSendValidation($id,'mainFilterUltimaDeclaracion','ultima_declaracion','bys');
        $this->saveLastSendValidationRelation($IdLastVal,$id,'ultima_declaracion',$id,'bys');

        $correos_validadores = self::getEmailValidador(3,'bys');

        self::sendEmail('/provider/correos/nuevo',null,$correos_validadores,'Nuevo proveedor',['modulo' => 'Financieros','tipo_provider' => 'bys']);


        self::AllSendNotification(null,'bys',null,'DATOS FINANCIEROS','Financieros');



        return $this->redirect('../../provider/dashboard');
    }


    public function actionConfirm(){
        $role = Yii::$app->user->identity->role;
        $status_global = 'status_bys';

        $id = Yii::$app->request->post()['provider_id'];
        $estado_financiero = Yii::$app->db->createCommand();
        $estado_financiero->update('provider.ultima_declaracion',[$status_global => Status::STATUS_VALIDADO],'provider_id ='.$id);
        $estado_financiero->execute();

        if(($requi_status = Status::find()->where(['and',['register_id' => $id],['status_bys' => Status::STATUS_PENDIENTE],['modelo'=>'UltimaDeclaracion']])->one())!==null){
            $requi_status->status_bys = Status::STATUS_TERMINADO_PRO;
            $requi_status->save();

            \Yii::$app->db->createCommand("update provider.ultima_declaracion set status = :statusv where status= :statusp and provider_id =:id")
                ->bindValue(':id', $id)
                ->bindValue(':statusv', Status::STATUS_VALIDADO)
                ->bindValue(':statusp', Status::STATUS_ENEDICION)
                ->execute();
        }

        Yii::$app->session->setFlash('success', 'Los datos se validaron correctamente');
        return Yii::$app->getResponse()->redirect(['datosFinancieros/declaracion-isr/index-validador']);

    }


    public function actionConsultaisn()
    {
        if (Yii::$app->user->can(Usuarios::ROLE_PROVIDER)){
            $id=Yii::$app->params['provider_id'];
        }
        $rfc = Provider::find('rfc')->where(['provider_id'=>$id])->one()['rfc'];
        if($rfc!=null){
            $mes = date('n');
            $anio = date('Y');
            Yii::$app->response->format = \yii\web\Response::FORMAT_JSON;
            $parametros = ['rfc'=>$rfc,'mes'=>$mes,'anio'=>$anio];

            $service = 'http://10.153.144.94/Retenedores/ws_pagonomina/ws_pagonomina.php?wsdl';
            $cliente_soap = new \SoapClient($service, array('exceptions' => true,));
            $respuesta_service = $cliente_soap->ConsultaPago($parametros);
            $response = json_decode($respuesta_service->json,true);

            if(isset($response['exito'])){
                $etapa = Yii::$app->db->createCommand();
                $etapa->update('provider.ultima_declaracion', ['resultado_consulta_isn' => $response['exito']], 'provider_id =' . $id);
                $etapa->execute();
                return $response['exito'];
            }
        }
        return "0";
    }
    /**
     * Deletes an existing UltimaDeclaracion model.
     * If deletion is successful, the browser will be redirected to the 'index' page.
     * @param integer $id
     * @return mixed
     */
    public function actionDelete($id)
    {
        $this->findModel($id)->delete();

        return $this->redirect(['index']);
    }

    /**
     * Finds the UltimaDeclaracion model based on its primary key value.
     * If the model is not found, a 404 HTTP exception will be thrown.
     * @param integer $id
     * @return UltimaDeclaracion the loaded model
     * @throws NotFoundHttpException if the model cannot be found
     */
    protected function findModel($id)
    {
        $model = UltimaDeclaracion::find()->where(['provider_id' => $id])->one();
        if($model == null || empty($model)){
            $model = new UltimaDeclaracion();
        }
        return $model;
    }

    public function findModelPorcentaje($id)
    {
        $model = Porcentaje::find()->where(['and', ['register_id' => $id], ['modelo' => 'ultima_declaracion']])->one();
        if($model == null || empty($model)){
            $model = new Porcentaje();
        }
        return $model;
    }
    //valida si puede entrar a action update
    public  function  verifyUpdate($provider){
        if($provider){
            $status = UltimaDeclaracion::find()->select('status_bys')->where(['provider_id' => $provider])->one()->status_bys;
            if($status == Status::STATUS_VALIDADO || $status == Status::STATUS_ENEDICION || $status == Status::STATUS_RECHAZADO){
                return true;
            }
        }
        return false;
    }
    //valida si puede entrar a action terminar
    public  function  verifyTerminar($provider){
        if($provider){
            $model =UltimaDeclaracion::find()->where(['provider_id' => $provider])->one();
            if(($model->status_bys == Status::STATUS_ENEDICION || $model->status_bys == Status::STATUS_RECHAZADO) && $this->estaLleno($model)){
                return true;
            }
        }
        return false;
    }
}