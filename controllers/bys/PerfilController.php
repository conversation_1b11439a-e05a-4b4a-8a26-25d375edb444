<?php

namespace app\controllers\bys;

use app\helpers\GeneralController;
use app\models\ComprobanteDomicilio;
use app\models\DatosValidados;
use app\models\ExpirationDocuments;
use app\models\Module;
use app\models\Movements;
use app\models\Perfil;
use app\models\Porcentaje;
use app\models\Provider;
use app\models\Rfc;
use app\models\Status;
use app\models\StatusQuery;
use app\models\Ubicacion;
use app\models\Usuarios;
use Yii;
use app\models\ClientesContratos;
use app\controllers\datosTecnicos\ClientesContratosController;
use app\models\ClientesContratosSearch;
use yii\web\Controller;
use yii\web\NotFoundHttpException;
use yii\filters\VerbFilter;
use yii\db\Query;
use yii\helpers\ArrayHelper;
use app\models\Curriculum;
use yii\web\Response;
use yii\widgets\ActiveForm;

if (Yii::$app->user->can(Usuarios::ROLE_PROVIDER)) {
    Yii::$app->params['provider_id'] = Yii::$app->user->identity->getProvider();
}

/**
 * PerfilController implements the CRUD actions for Perfil model.
 */
class PerfilController extends GeneralController
{
    /**
     * @inheritdoc
     */
    public function behaviors()
    {
        return [
            'verbs' => [
                'class' => VerbFilter::className(),
                'actions' => [
                    'delete' => ['POST','GET'],
                ],
            ],
        ];
    }


    public function actionIndex($id = null){
        $id = intval($id);
        if (Yii::$app->user->can(Usuarios::ROLE_PROVIDER)) {
            $id = Yii::$app->params['provider_id'];
        }
        if ($id == null || strlen($id) > 15 || !Provider::verifyProviderExistence($id) || /*!Provider::verifyEtapa3($id) ||*/
            !Yii::$app->user->can(Usuarios::ROLE_PROVIDER)) {
            $this->msgError();
        }

        $provider = Provider::find()->where(['provider_id' => $id])->one();
        $perfil = Perfil::find()->where(['provider_id'=>$provider->provider_id])->one();

        //Rechazos
        $modelStatus = Module::getstatusbymodule('bys_perfil',$provider->provider_id);
        $rechazo = $modelStatus->status_bys == Status::STATUS_RECHAZADO  || $modelStatus->status_bys == Status::STATUS_ENEDICION  ?
            Status::find()->getRechazo( 'bys_perfil', $provider->provider_id) : null;


        return $this->render('index', [
            'provider' => $provider,
            'model_rfc' => $this->findModelRfcProv($id),
            'modelStatus'=>$modelStatus,
            'modelPerfil' => $perfil,
            'rechazo'=>$rechazo
        ]);
    }


    public function actionView($user_id = null, $id = null)
    {
        $user_id = intval(base64_decode($user_id));
        $id = intval(base64_decode($id));
        if ($user_id == null
            || strlen($user_id) > 15
            || (!Provider::verifyProviderExistence($user_id) && Yii::$app->user->can(Usuarios::ROLE_PROVIDER))
            || (!Provider::verifyProvider($user_id) && Yii::$app->user->can(Usuarios::ROLE_PROVIDER))) {
            $this->msgError();
        }
        $modelos = [];
        $model_status = new Status();
        $model_status->scenario = Status::SCENARIO_CREATE;
        $model_clientes = $this->findModel($id);
        $model_curriculum = $this->findModelCurriculum($id);

        if (Yii::$app->user->can(Usuarios::ROLE_VALIDADOR_PROVEEDORES)) {
            if (isset($model_clientes->provider_id) && !empty($model_clientes->provider_id)) {
                $modelos[$model_clientes->formName()] = $model_clientes->attributes;
            }
            if (isset($model_curriculum->provider_id) && !empty($model_curriculum->provider_id)) {
                $modelos[$model_curriculum->formName()] = $model_curriculum->attributes;
            }

            $modelos = base64_encode(json_encode($modelos));

        }


        return $this->renderAjax('view', [
            'model' => $model_clientes,
            'model_status' => $model_status,
            'model_curriculum' => $model_curriculum,
            'modelos' => $modelos
        ]);
    }


    public function actionViewValidar($id = null, $user_id = null)
    {
        $queryParams = Yii::$app->request->queryParams;
        $id = isset($queryParams['id']) ? intval( $queryParams['id'] ) : null;
        $user_id = isset($queryParams['user_id']) ? intval( $queryParams['user_id'] ) : null;
        $opcionesArray = isset($queryParams['opciones']) ? $queryParams['opciones'] : null;
        $opcionesSerial = isset($opcionesArray) ? serialize($opcionesArray) : null;
        $opcionesBase64 = isset($opcionesSerial) ? base64_encode($opcionesSerial) : null;
        if ($user_id == null
            || strlen($user_id) > 15
            || (!Provider::verifyProviderExistence($user_id) && Yii::$app->user->can(Usuarios::ROLE_PROVIDER))
            || (!Provider::verifyProvider($user_id) && Yii::$app->user->can(Usuarios::ROLE_PROVIDER))
            || !Provider::verifyEtapa3($user_id)) {
            $this->msgError();
        }
        $modelos = [];
        $model_status = new Status();
        $model_status->scenario = Status::SCENARIO_CREATE;
        $model_clientes = $this->findModel($id);
        $model_curriculum = $this->findModelCurriculum($id);
        $model_validado = new DatosValidados();
        $model_validado->scenario = DatosValidados::SCENARIO_CREATE;
        if (Yii::$app->user->can(Usuarios::ROLE_VALIDADOR_PROVEEDORES) || Yii::$app->user->can(Usuarios::ROLE_ADMIN_PROVIDER)) {
            if (isset($model_clientes->provider_id) && !empty($model_clientes->provider_id)) {
                $modelos[$model_clientes->formName()] = $model_clientes->attributes;
            }
            if (isset($model_curriculum->provider_id) && !empty($model_curriculum->provider_id)) {
                $modelos[$model_curriculum->formName()] = $model_curriculum->attributes;
            }

            $modelos = base64_encode(json_encode($modelos));

        }

        $rechazo = Status::find()->getStatus($id, 'clientes_contratos', 'bys','TERMINADO PRO');
        $movements = $this->countAllMovements($id,'clientes_contratos');

        return $this->renderAjax('view-validar', [
            'model' => $model_clientes,
            'model_status' => $model_status,
            'model_curriculum' => $model_curriculum,
            'modelos' => $modelos,
            'model_validado' => $model_validado,
            'rechazo' => $rechazo,
            'namePro' => $this->getNameProvider($model_clientes->provider_id),
            'movements' => $movements,
            'id' => $id,
            'opciones' => $opcionesBase64,
            'moduleName' => 'clientes_contratos'
        ]);
    }


    public function actionViewValidarPerfil($id = null, $user_id = null)
    {
        $queryParams = Yii::$app->request->queryParams;
        $id = isset($queryParams['id']) ? intval( $queryParams['id'] ) : null;
        $opcionesArray = isset($queryParams['opciones']) ? $queryParams['opciones'] : null;
        $opcionesSerial = isset($opcionesArray) ? serialize($opcionesArray) : null;
        $opcionesBase64 = isset($opcionesSerial) ? base64_encode($opcionesSerial) : null;
        if($id == null || strlen($id)>15 || !Provider::verifyProviderExistence($id)){
            $this->msgError();
        }
        $modelos = [];
        $model_status = new Status();
        $model_status->scenario = Status::SCENARIO_CREATE;
        $model_domicilio = $this->findModelComprobanteDomicilio($id);
        $model_rfc = $this->findModelRfcProv($id);
        $ubicacion = $this->findModelUbicacionProv($id);
        $model = $this->findModelProv($id);
        $model_validado = new DatosValidados();
        $model_validado->scenario = DatosValidados::SCENARIO_CREATE;
        if (Yii::$app->user->can(Usuarios::ROLE_VALIDADOR_PROVEEDORES) || Yii::$app->user->can(Usuarios::ROLE_ADMIN_PROVIDER)) {
            if (isset($model_domicilio->provider_id) && !empty($model_domicilio->provider_id)) {
                $modelos[$model_domicilio->formName()] = $model_domicilio->attributes;
            }
            if (isset($model_rfc->provider_id) && !empty($model_rfc->provider_id)) {
                $modelos[$model_rfc->formName()] = $model_rfc->attributes;
            }
            if (isset($ubicacion->provider_id) && !empty($ubicacion->provider_id)) {
                $modelos[$ubicacion->formName()] = $ubicacion->attributes;
            }

            $modelos = base64_encode(json_encode($modelos));

        }

        $rechazo = Status::find()->getStatus($id, 'perfil', 'bys','TERMINADO PRO');
        $movements = $this->countAllMovements($id,'perfil');

        return $this->renderAjax('view-validar-perfil', [
            'model' => $model,
            'model_status' => $model_status,
            'ubicacion' => $ubicacion,
            'modelos' => $modelos,
            'model_validado' => $model_validado,
            'rechazo' => $rechazo,
            'namePro' => $this->getNameProvider($model->provider_id),
            'movements' => $movements,
            'id' => $id,
            'opciones' => $opcionesBase64,
            'moduleName' => 'perfil',
            'model_comprobante_domicilio' => $model_domicilio,
            'model_rfc' => $model_rfc
        ]);
    }

/*
    public function actionCreate($user_id)
    {
        if(Yii::$app->request->isAjax || Yii::$app->request->post()){
            if ($user_id == null || !Provider::verifyProvider($user_id) ) {
                $this->msgError();
            }
            $model = new ClientesContratos();

            if (Yii::$app->request->isAjax && $model->load(Yii::$app->request->post())) {
                Yii::$app->response->format = Response::FORMAT_JSON;
                $result = [];
                if (empty($model->nombre_razon_social)) {
                        $result['clientescontratos-nombre_razon_social'] = ['NOMBRE O RAZÓN SOCIAL NO PUEDE ESTAR VACÍO.'];
                }


                if(isset($model->month) && !empty($model->month) && isset($model->year) && !empty($model->year)){
                    $monthNumber = $this->monthToNumber($model->month);

                    $yearData = intval($model->year.$monthNumber);
                    $yearDataOld = $this->yearOld();

                    if ($yearData>$yearDataOld){
                        $result['clientescontratos-year'] = ['DEBE TENER COMO MÍNIMO UN AÑO CUMPLIDO DE ANTIGÜEDAD'];

                    }
                }

                return $result;
            }else if ($model->load(Yii::$app->request->post())) {

                $porcentaje = 0;
                if (isset($_POST['porcentaje_total_m']) && $_POST['porcentaje_total_m'] != '') {
                    $porcentaje = round($_POST['porcentaje_total_m']);
                }
                $model->porcentaje = $porcentaje;
                $model->provider_id = $user_id;
                $model->tipo = 'bys';



                $this->makeDir($model->path_factura);

                if (!empty($model->url_factura_c_c)) {
                    $new_nameFactura = str_replace('archivos_tmp/', $model->path_factura . '/', $model->url_factura_c_c);
                    $this->copyFile($model->url_factura_c_c, $new_nameFactura);
                    $model->url_factura_c_c = $new_nameFactura;
                }


                if ($model->save()) {

                    if($this->verifyProviderCert($model->provider_id,'bys')>0){
                        $arr_moviments = [
                            'provider_id' => $model->provider_id,
                            'model' => 'clientes_contratos',
                            'action' => 'Crear Experiencia comercial',
                            'origin_id' => $model->clientes_contratos_id,
                            'column' => null,
                            'column_data_old' => null,
                            'column_date_new' => null,
                            'full_model' => true,
                            'data_model' => $model->attributes
                        ];
                        $this->insertAllMovements($arr_moviments);
                    }
                    self::eliminarCita();

                    Provider::updateAll(['status_carta_bys' => 'PENDIENTE'], ['provider_id' => $model->provider_id]);
                    self::updateModulesBys($model->provider_id,'bys_experiencia');
                    return $this->redirect(['/bys/experiencia/view']);
                }
            }

            return $this->renderAjax('create', [
                'model' => $model,
            ]);
        }
        return $this->goHome();
    }
*/

    public function actionUpdate($id=null)
    {
        if(Yii::$app->request->isAjax || Yii::$app->request->post()) {
            /*if ( !Provider::verifyProvider($user_id) || !Provider::verifyEtapa3($user_id) ||
                !self::verifyUpdate($user_id, $id)) {
                echo $this->render('/site/error', ['name' => 'Lo sentimos...', 'message' => 'Acceso denegado']);

                return false;
            }*/
            $provider = Provider::find()->where(['user_id'=>Yii::$app->user->getId()])->one();
            $model = $this->findModel($id);
            $modelOld = $model->attributes;
            $urlFacturaOld = $model->url_factura_c_c;

            $modelStatus = Module::getstatusbymodule('bys_experiencia',$model->provider_id);
            $rechazo = $modelStatus->status_bys == Status::STATUS_RECHAZADO || $modelStatus->status_bys == Status::STATUS_ENEDICION  ?
                Status::find()->getRechazo( 'bys_experiencia',$model->provider_id) : null;

            if (Yii::$app->request->isAjax && $model->load(Yii::$app->request->post())) {
                Yii::$app->response->format = Response::FORMAT_JSON;
                $result = [];
                if (empty($model->nombre_razon_social)) {
                    $result['clientescontratos-nombre_razon_social'] = ['NOMBRE O RAZÓN SOCIAL NO PUEDE ESTAR VACÍO.'];
                }else{
                    $condicion = ['and', ['nombre_razon_social' => $model->nombre_razon_social], ['activo'=>true], ['provider_id'=>$provider->provider_id], ['tipo' => 'bys'] ];
                    if($id != null){ $condicion = array_merge($condicion, [ ['<>', 'clientes_contratos_id', $id] ]); }
                    $count_exist = ClientesContratos::find()->where($condicion)->count('1');
                    if ($count_exist > 0) {
                        $result['clientescontratos-nombre_razon_social'] = ['Ya se encuentra el cliente registrado.'];
                    }
                }
                if(isset($model->month) && !empty($model->month) && isset($model->year) && !empty($model->year)){
                    $monthNumber = $this->monthToNumber($model->month);
                    $yearData = intval($model->year.$monthNumber);
                    $yearDataOld = $this->yearOld();
                    if ($yearData>$yearDataOld){
                        $result['clientescontratos-year'] = ['DEBE TENER COMO MÍNIMO UN AÑO CUMPLIDO DE ANTIGÜEDAD'];
                    }
                }
                return $result;
            } else
                if ($model->load(Yii::$app->request->post())) {
                    $model->status_bys = Status::STATUS_ENEDICION;
                    $model->provider_id = $provider->provider_id ;
                    $porcentajeOld = $model->porcentaje;

                    if (isset($_POST['porcentaje_total_m']) && $_POST['porcentaje_total_m'] != '') {
                        $porcentaje = round($_POST['porcentaje_total_m']);
                    } else {
                        $porcentaje = $porcentajeOld;
                    }
                    $model->porcentaje = $porcentaje;
                    $model->tipo = 'bys';

                    $this->makeDir($model->path_factura);

                    if (!empty($model->url_factura_c_c) && $model->url_factura_c_c != $urlFacturaOld) {
                        $new_nameFactura = str_replace('archivos_tmp/', $model->path_factura . '/', $model->url_factura_c_c);
                        $this->copyFile($model->url_factura_c_c, $new_nameFactura);
                        $model->url_factura_c_c = $new_nameFactura;
                    }

                    if ($model->save()) {
                        $modelNew = $model->attributes;
                        $countValCert = $this->verifyProviderCert($model->provider_id, 'bys');
                        $countValRec = self::verifyValRec('clientes_contratos',$model->clientes_contratos_id);

                        if ($countValCert > 0 || $countValRec>0) {
                            $trueFalse = $countValCert>0?true:false;
                            $this->compareModels($modelOld, $modelNew, $model->clientes_contratos_id, 'clientes_contratos',$model->provider_id,$trueFalse);
                        }

                        $deleteLetter = false;
                        //$arrayChange = ['nombre_razon_social','month','year'];
                        $arrayChange = ['nombre_razon_social'];
                        foreach ($modelOld as $s => $d){
                            if(in_array($s,$arrayChange)){
                                if($d!=$modelNew[$s]){
                                    $deleteLetter = true;
                                }
                            }
                        }

                        /* if($deleteLetter){
                            Provider::updateAll(['status_carta_bys' => 'PENDIENTE'], ['provider_id' => $model->provider_id]);
                        } */
                        self::eliminarCita();
                        if (($requi_status = Status::find()->where(['and', ['register_id' => $id], ['status_bys' => Status::STATUS_PENDIENTE], ['modelo' => 'clientes_contratos']])->one()) !== null) {
                            $requi_status->status_bys = Status::STATUS_TERMINADO_PRO;
                            $requi_status->save();
                        }
                        self::updateModulesBys($model->provider_id,'bys_experiencia');
                        return $this->redirect(['/bys/experiencia/view']);
                    }
                }
            return $this->renderAjax('update', [
                'model' => $model,
                'rechazo' => $rechazo
            ]);
        }
        return $this->goHome();
    }


    public function actionTerminar($id = null)
    {
        $provider_id = Yii::$app->user->identity->getProvider();
        if($this->countClient($provider_id)>=1){
            $IdLastVal = $this->saveLastSendValidation($provider_id,'validClientes','clientes_contratos','bys');

            if ($id != null) {
                $status = $this->terminarUno('ClientesContratos', intval(base64_decode($id)), 'clientes_contratos_id', 'clientes_contratos', 'status_bys','bys',$IdLastVal);
            } else {
                $status = $this->terminarTodos('ClientesContratos', 'clientes_contratos_id', 'clientes_contratos', 'status_bys','bys',$IdLastVal);
            }

            if($status){
                $correos_validadores = self::getEmailValidador(2,'bys');

                self::sendEmail('/provider/correos/nuevo', null, $correos_validadores, 'Nuevo proveedor', ['modulo' => 'Experiencia comercial','tipo_provider' => 'bys']);

                self::AllSendNotification(null,'bys',null,'DATOS TECNICOS','Experiencia comercial');

                GeneralController::allModulesComplete($provider_id);
            }

        }else{
            Yii::$app->session->setFlash('error', 'Para continuar con el proceso, se requiere al menos de tres clientes, con una relación comercial comprobada mayor a 12 meses. cumplidos.');

        }
        return $this->redirect('index');
    }


    public function actionTerminarPerfil($id = null)
    {
        $id = intval(base64_decode($id));
        if($id == null || !Provider::verifyProvider($id) || !Provider::verifyEtapa3($id,true)){
            echo $this->render('/site/error',['name' => 'Lo sentimos...','message' => 'Acceso denegado']);

            return false;
        }
        $status='status_bys';

        $rfc = Yii::$app->db->createCommand();
        $rfc->update('provider.perfil',[$status => Status::STATUS_PORVALIDAR],'provider_id ='.$id);
        $rfc->execute();

        ExpirationDocuments::updateAll(['status' => true],['provider_id' => $id, 'module' => 'perfil']);

        $IdLastVal = $this->saveLastSendValidation($id,'mainPerfil','perfil','bys');
        $this->saveLastSendValidationRelation($IdLastVal,$id,'perfil',$id,'bys');

        self::AllSendNotification(null,'bys',null,'PERFIL','Perfil');

        GeneralController::allModulesComplete($id);

        return $this->redirect('index');
    }


    public function actionCurriculumupdate($id = null)
    {
        if (false) { //pendiente de validaciones
            $this->redirect("/");
        }
        $model = $this->findModelCurriculum(intval(base64_decode($id)), 'curriculum_id');
        $model->provider_id = Yii::$app->user->identity->providerId;
        if ($model->load(Yii::$app->request->post()) && $model->save()) {
            $this->redirect('/bys/perfil');
        }
        return $this->renderAjax('curriculumupdate', [
            'model' => $model,
        ]);
    }


    public function actionDelete($id)
    {

        $model = $this->findModel($id);
        if($this->verifyProviderCert($model->provider_id,'bys')>0){
            $arr_moviments = [
                'provider_id' => $model->provider_id,
                'model' => 'clientes_contratos',
                'action' => 'Eliminó Experiencia Comercial',
                'origin_id' => $id,
                'column' => null,
                'column_data_old' => null,
                'column_date_new' => null,
                'full_model' => true,
                'data_model' => $model->attributes
            ];
            $this->insertAllMovements($arr_moviments);
        }

        $model->activo = false;
        $model->save(false);

        return $this->redirect(['/bys/experiencia/view']);
    }


    protected function findModel($id){
        return (($model = ClientesContratos::findOne($id)) !== null)  ? $model : new ClientesContratos();
    }


    public function findModelPorcentaje($id)
    {
        $model = Porcentaje::find()->where(['register_id' => $id])->one();
        if ($model == null || empty($model)) {
            $model = new Porcentaje();
        }
        return $model;
    }

    public function findModelCurriculum($id, $search = 'provider_id')
    {
        $model = Curriculum::find()->where([$search => $id])->one();
        if ($model == null || empty($model)) {
            $model = new Curriculum();
        }
        return $model;
    }

    protected function findModelProv($id)
    {
        $model = Provider::find()->where(['provider_id' => $id])->one();
        if ($model == null || empty($model)) {
            throw new NotFoundHttpException('The requested page does not exist.');
        }
        return $model;
    }

    public function findModelUbicacionProv($id = 0)
    {

        if (($model = Ubicacion::find()->where(['and',['provider_id' => $id],['type_address_prov' => 'DOMICILIO FISCAL']])->one())===null) {
            $model = new Ubicacion();
        }
        return $model;
    }

    public function findModelRfcProv($id = 0)
    {

        if (($model = Rfc::find()->where(['provider_id' => $id])->one())===null) {
            $model = new Rfc();
        }
        return $model;
    }

    public function verifyUpdate($provider, $id)
    {
        if ($provider) {
            $status = ClientesContratos::find()->select('status_bys')->where(['and', ['provider_id' => $provider], ['clientes_contratos_id' => $id]])->one()['status_bys'];
            $rechazo = Status::find()->getStatusRechazarCotejo($provider);
            if ($status == Status::STATUS_ENEDICION || $status == Status::STATUS_RECHAZADO || $status == Status::STATUS_VALIDADO || !empty($rechazo)) {
                return true;
            }
        }
        return false;
    }

    public function findModelComprobanteDomicilio($id)
    {
        $model = ComprobanteDomicilio::find()->where(['provider_id' => $id])->one();
        if ($model == null || empty($model)) {
            $model = new ComprobanteDomicilio();
        }
        return $model;
    }
}
