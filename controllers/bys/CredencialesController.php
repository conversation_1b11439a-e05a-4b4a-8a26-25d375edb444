<?php

namespace app\controllers\bys;

use app\helpers\GeneralController;
use app\models\DatosValidados;
use app\models\Porcentaje;
use app\models\Provider;
use app\models\Status;
use app\models\Usuarios;
use kartik\form\ActiveForm;
use Yii;
use app\models\Certificacion;
use app\models\CertificacionSearch;
use yii\helpers\ArrayHelper;
use yii\db\Command;
use yii\db\Query;
use yii\web\Controller;
use yii\web\NotFoundHttpException;
use yii\filters\VerbFilter;
use yii\web\Response;
use yii\web\UploadedFile;
use app\models\Module;

if (Yii::$app->user->can(Usuarios::ROLE_PROVIDER)) {
    Yii::$app->params['provider_id'] = Yii::$app->user->identity->getProvider();
}

/**
 * CertificacionController implements the CRUD actions for Certificacion model.
 */
class CredencialesController extends GeneralController
{
    /**
     * @inheritdoc
     */
    public function behaviors()
    {
        return [
            'verbs' => [
                'class' => VerbFilter::className(),
                'actions' => [
                    'delete' => ['POST','GET'],
                ],
            ],
        ];
    }

    public function actionIndex($id = null)
    {

        $id = intval($id);
        if (Yii::$app->user->can(Usuarios::ROLE_PROVIDER)) {
            $id = Yii::$app->params['provider_id'];
        }
        if ($id == null || strlen($id) > 15 || !Provider::verifyProviderExistence($id) || !Yii::$app->user->can(Usuarios::ROLE_PROVIDER) || !Provider::verifyEtapa3($id)) {
            $this->msgError();
        }

        $searchModel = new CertificacionSearch();
        $dataProvider = $searchModel->search(Yii::$app->request->queryParams);
        $validados = $searchModel->search(Yii::$app->request->queryParams, 'validadosFilter');
        $rechazados = $searchModel->search(Yii::$app->request->queryParams, 'rechazadosFilter');
        $pendientes = $searchModel->search(Yii::$app->request->queryParams, 'pendientesFilter');

        $rechazo = Status::find()->getStatusRechazarCotejo($id);

        $modelImport = new \yii\base\DynamicModel([
            'permission'=>'Permisos',
        ]);
        $modelImport->addRule(['permission'],'string');

        $countPermissionFalse = Certificacion::find()->where(['and',['provider_id' => $id],['permissions' => false]])->count('1');

        return $this->render('index', [
            'searchModel' => $searchModel,
            'dataProvider' => $dataProvider,
            'validados' => $validados,
            'rechazados' => $rechazados,
            'pendientes' => $pendientes,
            'rechazo' => $rechazo,
            'id' => $id,
            'modelImport' => $modelImport,
            'countPermissionFalse' => $countPermissionFalse
        ]);
    }

    public function actionNotPermissions($id = null){
        $id = intval($id);

        if($id){
            $cer = new Certificacion();
            $cer->provider_id = $id;
            $cer->status_bys = 'POR VALIDAR';
            $cer->permissions = false;
            if($cer->save(false)){
                //$IdLastVal = $this->saveLastSendValidation(Yii::$app->user->identity->getProvider(),'validCertificacion','certificacion','bys');
            }

        }


        return $this->redirect(['/bys/experiencia/view']);
    }

    /**
     * Displays a single Certificacion model.
     * @param string $id
     * @return mixed
     */
    public function actionView($id = null, $user_id = null)
    {
        $user_id = intval($user_id);
        $id = intval($id);
        $modelos = [];
        if ($user_id == null
            || strlen($user_id) > 15
            || (!Certificacion::verifyCertificacion($id) && Yii::$app->user->can(Usuarios::ROLE_PROVIDER))
            || (!Certificacion::verifyProviderExistenceCer($id) && Yii::$app->user->can(Usuarios::ROLE_PROVIDER))
            || !Provider::verifyEtapa3($user_id)) {
            $this->msgError();
        }
        $model_status = new Status();
        $model_status->scenario = Status::SCENARIO_CREATE;
        $certificacion = $this->findModel($id);

        if (Yii::$app->user->can(Usuarios::ROLE_VALIDADOR_PROVEEDORES)) {
            if (isset($certificacion->provider_id) && !empty($certificacion->provider_id)) {
                $modelos[$certificacion->formName()] = $certificacion->attributes;
            }
            $modelos = base64_encode(json_encode($modelos));

        }
        return $this->renderAjax('view', [
            'model' => $certificacion,
            'model_status' => $model_status,
            'modelos' => $modelos
        ]);
    }


    /**
     * Displays a single Certificacion model.
     * @param string $id
     * @return mixed
     */
    public function actionViewValidar($id = null, $user_id = null)
    {
        $queryParams = Yii::$app->request->queryParams;
        $id = isset($queryParams['id']) ? intval( $queryParams['id'] ) : null;
        $user_id = isset($queryParams['user_id']) ? intval( $queryParams['user_id'] ) : null;
        $opcionesArray = isset($queryParams['opciones']) ? $queryParams['opciones'] : null;
        $opcionesSerial = isset($opcionesArray) ? serialize($opcionesArray) : null;
        $opcionesBase64 = isset($opcionesSerial) ? base64_encode($opcionesSerial) : null;
        $modelos = [];
        if ($user_id == null
            || strlen($user_id) > 15
            || (!Certificacion::verifyCertificacion($id) && Yii::$app->user->can(Usuarios::ROLE_PROVIDER))
            || (!Certificacion::verifyProviderExistenceCer($id) && Yii::$app->user->can(Usuarios::ROLE_PROVIDER))
            || !Provider::verifyEtapa3($user_id)) {
            $this->msgError();
        }
        $model_status = new Status();
        $model_status->scenario = Status::SCENARIO_CREATE;
        $model_validado = new DatosValidados();
        $model_validado->scenario = DatosValidados::SCENARIO_CREATE;
        $certificacion = $this->findModel($id);

        if (Yii::$app->user->can(Usuarios::ROLE_VALIDADOR_PROVEEDORES) || Yii::$app->user->can(Usuarios::ROLE_ADMIN_PROVIDER)) {
            if (isset($certificacion->provider_id) && !empty($certificacion->provider_id)) {
                $modelos[$certificacion->formName()] = $certificacion->attributes;
            }
            $modelos = base64_encode(json_encode($modelos));

        }
        $id_provider = Certificacion::find()->select('provider_id')->where(['certificacion_id' => $id])->one()['provider_id'];

        $rechazo = Status::find()->getStatus($id, 'certificacion', 'bys','TERMINADO PRO');
        $movements = $this->countAllMovements($id,'certificacion');

        return $this->renderAjax('view-validar', [
            'model' => $certificacion,
            'model_status' => $model_status,
            'modelos' => $modelos,
            'id_provider' => $id_provider,
            'model_validado' => $model_validado,
            'rechazo' => $rechazo,
            'namePro' => $this->getNameProvider($id_provider),
            'movements' => $movements,
            'id' => $id,
            'opciones' => $opcionesBase64,
            'moduleName' => 'certificacion'
        ]);
    }
 
    /* public function actionCreate($user_id) {
        if(Yii::$app->request->isAjax || Yii::$app->request->post()) {
            if ($user_id == null || !Provider::verifyProvider($user_id) || !Provider::verifyEtapa3($user_id)) {
                $this->msgError();
            }
            $model = new Certificacion();
            $model->url_archivoURL = UploadedFile::getInstance($model, 'url_archivo');
            $t = $model->getDb()->beginTransaction();
            if (Yii::$app->request->isAjax && $model->load(Yii::$app->request->post())) {
                Yii::$app->response->format = Response::FORMAT_JSON;
                return ActiveForm::validate($model);
            } else if ($model->load(Yii::$app->request->post())) {
                $porcentaje = 0;
                if (isset($_POST['porcentaje_total_m']) && $_POST['porcentaje_total_m'] != '') {
                    $porcentaje = round($_POST['porcentaje_total_m']);
                }
                $model->porcentaje = $porcentaje;
                $model->provider_id = $user_id;
                $this->makeDir($model->path);
                if (!empty($model->url_archivo) && $model->url_archivo != $model->url_archivoOLD) {
                    $new_nameFot = str_replace('archivos_tmp/', $model->path . '/', $model->url_archivo);
                    $this->copyFile($model->url_archivo, $new_nameFot);
                    $model->url_archivo = $new_nameFot;
                }
                if ($model->save(false)) {

                    if ($this->verifyProviderCert($model->provider_id, 'bys') > 0) {
                        $arr_moviments = [
                            'provider_id' => $model->provider_id,
                            'model' => 'certificacion',
                            'action' => 'Creacion Permisos',
                            'origin_id' => $model->certificacion_id,
                            'column' => null,
                            'column_data_old' => null,
                            'column_date_new' => null,
                            'full_model' => true,
                            'data_model' => $model->attributes
                        ];
                        $this->insertAllMovements($arr_moviments);
                    }
                    self::eliminarCita();
                    $t->commit();
                    self::updateModulesBys($model->provider_id,'bys_experiencia');
                    return $this->redirect(['/bys/experiencia/view']);
                }
                return $this->goHome();

            }
            return $this->renderAjax('create', [
                'model' => $model,
            ]);
        }
        return $this->goHome();
    }*/

    public function actionUpdate($id=null)
    {
        if(Yii::$app->request->isAjax  || Yii::$app->request->post()) {
            $user_id = Yii::$app->user->identity->providerid;
            $id = intval(base64_decode($id));
            /*if ( !Provider::verifyProvider($user_id) || !Provider::verifyEtapa3($user_id) || !self::verifyUpdate($user_id, $id)) {
                echo $this->render('/site/error', ['name' => 'Lo sentimos...', 'message' => 'Acceso denegado']);

                return false;
            }*/
            $model = $this->findModel($id);
            $modelOld = $model->attributes;
            $t = $model->getDb()->beginTransaction();
            $model->url_archivoOLD = $model->url_archivo;

            $modelStatus = Module::getstatusbymodule('bys_experiencia',$model->provider_id);
            $rechazo = $modelStatus->status_bys == Status::STATUS_RECHAZADO || $modelStatus->status_bys == Status::STATUS_ENEDICION  ?
                Status::find()->getRechazo( 'bys_experiencia',$model->provider_id) : null;

            if (Yii::$app->request->isAjax && $model->load(Yii::$app->request->post())) {
                Yii::$app->response->format = Response::FORMAT_JSON;
                $validaciones = [];
                if(empty($model->vigencia) && !$model->undefined){ $validaciones['certificacion-vigencia'] = ['El certificado debe tener una vigencia o ser indefinido']; }
                if(empty($model->url_archivo)){ $validaciones['certificacion-url_archivo'] = ['El documento es requerido']; }
                return array_merge($validaciones,ActiveForm::validate($model));
/*                 return ActiveForm::validate($model);
 */            } else if ($model->load(Yii::$app->request->post())) {

                $this->makeDir($model->path);
                if (!empty($model->url_archivo) && $model->url_archivo != $model->url_archivoOLD) {
                    $new_nameFot = str_replace('archivos_tmp/', $model->path . '/', $model->url_archivo);
                    $this->copyFile($model->url_archivo, $new_nameFot);
                    $model->url_archivo = $new_nameFot;
                }
                $model->provider_id = $user_id;
                $model->permissions = true;
                $model->status_bys = Status::STATUS_ENEDICION;
                if ($model->save()) {
                    if (($req_status = Status::find()->where(['and', ['register_id' => $id], ['status_bys' => Status::STATUS_PENDIENTE], ['modelo' => 'certificacion']])->one()) !== null) {
                        $req_status->status_bys = Status::STATUS_TERMINADO_PRO;
                        $req_status->save();
                    }
                    $countValCert = $this->verifyProviderCert($model->provider_id, 'bys');
                    $countValRec = self::verifyValRec('certificacion',$model->certificacion_id);
                    if ($countValCert > 0 || $countValRec>0) {
                        $trueFalse = $countValCert>0?true:false;
                        $this->compareModels($modelOld, $model->attributes, $model->certificacion_id, 'certificacion',$model->provider_id,$trueFalse);
                    }
                    self::eliminarCita();
                    $t->commit();
                    self::updateModulesBys($model->provider_id,'bys_experiencia');
                    return $this->redirect(['/bys/experiencia/view']);
                }else{
                    return $this->goHome();
                }
            }
            return $this->renderAjax('update', [
                'model' => $model,
                'rechazo' => $rechazo
            ]);
        }
        return $this->goHome();
    }



    //    public function actionTerminar($user_id,$id){
//
//
//        $tipo_provider = Provider::find()->select('tipo_provider')->where(['provider_id' => Yii::$app->params['provider_id']])->one()->tipo_provider;
//        ($tipo_provider=='bys')?$status='status_bys':$status='status_op';
//        if(!empty($id)){
//            $model = $this->findModel($id);
//            if($model->porcentaje == 100){
//                $model->$status = Status::STATUS_PORVALIDAR;
//                $model->save(false);
//            }
//        }else{
//            Certificacion::updateAll([$status => Status::STATUS_PORVALIDAR],['and',[$status =>  Status::STATUS_ENEDICION],['provider_id' => $user_id],['porcentaje'=>100]]);
//        }
//
//
//        $query = new Query;
//        $query	->select('usuarios.email')
//            ->from('usuarios')
//            ->innerJoin('usuarios_responsabilidades',
//                'usuarios_responsabilidades.user_id = usuarios.user_id')
//            ->where(['and',['usuarios.role' => 'VALIDADOR PROVEEDORES', 'usuarios.status' => 'ACTIVO'],['usuarios_responsabilidades.responsabilidad_id' => 2]]);
//        $command = $query->createCommand();
//        $correos_validadores = ArrayHelper::getColumn($command->queryAll(),'email');
//
//        self::sendEmail('/provider/correos/nuevo',null,$correos_validadores,'Nuevo proveedor',['modulo' => 'Credenciales']);
//
//        return $this->redirect(['bys/credenciales/index']);
//    }

    public function actionTerminar($id = null)
    {

        $IdLastVal = $this->saveLastSendValidation(Yii::$app->user->identity->getProvider(),'validCertificacion','certificacion','bys');

        if ($id != null) {
            $status = $this->terminarUno('Certificacion', intval(base64_decode($id)), 'certificacion_id', 'certificacion', 'status_bys','',$IdLastVal);
        } else {
            $status = $this->terminarTodos('Certificacion', 'certificacion_id', 'certificacion', 'status_bys','',$IdLastVal);
        }

        if($status){
            $correos_validadores = self::getEmailValidador(2,'bys');

            self::sendEmail('/provider/correos/nuevo', null, $correos_validadores, 'Nuevo proveedor', ['modulo' => 'Credenciales','tipo_provider' => 'bys']);

            self::AllSendNotification(null,'bys',null,'DATOS TECNICOS','Credenciales');

            GeneralController::allModulesComplete(Yii::$app->user->identity->getProvider());
        }

        return $this->redirect('index');
    }

    /**
     * Deletes an existing Certificacion model.
     * If deletion is successful, the browser will be redirected to the 'index' page.
     * @param string $id
     * @return mixed
     */
    public function actionDelete($id)
    {


        $model = $this->findModel($id);

        if ($this->verifyProviderCert($model->provider_id, 'bys') > 0) {
            $arr_moviments = [
                'provider_id' => $model->provider_id,
                'model' => 'certificacion',
                'action' => 'Eliminó Permiso',
                'origin_id' => $id,
                'column' => null,
                'column_data_old' => null,
                'column_date_new' => null,
                'full_model' => true,
                'data_model' => $model->attributes
            ];
            $this->insertAllMovements($arr_moviments);
        }

        $model->activo = false;
        $model->save(false);

        return $this->redirect(['/bys/experiencia/view']);
    }

    /**
     * Finds the Certificacion model based on its primary key value.
     * If the model is not found, a 404 HTTP exception will be thrown.
     * @param string $id
     * @return Certificacion the loaded model
     * @throws NotFoundHttpException if the model cannot be found
     */
    protected function findModel($id){
        return  (($model = Certificacion::findOne($id)) !== null) ? $model : new Certificacion();
    }

    public function findModelPorcentaje($id)
    {
        $model = Porcentaje::find()->where(['register_id' => $id])->one();
        if ($model == null || empty($model)) {
            $model = new Porcentaje();
        }
        return $model;
    }

    public function verifyUpdate($provider, $id)
    {

        if ($provider) {
            $status = Certificacion::find()->select('status_bys')->where(['and', ['provider_id' => $provider], ['certificacion_id' => $id]])->one()['status_bys'];
            $rechazo = Status::find()->getStatusRechazarCotejo($provider);

            if ($status == Status::STATUS_VALIDADO || $status == Status::STATUS_ENEDICION || $status == Status::STATUS_RECHAZADO || !empty($rechazo)) {
                return true;
            }
        }
        return false;
    }
}
