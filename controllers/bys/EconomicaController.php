<?php

namespace app\controllers\bys;

use app\helpers\FileHelper;
use app\helpers\GeneralController;
use app\models\ActaConstitutiva;
use app\models\ActaConstitutivaSearch;
use app\models\AltaHacienda;
use app\models\AltaHaciendaSearch;
use app\models\CatActividades;
use app\models\CatEntidades;
use app\models\CatMunicipios;
use app\models\CatRamas;
use app\models\CatSectores;
use app\models\CertificacionSearch;
use app\models\City;
use app\models\Clase;
use app\models\ConceptoFamilia;
use app\models\ConceptoGrupo;
use app\models\ConceptoLinea;
use app\models\Curp;
use app\models\DatosValidados;
use app\models\EscrituraPublica;
use app\models\EscrituraPublicaSearch;
use app\models\Giro;
use app\models\GrupoProducto;
use app\models\Historico;
use app\models\IdOficial;
use app\models\ComprobanteDomicilio;
use app\models\Division;
use app\models\Grupo;
use app\models\ModelGiro;
use app\models\ModificacionActa;
use app\models\Module;
use app\models\Porcentaje;
use app\models\Producto;
use app\models\ProviderGiro;
use app\models\RegistroImss;
use app\models\RepresentanteLegal;
use app\models\Model;
use app\models\Productos;
use app\models\Provider;
use app\models\RepresentanteLegalSearch;
use app\models\State;
use app\models\Status;
use app\models\RegistroActas;
use kartik\form\ActiveForm;
use Yii;
use app\models\Rfc;
use app\models\RfcSearch;
use app\models\Usuarios;
use app\models\UsuariosResponsabilidades;
use yii\db\Expression;
use yii\db\Query;
use yii\helpers\ArrayHelper;
use yii\helpers\Json;
use yii\web\Controller;
use yii\web\NotFoundHttpException;
use yii\filters\VerbFilter;
use yii\web\Response;
use yii\web\UploadedFile;

if(Yii::$app->user->can(Usuarios::ROLE_PROVIDER)){
    Yii::$app->params['provider_id'] = Yii::$app->user->identity->getProvider();
}

/**
 * RfcController implements the CRUD actions for Rfc model.
 */
class EconomicaController extends GeneralController{

    public function behaviors(){
        return [
            'verbs' => [
                'class' => VerbFilter::className(),
                'actions' => [
                    'delete' => ['POST','GET'],
                ],
            ],
        ];
    }

    public function actionIndex(){
        echo $this->render('/site/error',['name' => 'Lo sentimos...','message' => 'Acceso denegado']);
        return false;
    }

    public function actionView(){
        $provider = Provider::find()->where(['user_id'=>Yii::$app->user->getId()])->one();

        //Rechazos
        $modelStatus = Module::getstatusbymodule('bys_bys',$provider->provider_id);
        $rechazo = $modelStatus->status_bys == Status::STATUS_RECHAZADO  || $modelStatus->status_bys == Status::STATUS_ENEDICION  ?
            Status::find()->getRechazo( 'bys_bys', $provider->provider_id) : null;

        return $this->render('view', [
            'model' => $this->findModel($provider->provider_id),
            //'model_act' => $this->findModelAct($provider->provider_id),
            'modelProductos' => $this->getViewProviderGiro($provider->provider_id),
            /* 'modelProductos' => $this->getDataProviderGiro($provider->provider_id), */
            'provider'=>$provider,
            'modelStatus'=>$modelStatus,
            'rechazo'=>$rechazo
        ]);
    }


    public function actionUpdate(){
        $id = Yii::$app->user->identity->providerid;

        //Rechazos
        $modelStatus = Module::getstatusbymodule('bys_bys',$id);
        $rechazo = $modelStatus->status_bys == Status::STATUS_RECHAZADO  || $modelStatus->status_bys == Status::STATUS_ENEDICION  ?
            Status::find()->getRechazo( 'bys_bys', $id) : null;

        $dataFiles = $dataModelGiros = [];
        $modelGiro = $this->findModelAct($id);
        if($modelGiro){
            foreach ($modelGiro as $v){
                $dataModelGiros[$v->giro_id] = $v->attributes;
                if(isset($v->url_factura_c_c) && !empty($v->url_factura_c_c)){
                    $dataFiles[$v->giro_id] = $v->url_factura_c_c;
                }
            }
        }
        $modelProviderGiro = $this->getDataProviderGiro($id);
        if (ModelGiro::loadMultiple($modelGiro, Yii::$app->request->post()) ) {
            //$t = Yii::$app->db->beginTransaction();
            $status = 'status_bys';

            $update = Yii::$app->db->createCommand();
            $update->update('provider.alta_hacienda', [$status => Status::STATUS_ENEDICION], 'provider_id =' . $id);
            $update->execute();
            if (($requi_status = Status::find()->where(['and', ['register_id' => $id], ['status_bys' => Status::STATUS_PENDIENTE],['modelo' => 'alta_hacienda']])->one())!==null) {
                $requi_status->status_bys = Status::STATUS_TERMINADO_PRO;
                $requi_status->save();
            }

            $verify_save = true;



            if(isset(Yii::$app->request->post()['ProviderGiro'])){
                $dataProviderGiro = Yii::$app->request->post()['ProviderGiro'];
                ProviderGiro::deleteAll(['provider_id' => $id]);
                foreach ($dataProviderGiro as $v => $k){
                    if(isset($k['producto_id']) && !empty($k['producto_id'])){

                        if (!empty($k['documento']) && GeneralController::str_contains($k['documento'], "archivos_tmp") ) {
                            $user_id = Yii::$app->user->getId();
                            $provider_giro_path = ProviderGiro::$path . "/{$user_id}/";
                            $this->makeDir($provider_giro_path);
                            $new_namePg = str_replace('archivos_tmp/', $provider_giro_path, $k['documento']);
                            $this->copyFile($k['documento'], $new_namePg);
                            $k['documento'] = $new_namePg;
                        }

                        foreach ($k['producto_id'] as $val){
                            try{
                                $proGiro = new ProviderGiro();
                                $proGiro->producto_id = $val;
                                $proGiro->provider_id = $id;
                                $proGiro->documento = $k['documento'];
                                $proGiro->vigencia = $k['vigencia'];
                                $proGiro->especialidad = isset($k['especialidad']) ? $k['especialidad'] : false;
                                $proGiro->indefinido = isset($k['indefinido']) ? $k['indefinido'] : false;
                                $proGiro->save();
                            }catch (\Exception $e){
                                Yii::$app->session->setFlash('error', "Seleccionar al menos un producto");
                            }

                        }
                    }
                }
            }
            if ($verify_save) {

                $countValCert = 0;
                $countValRec = 0;

                $oldIDs = ArrayHelper::map($modelGiro, 'giro_id', 'giro_id');
                $modelGiro = ModelGiro::createMultiple(Giro::classname(), $modelGiro);
                ModelGiro::loadMultiple($modelGiro, Yii::$app->request->post());
                $deletedIDs = array_diff($oldIDs, array_filter(ArrayHelper::map($modelGiro, 'giro_id', 'giro_id')));
                //$validar = ModelGiro::validateMultiple($modelGiro);

                if (!empty($deletedIDs)) {
                    Giro::deleteAll(['giro_id' => $deletedIDs]);
                }

                $total = 0;
                $error = false;
                $msgErr = null;
                $this->makeDir('bys/actividad_economica/giro');
                foreach ($modelGiro as $modelAct) {
                    //try{

                        $actID= 0;
                        $nombreAct = strtoupper(trim($modelAct->act_name));
                        $modelActOld = [];
                        if(($actName = CatActividades::find()->where(['nombre_actividad' => $nombreAct])->one())===null){
                            $ramaId = CatRamas::find()->select('rama_id')->where(['nombre_rama' => 'OTROS'])->one()['rama_id'];
                            $actName = new CatActividades();
                            $actName->nombre_actividad = $nombreAct;
                            $actName->descripcion = $nombreAct;
                            $actName->rama_id = $ramaId;
                            if($actName->save()){
                                $actID = $actName->actividad_id;
                            }

                        }else{
                            $actID = $actName->actividad_id;
                        }

                        $modelAct->actividad_id = $actID;
                        $modelAct->provider_id = $id;

                        if (!($flag = $modelAct->save())) {
                            //$t->rollBack();
                            $err = isset($modelAct->errors['act_name'])?$modelAct->errors['act_name']:'No puede repetir la actividad';
                            Yii::$app->session->setFlash('error', 'Error inesperado');
                            return $this->redirect(['update']);
                            break;
                        }else{

                            if (!empty($modelAct->url_factura_c_c) && isset($dataFiles[$modelAct->giro_id]) && $modelAct->url_factura_c_c != $dataFiles[$modelAct->giro_id]) {
                                $new_nameFactura = str_replace('archivos_tmp/', 'bys/actividad_economica/giro/', $modelAct->url_factura_c_c);
                                $this->copyFile($modelAct->url_factura_c_c, $new_nameFactura);
                                $modelAct->url_factura_c_c = $new_nameFactura;
                                $modelAct->save();
                            }else if (!empty($modelAct->url_factura_c_c)) {
                                $new_nameFactura = str_replace('archivos_tmp/', 'bys/actividad_economica/giro/', $modelAct->url_factura_c_c);
                                $this->copyFile($modelAct->url_factura_c_c, $new_nameFactura);
                                $modelAct->url_factura_c_c = $new_nameFactura;
                                $modelAct->save();
                            }
                            $total += $modelAct->porcentaje;

                            $countValCert = $this->verifyProviderCert($id, 'bys');
                            //$countValRec = self::verifyValRec('alta_hacienda',$model->provider_id);

                            if (($countValCert > 0 ) && isset($dataModelGiros[$modelAct->giro_id])) {
                                $trueFalse = $countValCert>0?true:false;
                                $this->compareModels($dataModelGiros[$modelAct->giro_id], $modelAct->attributes, $id, 'giro',$id,$trueFalse);
                                unset($dataModelGiros[$modelAct->giro_id]);
                            }else if($countValCert > 0 ){

                                $acInsetr = $modelAct->attributes;
                                $arr_moviments = [
                                    'model' => 'alta_hacienda',
                                    'action' => 'Crear Actividad Economica',
                                    'origin_id' => $acInsetr['provider_id'],
                                    'provider_id' => $acInsetr['provider_id'],
                                    'column' => null,
                                    'column_data_old' => null,
                                    'column_date_new' => null,
                                    'full_model' => true,
                                    'data_model' => $acInsetr
                                ];
                                $this->insertAllMovements($arr_moviments);
                            }
                        }

                    /*}catch (\Exception $e){
                        $error = true;
                        $msgErr = 'Seleccionar al menos una actividad';
                    }*/

                }


                if(!empty($dataModelGiros) && $countValCert > 0 || $countValRec>0){

                    foreach ($dataModelGiros as $key => $da){
                        $arr_moviments = [
                            'model' => 'alta_hacienda',
                            'action' => 'Eliminó Actividad Económica',
                            'origin_id' => $da['provider_id'],
                            'provider_id' => $da['provider_id'],
                            'column' => null,
                            'column_data_old' => null,
                            'column_date_new' => null,
                            'full_model' => true,
                            'data_model' => $da,
                        ];
                        $this->insertAllMovements($arr_moviments);
                    }

                }

                if($total!=100 || $error){
                    //$t->rollBack();
                    $msgErr = $msgErr!=null?$msgErr:"El porcentaje debe ser igual a 100";
                    Yii::$app->session->setFlash('error', $msgErr);
                    return $this->redirect(['update']);
                }

                /* $this->makeDir($model->path);


                    if (!empty($model->url_rfc) && $model->url_rfc != $urlRfcOld) {
                        $new_nameRfc = str_replace('archivos_tmp/', $model->path . '/', $model->url_rfc);
                        $this->copyFile($model->url_rfc, $new_nameRfc);
                        $model->url_rfc = $new_nameRfc;
                    }
                    $model->save();


                    $countValCert = $this->verifyProviderCert($id, 'bys');
                    $countValRec = self::verifyValRec('alta_hacienda',$id);

                    if ($countValCert > 0 || $countValRec>0) {
                        $trueFalse = $countValCert>0?true:false;
                        $this->compareModels($modelOld, $model->attributes, $model->provider_id, 'rfc_economica',$model->provider_id,$trueFalse);
                    }
                */

                self::eliminarCita();
                //$t->commit();
                self::updateModulesBys($id,'bys_bys');
                return $this->redirect(['view']);

            }

        }

        $sector = CatSectores::find()->getSector();
        $rama = [];
        $actividad = [];

        if ($modelGiro[0]['actividad_id']) {
            for ($x = 0; $x < count($modelGiro); $x++) {
                $rama[$x] = !empty($modelGiro[$x]['actividad_id']) ? CatRamas::find()->getRama($modelGiro[$x]['actividad_id']) : '';
                $actividad[$x] = !empty($modelGiro[$x]['actividad_id']) ? CatActividades::find()->getActividad($modelGiro[$x]['actividad_id']) : [];
            }
        }
        $mods = ArrayHelper::map(
            ModificacionActa::find()->select(['modificacion_acta_id', 'nombre_documento'])
                ->where(['and', ['provider_id' => Yii::$app->user->identity->providerid], ['activo' => TRUE], ['status_' . Yii::$app->user->identity->tipo => Status::STATUS_VALIDADO]])
                ->asArray()->all(), 'modificacion_acta_id', 'nombre_documento');


        $grupoFamilia = ArrayHelper::map(
            ConceptoFamilia::find()
                ->select(['concepto_familia_id'])
                ->addSelect(new \yii\db\Expression("concat_ws(' ',familia,concepto) as concepto"))
                ->all(),'concepto_familia_id','concepto');


        return $this->render('update', [
            'modificaciones' => $mods,
            'modelGiro' => (empty($modelGiro)) ? [new Giro()] : $modelGiro,
            'modelProviderGiro' => (empty($modelProviderGiro)) ? [new ProviderGiro()] : $modelProviderGiro,
            'rama' => $rama,
            'sector' => $sector,
            'actividad' => $actividad,
            'provider_id' => $id,
            'rechazo' => $rechazo,
            'grupoFamilia' => $grupoFamilia
        ]);
    }

    public function actionUpdateActividad($id=null){
        $isRequest = Yii::$app->request->isAjax;
        $postRequest = Yii::$app->request->post();
        $user_id = Yii::$app->user->getId();
        $provider = Provider::find()->where(['user_id'=>$user_id])->one();

        $modelStatus = Module::getstatusbymodule('bys_bys',$provider->provider_id);
        $rechazo = $modelStatus->status_bys == Status::STATUS_RECHAZADO  || $modelStatus->status_bys == Status::STATUS_ENEDICION  ?
            Status::find()->getRechazo( 'bys_bys', $provider->provider_id) : null;
    
        $isUpdate = !is_null($id);

        $modelGiro = $this->getActividadGiro($provider->provider_id, $id, $isUpdate);
        $actividad_name = $isUpdate ? $modelGiro->actividad->nombre_actividad : null;

        $modelGiro->scenario = Giro::SCENARIO_BYS;

        if( $isRequest || $postRequest ){
            if( $isRequest && $modelGiro->load($postRequest) ){
                Yii::$app->response->format = Response::FORMAT_JSON; 
                $result = [];
                $isRepeted = self::findRepetedActividad($provider->provider_id, $modelGiro->act_name, $id, $isUpdate);
                if( $isRepeted ){ $result['giro-act_name'] = ['Esta actividad ya se encuentra registrada']; }
                $porcentaje = self::getPorcentajeActividades($provider->provider_id, $id, $isUpdate);
                $porcentaje_modelo = $modelGiro->porcentaje != '' ? $modelGiro->porcentaje : 0;
                if( ($porcentaje + $porcentaje_modelo) > 100 ){
                    $result['giro-porcentaje'] = ['La suma de porcentajes de tus actividades no debe ser mayor a 100'];
                }
                return $result; 
            }else if ($modelGiro->load($postRequest)) {
                $nombre_actividad = strtoupper( trim($modelGiro->act_name) );
                $actividad = CatActividades::find()->where(['nombre_actividad' => $nombre_actividad])->one();
                if($actividad === null){
                    $rama_id = CatRamas::find()->select('rama_id')->where(['nombre_rama' => 'OTROS'])->one()['rama_id'];
                    $actividad = new CatActividades();
                    $actividad->nombre_actividad = $nombre_actividad;
                    $actividad->descripcion = $nombre_actividad;
                    $actividad->rama_id = $rama_id;
                    $actividad->save();
                }

                $modelGiro->actividad_id = $actividad->actividad_id;
                $modelGiro->provider_id = $provider->provider_id;

                if(!$isUpdate){
                    $modelGiro->created_by = Yii::$app->user->getId();
                    $modelGiro->created_at = date('Y-m-d H:i:s');
                }

                $modelGiro->active = true;
                $modelGiro->last_updated_at = date('Y-m-d H:i:s');
                $modelGiro->last_updated_by = Yii::$app->user->getId();

                $modelGiro->save();

                return $this->redirect(['view']);

            }

            return $this->renderAjax('_form_giro', [
                'provider_id' => $provider->provider_id,
                'isUpdate' => $isUpdate,
                'rechazo' => $rechazo,
                'model' => $modelGiro,
                'actividad_name' => $actividad_name,
            ]);
        }


    }

    public function actionUpdateProducto($data=null){
        $isRequest = Yii::$app->request->isAjax;
        $postRequest = Yii::$app->request->post();
        $user_id = Yii::$app->user->getId();
        $provider = Provider::find()->where(['user_id'=>$user_id])->one();

         //Rechazos
        $modelStatus = Module::getstatusbymodule('bys_bys',$provider->provider_id);
        $rechazo = $modelStatus->status_bys == Status::STATUS_RECHAZADO  || $modelStatus->status_bys == Status::STATUS_ENEDICION  ?
            Status::find()->getRechazo( 'bys_bys', $provider->provider_id) : null;

        $isUpdate = isset($data);
        $data_ids = $isUpdate ? explode(",", base64_decode($data)) : null;

        $model = self::getProductosProviderGiro($provider->provider_id, $data_ids, $isUpdate);

        $divisiones = ArrayHelper::map( $isUpdate ? Division::find()->where(['division_id' => $model['familia'] ])->all() : Division::find()->all(), 'division_id', 'descripcion');
        $grupo = $isUpdate ? ArrayHelper::map( Grupo::find()->where(['grupo_id' => $model['grupo']])->all(), 'grupo_id', 'descripcion') : null;
        $clase = $isUpdate ? ArrayHelper::map( Clase::find()->where(['clase_id' => $model['clase']])->all(), 'clase_id', 'descripcion') : null;
        $list_productos = $isUpdate ? ArrayHelper::map( Productos::findAll(['clase_id' => $model['clase']]), 'producto_id','descripcion') : null;

        if( $isRequest || $postRequest ){
            if ( $isRequest && isset($_POST['Productos']) ) {
                Yii::$app->response->format = Response::FORMAT_JSON; 
                $result = [];
                $productos = $_POST['Productos']['producto_id'];
                $docData = $_POST['ProviderGiro']['documento'];
                $vigencia = $_POST['ProviderGiro']['vigencia'];
                $isIndefinido = $_POST['ProviderGiro']['indefinido'];
                $factura = $_POST['ProviderGiro']['factura'];
                $reqPermiso = Productos::ajaxPermiso($productos);
                if( !is_array($productos) || count($productos) == 0 ){ $result['productos-producto_id'] = ['Debe seleccionar al menos un producto']; }
                if( $reqPermiso && empty($docData) ){ $result['providergiro-documento'] = ['Uno o más productos requieren de un permiso']; }
                if( $reqPermiso && (empty($vigencia) && !$isIndefinido ) ){ $result['providergiro-vigencia'] = ['Uno o más productos requieren de un permiso']; }
                if( empty($factura) ){ $result['providergiro-factura'] = ['La factura es un campo requerido'];  }
                return $result; 
            } else if ( isset($_POST['Productos']) ) {
                $productos = $_POST['Productos']['producto_id'];
                $incoming_factura = isset($_POST['ProviderGiro']['factura']) ? $_POST['ProviderGiro']['factura'] : null;
                $req_documento = isset($_POST['ProviderGiro']['documento']) ? $_POST['ProviderGiro']['documento'] : null;
                $req_vigencia = isset($_POST['ProviderGiro']['vigencia']) ? $_POST['ProviderGiro']['vigencia'] : null;
                $req_indefinido = isset($_POST['ProviderGiro']['indefinido']) ? filter_var($_POST['ProviderGiro']['indefinido'], FILTER_VALIDATE_BOOLEAN) : false;
                $req_especialidad = isset($_POST['ProviderGiro']['especialidad']) ? filter_var($_POST['ProviderGiro']['especialidad'], FILTER_VALIDATE_BOOLEAN) : false;

                if( !empty($incoming_factura) && GeneralController::str_contains($incoming_factura, "archivos_tmp") ){
                    $user_id = Yii::$app->user->getId();
                    $provider_giro_path = ProviderGiro::$path . "/{$user_id}/";
                    $this->makeDir($provider_giro_path);
                    if(is_null($model['factura'])){
                        $new_filename_factura = str_replace('archivos_tmp/', $provider_giro_path, $incoming_factura);
                        $this->copyFile($incoming_factura, $new_filename_factura);
                        $incoming_factura = $new_filename_factura;
                    }else{
                        $new_filename_factura = $provider_giro_path. md5(uniqid()) . time() . ".pdf";
                        FileHelper::mergePdfFiles([$model['factura'], $incoming_factura], $new_filename_factura);
                        $incoming_factura = $new_filename_factura;
                    }
                    
                }
                
                if( !empty($req_documento) && GeneralController::str_contains($req_documento, "archivos_tmp") ){
                    $user_id = Yii::$app->user->getId();
                    $provider_giro_path = ProviderGiro::$path . "/{$user_id}/";
                    $this->makeDir($provider_giro_path);
                    $new_namePg = str_replace('archivos_tmp/', $provider_giro_path, $req_documento);
                    $this->copyFile($req_documento, $new_namePg);
                    $req_documento = $new_namePg;
                }

                //Se hara unset a los valores que seran actualizados
                $ids_delete = $data_ids;
                
                foreach($productos as $producto){
                    $model_producto = self::getOrCreateProviderGiro($producto, $provider->provider_id);
                    if($isUpdate){
                        //Valida cuales seran eliminados
                        if( !is_null($model_producto->id) ){
                            if( ( $key = array_search($model_producto->id, $ids_delete) ) !== false ){
                                unset($ids_delete[$key]);
                            }
                        }
                    }

                    $model_producto->factura = /* empty($incoming_factura) ? null : */ $incoming_factura;
                    $model_producto->documento = $req_documento;
                    $model_producto->vigencia = $req_indefinido ? null : $req_vigencia;
                    $model_producto->indefinido = $req_indefinido;
                    $model_producto->especialidad = $req_especialidad;
                    $model_producto->save();
                    
                }

                if($isUpdate){ self::deleteProviderGiro($ids_delete); }

                /* if (($requi_status = Status::find()->where(['and', ['register_id' => $id_register], ['status_bys' => Status::STATUS_PENDIENTE], ['modelo' => 'bys_bys']])->one()) !== null) {
                    $requi_status->status_bys = Status::STATUS_TERMINADO_PRO;
                    $requi_status->save();
                } */
                
                self::updateModulesBys($provider->provider_id,'bys_bys');

                return $this->redirect(['view']);

            } 

            return $this->renderAjax('_form_producto', [
                'provider_id' => $provider->provider_id,
                'isUpdate' => $isUpdate,
                'rechazo' => $rechazo,
                'model' => $model,
                'divisiones' => $divisiones,
                'grupo' => $grupo,
                'clase' => $clase,
                'list_productos' => $list_productos
            ]);

        }
    }

    public function actionDeleteProductos($data){
        $ids_delete =  explode(",", base64_decode($data));
        self::deleteProviderGiro($ids_delete);
        return $this->redirect(['view']);
    }

    public function actionDeleteActividad($id){
        $model = Giro::findOne(['giro_id' => $id]);
        $model->active = false;
        $model->last_updated_at = date('Y-m-d H:i:s');
        $model->last_updated_by = Yii::$app->user->getId();
        $model->save();

        return $this->redirect(['view']);
    }

    public function actionCheckPermisoRequerido(){
        $response = json_encode(false);
        $request = Yii::$app->request->post();
        if($request == null ){ return $response; }
        if( count($request['productos']) == 0 ){ return $response; }
        foreach($request['productos'] as $producto_id){
            $isPermisoRequerido = Productos::findOne(['producto_id' => $producto_id])->permiso;
            if($isPermisoRequerido){ return json_encode(true); }
        }

        return $response;
    }

    public function actionSendEmaill()
    {

        self::sendEmail2('/provider/correos/nuevo', '<EMAIL>', '<EMAIL>', 'Nuevo proveedor', ['modulo' => 'Representante Legal', 'tipo_provider' => 'bys']);


    }

    public function actionIndexValidador()
    {
        $searchModel = new RfcSearch();
        $searchPropiedad = new EscrituraPublicaSearch();
        $searchActaConstitutiva = new ActaConstitutivaSearch();
        $searchHacienda = new AltaHaciendaSearch();
        $dataProvider = $searchModel->search(Yii::$app->request->queryParams,'mainFilter');
        $datosPropiedad = $searchPropiedad->search(Yii::$app->request->queryParams,'mainPropiedad');
        $datosHacienda = $searchHacienda->search(Yii::$app->request->queryParams,'mainHacienda');


        return $this->render('index-validador', [
            'searchModel' => $searchModel,
            'searchPropiedad' => $searchPropiedad,
            'searchActaConstitutiva' => $searchActaConstitutiva,
            'searchHacienda' => $searchHacienda,
            'dataProvider' => $dataProvider,
            'datosPropiedad' => $datosPropiedad,
            'datosHacienda' => $datosHacienda,
        ]);
    }

    public function actionTerminar($id){
        $id = intval(base64_decode($id));
        if($id == null || !Provider::verifyProvider($id) || !Provider::verifyEtapa2($id)|| !self::verifyTerminar($id)){
            echo $this->render('/site/error',['name' => 'Lo sentimos...','message' => 'Acceso denegado']);

            return false;
        }

        $status='status_bys';

        $rfc = Yii::$app->db->createCommand();
        $rfc->update('provider.alta_hacienda',[$status => Status::STATUS_PORVALIDAR],'provider_id ='.$id);
        $rfc->execute();

        $correos_validadores = self::getEmailValidador(1,'bys');


        $IdLastVal = $this->saveLastSendValidation($id,'mainHacienda','alta_hacienda','bys');
        $this->saveLastSendValidationRelation($IdLastVal,$id,'alta_hacienda',$id,'bys');


        self::AllSendNotification(null,'bys',null,'DATOS LEGALES','Actividad ecónomica');


        self::sendEmail('/provider/correos/nuevo', null, $correos_validadores, 'Nuevo proveedor', ['modulo' => 'Actividad ecónomica','tipo_provider' => 'bys']);

        GeneralController::allModulesComplete($id);

        return $this->redirect('../../provider/dashboard');
    }

    public function actionViewValidar($id=null){
        $queryParams = Yii::$app->request->queryParams;
        $id = isset($queryParams['id']) ? intval( $queryParams['id'] ) : null;
        $opcionesArray = isset($queryParams['opciones']) ? $queryParams['opciones'] : null;
        $opcionesSerial = isset($opcionesArray) ? serialize($opcionesArray) : null;
        $opcionesBase64 = isset($opcionesSerial) ? base64_encode($opcionesSerial) : null;
        if($id == null || strlen($id)>15 || !Provider::verifyProviderExistence($id)){
            $this->msgError();
        }
        $modelos = [];
        $model = $this->findModel($id);
        //$model_rfc = $this->findModelRfc($id);
        $model_status = new Status();
        $model_status->scenario = Status::SCENARIO_CREATE;
        $model_validado = new DatosValidados();
        $model_validado->scenario = DatosValidados::SCENARIO_CREATE;
        $model_act = $this->findModelAct($id);
        $model_pro = $this->findModelProviderGiro($id);

        $model_pro_giro =$this->findModelProviderGiroValSave($id);
        if (isset($model_act[0]->provider_id) && !empty($model_act[0]->provider_id)) {
            for ($x = 0; $x < count($model_act); $x++) {
                $modelos[$model_act[$x]->formName()][] = $model_act[$x]->attributes;
            }
        }

        if (isset($model_pro_giro[0]->provider_id) && !empty($model_pro_giro[0]->provider_id)) {
            for ($x = 0; $x < count($model_pro_giro); $x++) {
                $modelos[$model_pro_giro[$x]->formName()][] = $model_pro_giro[$x]->attributes;
            }
        }

        $tipo_persona = Provider::find()->select('tipo_persona')->where(['provider_id' => $id])->one()->tipo_persona;
        $status_global = 'status_bys';
        $id_serial = AltaHacienda::find()->select('alta_hacienda_id')->where(['and',['provider_id' => $id],[$status_global => Status::STATUS_PORVALIDAR]])->one()['alta_hacienda_id'];
        $rechazo = Status::find()->getStatus($id,'alta_hacienda','bys','TERMINADO PRO');
        $movements = $this->countAllMovements($id,'alta_hacienda');


        $moSearch = new CertificacionSearch();
        $moSearch->provIdVal = $id;
        $dataModSearch = $moSearch->search(Yii::$app->request->queryParams,'dataRevisor');

        return $this->renderAjax('view-validar', [
            'model_status' => $model_status,
            'model_act' => (empty($model_act)) ? [new Giro()] : $model_act,
            'tipo_persona' => $tipo_persona,
            'modelos' => base64_encode(json_encode($modelos)),
            'model_validado' => $model_validado,
            'id_provider' => $id,
            'id_serial' => $id_serial,
            'model' => $model,
            'modelProductos' => $model_pro,
            'rechazo' => $rechazo,
            'namePro' => $this->getNameProvider($id),
            'movements' => $movements,
            'id' => $id,
            'opciones' => $opcionesBase64,
            'moduleName' => 'alta_hacienda',
            'moSearch' => $moSearch,
            'dataModSearch' => $dataModSearch
        ]);

    }

    public function actionViewVal()
    {
        $id = Yii::$app->user->identity->providerid;
        if($id == null || !Provider::verifyProvider($id) || !Provider::verifyEtapa2($id)){
            echo $this->render('/site/error',['name' => 'Lo sentimos...','message' => 'Acceso denegado']);

            return false;
        }

        $representante =$this->findModelRepresentanteLegal($id);
        $rechazo = Status::find()->getStatus($id,'alta_hacienda','bys');
        return $this->render('view', [
            'model' => $this->findModel($id),
            'model_curp' => $this->findModelCurp($id),
            'model_idoficial' => $this->findModelIdoficial($id),
            'model_representante_legal' => $representante,
            'model_registro_imss' => $this->findModelRegistroImss($id),
            'model_comprobante_domicilio' => $this->findModelComprobanteDomicilio($id),
            'model_act' => $this->findModelAct($id),
            'model_registro_acta' => $this->findModelActa($id),
            'rechazo' => $rechazo,
            'model_porcentaje' => $this->findModelPorcentaje($id),
            'model_alta_hacienda' => $this->findModelAltaHacienda($id),
        ]);
    }

    /**
     * Deletes an existing Rfc model.
     * If deletion is successful, the browser will be redirected to the 'index' page.
     * @param integer $id
     * @return mixed
     */
    public function actionDelete($id)
    {
        $this->findModel($id)->delete();

        return $this->redirect(['index']);
    }

    /**
     * Finds the Rfc model based on its primary key value.
     * If the model is not found, a 404 HTTP exception will be thrown.
     * @param integer $id
     * @return Rfc the loaded model
     * @throws NotFoundHttpException if the model cannot be found
     */
    protected function findModel($id)
    {
        $model = Rfc::find()->where(['provider_id' => $id])->one();
        if($model == null || empty($model)){
            $model = new Rfc();
        }
        return $model;
    }

    public function findModelCurp($id)
    {
        $model = Curp::find()->where(['provider_id' => $id])->one();
        if($model == null || empty($model)){
            $model = new Curp();
        }
        return $model;
    }

    public function findModelActa($id)
    {
        $model = ActaConstitutiva::find()->where(['provider_id' => $id])->one();
        if(!$model){
            $model = new ActaConstitutiva();
        }
        return $model;
    }

    public function findModelIdoficial($id)
    {
        $model = IdOficial::find()->where(['provider_id' => $id])->one();
        if($model == null || empty($model)){
            $model = new IdOficial();
        }
        return $model;
    }
    public function findModelComprobanteDomicilio($id)
    {
        $model = ComprobanteDomicilio::find()->where(['provider_id' => $id])->one();
        if($model == null || empty($model)){
            $model = new ComprobanteDomicilio();
        }
        return $model;
    }
    public function findModelRegistroImss($id)
    {
        $model = RegistroImss::find()->where(['provider_id' => $id])->one();
        if($model == null || empty($model)){
            $model = new RegistroImss();
        }
        return $model;
    }
    public function findModelRepresentanteLegal($id)
    {
        $model = RepresentanteLegal::find()->where(['and', ['provider_id' => Yii::$app->user->identity->providerid], ['representante_legal_id' => $id]])->one();
        if($model == null || empty($model)){
            $model = new RepresentanteLegal();
        }
        return $model;
    }

    protected function findModelRepresentanteLegalVal($id)
    {
        if (($model = RepresentanteLegal::findOne($id)) !== null) {
            return $model;
        } else {
            throw new NotFoundHttpException('The requested page does not exist.');
        }
    }


    public function findModelAltaHacienda($id)
    {
        $model = AltaHacienda::find()->where(['provider_id' => $id])->one();
        if($model == null || empty($model)){
            $model = new AltaHacienda();
        }
        return $model;
    }


    public function findModelRep($id)
    {
        $model = RepresentanteLegal::find()->where(['and',['provider_id' => $id],['activo' => true]])->one();
        if($model == null || empty($model)){
            $model = new RepresentanteLegal();
        }
        return $model;
    }


    public function findModelRegistroActas($id)
    {
        $model = ModificacionActa::find()->where(['provider_id' => $id])->one();
        if($model == null || empty($model)){
            $model = new ModificacionActa();
        }
        return $model;
    }

    public function findModelPorcentaje($id)
    {
        $model = Porcentaje::find()->where(['and', ['register_id' => $id], ['modelo' => 'alta_hacienda']])->one();
        if($model == null || empty($model)){
            $model = new Porcentaje();
        }
        return $model;
    }

    public function actionList_city($id = null){


        $con ='';
        $con .='<option value=""></option>';
        if(Yii::$app->request->isAjax && $id!=null && !empty($id)){
            $city = ArrayHelper::map(CatMunicipios::find()->select(['cv_municipio','nombre'])->where(['cv_entidad' => $id])->asArray()->all(),'cv_municipio','nombre');

            if(count($city)>0){
                foreach ($city as $key=>$value){
                    $con .='<option value="'.$key.'">'.$value.'</option>';
                }
            }else{
                $con .='<option value="">No hay ciudades</option>';
            }
        }

        echo $con;


    }
    public function findModelAct($id)
    {
        $model = Giro::find()->where(['and', ['provider_id' => $id, 'active' => true]])->orderBy(['giro_id'=>SORT_ASC])->all();
        if($model == null || empty($model)){
            $model = [new Giro()];
        }
        return $model;
    }

    public function findModelProviderGiroValSave($id)
    {
        $model = ProviderGiro::find()->where(['and',['provider_id' => $id, 'active' => true]])->all();
        if($model == null || empty($model)){
            $model = [new ProviderGiro()];
        }
        return $model;
    }

    public function findModelProviderGiro($id){
        $model = ProviderGiro::find()->select(["pg.provider_id","cl.concepto_grupo_id as grupo","MAX(pg.documento::text) as documento", "MAX(pg.vigencia) as vigencia"])
                ->addSelect(new yii\db\Expression("string_agg(pg.producto_id::text, ',') as producto_id"))
                ->from('provider_giro pg')->innerJoin('productos_servicios.concepto_linea cl', 'cl.concepto_linea_id = pg.producto_id')
                ->groupBy(["cl.concepto_grupo_id", "pg.provider_id"])->where(['provider_id' => $id])->asArray()->all();

        if($model == null || empty($model)){
            $model = [new ProviderGiro()];
        }
        return $model;
    }

    public function getDataProviderGiro($provider_id){
        $model = ProviderGiro::find()->select(["pg.provider_id","d.division_id as familia", "g.grupo_id as grupo", "c.clase_id as clase","MAX(pg.documento::text) as documento", "MAX(pg.vigencia) as vigencia", "coalesce(bool_or(pg.especialidad), false)  as especialidad", "coalesce(bool_or(pg.indefinido), false) as indefinido"])
                ->addSelect(new yii\db\Expression("string_agg(pg.producto_id::text, ',') as producto_id"))->from('provider_giro pg')
                ->innerJoin('productos.producto p', 'p.producto_id = pg.producto_id')
                ->innerJoin('productos.clase c', 'c.clase_id = p.clase_id')
                ->innerJoin('productos.grupo g', 'g.grupo_id = c.grupo_id')
                ->innerJoin('productos.division d', 'd.division_id = g.division_id')
                ->groupBy(["pg.provider_id", "d.division_id", "g.grupo_id", "c.clase_id"])->where(['and',['provider_id' => $provider_id, 'active' => true]])->asArray()->all();

        if($model == null || empty($model)){
            $model = [new ProviderGiro()];
        }
        return $model;
    }

    public function getViewProviderGiro($provider_id){
        $response = ProviderGiro::find()->select([
            "pg.provider_id", "d.descripcion as familia", "g.descripcion as grupo", "c.descripcion as clase", "MAX(pg.factura::text) as factura",
            "MAX(pg.documento::text) as documento", "MAX(pg.vigencia) as vigencia", "coalesce(bool_or(pg.especialidad), false)  as especialidad", "coalesce(bool_or(pg.indefinido), false) as indefinido"])
            ->addSelect(new Expression("string_agg(p.descripcion::text,',') as productos"))->from('provider_giro pg')
            ->addSelect(new Expression("string_agg(pg.id::text,',') as ids"))
            ->addSelect(new Expression("string_agg(p.clave::text,', ') as claves"))
            ->addSelect(new yii\db\Expression("string_agg(pg.producto_id::text, ',') as producto_ids"))
            ->innerJoin('productos.producto p', 'p.producto_id = pg.producto_id')
            ->innerJoin('productos.clase c', 'c.clase_id = p.clase_id')
            ->innerJoin('productos.grupo g', 'g.grupo_id = c.grupo_id')
            ->innerJoin('productos.division d', 'd.division_id = g.division_id')
            ->groupBy(["pg.provider_id", "d.division_id", "g.grupo_id", "c.clase_id"])
            ->where(['and', ['provider_id' => $provider_id, 'active' => true ]])->asArray()->all();

        if($response == null || empty($response)){
            $response = [new ProviderGiro()];
        }
        return $response;
    }

    public function getActividadGiro($provider_id, $id, $isUpdate=false){
        $model = new Giro();
        if($isUpdate){
            $model = Giro::find()->where(['and', ['provider_id' => $provider_id, 'giro_id' => $id, 'active' => true]])->one();
        }

        return $model;
    }

    public function getPorcentajeActividades($provider_id, $id, $isUpdate=false){
        $suma_porcentaje = 0;
        $condicion = $isUpdate ? ['and', ['provider_id' => $provider_id, 'active' => true], ['not in', 'giro_id', $id]] : ['and', ['provider_id' => $provider_id, 'active' => true]];
        $actividades = Giro::find()->where($condicion)->all();
        if( count($actividades) > 0){
            foreach($actividades as $actividad){
                $suma_porcentaje += $actividad->porcentaje ? $actividad->porcentaje : 0;
            }
        }
        
        return $suma_porcentaje;
    }

    public function findRepetedActividad($provider_id, $valor, $id, $isUpdate=false){
        $isRepeted = false;
        $nombre_actividad = strtoupper( trim($valor) );
        $actividad = CatActividades::find()->where(['nombre_actividad' => $nombre_actividad])->one();
        if( !is_null($actividad) ){
            $condicion = $isUpdate ? 
            ['and', ['provider_id' => $provider_id, 'active' => true, 'actividad_id' => $actividad->actividad_id], ['not in', 'giro_id', $id]] : 
            ['and', ['provider_id' => $provider_id, 'active' => true, 'actividad_id' => $actividad->actividad_id]];
            $actividad = Giro::find()->where($condicion)->one();
            $isRepeted = !is_null($actividad);
        }
        return $isRepeted;
    }

    public function getProductosProviderGiro($provider_id, $ids, $isUpdate=false){
        if($isUpdate){
            $model = ProviderGiro::find()->select(["d.division_id as familia", "g.grupo_id as grupo", "c.clase_id as clase", "MAX(pg.factura::text) as factura","MAX(pg.documento::text) as documento", "MAX(pg.vigencia) as vigencia", "coalesce(bool_or(pg.especialidad), false)  as especialidad", "coalesce(bool_or(pg.indefinido), false) as indefinido"])
                    ->addSelect(new yii\db\Expression("string_agg(pg.producto_id::text, ',') as productos"))->from('provider_giro pg')
                    ->innerJoin('productos.producto p', 'p.producto_id = pg.producto_id')
                    ->innerJoin('productos.clase c', 'c.clase_id = p.clase_id')
                    ->innerJoin('productos.grupo g', 'g.grupo_id = c.grupo_id')
                    ->innerJoin('productos.division d', 'd.division_id = g.division_id')
                    ->groupBy(["pg.provider_id", "d.division_id", "g.grupo_id", "c.clase_id"])
                    ->where(['and', ['provider_id' => $provider_id], ['in', 'pg.id', $ids]])->asArray()->one();
        }else{
            $model = [
                'familia' => null,
                'factura'=>null,
                'grupo' => null,
                'clase' => null,
                'documento' => null,
                'vigencia' => null,
                'especialidad' => false,
                'indefinido' => false,
                'productos' => null,
            ];
        }
        return $model;
    }

    public function getOrCreateProviderGiro($producto_id, $provider_id){
        $response = ProviderGiro::find()->where(['and',['provider_id' => $provider_id, 'producto_id' => $producto_id, 'active' => true]])->one();
        if($response == null || empty($response)){
            $response = new ProviderGiro();
            $response->producto_id = $producto_id;
            $response->provider_id = $provider_id;
            $response->active = true;
            $response->created_by = Yii::$app->user->getId();
            $response->created_at = date('Y-m-d H:i:s');
        }
        $response->last_updated_at = date('Y-m-d H:i:s');
        $response->last_updated_by = Yii::$app->user->getId();
        return $response;
    }

    public function deleteProviderGiro($arr_ids){
        foreach($arr_ids as $id){
            $model = ProviderGiro::findOne(['id' => $id]);
            $model->active = false;
            $model->last_updated_at = date('Y-m-d H:i:s');
            $model->last_updated_by = Yii::$app->user->getId();
            $model->save();
        }
    }

    public function actionList_rama($id=null){
        $con ='';
        $con .='<option value=""></option>';
        if(Yii::$app->request->isAjax && $id!=null && !empty($id)){
            $rama =  ArrayHelper::map(CatRamas::find()->select(['rama_id','nombre_rama'])
                ->where(['sector_id' => $id])
                ->orderBy(['nombre_rama'=>SORT_ASC])
                ->asArray()->all(),'rama_id','nombre_rama');
            if (count($rama) > 0) {
                foreach ($rama as $key => $value) $con .="<option value='" . $key . "'>" . $value . "</option>";
            } else {
                $con .="<option>No hay ramas</option>";
            }
        }else{
            $con .="<option>No hay ramas</option>";
        }


        echo $con;
    }

    public function actionList_actividad($id = null){
        $con ='';
        $con .='<option value=""></option>';
        if(Yii::$app->request->isAjax && $id!=null && !empty($id)){
            $act1 =  ArrayHelper::map(CatActividades::find()->select(['actividad_id','nombre_actividad'])
                ->where(['rama_id' => $id])
                ->orderBy(['nombre_actividad'=>SORT_ASC])
                ->asArray()->all(),'actividad_id','nombre_actividad');

            if (count($act1) > 0) {
                foreach ($act1 as $key => $value) $con .="<option value='" . $key . "'>" . $value . "</option>";
            } else {
                $con .="<option>No hay actividades</option>";
            }
        }else{
            $con .="<option>No hay ramas</option>";
        }

        echo $con;
    }

    public function actionList_name_sector_rama($id = null){

        $this->enableCsrfValidation = false;
        \Yii::$app->response->format = \yii\web\Response::FORMAT_JSON;
        $out = ['rama' => '', 'sector' => ''];
        if(Yii::$app->request->isAjax){


            if(!empty($id)){
                $data = Yii::$app->db->createCommand("select r.nombre_rama as rama, s.nombre_sector as sector from provider.cat_ramas r
                        join provider.cat_actividades a on a.rama_id = r.rama_id
                        join provider.cat_sectores s on s.sector_id = r.sector_id
                        where a.nombre_actividad = :id",[':id' => $id])->queryAll();

                if($data){
                    $out = array_values($data);
                }else{
                    $data = Yii::$app->db->createCommand("select r.nombre_rama as rama, s.nombre_sector as sector from provider.cat_ramas r
                        join provider.cat_sectores s on s.sector_id = r.sector_id
                        where s.nombre_sector = :id",[':id' => 'OTROS'])->queryAll();
                    $out = array_values($data);
                }

            }
        }


        return $out;

    }

    //valida si puede entrar a action update
    public  function  verifyUpdate($provider){
        if($provider){
            $status = AltaHacienda::find()->select('status_bys')->where(['provider_id' => $provider])->one()->status_bys;
            if($status == Status::STATUS_ENEDICION || $status == Status::STATUS_RECHAZADO || $status == Status::STATUS_VALIDADO){
                return true;
            }
        }
        return false;
    }

    public  function  verifyTerminar($provider){
        if($provider){
            $status = AltaHacienda::find()->select('status_bys')->where(['provider_id' => $provider])->one()['status_bys'];
            $model_act = $this->findModelAct($provider);
            $esta_lleno = \app\helpers\GeneralController::estaLleno($model_act[0]);
            if(($status == Status::STATUS_ENEDICION || $status == Status::STATUS_RECHAZADO || $status == Status::STATUS_VALIDADO) && $esta_lleno){
                return true;
            }
        }
        return false;
    }

    public function actionList_productos($id = null){
        $con ='';
        $con .='<option value=""></option>';
        if(Yii::$app->request->isAjax && $id!=null && !empty($id)){
            $act1 =  ArrayHelper::map(Producto::find()->select(['producto_id','nombre'])
                ->where(['grupo_producto_id' => $id])
                ->orderBy(['producto_id'=>SORT_ASC])
                ->asArray()->all(),'producto_id','nombre');

            if (count($act1) > 0) {
                foreach ($act1 as $key => $value) $con .="<option value='" . $key . "'>" . $value . "</option>";
            } else {
                $con .="<option>No hay Productos</option>";
            }
        }

        echo $con;
    }

    public function actionList_nama_act($q = null)
    {
//        $query = new Query();
//
//        $query->select('need')
//            ->from('origin')
//            ->where("need ILIKE ")
//            ->orderBy('need');
//        $command = $query->createCommand();
//        $data = $command->queryAll();
//
        $data = Yii::$app->db->createCommand("select nombre_actividad from provider.cat_actividades where nombre_actividad ilike :d", [':d' => "%$q%"])->queryAll();
        $out = [];
        foreach ($data as $d) {
            $out[] = ['value' => $d['nombre_actividad']];
        }
        echo Json::encode($out);exit();
    }

    public function actionList_grupo($id=null){

        $con = '';
        $con.='<option></option>';
        if(Yii::$app->request->isAjax){
            $data = ArrayHelper::map(ConceptoGrupo::find()
                ->select(['concepto_grupo_id'])
                ->addSelect(new \yii\db\Expression("concat_ws(' ',grupo,concepto) as concepto"))
                ->where(['concepto_familia_id' => intval($id)])->asArray()->all(),'concepto_grupo_id','concepto');
            if($data){
                foreach ($data as $key => $val){
                    $con .='<option value="'.$key.'">'.$val.'</option>';
                }
            }
        }
        echo $con;
    }

    public function actionList_product($id = null){

        $con = '';
        $con .='<option></option>';
        if(true){

            $data = ArrayHelper::map(ConceptoLinea::find()
                ->select(['concepto_linea_id'])
                ->addSelect(new \yii\db\Expression("concat_ws(' ',linea,concepto) as concepto"))
                ->where(['concepto_grupo_id' => intval($id)])->asArray()->all(),'concepto_linea_id','concepto');
            if($data){
                foreach ($data as $key => $val){
                    $con .='<option value="'.$key.'">'.$val.'</option>';
                }
            }
        }
        echo $con;
    }

    public function actionBuscarPorCodigo($codigo){
        $respuesta = [ "isClase" => false, "isProducto" => false, 'familia' => null, 'familia_id' => null, 'grupo' => null, 'grupo_id' => null, 'clase' => null, 'clase_id' => null, 'producto' => null, 'producto_id' => null ];
        $respuesta['familia'] = $this->getSelectData(Division::find()->all(), 'division_id', 'descripcion');
        if($codigo == null){ return $respuesta; }
        $dataProducto = Productos::find()->where(['clave' => $codigo])->one();
        if(empty($dataProducto)){
            $dataClase = Clase::find()->where(['clave' => $codigo])->one();
            if(!empty($dataClase)){ 
                $respuesta['isClase'] = true;
                $respuesta['clase_id'] = $dataClase->clase_id;
                $respuesta['clase'] = "<option value='{$dataClase->clase_id}'>{$dataClase->descripcion}</option>";
                $dataGrupo = Grupo::find()->where(['grupo_id' => $dataClase->grupo_id])->one();
                $respuesta['grupo_id'] = $dataGrupo->grupo_id;
                $respuesta['grupo'] = "<option value='{$dataGrupo->grupo_id}'>{$dataGrupo->descripcion}</option>";
                $respuesta['familia_id'] = $dataGrupo->division_id;
            }
        }else{
            $respuesta['isProducto'] = true;
            $respuesta['producto_id'] = $dataProducto->producto_id;
            $respuesta['producto'] = "<option value='{$dataProducto->producto_id}'>{$dataProducto->descripcion}</option>";
            $dataClase = Clase::find()->where(['clase_id' => $dataProducto->clase_id])->one();
            $respuesta['clase_id'] = $dataClase->clase_id;
            $respuesta['clase'] = "<option value='{$dataClase->clase_id}'>{$dataClase->descripcion}</option>";
            $dataGrupo = Grupo::find()->where(['grupo_id' => $dataClase->grupo_id])->one();
            $respuesta['grupo_id'] = $dataGrupo->grupo_id;
            $respuesta['grupo'] = "<option value='{$dataGrupo->grupo_id}'>{$dataGrupo->descripcion}</option>";
            $respuesta['familia_id'] = $dataGrupo->division_id;
        }
        return json_encode($respuesta);
    }


    public function getSelectData($data, $keyName, $attValue){
        $response = '<option></option>';
        if(!empty($data)){
            $arrData = ArrayHelper::map($data,$keyName,$attValue);
            foreach ($arrData as $key => $val){ $response .='<option value="'.$key.'">'.$val.'</option>'; }
        }
        return $response;
    }

    public function actionGetGrupos($id){
        $response = '<option></option>';
        $arrGrupos = ArrayHelper::map(Grupo::find()->where(['division_id' => intval($id)])->all(),'grupo_id','descripcion');
        if($arrGrupos){
            foreach ($arrGrupos as $key => $val){ $response .='<option value="'.$key.'">'.$val.'</option>'; }
        }

        echo $response;
    }

    public function actionGetClases($id){
        $response = '<option></option>';
        $arrGrupos = ArrayHelper::map(Clase::find()->where(['grupo_id' => intval($id)])->all(),'clase_id','descripcion');
        if($arrGrupos){
            foreach ($arrGrupos as $key => $val){ $response .='<option value="'.$key.'">'.$val.'</option>'; }
        }

        echo $response;
    }

    public function actionGetProductos($id){
        $response = '<option></option>';
        $arrGrupos = ArrayHelper::map(Productos::find()->where(['clase_id' => intval($id)])->all(),'producto_id','descripcion');
        if($arrGrupos){
            foreach ($arrGrupos as $key => $val){ $response .='<option value="'.$key.'">'.$val.'</option>'; }
        }

        echo $response;
    }

    public function actionFindProductosByText($texto){
        $query = "SELECT p.producto_id, p.descripcion FROM productos.producto p WHERE unaccent(p.descripcion) ILIKE unaccent('%{$texto}%') ORDER BY p.descripcion LIMIT 20";
        $return_query = Yii::$app->db->createCommand($query)->queryAll();
        return json_encode($return_query);
    }
}
