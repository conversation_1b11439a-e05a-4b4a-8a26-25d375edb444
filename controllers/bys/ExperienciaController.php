<?php

namespace app\controllers\bys;

use app\helpers\GeneralController;
use app\models\Certificacion;
use app\models\CertificacionSearch;
use app\models\ClientesContratos;
use app\models\ClientesContratosSearch;
use app\models\Curriculum;
use app\models\Module;
use app\models\Porcentaje;
use app\models\Provider;
use app\models\Status;
use app\models\Usuarios;
use Yii;
use yii\data\ActiveDataProvider;
use app\models\UltimaDeclaracion;
use kartik\form\ActiveForm;
use yii\web\NotFoundHttpException;
use yii\filters\VerbFilter;
use yii\web\Response;

if (Yii::$app->user->can(Usuarios::ROLE_PROVIDER)) {
    Yii::$app->params['provider_id'] = Yii::$app->user->identity->getProvider();
}

/**
 * UltimaDeclaracionController implements the CRUD actions for UltimaDeclaracion model.
 */
class ExperienciaController extends GeneralController
{
    /**
     * @inheritdoc
     */
    public function behaviors()
    {
        return [
            'verbs' => [
                'class' => VerbFilter::className(),
                'actions' => [
                    'delete' => ['POST', 'GET'],
                ],
            ],
        ];
    }



    public function actionView()
    {
        if (Yii::$app->user->can(Usuarios::ROLE_PROVIDER)) {
            $provider = Provider::find()->where(['user_id' => Yii::$app->user->id])
                ->one();
            //var_dump($provider->provider_id).exit();
        }
        if ($provider == null || !Provider::verifyProvider($provider->provider_id) || !Provider::verifyEtapa3($provider->provider_id)) {
            $this->msgError();
        }


        $curriculum = Curriculum::find()
            ->where(['provider_id' => $provider->provider_id])->one();
        if(!$curriculum)
            $curriculum = new Curriculum();
        $countPermissionFalse = Certificacion::find()->where(['and', ['provider_id' => $provider->provider_id], ['permissions' => false]])->count('1');


        //Rechazos
        $modelStatus = Module::getstatusbymodule('bys_experiencia', $provider->provider_id);
        $rechazo = $modelStatus->status_bys == Status::STATUS_RECHAZADO || $modelStatus->status_bys == Status::STATUS_ENEDICION  ?
            Status::find()->getRechazo( 'bys_experiencia', $provider->provider_id) : null;

        return $this->render('view', [
            'rechazo' => $rechazo,
            'countPermissionFalse' => $countPermissionFalse,
            'provider' => $provider,
            'curriculum'=>$curriculum,
            'modelStatus'=>$modelStatus,
            'rechazo'=>$rechazo,
            'cer' => $this->getDataProvider('Certificacion', $provider->provider_id),
            'cli' => self::getProviderClientesContratos($provider->provider_id),
        ]);
    }

    private function getProviderClientesContratos($provider_id){
        return new ActiveDataProvider([
            'query' => ClientesContratos::find()->where(["and", ["activo" => true], ['provider_id' => $provider_id], ["tipo" => "bys"] ] )
        ]);
    }

    private function getDataProvider($model,$id){
        $model_ = '\app\\models\\'.$model;
        return new ActiveDataProvider(
            ['query'=>$model_::find()->where(['and',['activo'=>true],['provider_id'=>$id]])]
        );
    }



    public function actionDelete($id)
    {
        $this->findModel($id)->delete();

        return $this->redirect(['index']);
    }

    /**
     * Finds the UltimaDeclaracion model based on its primary key value.
     * If the model is not found, a 404 HTTP exception will be thrown.
     * @param integer $id
     * @return UltimaDeclaracion the loaded model
     * @throws NotFoundHttpException if the model cannot be found
     */
    protected function findModel($id)
    {
        $model = UltimaDeclaracion::find()->where(['provider_id' => $id])->one();
        if ($model == null || empty($model)) {
            $model = new UltimaDeclaracion();
        }
        return $model;
    }

    public function findModelPorcentaje($id)
    {
        $model = Porcentaje::find()->where(['and', ['register_id' => $id], ['modelo' => 'ultima_declaracion']])->one();
        if ($model == null || empty($model)) {
            $model = new Porcentaje();
        }
        return $model;
    }

    //valida si puede entrar a action update
    public function verifyUpdate($provider)
    {
        if ($provider) {
            $status = UltimaDeclaracion::find()->select('status_bys')->where(['provider_id' => $provider])->one()->status_bys;
            if ($status == Status::STATUS_VALIDADO || $status == Status::STATUS_ENEDICION || $status == Status::STATUS_RECHAZADO) {
                return true;
            }
        }
        return false;
    }

    //valida si puede entrar a action terminar
    public function verifyTerminar($provider)
    {
        if ($provider) {
            $model = UltimaDeclaracion::find()->where(['provider_id' => $provider])->one();
            if (($model->status_bys == Status::STATUS_ENEDICION || $model->status_bys == Status::STATUS_RECHAZADO) && $this->estaLlenoUltimaDeclaracion($model)) {
                return true;
            }
        }
        return false;
    }

    public function actionUpdateexp(){

        $isRequest = Yii::$app->request->isAjax;
        $postRequest = Yii::$app->request->post();
        $user_id = Yii::$app->user->getId();

        $provider = Provider::find()->where(['user_id' => $user_id])->one();
        $curriculum = self::findModelCurriculum($provider->provider_id);
        $curriculum->provider_id = $provider->provider_id;

        $regex_url = "/(www\.)?[-a-zA-Z0-9@:%._\+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b([-a-zA-Z0-9()!@:%_\+.~#?&\/\/=]*)/";
        $regex_email = "/.*@.*/";

        //Rechazos
        $modelStatus = Module::getstatusbymodule('bys_experiencia', $provider->provider_id);
        $rechazo = $modelStatus->status_bys == Status::STATUS_RECHAZADO || $modelStatus->status_bys == Status::STATUS_ENEDICION  ?
            Status::find()->getRechazo( 'bys_experiencia',$provider->provider_id) : null;

        if( $isRequest || $postRequest ){
            if ( $isRequest && $provider->load($postRequest) ) {
                Yii::$app->response->format = Response::FORMAT_JSON; 
                $result = [];
                if($provider->pagina_web != "" && (!preg_match($regex_url,$provider->pagina_web) || preg_match($regex_email, $provider->pagina_web) )){
                    $result['provider-pagina_web'] = ['El formato de pagina web no es valido'];
                }
                return $result; 
            } else if ( $provider->load($postRequest) && $curriculum->load($postRequest) ){

                $curriculum_doc = $curriculum->documento;

                if( !empty($curriculum_doc) && GeneralController::str_contains($curriculum_doc, "archivos_tmp") ){
                    $user_id = Yii::$app->user->getId();
                    $curr_path = Curriculum::$path . "/{$user_id}/";
                    $this->makeDir($curr_path);
                    $new_nameCurr = str_replace('archivos_tmp/', $curr_path, $curriculum_doc);
                    $this->copyFile($curriculum_doc, $new_nameCurr);
                    $curriculum_doc = $new_nameCurr;
                }

                $curriculum->documento = $curriculum_doc;

                $provider->save();
                if (!$curriculum->save()) return var_dump( $curriculum->errors );
                self::updateModulesBys($provider->provider_id,'bys_experiencia');
                return $this->redirect('/bys/experiencia/view');

            }
        }

        return $this->renderAjax('updateexp', [
            'model' => $provider,
            'modelC' => $curriculum,
            'rechazo' => $rechazo
        ]);

    }

    protected function findModelCurriculum($id)
    {
        return ($model = Curriculum::find()->where(['provider_id' => $id])->one()) !== null ? $model : new Curriculum();
    }
}
