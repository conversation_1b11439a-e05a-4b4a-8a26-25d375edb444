<?php

namespace app\controllers\bys;

use app\helpers\GeneralController;
use app\models\CapacidadProduccion;
use app\models\CatAsentamientos;
use app\models\CatColonia;
use app\models\CatEntidad;
use app\models\CatEntidades;
use app\models\CatLocalidades;
use app\models\CatMunicipio;
use app\models\CatMunicipios;
use app\models\CatVialidad;
use app\models\DatosValidados;
use app\models\DireccionNl;
use app\models\FotografiaNegocio;
use app\models\Historico;
use app\models\Module;
use app\models\Porcentaje;
use app\models\Provider;
use app\models\RelacionAccionistas;
use app\models\Status;
use app\models\Usuarios;
use kartik\form\ActiveForm;
use Yii;
use yii\data\ActiveDataProvider;
use yii\db\Query;
use app\models\Ubicacion;
use app\models\UbicacionSearch;
use yii\helpers\ArrayHelper;
use yii\web\Controller;
use yii\web\NotFoundHttpException;
use yii\filters\VerbFilter;
use yii\web\Response;

if(Yii::$app->user->can(Usuarios::ROLE_PROVIDER)){
    Yii::$app->params['provider_id'] = Yii::$app->user->identity->getProvider();
}
/**
 * UbicacionController implements the CRUD actions for Ubicacion model.
 */
class UbicacionController extends GeneralController
{
    /**
     * @inheritdoc
     */
    public function behaviors()
    {
        return [
            'verbs' => [
                'class' => VerbFilter::className(),
                'actions' => [
                    'delete' => ['POST','GET'],
                ],
            ],
        ];
    }

    /**
     * Lists all Ubicacion models.
     * @return mixed
     */

    public function actionIndex($id=null){
        $id = Yii::$app->user->can(Usuarios::ROLE_PROVIDER) ? Yii::$app->params['provider_id'] : intval($id);
        if( $id == null || strlen($id)>15 ){ $this->msgError(); }

        $provider = Provider::find()->where(['user_id' => Yii::$app->user->getId()])->one();

        //Rechazos
        $modelStatus = Module::getstatusbymodule('bys_domicilio',$provider->provider_id);
        $rechazo = $modelStatus->status_bys == Status::STATUS_RECHAZADO  || $modelStatus->status_bys == Status::STATUS_ENEDICION  ?
            Status::find()->getRechazo( 'bys_domicilio', $provider->provider_id) : null;

        $opciones_ubicacion_fiscal = ['and', ["provider_id" => $id], ["type_address_prov" => "DOMICILIO FISCAL"], ["activo" => true] ];
        $ubicacion_fiscal = Ubicacion::find()->where($opciones_ubicacion_fiscal)->one();
        $show_tooltip_nl = ($ubicacion_fiscal != null && $ubicacion_fiscal->state_fiscal != 19);
        $ubicacion_nl = Ubicacion::find()->where(['and', ['provider_id' => $id], ['type_address_prov' => 'NL'], ["activo" => true] ])->one();

        if($show_tooltip_nl && !$ubicacion_nl){
            Yii::$app->session->setFlash('1','Es necesario anexar una ubicación dentro del Estado de Nuevo León.');
        }

        $tieneNotif = Ubicacion::find()->where(['and',['provider_id'=>$id,'activo'=>true,'notifications'=>true]])->one();

        if(!$tieneNotif){
            Yii::$app->session->setFlash('2','Es necesario marcar una ubicación para notificaciones en el Estado de Nuevo León');
        }


        return $this->render('index', [
            'dataProvider' => $this->getDataProvider('Ubicacion',$provider->provider_id),
            //'show_tooltip_nl' => $show_tooltip_nl,
            'modelStatus'=>$modelStatus,
            'rechazo'=>$rechazo,
            'provider'=>$provider
        ]);

    }

    public function actionNotific(){
        return Ubicacion::find()
            ->select("count(1) as total")
            ->where(['and',['provider_id'=>Yii::$app->user->identity->provider,'activo'=>true,'notifications'=>true]])->asArray()->all()[0]['total'];
    }

    private function getDataProvider($model,$id){
        $model_ = '\app\\models\\'.$model;
        return new ActiveDataProvider(
            ['query'=>$model_::find()->where(['and',['activo'=>true],['provider_id'=>$id]])/* ->andWhere(['is distinct from','type_address_prov', 'NL']) */]
        );
    }

    /* public function actionIndex($id=null)
    {
        $id = intval($id);
        if (Yii::$app->user->can(Usuarios::ROLE_PROVIDER)){
            $id=Yii::$app->params['provider_id'];
        }
        $searchModel = new UbicacionSearch();
        $dataProvider = $searchModel->search(Yii::$app->request->queryParams);
        $validados = $searchModel->search(Yii::$app->request->queryParams,'validadosFilter');
        $rechazados = $searchModel->search(Yii::$app->request->queryParams,'rechazadosFilter');
        $pendientes = $searchModel->search(Yii::$app->request->queryParams,'pendientesFilter');


        $model_direccion = $this->findModelDireccionNl($id);
        $colonia_nl = new CatAsentamientos();
        if(isset($model_direccion->colonia_fiscal) && !empty($model_direccion->colonia_fiscal)){
            $colonia_nl = CatAsentamientos::find()->select(['asentamiento_id','nombre'])
                ->where(['asentamiento_id' => $model_direccion->colonia_fiscal])->one();
        }
        $city_nl = new CatMunicipios();
        $entidad = Ubicacion::find()->select('state_fiscal')->where(['and',['provider_id' => $id],['type_address_prov' => 'DOMICILIO FISCAL']])->one()['state_fiscal'];
        //CatMunicipios::find()->select('entidad_id')->where(['municipio_id' => $city])->one()['entidad_id'];

        if(!Yii::$app->request->get()){
            $model_porcentaje = $this->findModelPorcentaje($id);
            $verify = Porcentaje::find()->select(['register_id'])->where(['and',['register_id' =>$id],['modelo'=>'ubicacion']])->one();
            if($verify){
                if (($dataProvider->getTotalCount() + $pendientes->getTotalCount() + $rechazados->getCount() + $validados->getCount()) != 0) {
                    $model_porcentaje->porcentaje = (($pendientes->getTotalCount() + $validados->getCount()) / ($dataProvider->getTotalCount() + $pendientes->getTotalCount() + $rechazados->getCount() + $validados->getCount()) * 100);
                    \Yii::$app->db->createCommand("update provider.porcentaje set porcentaje = $model_porcentaje->porcentaje where modelo='ubicacion' and register_id =:id")
                        ->bindValue(':id', $id)
                        ->execute();
                }
            }else{
                $porcen = new Porcentaje();
                $porcen->modelo = 'ubicacion';
                $porcen->register_id = $id;
                if($dataProvider->getTotalCount() + $pendientes->getTotalCount() + $validados->getCount() > 0){
                    $porcen->porcentaje = round(($pendientes->getTotalCount() + $validados->getCount())/($dataProvider->getTotalCount() + $pendientes->getTotalCount() + $rechazados->getCount() + $validados->getCount())*100);
                }else{
                    $porcen->porcentaje = 0;
                }
                $porcen->save();
            }
        }

        if($entidad!=19 && $model_direccion->city_fiscal){
            $city_nl = CatMunicipios::find()->select(['municipio_id', 'nombre'])->where(['municipio_id' => $model_direccion->city_fiscal])->one();
        }

        $rechazo = Status::find()->getStatusRechazarCotejo($id);
        $rechazoDirNl = [];
        if(isset($model_direccion->ubicacion_id) && !empty($model_direccion->ubicacion_id)){
            $rechazoDirNl = Status::find()->getStatus($model_direccion->ubicacion_id, 'direccion_nl', 'bys');
        }

        return $this->render('index', [
            'searchModel' => $searchModel,
            'dataProvider' => $dataProvider,
            'validados' => $validados,
            'rechazados' => $rechazados,
            'pendientes' => $pendientes,
            'entidad' => $entidad,
            'model_direccion' => $model_direccion,
            'colonia_nl' => $colonia_nl,
            'city_nl' => $city_nl,
            'rechazo' => $rechazo,
            'rechazoDirNl' => $rechazoDirNl
        ]);
    } */
    
    /**
     * Displays a single Ubicacion model.
     * @param integer $id
     * @return mixed
     */
    public function actionView($id=null)
    {

        $id = intval($id);
        if( !Ubicacion::verifyUbicacion($id)
        || !Ubicacion::verifyProviderExistenceUbi($id)){
            $this->msgError();
        }
        $models = '';
        $id_provider = Ubicacion::find()->select('provider_id')->where(['ubicacion_id'=>$id])->one()->provider_id;
        $tipo_provider = Provider::find()->select('tipo_provider')->where(['provider_id' => $id_provider])->one()->tipo_provider;
        $model_status = new Status();
        $model_status->scenario = Status::SCENARIO_CREATE;
        $rechazo = Status::find()->getStatus($id,'Ubicacion', $tipo_provider);
        $ubicacion = $this->findModel($id);
        $modelFotografia = $this->findFotografia($ubicacion->ubicacion_id); //Inicializacion del modelo Fotografia Negocio
        if(Yii::$app->user->can(Usuarios::ROLE_VALIDADOR_PROVEEDORES)){
            $modelos = [];
            if(isset($ubicacion->provider_id) && !empty($ubicacion->provider_id)){
                $modelos[$ubicacion->formName()] = $ubicacion->attributes;
            }
            $models = base64_encode(json_encode($modelos));

        }

        return $this->renderAjax('view', [
            'model' => $ubicacion,
            'modelFotografia' => $modelFotografia,
            'model_status' => $model_status,
            'rechazo' => $rechazo,
            'modelos' => $models
        ]);
    }


    /**
     * Displays a single Ubicacion model.
     * @param integer $id
     * @return mixed
     */
    public function actionViewVisit($id=null,$user_id=null)
    {

        $user_id = intval($user_id);
        $id = intval($id);
        if($user_id == null
            || strlen($user_id)>15
            || !Ubicacion::verifyUbicacion($id)
            || !Ubicacion::verifyProviderExistenceUbi($id)
            || (!Provider::verifyProvider($user_id) && Yii::$app->user->can(Usuarios::ROLE_PROVIDER))
            || !Provider::verifyEtapa3($user_id)){
            $this->msgError();
        }
        $models = '';
        $id_provider = Ubicacion::find()->select('provider_id')->where(['ubicacion_id'=>$id])->one()->provider_id;
        $tipo_provider = Provider::find()->select('tipo_provider')->where(['provider_id' => $id_provider])->one()->tipo_provider;
        $model_status = new Status();
        $model_status->scenario = Status::SCENARIO_CREATE;
        $rechazo = Status::find()->getStatus($id,'Ubicacion', $tipo_provider);
        $ubicacion = $this->findModel($id);
        if(Yii::$app->user->can(Usuarios::ROLE_VALIDADOR_PROVEEDORES)){
            $modelos = [];
            if(isset($ubicacion->provider_id) && !empty($ubicacion->provider_id)){
                $modelos[$ubicacion->formName()] = $ubicacion->attributes;
            }
            $models = base64_encode(json_encode($modelos));

        }

        return $this->renderAjax('view-visit', [
            'model' => $ubicacion,
            'model_status' => $model_status,
            'rechazo' => $rechazo,
            'modelos' => $models
        ]);
    }

     /**
     * Displays a single Ubicacion model.
     * @param integer $id
     * @return mixed
     */
     public function actionViewValidar($id=null,$user_id=null)
     {
        $queryParams = Yii::$app->request->queryParams;
        $id = isset($queryParams['id']) ? intval( $queryParams['id'] ) : null;
        $user_id = isset($queryParams['user_id']) ? intval( $queryParams['user_id'] ) : null;
        $opcionesArray = isset($queryParams['opciones']) ? $queryParams['opciones'] : null;
        $opcionesSerial = isset($opcionesArray) ? serialize($opcionesArray) : null;
        $opcionesBase64 = isset($opcionesSerial) ? base64_encode($opcionesSerial) : null;
         if($user_id == null 
         || strlen($user_id)>15 
         || !Ubicacion::verifyUbicacion($id) 
         || !Ubicacion::verifyProviderExistenceUbi($id) 
         || (!Provider::verifyProvider($user_id) && Yii::$app->user->can(Usuarios::ROLE_PROVIDER))
         || !Provider::verifyEtapa3($user_id)){
             $this->msgError();
         }
         $modelos = [];
         $id_provider = Ubicacion::find()->select('provider_id')->where(['ubicacion_id'=>$id])->one()->provider_id;
         $tipo_provider = Provider::find()->select('tipo_provider')->where(['provider_id' => $id_provider])->one()->tipo_provider;
         $model_status = new Status();
         $model_status->scenario = Status::SCENARIO_CREATE;
         $model_validado = new DatosValidados();
         $model_validado->scenario = DatosValidados::SCENARIO_CREATE;
         $ubicacion = $this->findModel($id);
         if(Yii::$app->user->can(Usuarios::ROLE_VALIDADOR_PROVEEDORES) || Yii::$app->user->can(Usuarios::ROLE_ADMIN_PROVIDER)){
             if(isset($ubicacion->provider_id) && !empty($ubicacion->provider_id)){
                 $modelos[$ubicacion->formName()] = $ubicacion->attributes;
             }
             $modelos = base64_encode(json_encode($modelos));
 
         }
         $rechazo = Status::find()->getStatus($id,'ubicacion','bys','TERMINADO PRO');
         $movements = $this->countAllMovements($id,'ubicacion');
         return $this->renderAjax('view-validar', [
             'model' => $ubicacion,
             'model_status' => $model_status,
             'rechazo' => $rechazo,
             'modelos' => $modelos,
             'model_validado' => $model_validado,
             'id_provider' => $id_provider,
             'rechazo' => $rechazo,
             'namePro' => $this->getNameProvider($id_provider),
             'movements' => $movements,
             'id' => $id,
             'opciones' => $opcionesBase64,
             'moduleName' => 'ubicacion'
         ]);
     }


    public function actionViewValidarNl($id=null,$user_id=null)
    {
        $queryParams = Yii::$app->request->queryParams;
        $id = isset($queryParams['id']) ? intval( $queryParams['id'] ) : null;
        $user_id = isset($queryParams['user_id']) ? intval( $queryParams['user_id'] ) : null;
        $opcionesArray = isset($queryParams['opciones']) ? $queryParams['opciones'] : null;
        $opcionesSerial = isset($opcionesArray) ? serialize($opcionesArray) : null;
        $opcionesBase64 = isset($opcionesSerial) ? base64_encode($opcionesSerial) : null;
        if($user_id == null
            || strlen($user_id)>15
            || !Ubicacion::verifyUbicacionNl($id)
            || !Ubicacion::verifyProviderExistenceUbiNl($id)
            || (!Provider::verifyProvider($user_id) && Yii::$app->user->can(Usuarios::ROLE_PROVIDER))
            || !Provider::verifyEtapa3($user_id)){
            $this->msgError();
        }
        $modelos = [];
        $id_provider = Ubicacion::find()->select('provider_id')->where(['ubicacion_id'=>$id])->one()['provider_id'];
        $model_status = new Status();
        $model_status->scenario = Status::SCENARIO_CREATE;
        $model_validado = new DatosValidados();
        $model_validado->scenario = DatosValidados::SCENARIO_CREATE;
        $direccionNl = $this->findModelDireccionNlId($id);
        if(Yii::$app->user->can(Usuarios::ROLE_VALIDADOR_PROVEEDORES) || Yii::$app->user->can(Usuarios::ROLE_ADMIN_PROVIDER)){
            if(isset($direccionNl->provider_id) && !empty($direccionNl->provider_id)){
                $modelos[$direccionNl->formName()] = $direccionNl->attributes;
            }
            $modelos = base64_encode(json_encode($modelos));

        }
        $rechazo = Status::find()->getStatus($id,'direccion_nl','bys','TERMINADO PRO');
        $movements = $this->countAllMovements($id,'direccion_nl');

        return $this->renderAjax('view-validar-nl', [
            'model' => $direccionNl,
            'model_status' => $model_status,
            'rechazo' => $rechazo,
            'modelos' => $modelos,
            'model_validado' => $model_validado,
            'id_provider' => $id_provider,
            'rechazo' => $rechazo,
            'namePro' => $this->getNameProvider($id_provider),
            'movements' => $movements,
            'id' => $id,
            'opciones' => $opcionesBase64,
            'moduleName' => 'direccion_nl'
        ]);
    }

    public function actionTerminar($id = null)
    {
        $IdLastVal = $this->saveLastSendValidation(Yii::$app->user->identity->getProvider(),'validUbicacion','ubicacion','bys');

        if ($id != null) {
            $status = $this->terminarUno('Ubicacion', intval(base64_decode($id)), 'ubicacion_id', 'ubicacion', 'status_bys','',$IdLastVal);
        } else {
            $status = $this->terminarTodos('Ubicacion', 'ubicacion_id', 'ubicacion', 'status_bys','',$IdLastVal);
        }
        if ($status) {
            $correos_validadores = self::getEmailValidador(2, 'bys');

            self::sendEmail('/provider/correos/nuevo', null, $correos_validadores, 'Nuevo proveedor', ['modulo' => 'Ubicación', 'tipo_provider' => 'bys']);

            self::AllSendNotification(null, 'bys', null, 'DATOS TECNICOS', 'Ubicación');

            GeneralController::allModulesComplete(Yii::$app->user->identity->getProvider());
        }
        return $this->redirect('index');
    }

    public function actionConfirm(){

        $modelos = isset(Yii::$app->request->post()['modelos'])?Yii::$app->request->post()['modelos']:[];

        $status_global = 'status_bys';

        $id = Yii::$app->request->post()['ubicacion_id'];
        $model = $this->findModel($id);
        $t = Yii::$app->db->beginTransaction();
        $rfc = Yii::$app->db->createCommand();
        $rfc->update('provider.ubicacion',[$status_global => Status::STATUS_VALIDADO],'ubicacion_id ='.$id);
        if($rfc->execute()){
            $val = true;
            if(($requi_status = Status::find()->where(['and',['register_id' => $model->ubicacion_id],[$status_global => Status::STATUS_PENDIENTE],['modelo'=>'Ubicacion']])->one())!==null){
                $requi_status->status_bys = Status::STATUS_TERMINADO_PRO;
                $val = $requi_status->save();
            }

            if($val){
                $rfc_pro = Provider::find()->select(['provider_id','rfc'])->where("provider_id =(select provider_id from provider.ubicacion where ubicacion_id=$id)")->one();
                $valid = true;
                if($rfc_pro['provider_id']){
                    if(!empty($modelos)){
                        $models_arr = json_decode(base64_decode($modelos),true);
                        foreach ($models_arr as $k => $value){
                            $model_historico = new Historico();
                            if(DatosValidados::Verify($k)){
                                $value[$status_global] = Status::STATUS_VALIDADO;
                            }
                            $model_historico->provider_id = $rfc_pro['provider_id'];
                            $model_historico->rfc = $rfc_pro['rfc'];
                            $model_historico->validador_id = Yii::$app->user->getId();
                            $model_historico->modelo = $k;
                            $model_historico->data = json_encode($value);
                            $valid = $valid && $model_historico->save();
                        }
                    }
                }else{$valid = false;}

                if($valid){
                    $t->commit();
                    Yii::$app->session->setFlash('success', 'Los datos se validaron correctamente');
                }else{
                    $t->rollBack();
                    Yii::$app->session->setFlash('error', 'Error al validar la información');

                }
            }
        }else{
            $t->rollBack();
            Yii::$app->session->setFlash('error', 'Error al validar la información');
        }

        return Yii::$app->getResponse()->redirect(['datosTecnicos/validador/index-validador']);

    }
    /**
     * Creates a new Ubicacion model.
     * If creation is successful, the browser will be redirected to the 'view' page.
     * @return mixed
     */

    public function actionCreate(){
        $isRequest = Yii::$app->request->isAjax;
        $postRequest = Yii::$app->request->post();
        if( $isRequest || $postRequest ){
            $modelUbicacion = new Ubicacion();
            $modelFotografia = new FotografiaNegocio();
            $modelCatAsentamientos = new CatAsentamientos();
            $estado = [];
            $id = Provider::find()->select('provider_id')->where(['user_id' => Yii::$app->user->id])->one()->provider_id;
            $modelUbicacion->provider_id = $id;
            $modelFotografia->provider_id = $id;

            if ($id == null || !Provider::verifyProvider($id) || !Provider::verifyEtapa3($id)) { $this->msgError(); }
 
            if ( $isRequest && $modelUbicacion->load($postRequest) ) {
                Yii::$app->response->format = Response::FORMAT_JSON;
                return ActiveForm::validate($modelUbicacion);
            } else if ( $modelUbicacion->load($postRequest) ) {
                $location = json_decode($modelUbicacion->geo_ubicacion);
                if (isset($location[0]) && !empty($location[0]) && isset($location[1]) && !empty($location[1])) {
                    $val_geo = Ubicacion::findBySql("select ST_MakePoint(" . $location[0] . "," . $location[1] . ")")->asArray()->all();
                    $modelUbicacion->geo_ubicacion = $val_geo[0]['st_makepoint'];
                }

                if (!empty($modelUbicacion->url_comprobante_domicilio) && $modelUbicacion->url_comprobante_domicilio) {
                    $user_id = Yii::$app->user->getId();
                    $ubicacion_path = "{$modelUbicacion->path}/{$user_id}/";
                    $this->makeDir($ubicacion_path);
                    $new_nameUb = str_replace('archivos_tmp/', $ubicacion_path, $modelUbicacion->url_comprobante_domicilio);
                    $this->copyFile($modelUbicacion->url_comprobante_domicilio, $new_nameUb);
                    $modelUbicacion->url_comprobante_domicilio = $new_nameUb;
                }

                $modelUbicacion->type_address_prov = $modelUbicacion->tipo == "DOMICILIO FISCAL" ? "DOMICILIO FISCAL" : null;
                $modelUbicacion->descripcion = $modelUbicacion->tipo == "DOMICILIO FISCAL" ? "DOMICILIO FISCAL" : ($modelUbicacion->tipo == "OTRA" ? $modelUbicacion->descripcion : null );

                if($modelUbicacion->tipo == "DOMICILIO FISCAL" && $modelUbicacion->state_fiscal == 19){
                    Ubicacion::deleteAll(['and',['type_address_prov' => 'NL'],['provider_id' => $id]]);
                }

                if($modelUbicacion->type_video != null && isset($postRequest['Ubicacion'])){
                    if($modelUbicacion->type_video == "ARCHIVO" && isset($postRequest['Ubicacion']['path_video_att']) && !empty($postRequest['Ubicacion']['path_video_att'])){
                        $modelUbicacion->path_video_att = $postRequest['Ubicacion']['path_video_att'];
                        $user_id = Yii::$app->user->getId();
                        $ubicacion_path = "{$modelUbicacion->path}/{$user_id}/";
                        $this->makeDir($ubicacion_path);
                        $new_name_video = str_replace('archivos_tmp/', $ubicacion_path, $modelUbicacion->path_video_att);
                        $this->copyFile($modelUbicacion->path_video_att, $new_name_video);
                        $modelUbicacion->url_video = $new_name_video;
                    }
                    if($modelUbicacion->type_video == "URL" && isset($postRequest['Ubicacion']['url_video_att']) && !empty($postRequest['Ubicacion']['url_video_att'])){
                        $modelUbicacion->url_video = $postRequest['Ubicacion']['url_video_att'];
                    }
                }

                if (!$modelUbicacion->save()) { return $this->goHome(); }
                else{
                   
                    $modelFotografia->load($postRequest);
                    $this->makeDir($modelFotografia->path);

                    if (!empty($modelFotografia->url_archivo) && $modelFotografia->url_archivo) {
                        $new_nameFot = str_replace('archivos_tmp/', $modelFotografia->path . '/', $modelFotografia->url_archivo);
                        $this->copyFile($modelFotografia->url_archivo, $new_nameFot);
                        $modelFotografia->url_archivo = $new_nameFot;
                    }

                    $modelFotografia->ubicacion_id = $modelUbicacion->ubicacion_id; 
                    $modelFotografia->save(); 
                    /* Yii::info("Se inserto ubicacion con ID : ".$modelUbicacion->ubicacion_id, 'LOG'); 
                    Yii::info("Se inserto fotografia con ID : ".$modelFotografia->fotografia_negocio_id, 'LOG');  */
                }

                if($this->verifyProviderCert($modelUbicacion->provider_id,'bys')>0) {
                    $arr_moviments = [
                        'provider_id' => $modelUbicacion->provider_id,
                        'model' => 'ubicacion',
                        'action' => 'Creacion Ubicación',
                        'origin_id' => $modelUbicacion->ubicacion_id,
                        'column' => null,
                        'column_data_old' => null,
                        'column_date_new' => null,
                        'full_model' => true,
                        'data_model' => $modelUbicacion->attributes
                    ];
                    $this->insertAllMovements($arr_moviments);
                }

                self::eliminarCita();
                self::updateModulesBys($modelUbicacion->provider_id,'bys_domicilio');
                return $this->redirect(['index']);
            }

            $estado = ArrayHelper::map(CatEntidades::find()->select(['entidad_id', 'nombre'])->asArray()
                ->all(), 'entidad_id', 'nombre');

            $vialidad = ArrayHelper::map(CatVialidad::find()->select(['vialidad_id', 'descripcion'])->asArray()
                ->all(), 'vialidad_id', 'descripcion');
            
            return $this->renderAjax('create', [
                'model' => $modelUbicacion,
                'fotografia' => $modelFotografia,
                'state' => $estado,
                'model_catcolonia' => $modelCatAsentamientos,
                'vialidad' => $vialidad
            ]);

        }
        return $this->goHome();
    }

    /**
     * Updates an existing Ubicacion model.
     * If update is successful, the browser will be redirected to the 'view' page.
     * @param integer $id
     * @return mixed
     */

    public function actionUpdate($id=null){
        $isRequest = Yii::$app->request->isAjax;
        $postRequest = Yii::$app->request->post();
        $user_id = Yii::$app->user->getId();
        $provider = Provider::find()->where(['user_id'=>$user_id])->one();

        $modelStatus = Module::getstatusbymodule('bys_domicilio',$provider->provider_id);
        $rechazo = $modelStatus->status_bys == Status::STATUS_RECHAZADO  || $modelStatus->status_bys == Status::STATUS_ENEDICION  ?
            Status::find()->getRechazo( 'bys_domicilio', $provider->provider_id) : null;


        if( $isRequest || $postRequest ){
            /* if ($user_id == null || !Provider::verifyProvider($user_id) || !Provider::verifyEtapa3($user_id) || !UbicacionController::verifyUpdate($user_id, $id)) {
                echo $this->render('/site/error', ['name' => 'Lo sentimos...', 'message' => 'Acceso denegado']);
                return false;
            } */

            $colonia = $localidad = $city = $state = [];

            $modelUbicacion = $this->findModel($id); //Modelo ubicacion
            $modelFotografia = $this->findFotografia($modelUbicacion->ubicacion_id); //Inicializacion del modelo Fotografia Negocio
            $modelUbicacion->provider_id = $provider->provider_id;
            $modelFotografia->provider_id = $provider->provider_id;

            /* if($modelUbicacion->type_video != null){
                $modelUbicacion->path_video_att = $modelUbicacion->type_video == "ARCHIVO" ? $modelUbicacion->url_video : null;
                $modelUbicacion->url_video_att = $modelUbicacion->type_video == "URL" ? $modelUbicacion->url_video : null;
            } */

            if($modelUbicacion->type_video == "URL"){
                $modelUbicacion->url_video = null;
            }

            $modelUbicacionOld = $modelUbicacion->attributes;
            $modelFotografiaOld = $modelFotografia->attributes;


            $modelFotografia->url_archivoOLD = $modelFotografia->url_archivo;

            if (!empty($modelUbicacion->state_fiscal)) {
                $city = ArrayHelper::map(CatMunicipios::find()->select(['municipio_id', 'nombre'])
                    ->where(['entidad_id' => $modelUbicacion->state_fiscal])->asArray()->orderBy(['nombre' => 'ASC'])->all(), 'municipio_id', 'nombre');
            }
            if (!empty($modelUbicacion->state_fiscal) && !empty($modelUbicacion->city_fiscal)) {
                $colonia = ArrayHelper::map(CatAsentamientos::find()->select(['asentamiento_id', 'nombre'])
                    ->where(['and', ['entidad_id' => $modelUbicacion->state_fiscal], ['municipio_id' => $modelUbicacion->city_fiscal]])->asArray()->orderBy(['nombre' => 'ASC'])->all(), 'asentamiento_id', 'nombre');
                $localidad = ArrayHelper::map(CatLocalidades::find()->select(['localidad_id', 'nombre'])
                    ->where(['and', ['entidad_id' => $modelUbicacion->state_fiscal], ['municipio_id' => $modelUbicacion->city_fiscal]])->asArray()->orderBy(['nombre' => 'ASC'])->all(), 'localidad_id', 'nombre');
            }

            if ( $isRequest && $modelUbicacion->load($postRequest) ) {
                Yii::$app->response->format = Response::FORMAT_JSON;
                return ActiveForm::validate($modelUbicacion);
            } else if ( $modelUbicacion->load($postRequest) ) {

                $location = json_decode($modelUbicacion->geo_ubicacion);
                if (isset($location[0]) && !empty($location[0]) && isset($location[1]) && !empty($location[1])) {
                    $val_geo = Ubicacion::findBySql("select ST_MakePoint(" . $location[0] . "," . $location[1] . ")")->asArray()->all();
                    $modelUbicacion->geo_ubicacion = $val_geo[0]['st_makepoint'];
                }

                if (!empty($modelUbicacion->url_comprobante_domicilio) && GeneralController::str_contains($modelUbicacion->url_comprobante_domicilio, "archivos_tmp") ) {

                    $ubicacion_path = "{$modelUbicacion->path}/{$user_id}/";
                    $this->makeDir($ubicacion_path);
                    $new_nameUb = str_replace('archivos_tmp/', $ubicacion_path, $modelUbicacion->url_comprobante_domicilio);
                    $this->copyFile($modelUbicacion->url_comprobante_domicilio, $new_nameUb);
                    $modelUbicacion->url_comprobante_domicilio = $new_nameUb;
                }

                $modelUbicacion->status_bys = Status::STATUS_ENEDICION;
                $modelUbicacion->type_address_prov = $modelUbicacion->tipo == "DOMICILIO FISCAL" ? "DOMICILIO FISCAL" : null;
                $modelUbicacion->descripcion = $modelUbicacion->tipo == "DOMICILIO FISCAL" ? "DOMICILIO FISCAL" : ($modelUbicacion->tipo == "OTRA" ? $modelUbicacion->descripcion : null );

                $modelUbicacion->type_address_prov = $modelUbicacion->tipo == "DOMICILIO FISCAL" ? "DOMICILIO FISCAL" : null;
                $modelUbicacion->descripcion = $modelUbicacion->tipo == "DOMICILIO FISCAL" ? "DOMICILIO FISCAL" : ($modelUbicacion->tipo == "OTRA" ? $modelUbicacion->descripcion : null );

                if($modelUbicacion->tipo == "DOMICILIO FISCAL" && $modelUbicacion->state_fiscal == 19){
                    $ubicacion_nl = Ubicacion::find()->where(['and', ['provider_id' => $provider->provider_id], ['type_address_prov' => 'NL'], ["activo" => true] ])->one();
                    if($ubicacion_nl != null){
                        $ubicacion_nl->activo = false;
                        $ubicacion_nl->save();
                        FotografiaNegocio::updateAll(['activo' => false], ['ubicacion_id' => $ubicacion_nl->ubicacion_id]);
                    }
                }

                if($modelUbicacion->tipo == "DIRECCIÓN NUEVO LEÓN"){ $modelUbicacion->type_address_prov = 'NL'; }

                /* if($modelUbicacion->type_video != null && isset($postRequest['Ubicacion'])){
                    if( $modelUbicacion->type_video == "ARCHIVO" && array_key_exists('path_video_att',$postRequest['Ubicacion']) ){
                        $modelUbicacion->path_video_att = $postRequest['Ubicacion']['path_video_att'];
                        if($modelUbicacion->path_video_att != null && GeneralController::str_contains($modelUbicacion->path_video_att, "archivos_tmp")){

                            $ubicacion_path = "{$modelUbicacion->path}/{$user_id}/";
                            $this->makeDir($ubicacion_path);
                            $new_name_video = str_replace('archivos_tmp/', $ubicacion_path, $modelUbicacion->path_video_att);
                            $this->copyFile($modelUbicacion->path_video_att, $new_name_video);
                            $modelUbicacion->url_video = $new_name_video;
                        }else{ $modelUbicacion->url_video = $modelUbicacion->path_video_att; }
                    }
                    if( $modelUbicacion->type_video == "URL" && array_key_exists('url_video_att',$postRequest['Ubicacion']) ){
                        $modelUbicacion->url_video = $postRequest['Ubicacion']['url_video_att'];
                    }
                } */

                $modelUbicacion->url_video = $this->validUrlVideo($modelUbicacion->url_video, $user_id);
                $modelUbicacion->url_video_2 = $this->validUrlVideo($modelUbicacion->url_video_2, $user_id);
                $modelUbicacion->url_video_3 = $this->validUrlVideo($modelUbicacion->url_video_3, $user_id);
                $modelUbicacion->url_escrito = $this->validUrlVideo($modelUbicacion->url_escrito, $user_id);

                if(!$modelUbicacion->save()){ return $this->goHome(); }
                else{

                    //Logica de FotografiaController actionUpdate()
                    $modelFotografia->load($postRequest);
                    
                    $this->makeDir($modelFotografia->path);

                    if (!empty($modelFotografia->url_archivo) && $modelFotografia->url_archivo != $modelFotografia->url_archivoOLD) {
                        $new_nameFot = str_replace('archivos_tmp/', $modelFotografia->path . '/', $modelFotografia->url_archivo);
                        $this->copyFile($modelFotografia->url_archivo, $new_nameFot);
                        $modelFotografia->url_archivo = $new_nameFot;
                    }

                    $modelFotografia->status_bys = Status::STATUS_ENEDICION;
                    $modelFotografia->provider_id = $provider->provider_id;
                    $modelFotografia->ubicacion_id = $modelUbicacion->ubicacion_id; 
                    if($modelFotografia->save()){
                        if (($requi_status = Status::find()->where(['and', ['register_id' => $id], ['status_bys' => Status::STATUS_PENDIENTE], ['modelo' => 'fotografia_negocio']])->one()) !== null) {
                            $requi_status->status_bys = Status::STATUS_TERMINADO_PRO;
                            $requi_status->save();
                        }
                        $countValCert = $this->verifyProviderCert($modelFotografia->provider_id, 'bys');
                        $countValRec = self::verifyValRec('fotografia_negocio',$modelFotografia->fotografia_negocio_id);
                        if ($countValCert > 0 || $countValRec>0) {
                            $trueFalse = $countValCert>0?true:false;
                            $this->compareModels($modelFotografiaOld, $modelFotografia->attributes, $modelFotografia->fotografia_negocio_id, 'fotografia_negocio',$modelFotografia->provider_id,$trueFalse);
                        }
                        self::eliminarCita();
                        self::updateModulesBys($modelUbicacion->provider_id,'bys_domicilio');
                        return $this->redirect(['index']);
                    }
                }

                $countValCert = $this->verifyProviderCert($modelUbicacion->provider_id, 'bys');
                $countValRec = self::verifyValRec('ubicacion',$modelUbicacion->ubicacion_id);

                if ($countValCert > 0 || $countValRec>0) {
                    $trueFalse = $countValCert>0?true:false;
                    $this->compareModels($modelUbicacionOld, $modelUbicacion->attributes, $modelUbicacion->ubicacion_id, 'ubicacion',$modelUbicacion->provider_id,$trueFalse);
                }
                self::eliminarCita();
                self::updateModulesBys($modelUbicacion->provider_id,'bys_domicilio');

                return $this->redirect(['index']);
            }

            $state = ArrayHelper::map(CatEntidades::find()->select(['entidad_id', 'nombre'])->asArray()->orderBy(['nombre' => 'ASC'])
                ->all(), 'entidad_id', 'nombre');

            $vialidad = ArrayHelper::map(CatVialidad::find()->select(['vialidad_id', 'descripcion'])->asArray()->orderBy(['descripcion' => 'ASC'])
                ->all(), 'vialidad_id', 'descripcion');

            return $this->renderAjax('update', [
                'model' => $modelUbicacion,
                'fotografia' => $modelFotografia,
                'colonia' => $colonia,
                'city' => $city,
                'state' => $state,
                'localidad' => $localidad,
                'vialidad' => $vialidad,
                'rechazo' => $rechazo
            ]);

        }

        return $this->goHome();
    }

    /**
     * Deletes an existing Ubicacion model.
     * If deletion is successful, the browser will be redirected to the 'index' page.
     * @param integer $id
     * @return mixed
     */
    public function actionDelete($id)
    {

        $modelUbicacion = Ubicacion::findOne(intval($id));

        //Ubicacion::updateAll(['activo' => false], ['ubicacion_id' => intval($id)]);

        if ($this->verifyProviderCert($modelUbicacion->provider_id, 'bys') > 0) {
            $arr_moviments = [
                'provider_id' => $modelUbicacion->provider_id,
                'model' => 'ubicacion',
                'action' => 'Eliminó Ubicación',
                'origin_id' => $id,
                'column' => null,
                'column_data_old' => null,
                'column_date_new' => null,
                'full_model' => true,
                'data_model' => $modelUbicacion->attributes
            ];
            $this->insertAllMovements($arr_moviments);
        }

        $modelUbicacion->activo = false;
        $modelUbicacion->save(false);
        FotografiaNegocio::updateAll(['activo' => false], ['ubicacion_id' => intval($id)]);


        return $this->redirect(['index']);
    }

    /**
     * Finds the Ubicacion model based on its primary key value.
     * If the model is not found, a 404 HTTP exception will be thrown.
     * @param integer $id
     * @return Ubicacion the loaded model
     * @throws NotFoundHttpException if the model cannot be found
     */
    protected function findModel($id){
        $model = null;
        if( empty($id) ){ $model = new Ubicacion(); }
        else{ $model = Ubicacion::findOne($id); $model !== null ? $model : new Ubicacion(); }
        return $model;
    }

    protected function findFotografia($ubicacion_id) {
        $model = null;
        if( empty($ubicacion_id) ){ $model = new FotografiaNegocio(); }
        else{
            $model = FotografiaNegocio::find()->where(['and', ['ubicacion_id' => $ubicacion_id], ['activo' => true]])->one();            
            $model = empty($model) ? new FotografiaNegocio() : $model;
        }
        return $model;
    }

    public function actionList_colpostal($id){
        $colonias =  ArrayHelper::map(CatAsentamientos::find()->select(['cv_asentamiento','nombre'])
            ->where(['cp' => $id])
            ->orderBy(['nombre'=>SORT_ASC])
            ->asArray()->orderBy(['nombre'=>'ASC'])->all(),'cv_asentamiento','nombre');
        $con = '';
        $con .='<option value=""></option>';
        if (count($colonias) > 0) {
            foreach ($colonias as $key => $value) $con .="<option value='" . $key . "'>" . $value . "</option>";
        } else {
            $con .="<option></option>";
        }
        echo $con;
    }

    public function actionGetInfoByCp($cp){
        $arr_return = [ "entidad_id" => null, "municipio_id" => null, "municipios" => null, "localidades"=> null, "colonias" => null ];
        $state_and_municipio = Yii::$app->db->createCommand("SELECT DISTINCT municipio_id, entidad_id from cat_asentamientos where cp = '{$cp}'")->queryOne();
        if($state_and_municipio == null){ return json_encode($arr_return); }

        $state_id = $state_and_municipio["entidad_id"];
        $city_id = $state_and_municipio["municipio_id"];

        $arr_return["entidad_id"] = $state_id;
        $arr_return["municipio_id"] = $city_id;
        $arr_return["municipios"] = $this->actionGetCiudadesHtml($state_id);
        $arr_return["localidades"] = $this->actionGetLocalidadesHtml($state_id,$city_id);
        $arr_return["colonias"] = $this->actionGetColoniasByCpHtml($cp);
        return json_encode($arr_return);
    }

    public function actionGetCiudadesHtml($state_id){
        $ciudades = ArrayHelper::map(CatMunicipios::find()->select(['municipio_id', 'nombre'])
            ->where(['entidad_id' =>$state_id])->asArray()->orderBy(['nombre'=>'ASC'])->all(),'municipio_id', 'nombre');

        $htmlData ='<option value="">Selecciona...</option>';

        if($ciudades){ foreach ($ciudades as $key => $ciudad) $htmlData .="<option value=".$key.">".$ciudad."</option>"; }
        else{ $htmlData .='<option value="">No hay resultados</option>'; }

        return $htmlData;
    }

    public function actionGetColoniasByCpHtml($cp){
        $colonias =  ArrayHelper::map(CatAsentamientos::find()->select(['cv_asentamiento','nombre'])->where(['cp' => $cp]) ->orderBy(['nombre'=>SORT_ASC])
                        ->asArray()->orderBy(['nombre'=>'ASC'])->all(),'cv_asentamiento','nombre');

        $htmlData ='<option value="">Selecciona...</option>';

        if (count($colonias) > 0){ foreach ($colonias as $key => $colonia) $htmlData .="<option value=".$key.">".$colonia."</option>"; }
        else{ $htmlData .='<option value="">No hay resultados</option>'; }

        return $htmlData;
    }

    public function actionGetLocalidadesHtml($state_id, $city_id){
        $localidades = ArrayHelper::map(CatLocalidades::find()->select(['localidad_id', 'nombre'])
            ->where(['and',['entidad_id' =>$state_id],['municipio_id' => $city_id]])->asArray()->orderBy(['nombre'=>'ASC'])->all(),'localidad_id', 'nombre');

        $htmlData ='<option value="">Selecciona...</option>';

        if (count($localidades) > 0){ foreach ($localidades as $key => $localidad) $htmlData .="<option value=".$key.">".$localidad."</option>"; }
        else{ $htmlData .='<option value="">No hay resultados</option>'; }

        return $htmlData;
    }

    public function actionList_city($id){
        $city = ArrayHelper::map(CatMunicipios::find()->select(['municipio_id', 'nombre'])
            ->where(['entidad_id' =>$id])->asArray()->orderBy(['nombre'=>'ASC'])->all(),'municipio_id', 'nombre');


        $con = '';

        $con .='<option value="">Selecciona...</option>';

        if($city){
            foreach ($city as $key => $val)
                $con .="<option value=".$key.">".$val."</option>";
        }else{
            $con .='<option value="">No hay resultados</option>';
        }
        echo $con;
    }

    public function actionList_localidades($id,$state){
        $city = ArrayHelper::map(CatLocalidades::find()->select(['localidad_id', 'nombre'])
            ->where(['and',['entidad_id' =>$state],['municipio_id' => $id]])->asArray()->orderBy(['nombre'=>'ASC'])->all(),'localidad_id', 'nombre');


        $con = '';

        $con .='<option value="">Selecciona...</option>';

        if($city){
            foreach ($city as $key => $val)
                $con .="<option value=".$key.">".$val."</option>";
        }else{
            $con .='<option value="">No hay resultados</option>';
        }
        echo $con;
    }

    public function actionList_asentamiento($id,$state){
        $asentamiento = ArrayHelper::map(CatAsentamientos::find()->select(['asentamiento_id', 'nombre'])
            ->where(['and',['entidad_id' =>$state],['municipio_id' => $id]])->asArray()->orderBy(['nombre'=>'ASC'])->all(),'asentamiento_id', 'nombre');

        $con = '';
        $con .='<option value="">Selecciona...</option>';
        if($asentamiento){
            foreach ($asentamiento as $key => $val)
                $con .="<option value=".$key.">".$val."</option>";
        }else{
            $con .='<option value="">No hay resultados</option>';
        }
        echo $con;
    }

    public function actionList_state($id){
        $state = CatColonia::find()->select(['cv_entidad'])->where(['cv_cp' =>$id])->one();

        $list_state =  CatEntidad::find()->select(['cv_entidad','nombre'])
            ->where(['cv_entidad' => $state])
            ->orderBy(['nombre'=>SORT_ASC])
            ->asArray()->all();
        return \GuzzleHttp\json_encode($list_state);

    }

    public function obtenerPorcentajeDireccionNL($modulo, $provider_id){
        $count_atributos_llenos = 0;
        $datos_validar = array_diff($modulo['Ubicacion']['atributos'],  ["encargado", "geo_ubicacion"] );
        $ubicacion_fiscal = Ubicacion::find()->where(['and', ['provider_id' => $provider_id], ['type_address_prov' => 'DOMICILIO FISCAL'], ["activo" => true] ])->one();
        if( $ubicacion_fiscal == null ){ return 0; }
        if( $ubicacion_fiscal->state_fiscal == 19 ){ return 100; }
        $direccion_nl = Ubicacion::find()->where(['and', ['provider_id' => $provider_id], ['type_address_prov' => 'NL'], ["activo" => true] ])->one();
        if($direccion_nl == null){ return 0; }
        foreach($datos_validar as $atributo){
            if($direccion_nl[$atributo] != null || $direccion_nl[$atributo] != '' || $direccion_nl[$atributo] === 0){
                $count_atributos_llenos++;
            } 
        }

        $diferencia = count($datos_validar) - $count_atributos_llenos;
        if($diferencia == 0){ return 100; }
        else{ return intval(100 - (($diferencia/count($datos_validar)) * 100)); }

        return 0;
    }

    public function obtenerPorcentajeDireccionFiscal($modulo, $provider_id){
        $count_atributos = 0 ;
        $count_atributos_llenos = 0;
        $opciones_ubicacion = ['and', ["provider_id" => $provider_id], ["type_address_prov" => "DOMICILIO FISCAL"], ["activo" => true] ];
        $ubicacion_fiscal = Ubicacion::find($modulo['Ubicacion']['atributos'])->where($opciones_ubicacion)->one();
        if( $ubicacion_fiscal == null ){ return 0; }
        $count_atributos += count($modulo['Ubicacion']['atributos']);
        $count_atributos += count($modulo['FotografiaNegocio']['atributos']);
        foreach($modulo['Ubicacion']['atributos'] as $atributo){
            if($ubicacion_fiscal[$atributo] != null || $ubicacion_fiscal[$atributo] != '' || $ubicacion_fiscal[$atributo] === 0){
                $count_atributos_llenos++;
            } 
        }
        $fotografia_ubicacion = FotografiaNegocio::find($modulo['FotografiaNegocio']['atributos'])->where(["and", ["activo" => true], ["provider_id" => $provider_id], ["ubicacion_id" => $ubicacion_fiscal->ubicacion_id] ])->one();
        if($fotografia_ubicacion != null){
            foreach($modulo['FotografiaNegocio']['atributos'] as $atributo){
                if($fotografia_ubicacion[$atributo] != null || $fotografia_ubicacion[$atributo] != '' || $fotografia_ubicacion[$atributo] === 0){
                    $count_atributos_llenos++;
                } 
            }
        }

        $diferencia = $count_atributos - $count_atributos_llenos;
        if($diferencia == 0){ return 100; }
        else{ return intval(100 - (($diferencia/$count_atributos) * 100)); }

        return 0;
    }

    public function datosModuloUbicacion($modulo_atributos, $direccion_modulo, $provider_id){
        $ubicacion_nl = UbicacionController::obtenerPorcentajeDireccionNL($modulo_atributos, $provider_id);
        $direccion_fisica = UbicacionController::obtenerPorcentajeDireccionFiscal($modulo_atributos, $provider_id);
        $direccion_modulo['porcentaje'] = intval( ( $direccion_modulo['porcentaje'] + $ubicacion_nl + $direccion_fisica ) / 3);
        if($direccion_modulo['porcentaje'] != 100){
            $direccion_modulo['pendiente'] = $direccion_modulo['enviado'] = $direccion_modulo['rechazo'] = $direccion_modulo['terminado'] = 0;
       }
        $notif = Ubicacion::find()->where(['and',['activo'=>true],['notifications'=>true],['provider_id'=>$provider_id]])->all();
        if(!$notif && $direccion_modulo['porcentaje']>5){
            $direccion_modulo['porcentaje'] -= 5;
        }
        return $direccion_modulo;
    }

    public function findModelPorcentaje($id)
    {
        $model = Porcentaje::find()->where(['register_id' => $id])->one();
        if($model == null || empty($model)){
            $model = new Porcentaje();
        }
        return $model;
    }
    public function findModelDireccionNl($id)
    {
        $model = Ubicacion::find()->where(['and',['provider_id' => $id],['type_address_prov' => 'NL']])->orderBy(['ubicacion_id' => SORT_DESC])->one();
        if($model == null || empty($model)){
            $model = new Ubicacion();
        }
        return $model;
    }

    public function findModelDireccionFisicaValidacion($provider_id){
        $respuesta = [ "ubicacion" => null, "fotografia" => null];
        $direccion_fisica = Ubicacion::find()->where(['and', ["provider_id" => $provider_id], ["type_address_prov" => "DOMICILIO FISCAL"], ["activo" => true] ])->one();
        if($direccion_fisica == null){ return $respuesta; }
        $fotografia_ubicacion = FotografiaNegocio::find()->where(['and', ["ubicacion_id" => $direccion_fisica->ubicacion_id, "activo" => true]])->one();
        $respuesta["ubicacion"] = $direccion_fisica;
        $respuesta["fotografia"] = $fotografia_ubicacion;
        return $respuesta;
    }

    public function findDireccionesArray($provider_id, $tipo_provider){
        $direcciones = [];
        $parametros = ['and', ["provider_id" => $provider_id], ['is', 'type_address_prov', null], ["activo" => true] ];
        if($tipo_provider == 'op'){ $parametros = array_merge($parametros, [["status_op" => STATUS::STATUS_VALIDADO]]); }
        $ubicaciones = Ubicacion::find()->where($parametros)->all();
        if($ubicaciones == null){ return $direcciones; }
        foreach($ubicaciones as $ubicacion){
            $respuesta = [ "ubicacion" => null, "fotografia" => null];
            $fotografia_ubicacion = FotografiaNegocio::find()->where(['and', ["ubicacion_id" => $ubicacion->ubicacion_id, "activo" => true]])->one();
            $respuesta["ubicacion"] = $ubicacion ? $ubicacion : new Ubicacion();
            $respuesta["fotografia"] = $fotografia_ubicacion ? $fotografia_ubicacion : new FotografiaNegocio();
            array_push($direcciones, $respuesta);
        }
        return $direcciones;
    }

    public function findModelDireccionFotografia($provider_id, $params, $isRequired = false){
        $respuesta = [ "ubicacion" => $isRequired ? new Ubicacion() : null , "fotografia" => $isRequired ? new FotografiaNegocio() : null ];
        $parametros = ['and', ["provider_id" => $provider_id], ["activo" => true] ];
        if($params){ $parametros = array_merge($parametros, $params); }
        $direccion_fisica = Ubicacion::find()->where($parametros)->one();
        if($direccion_fisica == null){ return $respuesta; }
        $fotografia_ubicacion = FotografiaNegocio::find()->where(['and', ["ubicacion_id" => $direccion_fisica->ubicacion_id, "activo" => true]])->one();
        $respuesta["ubicacion"] = $direccion_fisica;
        $respuesta["fotografia"] = !empty($fotografia_ubicacion) ? $fotografia_ubicacion : New FotografiaNegocio();
        return $respuesta;
    }

    public function getArrayUbicaciones($provider_id){
        $arr_response = [];
        $ubicaciones = Ubicacion::find()->where(['and', ["provider_id" => $provider_id], ["activo" => true] ])->all();
        foreach($ubicaciones as $ubicacion){
            $formato = ['coords' => null, 'titulo' => null];
            $location = json_encode($ubicacion->geo_ubicacion);
            $formato['coords'] = $location;
            $formato['titulo'] = $ubicacion->tipo;
            $colonia = !empty($ubicacion->colonia_fiscal) ? CatAsentamientos::find()->where(['asentamiento_id' => $ubicacion->colonia_fiscal])->one()->nombre : null;
            $ciudad = !empty($ubicacion->city_fiscal) ? CatMunicipios::find()->where(['municipio_id' => $ubicacion->city_fiscal])->one()->nombre : null;
            $estado = !empty($ubicacion->state_fiscal) ? CatEntidades::find()->where(['entidad_id' => $ubicacion->state_fiscal])->one()->nombre : null;
            $formato['desc'] = "$ubicacion->calle_fiscal, $colonia $ubicacion->cp_fiscal, $ciudad, $estado";
            array_push($arr_response, $formato);
        }

        return $arr_response;
    }

    public function findModelDireccionNlId($id)
    {
        $model = Ubicacion::find()->where(['ubicacion_id' => $id])->one();
        if($model == null || empty($model)){
            $model = new Ubicacion();
        }
        return $model;
    }
    public  function  verifyUpdate($provider, $id){
        if($provider){
            $status = Ubicacion::find()->select('status_bys')->where(['and',['provider_id' => $provider],['ubicacion_id'=>$id]])->one()['status_bys'];
            $rechazo = Status::find()->getStatusRechazarCotejo($provider);

            if($status == Status::STATUS_ENEDICION || $status == Status::STATUS_RECHAZADO || $status == Status::STATUS_VALIDADO || !empty($rechazo)){
                return true;
            }
        }
        return false;
    }


    public function actionMapaPrueba(){
        return $this->render('mapa-prueba');
    }

    public function validUrlVideo($value, $user_id){
        $modelPath = new Ubicacion();
        if($value != null && GeneralController::str_contains($value, "archivos_tmp")){
            $ubicacion_path = "{$modelPath->path}/{$user_id}/";
            $this->makeDir($ubicacion_path);
            $new_name_video = str_replace('archivos_tmp/', $ubicacion_path, $value);
            $this->copyFile($value, $new_name_video);
            return $new_name_video;
        }else{ return $value; }
    }

}