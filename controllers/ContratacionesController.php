<?php

namespace app\controllers;

use Yii;
use yii\web\Response;
use yii\widgets\ActiveForm;
use app\helpers\GeneralController;
use app\models\HistoricoContrataciones;
use app\models\HistoricoContratacionesSearch;
use app\models\Provider;

class ContratacionesController extends GeneralController{

    public function actionIndex(){

        $historicoSearch = new HistoricoContratacionesSearch();
        $dataProvider = $historicoSearch->search(Yii::$app->request->queryParams);

        return $this->render('index-historico', [
            'historicos' => $dataProvider,
            'searchModel' => $historicoSearch
        ]);

    }

    public function actionView($id){
        $registro = self::findModel($id);

        return $this->renderAjax('view-historico', [ 'model' => $registro ]);
    }

    public function actionViewList($user_id){
        $provider = Provider::findOne(['user_id'=>$user_id]);
        $registros = HistoricoContrataciones::findAll(['provider_id' => $provider->provider_id]);

        return $this->renderAjax('view-list-contrataciones', [ 'historicos' => $registros ]);
    }

    public function actionUpdateHistorico($id=null){ 
        $isRequest = Yii::$app->request->isAjax;
        $postRequest = Yii::$app->request->post();
        $registro = self::findModel($id);

        if ( $isRequest && $registro->load($postRequest) ) {
            Yii::$app->response->format = Response::FORMAT_JSON;
            $errors = ActiveForm::validate($registro);

            if(array_key_exists('historicocontrataciones-provider_id', $errors)){
                $errors['historicocontrataciones-provider_id'] = ["Se requiere que seleccione un proveedor"];
            }
            if(!is_null($registro->cumplimiento) && $registro->cumplimiento != 'SI'){
                if(is_null($registro->fecha_notificacion) || empty($registro->fecha_notificacion)){ $errors['historicocontrataciones-fecha_notificacion'] = ["Se requiere capture la fecha de notificacion"]; }
                if(is_null($registro->fecha_registro_incumplimiento) || empty($registro->fecha_registro_incumplimiento)){ $errors['historicocontrataciones-fecha_registro_incumplimiento'] = ["Se requiere capture la fecha de registro de incumplimiento"]; }
                if(is_null($registro->documento) || empty($registro->documento)){ $errors['historicocontrataciones-documento'] = ["Se requiere que se agregue al menos un documento"]; }
            }
            return $errors;
            //return ActiveForm::validate($registro);
        } else if ( $registro->load($postRequest) ){

            if($registro->cumplimiento == "SI"){
                $registro->documento = null;
                $registro->fecha_notificacion = null;
                $registro->fecha_registro_incumplimiento = null;
            }else{
                if (!empty($registro->documento) && GeneralController::str_contains($registro->documento, "archivos_tmp") ) {
                    if(!is_dir(HistoricoContrataciones::PATH)){ mkdir(HistoricoContrataciones::PATH,0775,true); }
                    $new_doc_path = str_replace('archivos_tmp/', HistoricoContrataciones::PATH, $registro->documento);
                    $this->copyFile($registro->documento, $new_doc_path);
                    $registro->documento = $new_doc_path;
                }
            }

            $registro->save();
            return $this->redirect(['index']);
        }
        return $this->render('form-historico', ['registro' => $registro]); 
    }

    public function actionDelete($id) {
        self::findModel($id)->delete();

        return $this->redirect(['index']);
    }

    public function findModel($id){
        return is_null($id) ? new HistoricoContrataciones() : HistoricoContrataciones::findOne($id);
    }

}

?>