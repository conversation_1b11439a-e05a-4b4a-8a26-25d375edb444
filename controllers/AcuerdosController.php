<?php

namespace app\controllers;

use app\models\UsuariosAcuerdos;
use yii\web\Controller;

class AcuerdosController extends Controller{

    public function actionAceptarAcuerdo($id){
        if( !is_null($id) ){
            $model = UsuariosAcuerdos::findOne($id);
            $model->valor = true;
            $model->last_updated_at = date('Y-m-d H:i:s');
            $model->save();
        }
    }

}

?>