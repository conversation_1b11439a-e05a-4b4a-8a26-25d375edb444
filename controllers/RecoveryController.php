<?php

/*
 * This file is part of the Dektrium project.
 *
 * (c) Dektrium project <http://github.com/dektrium/>
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace app\controllers;

use app\helpers\FunctionsHelper;
use Yii;
use app\models\RecoveryForm;
use app\models\Token;
use app\traits\AjaxValidationTrait;
use app\traits\EventTrait;
use yii\filters\AccessControl;
use yii\web\Controller;
use yii\web\NotFoundHttpException;
use app\models\ConcursosSearch;
use app\models\Rfc;
use Exception;

/**
 * RecoveryController manages password recovery process.
 *
 *
 * <AUTHOR> <<EMAIL>>
 */
class RecoveryController extends Controller
{
    use AjaxValidationTrait;
    use EventTrait;

    /**
     * Event is triggered before requesting password reset.
     * Triggered with \app\events\FormEvent.
     */
    const EVENT_BEFORE_REQUEST = 'beforeRequest';

    /**
     * Event is triggered after requesting password reset.
     * Triggered with \app\events\FormEvent.
     */
    const EVENT_AFTER_REQUEST = 'afterRequest';

    /**
     * Event is triggered before validating recovery token.
     * Triggered with \app\events\ResetPasswordEvent. May not have $form property set.
     */
    const EVENT_BEFORE_TOKEN_VALIDATE = 'beforeTokenValidate';

    /**
     * Event is triggered after validating recovery token.
     * Triggered with \app\events\ResetPasswordEvent. May not have $form property set.
     */
    const EVENT_AFTER_TOKEN_VALIDATE = 'afterTokenValidate';

    /**
     * Event is triggered before resetting password.
     * Triggered with \app\events\ResetPasswordEvent.
     */
    const EVENT_BEFORE_RESET = 'beforeReset';

    /**
     * Event is triggered after resetting password.
     * Triggered with \app\events\ResetPasswordEvent.
     */
    const EVENT_AFTER_RESET = 'afterReset';



    /**
     * @param string           $id
     * @param \yii\base\Module $module
     * @param array            $config
     */
    public function __construct($id, $module, $config = [])
    {

        parent::__construct($id, $module, $config);
    }

    /** @inheritdoc */
    public function behaviors()
    {
        return [
            'access' => [
                'class' => AccessControl::className(),
                'rules' => [
                    ['allow' => true, 'actions' => ['request', 'set-password', 'reset','request-email'], 'roles' => ['?']],
                ],
            ],
        ];
    }

    /**
     * Shows page where user can request password recovery.
     *
     * @return string
     * @throws \yii\web\NotFoundHttpException
     */
    public function actionRequest(){

        /** @var RecoveryForm $model */
        /*/
        $this->layout = 'request';
        
        $model = \Yii::createObject([
            'class'    => RecoveryForm::className(),
            'scenario' => RecoveryForm::SCENARIO_REQUEST,
        ]);
        $event = $this->getFormEvent($model);

        $this->performAjaxValidation($model);
        $this->trigger(self::EVENT_BEFORE_REQUEST, $event);

        if ($model->load(\Yii::$app->request->post()) && $model->sendRecoveryMessage()) {
            $this->trigger(self::EVENT_AFTER_REQUEST, $event);
            $this->redirect('request-email');
            return false;
        }
        $searchModel = new ConcursosSearch();
        $dataProvider = $searchModel->search(Yii::$app->request->queryParams);
        return $this->render('request', [
            'model' => $model,
            'searchModel' => $searchModel,
            'dataProvider' => $dataProvider,
        ]);
        /*/
        $this->layout = 'limpio';
        return $this->render('reset-password-fiel', [
            /* 'model' => $model,
            'searchModel' => $searchModel,
            'dataProvider' => $dataProvider, */
        ]);
        //*/
    }

    public function actionSetPassword($rfc=null, $token=null){
        $renderData = [ 'view' =>  'form-reset-password-fiel', 'data' => []];
        if(FunctionsHelper::isNullOrEmpty($rfc)){ $renderData['data'] = ['error' => "No se proporciono el RFC del usuario"]; }
        if(FunctionsHelper::isNullOrEmpty($token)){ $renderData['data'] = ['error' => "No se proporciono el token de validacion"]; }

        $fiel_data = base64_decode($token);

        $isValid = FunctionsHelper::isValidTokenFiel($rfc, $fiel_data);
        if(!$isValid){  $renderData['data'] = ['error' => "Token invalido"]; }


        /* if($this->request->isGet){
        } */

        if($this->request->isPost){
            //Se supone se valido por JS, asi que meh. Problema de otro, no mio. 
            try{
                $post_data = $this->request->post();
                if(FunctionsHelper::isNullOrEmpty($post_data['password'])){ throw new Exception("No se proporciono password"); }
                if(FunctionsHelper::isNullOrEmpty($post_data['repeat-password'])){ throw new Exception("No se proporciono la confirmacion del password"); }
                $provider_rfc = Rfc::findOne(['rfc' => $rfc]);
                if( is_null($provider_rfc) ){ throw new Exception("No se enconto un proveedor con los datos proporcionados"); }
                $user = $provider_rfc->provider->usuario;
                $user->password = $post_data['password'];
                $user->save();
                $this->redirect('/site/login');
            }catch(Exception $e){
                return json_encode(['error' => $e->getMessage()]);
            }
            
        }

        return $this->renderPartial($renderData['view'], [ 'data' => $renderData['data'] ] );
    }

    public function actionRequestEmail()
    {
//        if (!$this->module->enablePasswordRecovery) {
//            throw new NotFoundHttpException();
//        }

        /** @var RecoveryForm $model */
        $this->layout = 'request';



        return $this->render('request-email');
    }

    /**
     * Displays page where user can reset password.
     *
     * @param int    $id
     * @param string $code
     *
     * @return string
     * @throws \yii\web\NotFoundHttpException
     */
    public function actionReset($id, $code)
    {

        $this->layout = 'request';

        /** @var Token $token */
       
        $token = Token::find()->where(['user_id' => $id, 'code' => $code, 'type' => Token::TYPE_RECOVERY])->one();
        $event = $this->getResetPasswordEvent($token);

        $this->trigger(self::EVENT_BEFORE_TOKEN_VALIDATE, $event);

        if ($token === null || $token->isExpired || $token->user === null) {
            $this->trigger(self::EVENT_AFTER_TOKEN_VALIDATE, $event);
            \Yii::$app->session->setFlash(
                'danger',
                \Yii::t('user', 'Recovery link is invalid or expired. Please try requesting a new one.')
            );
            return $this->redirect(['/site/login',
                'title'  => 'Invalid or expired link',
            ]);

        }

        /** @var RecoveryForm $model */
        $model = \Yii::createObject([
            'class'    => RecoveryForm::className(),
            'scenario' => RecoveryForm::SCENARIO_RESET,
        ]);
        $event->setForm($model);

        $this->performAjaxValidation($model);
        $this->trigger(self::EVENT_BEFORE_RESET, $event);

        if ($model->load(\Yii::$app->getRequest()->post()) && $model->resetPassword($token)) {
            $this->trigger(self::EVENT_AFTER_RESET, $event);
            return $this->redirect(['/site/login']);

        }

        return $this->render('reset', [
            'model' => $model,
        ]);
    }
}
