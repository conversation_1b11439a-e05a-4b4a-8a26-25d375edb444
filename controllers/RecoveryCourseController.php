<?php

/*
 * This file is part of the Dektrium project.
 *
 * (c) Dektrium project <http://github.com/dektrium/>
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace app\controllers;

use app\models\RecoveryCourseForm;
use app\models\TokenCourse;
use Yii;
use app\traits\AjaxValidationTrait;
use app\traits\EventTrait;
use yii\filters\AccessControl;
use yii\web\Controller;
use yii\web\NotFoundHttpException;
use app\models\ConcursosSearch;

/**
 * RecoveryController manages password recovery process.
 *
 *
 * <AUTHOR> <<EMAIL>>
 */
class RecoveryCourseController extends Controller
{
    use AjaxValidationTrait;
    use EventTrait;

    /**
     * Event is triggered before requesting password reset.
     * Triggered with \app\events\FormEvent.
     */
    const EVENT_BEFORE_REQUEST = 'beforeRequest';

    /**
     * Event is triggered after requesting password reset.
     * Triggered with \app\events\FormEvent.
     */
    const EVENT_AFTER_REQUEST = 'afterRequest';

    /**
     * Event is triggered before validating recovery token.
     * Triggered with \app\events\ResetPasswordEvent. May not have $form property set.
     */
    const EVENT_BEFORE_TOKEN_VALIDATE = 'beforeTokenValidate';

    /**
     * Event is triggered after validating recovery token.
     * Triggered with \app\events\ResetPasswordEvent. May not have $form property set.
     */
    const EVENT_AFTER_TOKEN_VALIDATE = 'afterTokenValidate';

    /**
     * Event is triggered before resetting password.
     * Triggered with \app\events\ResetPasswordEvent.
     */
    const EVENT_BEFORE_RESET = 'beforeReset';

    /**
     * Event is triggered after resetting password.
     * Triggered with \app\events\ResetPasswordEvent.
     */
    const EVENT_AFTER_RESET = 'afterReset';



    /**
     * @param string           $id
     * @param \yii\base\Module $module
     * @param array            $config
     */
    public function __construct($id, $module, $config = [])
    {

        parent::__construct($id, $module, $config);
    }

    /** @inheritdoc */
    public function behaviors()
    {
        return [
            'access' => [
                'class' => AccessControl::className(),
                'rules' => [
                    ['allow' => true, 'actions' => ['request', 'reset','request-email'], 'roles' => ['?']],
                ],
            ],
        ];
    }

    /**
     * Shows page where user can request password recovery.
     *
     * @return string
     * @throws \yii\web\NotFoundHttpException
     */
    public function actionRequest()
    {
//        if (!$this->module->enablePasswordRecovery) {
//            throw new NotFoundHttpException();
//        }

        /** @var RecoveryCourseForm $model */
        $this->layout = 'requestcourse';

        $model = \Yii::createObject([
            'class'    => RecoveryCourseForm::className(),
            'scenario' => RecoveryCourseForm::SCENARIO_REQUEST,
        ]);
        $event = $this->getFormEvent($model);

        $this->performAjaxValidation($model);
        $this->trigger(self::EVENT_BEFORE_REQUEST, $event);
        if ($model->load(\Yii::$app->request->post()) && $model->sendRecoveryMessage()) {
            $this->trigger(self::EVENT_AFTER_REQUEST, $event);
            $this->redirect('request-email');
            return false;
        }
        return $this->render('request', [
            'model' => $model
        ]);
    }

    public function actionRequestEmail()
    {
//        if (!$this->module->enablePasswordRecovery) {
//            throw new NotFoundHttpException();
//        }

        /** @var RecoveryCourseForm $model */
        $this->layout = 'requestcourse';



        return $this->render('request-email');
    }

    /**
     * Displays page where user can reset password.
     *
     * @param int    $id
     * @param string $code
     *
     * @return string
     * @throws \yii\web\NotFoundHttpException
     */
    public function actionReset($id, $code)
    {

        $this->layout = 'requestcourse';

        /** @var TokenCourse $token */

        $token = TokenCourse::find()->where(['user_id' => $id, 'code' => $code, 'type' => TokenCourse::TYPE_RECOVERY])->one();

        $event = $this->getResetPasswordEventCourse($token);
        $this->trigger(self::EVENT_BEFORE_TOKEN_VALIDATE, $event);

        if ($token === null || $token->isExpired || $token->user === null) {
            $this->trigger(self::EVENT_AFTER_TOKEN_VALIDATE, $event);
            \Yii::$app->session->setFlash(
                'danger',
                \Yii::t('user', 'Recovery link is invalid or expired. Please try requesting a new one.')
            );
            return $this->redirect(['/site/',
                'title'  => 'Invalid or expired link',
            ]);

        }

        /** @var RecoveryCourseForm $model */
        $model = \Yii::createObject([
            'class'    => RecoveryCourseForm::className(),
            'scenario' => RecoveryCourseForm::SCENARIO_RESET,
        ]);
        $event->setForm($model);

        $this->performAjaxValidation($model);
        $this->trigger(self::EVENT_BEFORE_RESET, $event);

        if ($model->load(\Yii::$app->getRequest()->post()) && $model->resetPassword($token)) {
            $this->trigger(self::EVENT_AFTER_RESET, $event);
            return $this->redirect(['/site/']);

        }

        return $this->render('reset', [
            'model' => $model,
        ]);
    }
}
