<?php

namespace app\controllers;

use app\models\Bancos;
use app\models\DatosValidados;
use app\models\Historico;
use Yii;
use app\models\IntervencionBancaria;
use app\models\IntervencionBancariaSearch;
use yii\web\Controller;
use yii\web\NotFoundHttpException;
use yii\filters\VerbFilter;
use yii\helpers\ArrayHelper;
use yii\web\UploadedFile;
use app\models\Provider;
use app\models\Usuarios;
use app\models\Status;
use app\models\Porcentaje;
use app\helpers\GeneralController;
use yii\db\Query;

if(Yii::$app->user->can(Usuarios::ROLE_PROVIDER)){
    Yii::$app->params['provider_id'] = Yii::$app->user->identity->getProvider();
}
/**
 * IntervencionBancariaController implements the CRUD actions for IntervencionBancaria model.
 */
class IntervencionBancariaController extends GeneralController
{
    /**
     * @inheritdoc
     */
    public function behaviors()
    {
        return [
            'verbs' => [
                'class' => VerbFilter::className(),
                'actions' => [
                    'delete' => ['POST'],
                ],
            ],
        ];
    }

    /**
     * Lists all IntervencionBancaria models.
     * @return mixed
     */
    public function actionIndex()
    {
        echo $this->render('/site/error',['name' => 'Lo sentimos...','message' => 'Sin permisos para acceder a este módulo!']);

        return false;
        $id = Provider::find()->select('provider_id')->where(['user_id' => Yii::$app->user->id])->one()->provider_id;
        $searchModel = new IntervencionBancariaSearch();
        $dataProvider = $searchModel->search(Yii::$app->request->queryParams,$id);
        return $this->render('index', [
            'searchModel' => $searchModel,
            'dataProvider' => $dataProvider,
        ]);
    }

    /**
     * Displays a single IntervencionBancaria model.
     * @param integer $id
     * @return mixed
     * @throws NotFoundHttpException if the model cannot be found
     */
    public function actionView($id)
    {
        if($id == null || !Provider::verifyProvider($id) ||
            Yii::$app->user->can(Usuarios::ROLE_PROVIDER) && (!Provider::verifyEtapa2($id))){
            echo $this->render('/site/error',['name' => 'Lo sentimos...','message' => 'Acceso denegado']);

            return false;
        }
        $rechazo = [];
        $porcentaje = [];
        $modelos = [];
        $intervencion_ban = $this->findModel($id);

        if(Yii::$app->user->can(Usuarios::ROLE_PROVIDER)){
            if(Yii::$app->request->get('tipo')){
                $tipo_provider = Yii::$app->request->get('tipo');
            }else{
                $id_provider = Yii::$app->user->identity->getProvider();
                $tipo_provider = Provider::find()->select('tipo_provider')->where(['provider_id' => $id_provider])->one()->tipo_provider;
            }
            $rechazo = Status::find()->getStatus($id,'IntervencionBancaria',$tipo_provider);
            $porcentaje = $this->findModelPorcentaje($id);
        }
        if(Yii::$app->user->can(Usuarios::ROLE_VALIDADOR_PROVEEDORES) or Yii::$app->user->can(Usuarios::ROLE_VALIDADOR_CONTRATISTAS)){
            $rechazo = new Status();
            $rechazo->scenario = Status::SCENARIO_CREATE;

            if(isset($intervencion_ban->provider_id) && !empty($intervencion_ban->provider_id)){
                $modelos[$intervencion_ban->formName()] = $intervencion_ban->attributes;
            }
            $modelos = base64_encode(json_encode($modelos));
        }

        return $this->render('view', [
            'model' => $intervencion_ban,
            'model_porcentaje' => $porcentaje,
            'rechazo' => $rechazo,
            'modelos' => $modelos

        ]);
    }

        /**
     * Displays a single IntervencionBancaria model.
     * @param integer $id
     * @return mixed
     * @throws NotFoundHttpException if the model cannot be found
     */
     public function actionViewValidar($id)
     {
         if($id == null || !Provider::verifyProvider($id) ||
             Yii::$app->user->can(Usuarios::ROLE_PROVIDER) && (!Provider::verifyEtapa2($id))){
             echo $this->render('/site/error',['name' => 'Lo sentimos...','message' => 'Acceso denegado']);
 
             return false;
         }
         $rechazo = [];
         $porcentaje = [];
         $modelos = [];
         $intervencion_ban = $this->findModel($id);
 
         if(Yii::$app->user->can(Usuarios::ROLE_PROVIDER)){
             if(Yii::$app->request->get('tipo')){
                 $tipo_provider = Yii::$app->request->get('tipo');
             }else{
                 $id_provider = Yii::$app->user->identity->getProvider();
                 $tipo_provider = Provider::find()->select('tipo_provider')->where(['provider_id' => $id_provider])->one()->tipo_provider;
             }
             $rechazo = Status::find()->getStatus($id,'IntervencionBancaria',$tipo_provider);
             $porcentaje = $this->findModelPorcentaje($id);
         }
         //if(Yii::$app->user->can(Usuarios::ROLE_VALIDADOR_PROVEEDORES)){
         $rechazo = new Status();
         $rechazo->scenario = Status::SCENARIO_CREATE;
         $model_validado = new DatosValidados();
         $model_validado->scenario = DatosValidados::SCENARIO_CREATE;
             if(isset($intervencion_ban->provider_id) && !empty($intervencion_ban->provider_id)){
                 $modelos[$intervencion_ban->formName()] = $intervencion_ban->attributes;
             }
             $modelos = base64_encode(json_encode($modelos));
         //}
         return $this->renderAjax('view-validar', [
             'model' => $intervencion_ban,
             'model_porcentaje' => $porcentaje,
             'model_validado' => $model_validado,
             'rechazo' => $rechazo,
             'modelos' => $modelos
 
         ]);
     } 

    public function actionConfirm(){
        $modelos = isset(Yii::$app->request->post()['modelos'])?Yii::$app->request->post()['modelos']:[];

        $role = Yii::$app->user->identity->role;
        $status_global = $role == 'VALIDADOR PROVEEDORES'?'status_bys':($role == 'VALIDADOR CONTRATISTAS'?'status_op':'');
        $t = Yii::$app->db->beginTransaction();
        $id = Yii::$app->request->post()['provider_id'];
        $intervencion_ban = Yii::$app->db->createCommand();
        $intervencion_ban->update('provider.intervencion_bancaria',[$status_global => 'VALIDADO'],'provider_id ='.$id);
        if($intervencion_ban->execute()){
            $status_id = Status::find()->select('status_id')
                ->where(['and',['register_id' => $id],[$status_global => 'PENDIENTE']])
                ->andWhere(['modelo'=>'IntervencionBancaria'])->one();
            $validar = true;
            if(!empty($status_id['status_id'])){
                $requi_status = Status::findOne($status_id);
                $requi_status->$status_global = Status::STATUS_TERMINADO;
                $validar = $requi_status->save();
            }
            if($validar){

                $rfc_pro = Provider::find()->select('rfc')->where(['provider_id' => $id])->one()->rfc;
                $valid = true;
                if(!empty($modelos)){
                    $models_arr = json_decode(base64_decode($modelos),true);

                    foreach ($models_arr as $k => $value){
                        $model_historico = new Historico();
                        if(DatosValidados::Verify($k)){
                            $value[$status_global]='VALIDADO';
                        }
                        $model_historico->provider_id = $id;
                        $model_historico->rfc = $rfc_pro;
                        $model_historico->validador_id = Yii::$app->user->getId();
                        $model_historico->modelo = $k;
                        $model_historico->data = json_encode($value);
                        $valid = $valid && $model_historico->save();
                    }
                }
                if($valid){
                    $t->commit();
                    Yii::$app->session->setFlash('success', 'Los datos se validaron correctamente');
                }else{
                    $t->rollBack();
                    Yii::$app->session->setFlash('error', 'Error al validar la información');
                }
            }else{
                $t->rollBack();
                Yii::$app->session->setFlash('error', 'Error al validar la información');
            }

        }
        return Yii::$app->getResponse()->redirect(['datosFinancieros/declaracion-isr/index-validador']);

    }

    /**
     * Creates a new IntervencionBancaria model.
     * If creation is successful, the browser will be redirected to the 'view' page.
     * @return mixed
     */
    public function actionCreate()
    {
        $model = new IntervencionBancaria();

        $id = Provider::find()->select('provider_id')->where(['user_id' => Yii::$app->user->id])->one()->provider_id;

        if ($model->load(Yii::$app->request->post())) {
            $model->banco =Yii::$app->request->post('banco');
            $model->urlEstadoCuenta = UploadedFile::getInstance($model,'estado_cuenta');
            if(!empty($model->urlEstadoCuenta)){
                $model->estado_cuenta = !empty($model->urlEstadoCuenta)?$model->path.'/'.md5($model->urlEstadoCuenta->baseName).time().'.'.$model->urlEstadoCuenta->extension:'';
                if($model->save(false)) {

                    if(!file_exists($model->path)){
                        mkdir($model->path,0755,true);
                    }
                    !empty($model->urlEstadoCuenta)?$model->urlEstadoCuenta->saveAs($model->estado_cuenta):'';
                }
            }

            return $this->redirect(['view', 'id' => $model->id]);
        }

        return $this->render('create', [
            'model' => $model,
            'provider_id' =>$id,
        ]);
    }

    /**
     * Updates an existing IntervencionBancaria model.
     * If update is successful, the browser will be redirected to the 'view' page.
     * @param integer $id
     * @return mixed
     * @throws NotFoundHttpException if the model cannot be found
     */
    public function actionUpdate($id=null)
    {
        if (Yii::$app->user->can(Usuarios::ROLE_PROVIDER)){
            $id=Yii::$app->params['provider_id'];
        }
        if($id == null || !Provider::verifyProvider($id) || !Provider::verifyEtapa2($id)|| !IntervencionBancariaController::verifyUpdate($id)){
            echo $this->render('/site/error',['name' => 'Lo sentimos...','message' => 'Acceso denegado']);

            return false;
        }

        $model = $this->findModel($id);
        $model_porcentaje = $this->findModelPorcentaje($id);
        $urlEstadoCuentaOld = $model->estado_cuenta;
        if ($model->load(Yii::$app->request->post())) {
            $verify = Porcentaje::find()->select(['register_id'])->where(['and',['register_id' =>$id],['modelo'=>'metodo_pago']])->one();
            if($verify){
                $porcentajeOld = $model_porcentaje->porcentaje;

                if(isset($_POST['porcentaje_total']) && $_POST['porcentaje_total'] != ''){
                    $model_porcentaje->porcentaje = round($_POST['porcentaje_total']);
                }else{
                    $model_porcentaje->porcentaje = $porcentajeOld;
                }
                $status_id = Status::find()->select('status_id')
                    ->where(['and',['register_id' => $id],['status_bys' => 'PENDIENTE']])
                    ->andWhere(['modelo'=>'IntervencionBancaria'])->one();
                if(!empty($status_id['status_id'])){
                    $requi_status = Status::findOne($status_id);
                    $requi_status->status_bys = Status::STATUS_TERMINADO;
                    $requi_status->update();
                }
                \Yii::$app->db->createCommand("update provider.porcentaje set porcentaje = $model_porcentaje->porcentaje where modelo='metodo_pago' and register_id =:id")
                    ->bindValue(':id', $id)
                    ->execute();
            }else{
                $porcen = new Porcentaje();
                $porcen->modelo = 'metodo_pago';
                $porcen->register_id = $id;
                if(isset($_POST['porcentaje_total']) && $_POST['porcentaje_total'] != ''){
                    $porcen->porcentaje = round($_POST['porcentaje_total']);
                }else{
                    $porcen->porcentaje = 0;
                }
                $porcen->save();
            }

            $model->urlEstadoCuenta = !empty(UploadedFile::getInstance($model,'estado_cuenta'))?UploadedFile::getInstance($model,'estado_cuenta'):'';


            $model->estado_cuenta = !empty($model->urlEstadoCuenta)?$model->path.'/'.md5($model->urlEstadoCuenta->baseName).time().'.'.$model->urlEstadoCuenta->extension:$urlEstadoCuentaOld;
               
                if($model->save(false)) {

                    if(!file_exists($model->path)){
                        mkdir($model->path,0755,true);
                    }
                    !empty($model->urlEstadoCuenta)?$model->urlEstadoCuenta->saveAs($model->estado_cuenta):'';
                }

            return $this->redirect(['view', 'id' => $model->provider_id]);
        }

        return $this->render('update', [
            'model' => $model,
        ]);
    }

    /**
     * Deletes an existing IntervencionBancaria model.
     * If deletion is successful, the browser will be redirected to the 'index' page.
     * @param integer $id
     * @return mixed
     * @throws NotFoundHttpException if the model cannot be found
     */
    public function actionDelete($id)
    {
        $this->findModel($id)->delete();

        return $this->redirect(['index']);
    }

    /**
     * Finds the IntervencionBancaria model based on its primary key value.
     * If the model is not found, a 404 HTTP exception will be thrown.
     * @param integer $id
     * @return IntervencionBancaria the loaded model
     * @throws NotFoundHttpException if the model cannot be found
     */
    protected function findModel($id)
    {
        /*if (($model = IntervencionBancaria::findOne($id)) !== null) {
            return $model;
        }*/
        $model = IntervencionBancaria::find()->where(['provider_id' => $id])->one();
        if($model == null || empty($model)){
            $model = new IntervencionBancaria();
        }
        return $model;
        //throw new NotFoundHttpException('The requested page does not exist.');
    }
    public function actionBanco($id){
        $banco = Bancos::find()->select(['id_banco','banco'])->where(['id_banco'=> $id])->asArray()->all();

        return json_encode($banco);
    }
    public function findModelPorcentaje($id)
    {
        $model = Porcentaje::find()->where(['and', ['register_id' => $id], ['modelo' => 'metodo_pago']])->one();
        if($model == null || empty($model)){
            $model = new Porcentaje();
        }
        return $model;
    }
    public function actionTerminar($id){

        if($id == null || !Provider::verifyProvider($id) || !Provider::verifyEtapa2($id)|| !IntervencionBancariaController::verifyTerminar($id)){
            echo $this->render('/site/error',['name' => 'Lo sentimos...','message' => 'Acceso denegado']);
            return false;
        }
        $model = $this->findModel($id);
        if($model->status_bys!='PENDIENTE' && $model->status_bys!='RECHAZADO'){
            echo $this->render('/site/error',['name' => 'Lo sentimos...','message' => 'Acceso denegado']);
            return false;
        }
        if(Yii::$app->request->get('tipo')){
            $get_tipo = Yii::$app->request->get('tipo');
            ($get_tipo=='bys')?$status='status_bys':$status='status_op';
        }else{
            $tipo_provider = Provider::find()->select('tipo_provider')->where(['provider_id' => $id])->one()->tipo_provider;
            ($tipo_provider=='bys')?$status='status_bys':$status='status_op';
        }

        $rfc = Yii::$app->db->createCommand();
        $rfc->update('provider.intervencion_bancaria',[$status => 'PENDIENTE POR VALIDAR'],'provider_id ='.$id);
        $rfc->execute();

        $query = new Query;
        $query	->select('usuarios.email')  
                ->from('usuarios')
                ->innerJoin('usuarios_responsabilidades',
                    'usuarios_responsabilidades.user_id = usuarios.user_id')
                ->where(['and',['usuarios.role' => 'VALIDADOR PROVEEDORES', 'usuarios.status' => 'ACTIVO'],['usuarios_responsabilidades.responsabilidad_id' => 3]]);             
        $command = $query->createCommand();
        $correos_validadores = ArrayHelper::getColumn($command->queryAll(),'email');

//        Yii::$app->mailer->compose('/provider/correos/nuevo',['modulo' => 'Método de pago'])
//        ->setFrom('<EMAIL>')
//        ->setTo( $correos_validadores)
//        ->setSubject('Nuevo proveedor')
//        ->send();

        self::sendEmail('/provider/correos/nuevo',null,$correos_validadores,'Nuevo proveedor',['modulo' => 'Método de pago']);

        return $this->redirect('../../provider/dashboard');
    }
    //valida si puede entrar a action update
    public  function  verifyUpdate($provider){
        if($provider){
            $status = IntervencionBancaria::find()->select('status_bys')->where(['provider_id' => $provider])->one()->status_bys;
            if($status == 'VALIDADO'|| $status == 'PENDIENTE'|| $status == 'RECHAZADO'){
                return true;
            }
        }
        return false;
    }
    //valida si puede entrar a action terminar
    public  function  verifyTerminar($provider){
        if($provider){
            $status = IntervencionBancaria::find()->select('status_bys')->where(['provider_id' => $provider])->one()->status_bys;
            $porcentaje = Porcentaje::find()->select('porcentaje')->where(['and', ['register_id' => $provider], ['modelo' => 'metodo_pago']])->one()->porcentaje;
            if($status == 'PENDIENTE' && $porcentaje=='100' || $status == 'RECHAZADO' && $porcentaje=='100'){
                return true;
            }
        }
        return false;
    }
}
