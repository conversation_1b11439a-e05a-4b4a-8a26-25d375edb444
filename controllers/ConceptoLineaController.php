<?php

namespace app\controllers;

use app\helpers\GeneralController;
use app\models\ConceptoFamilia;
use app\models\ConceptoGrupo;
use app\models\ConceptoLinea;
use app\models\ConceptoLineaInegi;
use app\models\ConceptoLineaSearch;
use yii\helpers\ArrayHelper;
use yii\web\Controller;
use yii\web\NotFoundHttpException;
use yii\filters\VerbFilter;

/**
 * ConceptoLineaController implements the CRUD actions for ConceptoLinea model.
 */
class ConceptoLineaController extends GeneralController
{
    /**
     * @inheritDoc
     */
    public function behaviors()
    {
        return array_merge(
            parent::behaviors(),
            [
                'verbs' => [
                    'class' => VerbFilter::className(),
                    'actions' => [
                        'delete' => ['POST'],
                    ],
                ],
            ]
        );
    }

    /**
     * Lists all ConceptoLinea models.
     * @return mixed
     */
    public function actionIndex()
    {
        $searchModel = new ConceptoLineaSearch();
        $dataProvider = $searchModel->search($this->request->queryParams);

        return $this->render('index', [
            'searchModel' => $searchModel,
            'dataProvider' => $dataProvider,
        ]);
    }

    /**
     * Displays a single ConceptoLinea model.
     * @param int $concepto_linea_id Concepto Linea ID
     * @return mixed
     * @throws NotFoundHttpException if the model cannot be found
     */
    public function actionView($id)
    {
        return $this->render('view', [
            'model' => $this->findModel($id),
        ]);
    }

    /**
     * Creates a new ConceptoLinea model.
     * If creation is successful, the browser will be redirected to the 'view' page.
     * @return mixed
     */
    public function actionCreate()
    {
        $model = new ConceptoLinea();
        $modelInegi = new ConceptoLineaInegi();

        if ($this->request->isPost) {
            $t = \Yii::$app->db->beginTransaction();
            if ($model->load($this->request->post()) &&  $modelInegi->load($this->request->post())) {
                if($model->save()){
                    $modelInegi->concepto_linea_id = $model->concepto_linea_id;
                    if($modelInegi->save()){
                        $t->commit();
                        return $this->redirect(['view', 'id' => $model->concepto_linea_id]);
                    }else{$t->rollBack();}
                }else{$t->rollBack();}
            }
        } else {
            $model->loadDefaultValues();
        }

        $familia = ArrayHelper::map(ConceptoFamilia::find()->select(['familia','concepto'])->asArray()->all(),'familia','concepto');

        return $this->render('create', [
            'model' => $model,
            'familia' => $familia,
            'grupo' => [],
            'modelInegi' => $modelInegi
        ]);
    }

    /**
     * Updates an existing ConceptoLinea model.
     * If update is successful, the browser will be redirected to the 'view' page.
     * @param int $concepto_linea_id Concepto Linea ID
     * @return mixed
     * @throws NotFoundHttpException if the model cannot be found
     */
    public function actionUpdate($id)
    {
        $model = $this->findModel($id);
        $modelInegi = $this->findModelInegi($id);

        if ($this->request->isPost && $model->load($this->request->post()) && $modelInegi->load($this->request->post())) {
            $model->save();
            $modelInegi->save();
            return $this->redirect(['view', 'id' => $model->concepto_linea_id]);
        }

        $familia = ArrayHelper::map(ConceptoFamilia::find()->select(['familia','concepto'])->asArray()->all(),'familia','concepto');

        $grupo = [];
        if(!empty($model->concepto_grupo_id)){
            $grupo = ArrayHelper::map(\Yii::$app->db->createCommand("select concepto_grupo_id,concepto from productos_servicios.concepto_grupo where
                         concepto_familia_id in(select concepto_familia_id from productos_servicios.concepto_grupo where concepto_grupo_id = :id)",[':id' => $model->concepto_grupo_id])->queryAll(),'concepto_grupo_id','concepto');
        }
                return $this->render('update', [
            'model' => $model,
            'familia' => $familia,
            'grupo' => $grupo,
            'modelInegi' => $modelInegi
        ]);
    }

    /**
     * Deletes an existing ConceptoLinea model.
     * If deletion is successful, the browser will be redirected to the 'index' page.
     * @param int $concepto_linea_id Concepto Linea ID
     * @return mixed
     * @throws NotFoundHttpException if the model cannot be found
     */
    public function actionDelete($id)
    {
        $this->findModel($id)->delete();

        return $this->redirect(['index']);
    }

    /**
     * Finds the ConceptoLinea model based on its primary key value.
     * If the model is not found, a 404 HTTP exception will be thrown.
     * @param int $concepto_linea_id Concepto Linea ID
     * @return ConceptoLinea the loaded model
     * @throws NotFoundHttpException if the model cannot be found
     */
    protected function findModel($id)
    {
        if (($model = ConceptoLinea::findOne($id)) !== null) {
            return $model;
        }else{
            return new ConceptoLinea();
        }
    }
    protected function findModelInegi($id){

        if(($model = ConceptoLineaInegi::findOne(['concepto_linea_id' => $id]))!==null){
            return $model;
        }else{
            return new ConceptoLineaInegi();
        }

    }


    public function actionList_group($id){
        $con = '';
        $con .='<option></option>';
        if(\Yii::$app->request->isAjax){
            $data = ArrayHelper::map(ConceptoGrupo::find()->select(['concepto_grupo_id','concepto'])->where(['concepto_familia_id' => intval($id)])->asArray()->all(),'concepto_grupo_id','concepto');
            if($data){
                foreach ($data as $i => $v){
                    $con .='<option value="'.$i.'">'.$v.'</option>';
                }
            }
        }
        echo $con;
    }
}
