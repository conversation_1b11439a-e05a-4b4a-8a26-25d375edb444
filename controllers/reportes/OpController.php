<?php


namespace app\controllers\reportes;

use app\helpers\GeneralController;

use app\models\HistoricoCertificados;
use Yii;
use yii\data\SqlDataProvider;

class OpController extends GeneralController
{

    public function actionCertificados(){
        $condition = "";
        $params = [
          ':fi'=> isset(Yii::$app->request->queryParams['fi'])? Yii::$app->request->queryParams['fi'] : (date('Y')-1).'-01-01',
          ':ff'=>  isset(Yii::$app->request->queryParams['ff'])? Yii::$app->request->queryParams['ff'] : (date('Y')-1).'-12-31'
        ];

        //condiciones

        $sql = "
            
            SELECT
                p.provider_id as id, case when p.tipo_persona = 'Persona moral' then p.name_razon_social else CONCAT_WS(' ', p.pf_nombre, p.pf_ap_paterno, p.pf_ap_materno) end AS nombrerazonsocial,
                date(p.creation_date) AS iniciotramite, date(cerp.created_at) as primercertificado, date(ceru.created_at) as ultimocertificado, cert.total as totalcertificados,
                date(cerp.created_at) - date(p.creation_date) as dias, p.rfc, p.\"Clave_ProveedorSire\" AS siregob, date(agp.created_at) as primerenvio, date(agu.created_at) as ultimoenvio, 
                agt.total as totalenvios, p.permanently_disabled, p.estratificacion, p.vigencia
            FROM
                provider p
                INNER JOIN usuarios u ON p.user_id = u.user_id
                LEFT JOIN (
                  SELECT hc.created_at, hc.provider_id, ROW_NUMBER() OVER (PARTITION BY hc.provider_id ORDER BY hc.created_at asc) AS prim FROM historico_certificados hc
                  WHERE hc.tipo = 'CERTIFICADO' AND hc.provider_type = 'bys' AND DATE(created_at) between :fi AND :ff
                ) cerp ON p.provider_id = cerp.provider_id AND cerp.prim = 1
                LEFT JOIN (
                  SELECT hc.created_at, hc.provider_id, ROW_NUMBER() OVER (PARTITION BY hc.provider_id ORDER BY hc.created_at desc) AS ult FROM historico_certificados hc
                  WHERE hc.tipo = 'CERTIFICADO' AND hc.provider_type = 'bys' AND DATE(created_at) between :fi AND :ff
                ) ceru ON p.provider_id = ceru.provider_id AND ceru.ult = 1
                LEFT JOIN (
                  SELECT count(1) as total, hc.provider_id FROM historico_certificados hc
                  WHERE hc.tipo = 'CERTIFICADO' AND hc.provider_type = 'bys' AND DATE(created_at) between :fi AND :ff group by hc.provider_id
                ) cert ON p.provider_id = cert.provider_id
                left join (
                  select id_proveedor as id,created_at, row_number() over(partition by id_proveedor order by created_at asc) as contador
                  from provider.asignacion where date(created_at) between :fi AND :ff
                ) agp on agp.id = p.provider_id and agp.contador = 1
                left join (
                  select id_proveedor as id,created_at, row_number() over(partition by id_proveedor order by created_at desc) as contador
                  from provider.asignacion where date(created_at) between :fi AND :ff
                ) agu on agu.id = p.provider_id and agu.contador = 1
                left join (
                  select count(1) as total, id_proveedor as id from provider.asignacion 
                  where date(created_at) between :fi AND :ff group by id_proveedor
                ) agt on agt.id = p.provider_id
            WHERE
                DATE(p.creation_date) BETWEEN :fi AND :ff  $condition
            ORDER BY
                p.provider_id
        
        ";

        if(isset(Yii::$app->request->queryParams['d'])){
            $data = Yii::$app->db->createCommand($sql)->bindValues($params)->queryAll();
            return GeneralController::downloadExcel($data,'ReporteCertificaciones');
        }


        $data['data'] = new SqlDataProvider([
            'sql'=>$sql,
            'params'=>$params,
            'pagination'=>['pageSize'=>10]
        ]);


        return $this->render('certificados',$data);
    }


    public function actionRevision(){
        $where = "where 1 = 1 ";
        if(\Yii::$app->request->queryParams){ //filtros sqldataProvider
            $rfc = isset(Yii::$app->request->queryParams['rfc'])? Yii::$app->request->queryParams['rfc'] : false;
            $where .= $rfc && $rfc != ''?
                " and ( p.rfc ilike '%$rfc%' or name_razon_social ilike '%$rfc%' )":'';
            $tipo = isset(Yii::$app->request->queryParams['tipo'])? Yii::$app->request->queryParams['tipo'] : false;
            $where .= $tipo && $tipo != '' && $tipo !='0' ?
                " and tipo = '$tipo' ":'';
            $rev = isset(Yii::$app->request->queryParams['revisor'])? Yii::$app->request->queryParams['revisor'] : false;
            $where .= $rev && $rev != ''?
                " and  ( concat_ws(' ',u.nombre,u.primer_apellido,u.segundo_apellido) ilike '%$rev%' or u.email ilike '%$rev%' )":'';

            $mod = isset(Yii::$app->request->queryParams['modulo'])? Yii::$app->request->queryParams['modulo'] : false;
            $where .= $mod && $mod != '' && $mod !='0' ?
                " and modulo = '$mod' ":'';

            $inicio = isset(Yii::$app->request->queryParams['inicio'])? Yii::$app->request->queryParams['inicio'] : false;
            $fin = isset(Yii::$app->request->queryParams['fin'])? Yii::$app->request->queryParams['fin'] : false;
            $where .= $inicio && $fin?
                " and  date(fecha) between '$inicio' and  '$fin'  ":'';


        }


        $sql = "
            
            select p.rfc,p.permanently_disabled as estatus, CASE WHEN (p.tipo_persona = 'Persona moral') then p.name_razon_social else 
            concat_ws(' ',p.pf_nombre,p.pf_ap_paterno,p.pf_ap_materno) end as name_razon_social, b.*,u.user_id as revisor_id ,
            concat_ws(' ',u.nombre,u.primer_apellido,u.segundo_apellido) as revisor, u.email as email_revisor from (
            select * from (
            select created_id as usuario, created_Date as fecha, modelo as modulo , 'validacion' as tipo, '' as motivo, provider_id
            from provider.datos_validados where created_id in (
            select user_id from usuarios where role = 'VALIDADOR PROVEEDORES'
            )
            ) a union(
            select  created_id as usuario, created_Date as fecha, modelo as modulo, 'rechazo' as tipo, 
            regexp_replace(motivo, E'[\\n\\r\\t]+', ' ', 'g' ) as motivo, provider_id
            from provider.status where created_id in (
            select user_id from usuarios where role = 'VALIDADOR PROVEEDORES'
            )
            )) b  
            inner join usuarios u on u.user_id = b.usuario 
            inner join provider p on p.provider_id = b.provider_id
            $where
            order by fecha desc

        ";


//        var_dump($sql).exit();

        if(isset(Yii::$app->request->queryParams['d'])){
            $data = Yii::$app->db->createCommand($sql)->queryAll();
            return GeneralController::downloadExcel($data,'ReporteRevisiones');
        }

        $data['data'] = new SqlDataProvider([
            'sql'=>$sql
        ]);

        return $this->render('revision',$data);
    }


    public function actionByprovider($id){
        $query = Asignacion::find()
            ->where(['id_proveedor'=>$id]);
        $data['data'] = new ActiveDataProvider([
            'query'=> $query,
            'pagination'=>false
        ]);
        //select modelo, provider_id, count(1) as total from provider.status where provider_id = 10555 group by modelo, provider_id order by total desc
        $rechazos = Status::find()                    /*--------------------------------top 3 módulos rechazados --------------  */
            ->select("modelo, provider_id, count(1) as total")
            ->where(['provider_id'=>$id])
            ->groupBy("modelo, provider_id ")
            ->orderBy( " total desc")
            ->limit(3);
        //$rechazoSql = " select modelo, provider_id, count(1) as total from provider.status where provider_id = $id group by modelo, provider_id order by total desc limit 3";
        $data['rechazo'] = new ActiveDataProvider([
            'query'=>$rechazos,
            'pagination'=>false
        ]);
        /*$data['rechazoSql'] = new SqlDataProvider([
             'sql'=>$rechazoSql,
             'pagination'=>false
         ]);*/

        return $this->renderAjax('byprovider',$data);
    }


    public function actionEnvios(){

        //filtros
        $where = "where 1 = 1 ";
        if(\Yii::$app->request->queryParams){
            $proveedor = isset(Yii::$app->request->queryParams['proveedor'])? Yii::$app->request->queryParams['proveedor'] : false;
            $where .= $proveedor && $proveedor != ''?
                " and proveedor ilike '%$proveedor%' ":'';

            $estatus = isset(Yii::$app->request->queryParams['estatus'])? Yii::$app->request->queryParams['estatus'] : false;
            $where .= $estatus && $estatus != '' && $estatus !='0' ?
                " and estatus = '$estatus' ":'';

            $rev = isset(Yii::$app->request->queryParams['revisor'])? Yii::$app->request->queryParams['revisor'] : false;
            $where .= $rev && $rev != ''?
                " and  ( revisor_nombre ilike '%$rev%' or revisor_email ilike '%$rev%' )":'';


            $fecha = isset(Yii::$app->request->queryParams['fecha'])? Yii::$app->request->queryParams['fecha'] : false;
            $where .= $fecha && $fecha != ''?
                " and date(created_at) = '$fecha'":'';


            $val = isset(Yii::$app->request->queryParams['validador'])? Yii::$app->request->queryParams['validador'] : false;
            $where .= $val && $val != ''?
                " and  ( validador_nombre ilike '%$val%' or validador_email ilike '%$val%' )":'';



            $inicio = isset(Yii::$app->request->queryParams['inicio'])? Yii::$app->request->queryParams['inicio'] : false;
            $fin = isset(Yii::$app->request->queryParams['fin'])? Yii::$app->request->queryParams['fin'] : false;
            $where .= $inicio && $fin?
                " and  date(created_at) between '$inicio' and  '$fin'  ":'';




        }


        $sql = "
            select * from (
            select  case when pa.activo is true and pa.modo = 'REV' then 'Pendiente REV' when pa.activo is true and pa.modo = 'VAL' then 'Pendiente VAL' when pa.activo is false and pa.modo = 'REV' then 'Revisado' else 'Validado' end as estatus,
            case when pr.tipo_persona = 'Persona moral' then pr.name_razon_social else concat_ws(' ',pr.pf_nombre,pr.pf_ap_paterno, pr.pf_ap_materno) end as proveedor,
            u1.email as revisor_email, 
            concat_ws(' ',u1.nombre,u1.primer_apellido,u1.segundo_apellido) as revisor_nombre,
            case when pa.modo = 'VAL' then u2.email else ' Pendiente ' end as validador_email,
            case when pa.modo = 'VAL' then concat_ws(' ',u2.nombre,u2.primer_apellido,u2.segundo_apellido) else ' Pendiente ' end as validador_nombre,
            pa.*, pr.permanently_disabled as status, pr.rfc
            from provider.asignacion pa inner join provider pr on pr.provider_id = pa.id_proveedor
            left join usuarios u1 on u1.user_id = pa.id_validador
            left join usuarios u2 on u2.user_id = pa.last_updated_by 
            order by id_proveedor, created_at desc
            ) x $where
        
        ";

        if(isset(Yii::$app->request->queryParams['d'])){
            $data = Yii::$app->db->createCommand($sql)->queryAll();
            return GeneralController::downloadExcel($data,'EnviosRevisión');
        }
/*
        $data = Yii::$app->db->createCommand($sql)->queryAll();
        $aux = null; /* 2023-01-11     */
       // exit(json_encode($data));
     /*   foreach ($data as $index => $revision){
            $aux = $aux==null ? $revision['created_at'] : $aux;
            if ( $revision['modo'] == 'VAL'){

                //exit($aux);
                $fechaInicial= new \DateTime($aux);
                $fechaFinal= new \DateTime(date($revision['last_updated_at']));
                $diff = $fechaInicial->diff($fechaFinal);
                $dias = $diff->days . ' days ';
                $aux = null;
                $data['reporte'][$revision['id_proveedor']]['dias']=$dias;
            }
        }  */
        $data['data'] = new SqlDataProvider([
           'sql'=>  $sql
        ]);

        //var_dump(json_encode($data['reporte'])).exit();
        return $this->render('envios',$data);
    }


    function actionBymodule($inicio=false,$fin=false,$params=[]){

        if($inicio && $fin){
            $where = " date(created_date) between :inicio and :fin ";
            $params = [
                ':inicio'=>$inicio,
                ':fin'=>$fin
            ];
        }

        else
            $where = " date(created_date) >'2022-07-01' ";

        $data['data'] = new SqlDataProvider([
            'pagination'=>['pageSize'=>10],
            'sql'=>"
                select a.modelo as modulo,a.rec as rechazos, b.val as validaciones, (rec+val) as total from (
                select modelo, count(1) as rec from provider.status 
                where $where and created_id in ( select user_id from usuarios where role = 'VALIDADOR PROVEEDORES' )
                group by modelo
                ) a inner join (
                select modelo, count(1) as val from provider.datos_validados 
                where $where  and created_id in ( select user_id from usuarios where role = 'VALIDADOR PROVEEDORES' )
                group by modelo
                ) b on a.modelo=b.modelo
            ",
            'params'=>$params,
            'sort'=>[
                'defaultOrder'=>['total'=>SORT_DESC],
                'attributes'=>[
                    'total',
                    'validaciones',
                    'rechazos'
                ]
            ]
        ]);

        return $this->render('bymodule',$data);
    }


    function actionByenvios($type='dia',$inicio=false,$fin=false,$params=[]){

        $where = ' 1 = 1 ';
        if($inicio && $fin){
            $where .= " and date(created_at) between :inicio and :fin ";
            $params = [
                ':inicio'=>$inicio,
                ':fin'=>$fin
            ];
        }

        if ($type=='dia'){

            $sqlFechas = " 
                select date(created_at) as fecha, count(1) as total ,array_length(array_Agg(distinct id_proveedor),1) as unicos
                from provider.asignacion where $where 
                group by date(created_at) order by date(created_at) desc
            ";
        }else{
            $sqlFechas = " 
                select date_trunc('month',date(created_at)) as fecha, count(1) as total,array_length(array_Agg(distinct id_proveedor),1) as unicos
                from provider.asignacion where $where
                group by fecha order by fecha desc
                ";
        }

        $data['fechas'] = new SqlDataProvider([
            'sql'=>$sqlFechas,
            'params'=>$params,
            'pagination'=>['pageSize'=>10]]
        );

        return $this->render('byenvios',$data);
    }


    function actionPromedio($inicio=false,$fin=false){
        $params = '';
        if($inicio && $fin){
            $where = " and date(pa.created_at) between :inicio and :fin ";
            $params = [
                ':inicio'=>$inicio,
                ':fin'=>$fin
            ];
        }
        else
            $where = "";

        $sql = " 
                    select  count(1) as solicitudes
                            , round((sum(minutes)/1440)::numeric,2) as dias_naturales,  
                            round((sum(minutos_sin_fin)/ 1440)::numeric,2) as dias_laborales,    
                            round((sum(minutos_sin_fin)/ 1440 /3)::numeric,2) as dias_jornada, 
                            round((sum(minutes)/1440/count(1))::numeric,2) as indicador_naturales, 
                            round((sum(minutos_sin_fin)/ 1440/count(1))::numeric,2) as indicador_laborales, 
                            round((sum(minutos_sin_fin)/ 1440 /3/count(1))::numeric,2) as indicador_jornada 
                            from (
                                select *, minutes - (dias_fin * 1440) as minutos_sin_fin   
                                from (
                                    select id_proveedor, created_at,last_updated_at, ((last_updated_at)-(created_at)), 
                                    extract(epoch from((last_updated_at)-(created_at)))/60 as minutes,      
                                    count_weekend_days(date(created_at),date(last_updated_at)) as dias_fin   
                                    from provider.asignacion as pa
                                    where last_updated_at is not null and concat(modo,last_updated_by) != 'REV11850' $where ) x 
                                 ) y      
                    ";


        $sqlChart = "
        
            select *, round(y.dias_laborales/y.envios,2) as total, substr(mes::text,0, 8) as mes_corto from (
            select date_trunc('month',created_at) as mes, count(1) as envios, sum(dias_fin) as dias_fin,sum(dias_fin*1440) as dias_fin_minutos, sum(round(minutes::numeric,2)) as minutos_totales,
            sum(round(minutes::numeric,2) - (dias_fin * 1440)) as minutos, round((sum(minutes - (dias_fin * 1440))/ 1440)::numeric,2) as dias_laborales 
            from (
                select id_proveedor, (created_at),(last_updated_at), ((last_updated_at)-(created_at)) , 
                extract(epoch from((last_updated_at)-(created_at)))/60 as minutes, 
                count_weekend_days(date(created_at),date(last_updated_at)) as dias_fin
                    from provider.asignacion pa where last_updated_at is not null and
                    concat(modo,last_updated_by) != 'REV11850'	$where		
                ) x  
                group by date_trunc('month',created_at)
            ) y
            
        ";


        $sqlContraloria = "
        
        
            select key, 
            sum(case when rechazox = 1 then 1 else 0 end) as rechazados,
            sum(case when rechazox = 0 then 1 else 0 end) as validados 
            from (
                select sq1.*, case when ReMod+ReUp+ReRev = 0 then 0 else 1 end as rechazox 
            
                from (
                    select substring(last_updated_at::text,0,8) as key,
                    concat(substring(created_at::text,0,20),'_',id_proveedor) as keyc,
                    concat(substring(last_updated_at::text,0,20),'_',id_proveedor) as key1, 
                    concat(substring(date_rev::text,0,20),'_',id_proveedor) as key2, modo,
                    case when modo = 'REV' then 1 else 0 end as ReMod ,
                    case when q1.keyr is not null then 1 else 0 end as ReUp,
                    case when q2.keyr is not null then 1 else 0 end as ReRev
                    from provider.asignacion pas 
                    left join (
                        select 
                        distinct concat(substring(created_date::text,0,20),'_',provider_id) as keyr 
                        from provider.status
                    ) q1
                    on q1.keyr = concat(substring(pas.last_updated_at::text,0,20),'_',pas.id_proveedor)
                    left join (
                        select 
                        distinct concat(substring(created_date::text,0,20),'_',provider_id) as keyr 
                        from provider.status
                    ) q2
                    on q2.keyr = concat(substring(pas.date_rev::text,0,20),'_',pas.id_proveedor)
            
                    where date(pas.last_updated_at) > '2024-01-01'  and pas.activo is false order by keyc
                ) sq1
            ) sq2 group by sq2.key order by sq2.key desc
        
        ";


        $data = [
            'data' => Yii::$app->db->createCommand($sql,$params)->queryOne(),
            'chart' => Yii::$app->db->createCommand($sqlChart,$params)->queryAll(),
            'dataProvider'=> new SqlDataProvider([
                'sql'=>$sqlContraloria,
                'pagination'=>false
            ])
        ];

        return $this->render('promedio', $data);
    }









////OP

    public function actionRevisiones($fi=null,$ff=null){

        $where = " and date(created_date) between :inicio and :fin ";
        $params = [
            ':inicio'=>$fi ? $fi : date('Y').'-01-01',
            ':fin'=>$ff ? $ff : date('Y').'-12-31',
        ];

        $sqlRevisores = "
            
            select 
                u.user_id, u.email, v.total as validaciones, r.total as rechazos , r.total + v.total as total,
                 initcap(concat_ws(' ',u.nombre, u.primer_apellido, u.segundo_apellido)) as nombre_completo 
            from (
                select 
                    count(1) as total, created_id as id
                from 
                    provider.datos_validados where created_id in (
                        select user_id from usuarios where role = 'VALIDADOR CONTRATISTAS'
                    ) $where
                group by created_id 
            ) v inner join (
                select 
                    count(1) as total, created_id as id
                from 
                    provider.status where created_id in (
                        select user_id from usuarios where role = 'VALIDADOR CONTRATISTAS'
                    ) $where
                group by created_id 
            ) r on r.id= v.id	 
            inner join 
                usuarios u on u.user_id = r.id					
									
        ";

        if(isset(Yii::$app->request->queryParams['d1'])){
            $data = Yii::$app->db->createCommand($sqlRevisores)
                ->bindValues($params)->queryAll();
            return GeneralController::downloadExcel($data,'ReporteValidacionesValidador');
        }

        $sqlModulos = "
        
            select 
                x.modelo as modulo, v as validaciones, r as rechazos, v+r as total 
            from (
                select 
                        count(1) as v, modelo
                from 
                        provider.datos_validados where created_id in (
                                select user_id from usuarios where role = 'VALIDADOR CONTRATISTAS'
                        ) $where
                group by modelo 
                ) x inner join (
                select 
                        count(1) as r, modelo
                from 
                        provider.status where created_id in (
                                select user_id from usuarios where role = 'VALIDADOR CONTRATISTAS'
                        ) $where
                group by modelo 
            ) y 
            on x.modelo = y.modelo
        
        ";

        if(isset(Yii::$app->request->queryParams['d2'])){
            $data = Yii::$app->db->createCommand($sqlModulos)
                ->bindValues($params)->queryAll();
            return GeneralController::downloadExcel($data,'ReporteValidacionesModulo');
        }

        $data = [
            'revisores'=>new SqlDataProvider([
                'sql'=>$sqlRevisores,
                'params'=>$params
            ]),
            'modulos'=>new SqlDataProvider([
                'sql'=>$sqlModulos,
                'params'=>$params
            ]),

        ];

        return $this->render('revisiones',$data);
    }


    public function actionByrevisor($id=null,$fi=null,$ff=null){

        $where = '';
        $params = [];
        if($fi && $ff){
            $where = " and date(created_date) between :inicio and :fin ";
            $params[':inicio'] = $fi;
            $params[':fin']=$ff;
        }
        if($id){
            $user_id = " :id ";
            $params[':id']= intval($id);
        }else{
            $user_id = " 
                select user_id 
                from usuarios 
                where role = 'VALIDADOR CONTRATISTAS' 
            ";
        }

        $sql = "
            
            select 
                p.rfc,p.permanently_disabled as estatus, CASE WHEN (p.tipo_persona = 'Persona moral') then p.name_razon_social else 
                concat_ws(' ',p.pf_nombre,p.pf_ap_paterno,p.pf_ap_materno) end as name_razon_social, b.*,u.user_id as revisor_id ,
                concat_ws(' ',u.nombre,u.primer_apellido,u.segundo_apellido) as revisor, u.email as email_revisor 
            from (
                select * 
                from (
                    select 
                        created_id as usuario, created_Date as fecha, 
                        modelo as modulo , 'validacion' as tipo, '' as motivo, provider_id
                    from 
                        provider.datos_validados 
                    where 
                        created_id in (
                            $user_id
                        ) $where
                ) a union(
                    select  
                        created_id as usuario, created_Date as fecha, 
                        modelo as modulo, 'rechazo' as tipo, 
                        regexp_replace(motivo, E'[\\n\\r\\t]+', ' ', 'g' ) as motivo, provider_id
                    from 
                        provider.status 
                    where 
                        created_id in (
                            $user_id
                        ) $where
                )
            ) b  
            inner join 
                usuarios u 
                on u.user_id = b.usuario 
            inner join 
                provider p 
                on p.provider_id = b.provider_id
            
            order by fecha desc

        ";

        if(isset(Yii::$app->request->queryParams['d'])){
            $data = Yii::$app->db->createCommand($sql,$params)->queryAll();
            return GeneralController::downloadExcel($data,'ReporteRevisiones');
        }

        $data['data'] = new SqlDataProvider([
            'sql'=>$sql,
            'params'=>$params,
            'pagination'=>[
                'pageSize'=>10
            ]
        ]);

        //var_dump($sql,$params).exit();

        return $this->renderAjax('byrevisor', $data);
    }


    public function actionContratistas($fi=null,$ff=null){

        //condicional por fecha de registro de proveedor
        $where = " and date(creation_date) between :inicio and :fin ";
        if(isset(Yii::$app->request->queryParams['c'])){
            //condicional por fecha de certificados
            $where = " and date(hc.created_at) between :inicio and :fin ";
        }
        $params = [
            ':inicio'=>$fi ? $fi : date('Y').'-01-01',
            ':fin'=>$ff ? $ff : date('Y').'-12-31',
        ];

        $subQ = "
               
            select 
                distinct provider_id 
            from
            (
                (
                    select 
                        provider_id 
                    from 
                        provider 
                    where 
                        tipo_provider = 'op'
                ) union(
                    select 
                        distinct provider_id 
                    from
                        provider.status 
                    where 
                        created_id in (
                            select user_id 
                            from usuarios 
                            where role = 'VALIDADOR CONTRATISTAS'
                        )
                ) union (
                    select 
                        distinct provider_id 
                    from
                        provider.datos_validados 
                    where 
                        created_id in (
                            select user_id 
                            from usuarios 
                            where role = 'VALIDADOR CONTRATISTAS'
                        )
                )
            ) x
        
        ";

        $sql = " 
            
            select 
                pr.provider_id,
                (case when tipo_persona = 'Persona física' then 
                concat_ws(' ', pf_nombre, pf_ap_materno, pf_ap_paterno) else 
                name_razon_social end) as nombre_razon_social, 
                rfc, creation_date ,tipo_persona  , 
                string_agg(concat('https://proveedores.nl.gob.mx/',url_certificado,'|',created_at),e' \n') as certificados, 
                max(url_certificado) as ultimo_certificado, 
                max(hc.created_at) as fecha_ultimo_certificado, count(url_certificado) as no_certificados
            from 
                provider pr 
            left join 
		        historico_certificados hc 
            on 
		        hc.provider_id = pr.provider_id and tipo = 'CERTIFICADO' and provider_type ='op'
            where 
                pr.provider_id in ( $subQ ) $where and enabled is true
            group by
                pr.provider_id,
                (case when tipo_persona = 'Persona física' then 
                concat_ws(' ', pf_nombre, pf_ap_materno, pf_ap_paterno) else 
                name_razon_social end), 
                rfc, creation_date ,tipo_persona  
            
        ";


        if(isset(Yii::$app->request->queryParams['d']) || isset(Yii::$app->request->queryParams['c'])){
            $data = Yii::$app->db->createCommand($sql)
                ->bindValues($params)->queryAll();
            return GeneralController::downloadExcel($data,'ReporteContratistasCertificaciones');
        }
        $totalCert = HistoricoCertificados::find()
            ->select('count(distinct provider_id) as total')
            ->where(['and',
                ['between','created_at',$params[':inicio'],$params[':fin']],
                ['tipo'=>'CERTIFICADO'],['provider_type'=>'op']
            ])
            ->asArray()->all()[0]['total'];

        $data = [
            'data'=>new SqlDataProvider([
                'sql'=>$sql,
                'params'=>$params,
                'pagination'=>[
                    'pageSize'=>10
                ]
            ]),
            'totalCert'=>$totalCert
        ];


        return $this->render('contratistas',$data);
    }


    public function actionVisitas($fi=null,$ff=null){

        $where = " and date(created_at) between :inicio and :fin ";
        $params = [
            ':inicio'=>$fi ? $fi : date('Y').'-01-01',
            ':fin'=>$ff ? $ff : date('Y').'-12-31',
        ];

        $subQ = "
               
            select 
                distinct provider_id 
            from
            (
                (
                    select 
                        provider_id 
                    from 
                        provider 
                    where 
                        tipo_provider = 'op'
                ) union(
                    select 
                        distinct provider_id 
                    from
                        provider.status 
                    where 
                        created_id in (
                            select user_id 
                            from usuarios 
                            where role = 'VALIDADOR CONTRATISTAS'
                        )
                ) union (
                    select 
                        distinct provider_id 
                    from
                        provider.datos_validados 
                    where 
                        created_id in (
                            select user_id 
                            from usuarios 
                            where role = 'VALIDADOR CONTRATISTAS'
                        )
                )
            ) x
        
        ";

        $sqlNote = " 
            
            select 
                count(1) as total, count(distinct visit_id) as unicas
            from 
                provider.note_visit  
            where 
                active is true  $where 
                
        ";

        $sqlVisit = " 
            
            select 
                count(1) as total, 
                sum(case when status is true then 1 else 0 end) as pendientes,
                sum( case when status is false then 1 else 0 end) as realizadas
            from 
                visit 
            where 
                provider_type = 'op'  $where 
                
        ";


        $data = [
            'note'=> Yii::$app->db->createCommand($sqlNote,$params)->queryAll()[0],
            'visit'=> Yii::$app->db->createCommand($sqlVisit,$params)->queryAll()[0],
        ];

        return $this->render('visitas',$data);
    }


    public function actionExpedientes($fi=null,$ff=null){


        $subQ = "
               
            select 
                string_agg(distinct provider_id::text,', ') as todos 
            from
            (
                (
                    select 
                        provider_id 
                    from 
                        provider 
                    where 
                        tipo_provider = 'op'
                ) union(
                    select 
                        distinct provider_id 
                    from
                        provider.status 
                    where 
                        created_id in (
                            select user_id 
                            from usuarios 
                            where role = 'VALIDADOR CONTRATISTAS'
                        )
                ) union (
                    select 
                        distinct provider_id 
                    from
                        provider.datos_validados 
                    where 
                        created_id in (
                            select user_id 
                            from usuarios 
                            where role = 'VALIDADOR CONTRATISTAS'
                        )
                )
            ) x
        
        ";

        $subQRes = Yii::$app->db->createCommand($subQ)->queryAll()[0]['todos'];

        $where = " and date(created_date) between :inicio and :fin and provider_id in ($subQRes) ";
        $params = [
            ':inicio'=>$fi ? $fi : date('Y').'-01-01',
            ':fin'=>$ff ? $ff : date('Y').'-12-31',
        ];

        $case = "
            count(1) as total, 
            sum( case when status_op = 'RECHAZADO' then 1 else 0 end) as rechazado,
            sum( case when status_op = 'EN EDICION' then 1 else 0 end) as en_edicion,
            sum( case when status_op = 'POR VALIDAR' then 1 else 0 end) as por_validar,
            sum( case when status_op = 'VALIDADO' then 1 else 0 end) as validado
        ";

        $sql = "
            
                select * from (
                select 'RelaciónAccionistas' as modulo, $case
                from provider.relacion_accionistas 
                where activo is true $where
                ) a union (
                select 'ModificaciónDeActa' as modulo, $case
                from provider.modificacion_acta 
                where activo is true and provider_id in ($subQRes)
                ) union (
                select 'RepresentanteLegal' as modulo, $case
                from provider.representante_legal 
                where activo is true and provider_id in ($subQRes)
                ) union (
                select 'EscrituraPública' as modulo, $case
                from provider.escritura_publica 
                where 1 = 1 $where
                ) union (
                select 'RFC' as modulo, $case
                from provider.rfc 
                where 1 = 1 $where
                ) union (
                select 'AltaHacienda' as modulo, $case
                from provider.alta_hacienda
                where 1 = 1 $where
                ) union (
                select 'ClientesContratos' as modulo, $case
                from provider.clientes_contratos 
                where activo is true $where
                ) union (
                select 'MétodoPago' as modulo, $case
                from provider.intervencion_bancaria
                where 1 = 1 $where
                ) union (
                select 'Ubicación' as modulo, $case
                from provider.ubicacion 
                where activo is true $where
                ) union (
                select 'FotografíaNegocio' as modulo, $case
                from provider.fotografia_negocio 
                where activo is true $where
                ) union (
                select 'ExperienciaBIMOrganigrama' as modulo, $case
                from provider.organigrama
                where 1 = 1 $where
                ) union (
                select 'PersonalTécnico' as modulo, $case
                from provider.personal_tecnico 
                where activo is true $where
                ) union (
                select 'Maquinaria' as modulo, $case
                from provider.maquinaria_equipos 
                where activo is true $where
                ) union (
                select 'Experiencia' as modulo, $case
                from provider.experiencia 
                where activo is true $where
                ) union (
                select 'CapacidadContratacion' as modulo, $case
                from provider.capacidad_contratacion 
                where 1 = 1 $where
                ) union (
                select 'DeclaracionISR' as modulo, $case
                from provider.declaracion_isr 
                where  1 = 1 $where
                ) union (
                select 'EstadoFinanciero' as modulo, $case
                from provider.estado_financiero 
                where 1 = 1  $where
                )


        
            ";

        $data = [
            'data'=>new SqlDataProvider([
                'sql'=>$sql,
                'params'=>$params,
                'pagination'=>[
                    'pageSize'=>20
                ]
            ]),
        ];


        return $this->render('expedientes',$data);


    }




}