<?php

namespace app\controllers;


use app\models\Token;
use app\controllers\bys\UbicacionController;
use app\models\ActaCircunstanciada;
use app\models\ActaConstitutiva;
use app\models\AltaHacienda;
use app\helpers\GeneralController;
use app\models\Asignacion;
use app\models\BalanceEstado;
use app\models\Calendar;
use app\models\CapacidadContratacion;
use app\models\CapacidadProduccion;
use app\models\CatActividades;
use app\models\CatAsentamientos;
use app\models\CatEntidades;
use app\models\CatLocalidades;
use app\models\CatMunicipios;
use app\models\CatVialidad;
use app\models\Certificacion;
use app\models\ClientesContratos;
use app\models\ComprobanteDomicilio;
use app\models\Curp;
use app\models\Datos;
use app\models\DatosValidados;
use app\models\DeclaracionIsr;
use app\models\DeclaracionIva;
use app\models\DireccionNl;
use app\models\EscrituraPublica;
use app\models\Curriculum;
use app\models\EstadoFinanciero;
use app\models\EstadoFinancieroTipo;
use app\models\Experiencia;
use app\models\FotografiaNegocio;
use app\models\Giro;
use app\models\GrupoProducto;
use app\models\Historico;
use app\models\HistoricoCartaProtesta;
use app\models\HistoricoCertificados;
use app\models\HistoricoReasignacion;
use app\models\IdOficial;
use app\models\IntervencionBancaria;
use app\models\LoginForm;
use app\models\MaquinariaEquipos;
use app\models\Model;
use app\models\ModelGiro;
use app\models\ModificacionActa;
use app\models\Module;
use app\models\ModulesComplete;
use app\models\NoLocalizados;
use app\models\Organigrama;
use app\models\Perfil;
use app\models\PersonalTecnico;
use app\models\Producto;
use app\models\ProviderConcurso;
use app\models\ProviderGiro;
use app\models\ProviderGiroRespaldo;
use app\models\ProviderQuery;
use app\models\RegistroConcursos;
use app\models\RegistroImss;
use app\models\RegistroPublicoPropiedad;
use app\models\RelacionAccionistas;
use app\models\RepresentanteLegal;
use app\models\Rfc;
use app\models\SociedadMercantil;
use app\models\Status;
use app\models\SubespecialidadCategory;
use app\models\Ubicacion;
use app\models\UltimaDeclaracion;
use app\models\Usuarios;
use app\models\Auditoria;
use app\models\Modulos;
use app\models\VisitSearch;
use Smalot\PdfParser\Parser;
use Yii;
use mPDF;
use app\models\Provider;
use app\models\ProviderSearch;
use yii\data\SqlDataProvider;
use yii\filters\AccessControl;
use yii\helpers\ArrayHelper;
use app\models\Pgpf;
use app\models\Pgpm;
use app\models\UsuariosAcuerdos;
use yii\db\Expression;
use yii\web\Cookie;
use yii\web\NotFoundHttpException;
use yii\filters\VerbFilter;
use yii\web\Response;
use yii\web\UploadedFile;
use yii\widgets\ActiveForm;


/**
 * ProviderController implements the CRUD actions for Provider model.
 */
class ProviderController extends GeneralController
{
    /**
     * @inheritdoc
     */
    public function behaviors()
    {
        return [
            'access' => [
                'class' => AccessControl::class,
                'only' => ['index', 'view', 'update', 'create', 'foto-perfil', 'foto-banner', 'update-perfil'],
                'rules' => [
                    [
                        'actions' => [],
                        'allow' => true,
                        'roles' => ['@'],
                    ],
                ],
            ],
            'verbs' => [
                'class' => VerbFilter::class,
                'actions' => [
                    'delete' => ['POST'],
                    'validar-modulos-bys' => ['POST']
                ],
            ],
        ];
    }


    public function actionActivacion(){ //
        $dataProvider = ArrayHelper::getColumn(Yii::$app->db->createCommand("select provider_id from provider where provider_id not in ( select distinct provider_id from module )  ")->queryAll(), 'provider_id');
        foreach ($dataProvider as $provider_id) {
            Module::setModules($provider_id);
        }
        $this->redirect("/");
    }

    /**
     * Lists all Provider models.
     * @return mixed
     */
    public function actionIndex()
    {

        if (Yii::$app->user->can(Usuarios::ROLE_PROVIDER)) {
            return $this->redirect(['/provider/dashboard']);
        }
        $searchModel = new ProviderSearch();
        $dataProvider = $searchModel->search(Yii::$app->request->queryParams, 'mainFilter');
        return $this->render('index', [
            'searchModel' => $searchModel,
            'dataProvider' => $dataProvider,
        ]);
    }

    function separar()
    {


        $name_file = 'archivos_tmp/txt1.csv';


        $csv = array_map('str_getcsv', file($name_file));

        $file = fopen("json3.csv", "a");
        $salida = [];
        $csv = $csv[0];
        foreach ($csv as $full_name) {

            $tokens = explode(' ', trim($full_name));
            $names = array();
            $special_tokens = array('da', 'de', 'del', 'la', 'las', 'los', 'mac', 'mc', 'van', 'von', 'y', 'i', 'san', 'santa');

            $prev = "";
            foreach ($tokens as $token) {
                $_token = strtolower($token);
                if (in_array($_token, $special_tokens)) {
                    $prev .= "$token ";
                } else {
                    $names[] = $prev . $token;
                    $prev = "";
                }
            }

            $num_nombres = count($names);
            $nombres = $apellidos = "";
            $apellidos2 = "";
            switch ($num_nombres) {
                case 0:
                    $nombres = '';
                    break;
                case 1:
                    $nombres = $names[0];
                    break;
                case 2:
                    $nombres = $names[0];
                    $apellidos = $names[1];
                    $apellidos2 = "";
                    break;
                case 3:
                    $nombres = $names[0];
                    $apellidos = $names[1];
                    $apellidos2 = $names[2];
                    break;
                case 4:
                    $nombres = $names[0] . ' ' . $names[1];
                    $apellidos = $names[2];
                    $apellidos2 = $names[3];
                    break;
                case 5:
                    $nombres = $names[0] . ' ' . $names[1];
                    $apellidos = $names[2] . ' ' . $names[3];
                    $apellidos2 = $names[4];
                    break;
                case 6:
                    $nombres = $names[0] . ' ' . $names[1];
                    $apellidos = $names[2] . ' ' . $names[3];
                    $apellidos2 = $names[4] . ' ' . $names[5];
                    break;
                case 7:
                    $nombres = $names[0] . ' ' . $names[1];
                    $apellidos = $names[2] . ' ' . $names[3];
                    $apellidos2 = $names[4] . ' ' . $names[5] . ' ' . $names[6];
                    break;
                case 8:
                    $nombres = $names[0] . ' ' . $names[1];
                    $apellidos = $names[2] . ' ' . $names[3];
                    $apellidos2 = $names[4] . ' ' . $names[5] . ' ' . $names[6] . ' ' . $names[7];
                    break;
                case 9:
                    $nombres = $names[0] . ' ' . $names[1];
                    $apellidos = $names[2] . ' ' . $names[3];
                    $apellidos2 = $names[4] . ' ' . $names[5] . ' ' . $names[6] . ' ' . $names[7] . ' ' . $names[8];
                    break;
                default:
                    $nombres = $names[0] . ' ' . $names[1];
                    unset($names[0]);
                    unset($names[1]);

                    $apellidos = implode(' ', $names);
                    break;
            }

            $salida [] = strtoupper(mb_convert_case($nombres, MB_CASE_TITLE, 'UTF-8')) . ',' . strtoupper(mb_convert_case($apellidos, MB_CASE_TITLE, 'UTF-8')) . ',' . strtoupper(mb_convert_case($apellidos2, MB_CASE_TITLE, 'UTF-8'));

        }

        fwrite($file, json_encode($salida, JSON_UNESCAPED_UNICODE) . PHP_EOL);
        fclose($file);
        echo "exito!!!!";
    }

    /**
     * @param $id
     * @return string
     */
    public function actionViewprovider($id)
    {

        $model = $this->findModelProv($id);
        $rechazo = Status::find()->getStatus($id, 'perfil', $model->tipo_provider, 'TERMINADO PRO');
        return $this->render('viewprovider', [
            'model' => $model,
            'model_comprobante_domicilio' => $this->findModelComprobanteDomicilio($id),
            'ubicacion' => $this->findModelUbicacionProv($id),
            'rechazo' => $rechazo
        ]);
    }

    public function actionViewprovideragenda($id)
    {
        if (Yii::$app->request->isAjax) {

            return $this->renderAjax('viewprovideragenda', [
                'model' => $this->findModelProv($id),
            ]);

        }

        return $this->goHome();

    }

    /**
     * Deletes an existing ProjectProcedure model.
     * If deletion is successful, the browser will be redirected to the 'index' page.
     * @param integer $id
     * @return mixed
     */
    public function actionUpdateprovider($id)
    {
        $customer = Usuarios::findOne($id);
        $customer->address = 'si';
        $customer->update();
        return $this->redirect(['index']);
    }


    public function actionPdfselect($id)
    {
        $this->layout = 'home';
        return $this->render('pdf/pdf_select', [
            'model' => $this->findModel($id),
        ]);
    }


    public function actionView($id = null)
    {

        $this->layout = 'nomain';
        return $this->render('view', [
            'model' => $this->findModel($id),
        ]);

    }

    public function actionIndexDetalleDashboard(){


        $searchModel = new ProviderSearch();
        $dashboard = $searchModel->search(Yii::$app->request->queryParams, 'dashboardFilterCall');


        return $this->render('index-detalle-dashboard', [
            'searchModel' => $searchModel,
            'dashboard' => $dashboard
        ]);

    }

    public function actionDashboardCall($provider_id= null,$type = null)
    {
        if (Yii::$app->user->can(Usuarios::ROLE_CALL_CENTER)) {


            if ($type == 'op') {
                $datos = $this->getDataOP($provider_id);
            } else {
                $datos = $this->getDataBys($provider_id);
            }

            return $this->renderAjax('dashboard-call', [
                'datos' => $datos,
                'id_pro' => $provider_id,
                'namePro' => $this->getNameProvider($provider_id),
                'type' => $type


            ]);
        }

        return $this->goHome();
    }

    public function actionDashboard()
    {
        if (Yii::$app->user->can(Usuarios::ROLE_PROVIDER)) {
            $this->layout = 'nomain';
            $provider = Provider::find()
                ->where(['provider_id' => Yii::$app->user->identity->providerid])
                ->one();
            $tipo = $provider->tipo_provider;
            if ($tipo == 'op') {
                $datos = $this->getDataOP();
            } else {
                /* $datos = $this->getDataBys(); */
                $datos = $this->getDashboardDataBys();
            }

            //Si no encuentra un acuerdo hace un bypass
            //$avisoRegistro = UsuariosAcuerdos::obtenerAcuerdoActivo($provider->user_id, 'provider/dashboard', UsuariosAcuerdos::TYPE_AVISO_ACTUALIZACION_REGISTRO);

            //$provider_id = Yii::$app->user->identity->providerid;

            $cert = HistoricoCertificados::find()
                ->where(['and',['provider_id' => $provider->provider_id],['tipo' => 'CERTIFICADO'],['provider_type' => $tipo]])
                ->orderBy(['historico_certificados_id' => SORT_DESC])->one();


            $userCreationDate = Usuarios::find()
            ->select('creation_date')
            ->where(['user_id' => Yii::$app->user->id])
            ->scalar();

            $currentDate = new \DateTime();
            $creationDate = new \DateTime($userCreationDate);
            $interval = $currentDate->diff($creationDate);
            $daysSinceCreation = $interval->days;



            return $this->render('dashboard', [
                'datos' => $datos,
                'cert' => $cert,
                'provider'=>$provider,
                'modelPerfil'=>$modelPerfil = Perfil::find()->where(['provider_id'=>$provider->provider_id])->one(),
                //'avisoRegistro' => $avisoRegistro,
                'concursos' => RegistroConcursos::find()->where(['activo'=>true])
                    ->orderBy(['created_at'=>SORT_DESC])->limit(5)
                    ->asArray()->all(),
                'misconcursos'=>ProviderConcurso::find()->select('*')
                    ->innerJoin('registro_concursos', 'registro_concursos.concurso_id = provider_concurso.concurso_id ')
                    ->where(['provider_concurso.activo'=>true,'provider_concurso.provider_id'=>$provider->provider_id])
                    ->orderBy(['provider_concurso.created_at'=>SORT_DESC])
                    ->limit(5)->asArray()->all(),
                'daysSinceCreation' => $daysSinceCreation,
            ]);
        }

        return $this->goHome();

    }

    public function actionUpdateEmail()
    {
        \Yii::$app->response->format = \yii\web\Response::FORMAT_JSON;

        // Obtén los datos enviados por POST
        $newEmail = Yii::$app->request->post('email');
        $userId = Yii::$app->user->id;

        // Validar si el correo no está vacío y es válido
        if (filter_var($newEmail, FILTER_VALIDATE_EMAIL)) {
            // Buscar el registro en la tabla provider
            $provider = Provider::findOne(['user_id' => $userId]);
            $user = Usuarios::findOne($userId);

            if ($provider && $user) {
                // Actualizar el correo
                $provider->email = $newEmail;
                $user->email = $newEmail;

                if ($provider->save() && $user->save()) {
                    //funcionalidad de enviar el nuevo token al nuevo correo
                    $token = md5(Yii::$app->security->generateRandomString().'_'.time());
                    $modelToken = Token::find()->where(['user_id'=>$user->user_id])->one();
                    if(!$modelToken){
                        $modelToken = new Token();
                    }
                    $modelToken->code = $token;
                    $modelToken->type = 0;
                    $modelToken->save();
                    GeneralController::sendEmail('/provider/correos/confirmacion',null, $user->email,'Verificar cuenta',['token' => $token ,'id' => $user->user_id,'tipo_provider' => 'bys']);
                       //    return $this->redirect(['/course-provider/course']);

                    return ['success' => true];
                } else {
                    return ['success' => false, 'error' => 'No se pudo guardar el correo en la base de datos, es probable que el email solicitado ya esté en uso. Favor de validar.'];
                }
            } else {
                return ['success' => false, 'error' => 'Proveedor no encontrado.'];
            }
        } else {
            return ['success' => false, 'error' => 'Correo electrónico no válido.'];
        }
    }



    public function actionReenviarCorreo()
    {
        Yii::$app->response->format = \yii\web\Response::FORMAT_JSON;

        // Obtener el usuario actual
        $user = Yii::$app->user->identity;

        if ($user) {
            // Generar el token
            $token = md5(Yii::$app->security->generateRandomString().'_'.time());
            $modelToken = Token::find()->where(['user_id' => $user->id])->one();

            if (!$modelToken) {
                $modelToken = new Token();
            }

            // Asignar los valores al token
            $modelToken->code = $token;
            //$modelToken->type = 1;
            $modelToken->user_id = $user->id; // Asegúrate de asignar el user_id si es un nuevo token

            // Guardar el token en la base de datos
            if ($modelToken->save()) {
                // Enviar el correo de confirmación
                GeneralController::sendEmail('/provider/correos/confirmacion', null, $user->email, 'Verificar cuenta', [
                    'token' => $token,
                    'id' => $user->id,
                    'tipo_provider' => 'bys'
                ]);

                return ['success' => true];
            } else {
                return ['success' => false, 'error' => 'Error al guardar el token.'];
            }
        } else {
            return ['success' => false, 'error' => 'Usuario no encontrado.'];
        }
    }



    public function actionDashboardVal($id = null, $perfil = null)
    {
        if (Yii::$app->user->can(Usuarios::ROLE_VALIDADOR_PROVEEDORES)
            || Yii::$app->user->can(Usuarios::ROLE_ADMIN_PROVIDER)
            || Yii::$app->user->can(Usuarios::ROLE_CALL_CENTER)
            || Yii::$app->user->can(Usuarios::ROLE_ADMIN_CONTRATISTAS)) {

            $provider = Provider::findOne($id);

            if(is_null($perfil)){

                if(Yii::$app->user->can(Usuarios::ROLE_ADMIN_CONTRATISTAS)){
                    $tipo = 'op';
                }
                if(Yii::$app->user->can(Usuarios::ROLE_VALIDADOR_PROVEEDORES) || Yii::$app->user->can(Usuarios::ROLE_ADMIN_PROVIDER)){
                    $tipo = 'bys';
                }

            }else{
                $tipo = $perfil;
            }

            /* if (Yii::$app->user->can(Usuarios::ROLE_ADMIN_CONTRATISTAS) || $provider->tipo_provider == 'op') {
                $tipo = 'op';
            } else {
                $tipo = 'bys';
            } */


            if ($tipo == 'op') {
                $datos = $this->getDataOP($id);
            } else {
                $datos = $this->getDashboardDataBys($id);
            }

            return $this->renderAjax('dashboard-val', [
                'datos' => $datos,
                'id_pro' => $id,
                'namePro' => $provider->getNameOrRazonSocial(), //$this->getNameProvider($id),
                'tipo' => $tipo
            ]);
        }

        return $this->goHome();

    }

    private function getDashboardDataOp($id = null){
        $data_arriba = [];
        $data_abajo = [];

        $condicion = $id == null ? ['user_id' => Yii::$app->user->getId()] : ['provider_id' => $id];
        $provider = Provider::find()->where($condicion)->one();
        $provider_id = $provider->provider_id;
        $tipo_persona = $provider->tipo_persona;

        $status_global = 'status_op';

        //PERFIL
        /* $modelDomicilio = ComprobanteDomicilio::find()->where(['provider_id' => $provider_id])->one();
        $p_perfilPre = $this->porcentajeModelo($modelDomicilio);
        $p_cl = $this->porcentajeTablaClientes('ClientesContratos', 'status_op', $id);
        $p_clientes_contratos = (intval($p_cl) + $p_perfilPre) / 2;
        
        print_r("PORCENTAJE PERFIL $p_clientes_contratos"); */

        //ECONOMICA
        /* $modelGiro = Giro::find()->where(['provider_id' => $provider_id])->one();
        $modelRfc = Rfc::find()->where(['provider_id' => $provider_id])->one();

        $pRfcProv = !empty($modelRfc->url_rfc) ? 100 : 0;
        $p_hacienda = ($modelGiro) ? (intval($this->porcentajeModelo($modelGiro)) + $pRfcProv) / 2 : 0;

        print_r("PORCENTAJE ECONOMICA $p_hacienda"); */

        //LEGALES
        /*  if ($provider->tipo_persona == 'Persona moral') {
            $modelacta = ActaConstitutiva::find()->where(['provider_id' => $provider_id])->one();
            $modelImss = RegistroImss::find()->where(['provider_id' => $provider_id])->one();
            $modelEscritura = EscrituraPublica::find()->where(['provider_id' => $provider_id])->one();
            $modelLegal = RegistroPublicoPropiedad::find()->where(['provider_id' => $provider_id])->one();
            $modelLegal = $modelLegal ? $modelLegal : new RegistroPublicoPropiedad();
            $modelImss = $modelImss ? $modelImss : new RegistroImss();
            $modelEscritura =  $modelEscritura ?  $modelEscritura : new EscrituraPublica();

            
            $p_modA = $this->porcentajeTabla('ModificacionActa', 'status_op', $id);
            $p_modActa = $p_modA === null ? 100 : $p_modA;

            $p_relAcc = $this->porcentajeTabla('RelacionAccionistas', 'status_op', $id);

            $porRepLegal = $this->porcentajeTabla('RepresentanteLegal', 'status_op', $id);

            $p_legales = 0;

            print_r("PORCENTAJE ACTA ".$this->porcentajeModelo($modelacta));
            print_r("PORCENTAJE LEGAL ".$this->porcentajeModelo($modelLegal));
            print_r("PORCENTAJE IMSS ".$this->porcentajeModelo($modelImss));
            print_r("PORCENTAJE ESCRITURA ".$this->porcentajeModelo($modelEscritura));

            if ($modelLegal && $modelacta && $modelImss && $modelEscritura) {
                //$pLegales1 = intval(($this->porcentajeModelo($modelLegal) + $this->porcentajeModelo($modelacta) + $this->porcentajeModelo($modelImss)
                //        + $this->porcentajeModelo($modelEscritura)) / 4);
                //$p_legales = intval($pLegales1 > 15 ? ($pLegales1 + intval($p_modActa) + intval($p_relAcc) + intval($porRepLegal)) / 4 : $pLegales1);
            }

        } else {
            $modelCurp = Curp::find()->where(['provider_id' => $provider_id])->one();
            $modelOficial = IdOficial::find()->where(['provider_id' => $provider_id])->one();
            $p_legales = ($modelCurp && $modelOficial) ? intval(($this->porcentajeModelo($modelCurp) + $this->porcentajeModelo($modelOficial)) / 2) : 10;

        }

        print_r("PORCENTAJE LEGALES $p_legales"); */

        //BANCOS
        /* $modelPago = IntervencionBancaria::find()->where(['provider_id' => $provider_id])->one();
        $pago = ($modelPago) ? ($this->porcentajeModelo($modelPago)) : 10;

        print_r("PORCENTAJE PAGO $pago"); */

        //UBICACION
        $p_ub = $this->porcentajeTabla('Ubicacion', 'status_op', $id);

        $entidad = Ubicacion::find()->select('state_fiscal')->where(['and', ['provider_id' => $provider_id], ['type_address_prov' => 'DOMICILIO FISCAL']])->orderBy(['ubicacion_id' => SORT_DESC])->one()['state_fiscal'];
        $porcentrajeDirNL = 100;
        $dirNl = false;
        if (isset($entidad) && $entidad != 19 || $entidad == null) {
            $dirNl = true;
            $modelDirNL = Ubicacion::find()->where(['and', ['provider_id' => $provider_id], ['type_address_prov' => 'NL']])->orderBy(['ubicacion_id' => SORT_DESC])->one();
            $porcentrajeDirNL = 0;
            if ($modelDirNL != null) {
                $porcentrajeDirNL = $this->porcentajeModelo($modelDirNL);
            }


        }


        $p_fo = $this->porcentajeTabla('FotografiaNegocio', 'status_op', $id);


        $p_ub = $p_ub === null && $p_fo >= 100 ? 100 : $p_ub;
        $p_ubicacion = $p_ub == null && !$dirNl ? 0 : ($dirNl ? ($porcentrajeDirNL + $p_ub) / 2 : $p_ub);

        print_r("PORCENTAJE UBICACION $p_ubicacion");

        //TECNICOS

        /* $personal = $this->porcentajeTabla('PersonalTecnico', 'status_op', $id);
        
        $modelOrg = Organigrama::find()->select('status_op')->where(['provider_id' => $provider_id])->one()['status_op'];
        $arrOrg = ['VALIDADO', 'POR VALIDAR'];
        $organigrama = in_array($modelOrg, $arrOrg) ? 100 : 10;

        $organigramaTer = in_array($modelOrg, ['VALIDADO']) ? 100 : 10;
        $tecnicos = ($personal + $organigrama) / 2;

        print_r("PORCENTAJE TECNICOS $tecnicos"); */


        //Financieros

        /* $modelEF = EstadoFinanciero::find()->where(['provider_id' => $provider_id])->one();

        if ($modelEF == null) {
            $modelEF = new EstadoFinanciero();
            $modelEF->provider_id = $provider_id;
            $modelEF->save();
        }

        $modelCC = CapacidadContratacion::find()->where(['provider_id' => $provider_id])->one();
        $modelDA = DeclaracionIsr::find()->where(['provider_id' => $provider_id])->one();

        $modelCC = $modelCC ? $modelCC : new CapacidadContratacion();
        $modelDA = $modelDA ? $modelDA : new DeclaracionIsr();
        $financieros = ($modelEF && $modelCC && $modelDA) ? (($this->porcentajeModelo($modelEF) + $this->porcentajeModelo($modelDA) + $this->porcentajeModelo($modelCC)) / 3) : 10;

        print_r("PORCENTAJE financieros $financieros"); */


    }



    private function getDataOP($id = null)
    {
        if ($id != null) {
            if (($provider = \app\models\Provider::find()->where(['provider_id' => $id])->one()) !== null) {
                $provider_id = $provider->provider_id;
            } else {
                return $this->goHome();
            }
        } else {
            $provider = \app\models\Provider::find()->where(['user_id' => Yii::$app->user->getId()])->one();
            $provider_id = $provider->provider_id;
        }

        /*legales*/

        $modelRfc = Rfc::find()->where(['provider_id' => $provider_id])->one();

        $status_global = 'status_op';

        /*Perfil*/

        $statusPer = Perfil::find()->where(['provider_id' => $provider_id])->one();
        $modelDomicilio = ComprobanteDomicilio::find()->where(['provider_id' => $provider_id])->one();
        $p_perfilPre = self::porcentajeModelo($modelDomicilio);
        $p_cl = self::porcentajeTablaClientes('ClientesContratos', 'status_op', $id);
        $p_clientes_contratos = (intval($p_cl) + $p_perfilPre) / 2;
        $url_perfil = '/op/perfil/index';
        $img_perfil = 'curriculum';

        /*actividad economica*/

        $terminadoE = 0;

        $porRepLegal = 100;
        $porRepLegalCarta = 100;
        $modelGiro = Giro::find()->where(['and',['provider_id' => $provider_id, 'active' => true]])->one();

        $status_datos_hacienda = AltaHacienda::find()->select($status_global)->where(['provider_id' => $provider_id])->one()[$status_global];

        /*if ($provider->tipo_persona == 'Persona moral') {
            $porRepLegal = $this->porcentajeTabla('RepresentanteLegal', 'status_op',$id);
            $porRepLegalCarta = $this->porcentajeTablaCarta('RepresentanteLegal', 'status_op',$id);

            $p_hacienda = ($modelGiro)?
                intval(($this->porcentajeModelo($modelGiro) + $porRepLegal) / 2) :($porRepLegal>0?$porRepLegal/2:0);
        } else {*/
        $pRfcProv = !empty($modelRfc->url_rfc) ? 100 : 0;
        $p_hacienda = ($modelGiro) ?
            (intval(self::porcentajeModelo($modelGiro)) + $pRfcProv) / 2 : 0;
        //}


        if ($status_datos_hacienda == Status::STATUS_VALIDADO/* && $porRepLegalCarta == 100*/) {
            $terminadoE = 1;
        }

        $url_hacienda = "/bys/economica/view";

        if ($provider->tipo_persona == 'Persona moral') {
            $modelacta = ActaConstitutiva::find()->where(['provider_id' => $provider_id])->one();
            $modelImss = RegistroImss::find()->where(['provider_id' => $provider_id])->one();
            $modelEscritura = EscrituraPublica::find()->where(['provider_id' => $provider_id])->one();
            $modelLegal = RegistroPublicoPropiedad::find()->where(['provider_id' => $provider_id])->one();

            $p_modA = self::porcentajeTabla('ModificacionActa', 'status_op', $id);
            $p_modActa = $p_modA === null ? 100 : $p_modA;

            $p_relAcc = self::porcentajeTabla('RelacionAccionistas', 'status_op', $id);

            $porRepLegal = self::porcentajeTabla('RepresentanteLegal', 'status_op', $id);

            $p_legales = 0;

            if ($modelLegal && $modelacta && $modelImss && $modelEscritura) {
                $pLegales1 = intval((self::porcentajeModelo($modelLegal) + self::porcentajeModelo($modelacta) + self::porcentajeModelo($modelImss)
                        + self::porcentajeModelo($modelEscritura, 'ESCRITURA')) / 4);
                $p_legales = intval($pLegales1 > 15 ? ($pLegales1 + intval($p_modActa) + intval($p_relAcc) + intval($porRepLegal)) / 4 : $pLegales1);
            }

            $porMActa = self::porcentajeTablaCarta('ModificacionActa', 'status_op', $id);
            $porRelAc = self::porcentajeTablaCarta('RelacionAccionistas', 'status_op', $id);
            $porRepLegalCarta = self::porcentajeTablaCarta('RepresentanteLegal', 'status_op', $id);
        } else {
            $modelCurp = Curp::find()->where(['provider_id' => $provider_id])->one();
            $modelOficial = IdOficial::find()->where(['provider_id' => $provider_id])->one();
            $p_legales = ($modelCurp && $modelOficial) ?
                intval((self::porcentajeModelo($modelCurp)
                        + self::porcentajeModelo($modelOficial)) / 2) :
                10;

            $porMActa = 100;
            $porRelAc = 100;
            $porRepLegalCarta = 100;
        }

        $terminadoLegales = 0;

        if ($modelRfc->$status_global == Status::STATUS_VALIDADO && ($porMActa === null || $porMActa == 100) && $porRelAc == 100 && $porRepLegalCarta >= 100) {
            $terminadoLegales = 1;
        }
        $modelEF = EstadoFinanciero::find()->where(['provider_id' => $provider_id])->one();

        if ($modelEF == null) {
            $modelEF = new EstadoFinanciero();
            $modelEF->provider_id = $provider_id;
            $modelEF->save();
        }

        $modelCC = CapacidadContratacion::find()->where(['provider_id' => $provider_id])->one();
        $modelDA = DeclaracionIsr::find()->where(['provider_id' => $provider_id])->one();
        $financieros = 10;
        if($modelCC && $modelEF && $modelDA){
            $financieros =  self::porcentajeModelo($modelEF) + self::porcentajeModelo($modelDA) + self::porcentajeModelo($modelCC) / 3;

        }else if($modelCC && $modelEF){
            $financieros =  self::porcentajeModelo($modelEF) +  self::porcentajeModelo($modelCC) / 2;

        }else if($modelCC && $modelDA){
            $financieros =   self::porcentajeModelo($modelDA) + self::porcentajeModelo($modelCC) / 2;

        }

        if($financieros>100){
            $financieros = 100;
        }

        $modelPerfil = Curriculum::find()->where(['provider_id' => $provider_id])->one();
        $perfil = ($modelPerfil) ? (self::porcentajeModelo($modelPerfil)) : 80;


        $maquinaria = self::porcentajeTabla('MaquinariaEquipos', 'status_op', $id);
        $maquinaria_Enabled = self::porcentajeTablaCarta('MaquinariaEquipos', 'status_op', $id);

        $experiencia = self::porcentajeTabla('Experiencia', 'status_op', $id);
        $experiencia_Enabled = self::porcentajeTablaCarta('Experiencia', 'status_op', $id);

        $personal = self::porcentajeTabla('PersonalTecnico', 'status_op', $id);
        $personal_Enabled = self::porcentajeTablaCarta('PersonalTecnico', 'status_op', $id);


        $modelOrg = Organigrama::find()->select('status_op')->where(['provider_id' => $provider_id])->one()['status_op'];
        $arrOrg = ['VALIDADO', 'POR VALIDAR'];
        $organigrama = in_array($modelOrg, $arrOrg) ? 100 : 10;

        $organigramaTer = in_array($modelOrg, ['VALIDADO']) ? 100 : 10;
        $tecnicos = ($personal + $organigrama) / 2;
        $tecnicoEnabledT = ($personal_Enabled + $organigramaTer) / 2;


        $modelPago = IntervencionBancaria::find()->where(['provider_id' => $provider_id])->one();
        $pago = ($modelPago) ? (self::porcentajeModelo($modelPago)) : 10;


        /*ubicacion*/

        $p_ub = self::porcentajeTabla('Ubicacion', 'status_op', $id);
        $p_ubEnabled = self::porcentajeTablaCarta('Ubicacion', 'status_op', $id);

        $entidad = Ubicacion::find()->select('state_fiscal')->where(['and', ['provider_id' => $provider_id], ['type_address_prov' => 'DOMICILIO FISCAL']])->orderBy(['ubicacion_id' => SORT_DESC])->one()['state_fiscal'];
        //$entidad = CatMunicipios::find()->select('entidad_id')->where(['municipio_id' => $city])->one()['entidad_id'];
        $porcentrajeDirNL = 100;
        $statusDirNl = 'VALIDADO';
        $dirNl = false;
        /*
        if (isset($entidad) && $entidad != 19 || $entidad == null) {
            $dirNl = true;
            $modelDirNL = Ubicacion::find()->where(['and', ['provider_id' => $provider_id], ['type_address_prov' => 'NL']])->orderBy(['ubicacion_id' => SORT_DESC])->one();
            $statusDirNl = 'EN EDICION';
            $porcentrajeDirNL = 0;
            if ($modelDirNL != null) {
                $statusDirNl = $modelDirNL->status_op;

                $porcentrajeDirNL = self::porcentajeModelo($modelDirNL);
            }


        }
        */

        $p_fo = self::porcentajeTabla('FotografiaNegocio', 'status_op', $id);
        $p_foEnabled = self::porcentajeTablaCarta('FotografiaNegocio', 'status_op', $id);


        $p_ub = $p_ub === null && $p_fo >= 100 ? 100 : $p_ub;
        $p_ubicacion = $p_ub == null && !$dirNl ? 0 : ($dirNl ? ($porcentrajeDirNL + $p_ub) / 2 : $p_ub);
        $ubicacionT = (((intval($p_ubEnabled) >= 100 || $p_ubEnabled === null) && $p_foEnabled >= 100) && $statusDirNl == 'VALIDADO') ? 1 : 0;
        $url_ubicacion = '/op/ubicacion/index';
        $img_ubicacion = 'datos_propiedad';

        /*Fotografia*/


        $p_fotografia = $p_fo == null ? 0 : $p_fo;
        $fotografiaT = (intval($p_foEnabled) == 100) ? 1 : 0;
        $url_fotografia = '/op/fotografia/index';
        $img_fotografia = 'fotografia';


        $cartaUrl = '#';
        $cartaPor = 0;
        $cartaTer = 0;
        $visible = 0;
        $visible_carta = 0;
        $sombra_carta = '';

        $p_clCarta = self::porcentajeTablaClientesCarta('ClientesContratos', 'status_op', $id);
        $p_clientes_contratosCarta = $p_clCarta == null ? 0 : $p_clCarta;
        $perfilT = (intval($p_clientes_contratosCarta) >= 100) && $statusPer->$status_global == Status::STATUS_VALIDADO ? 1 : 0;
        if ($perfilT && $p_hacienda >= 100 && $p_legales >= 100 && $p_clientes_contratosCarta >= 100 && $fotografiaT ==1 &&
            $fotografiaT==1 && intval($tecnicoEnabledT) == 100 && intval($maquinaria_Enabled) == 100 && intval($experiencia_Enabled) == 100
            && $modelEF->status_op == Status::STATUS_VALIDADO) {

            $modelProvider = Provider::find()->where(['provider_id' => $provider_id])->one();
            switch ($modelProvider->status_carta_op) {
                case Status::CARTA_PENDIENTE :
                    $cartaUrl = '/carta/generacartaprotesta';
                    $cartaPor = 50;
                    $visible_carta = 1;
                    $visible = 0;
                    $sombra_carta = 'sombra_aqua';
                    break;
                case Status::CARTA_GENERADA:
                    $cartaUrl = '/carta/generacartaprotesta';
                    $cartaPor = 75;
                    $visible_carta = 1;
                    $visible = 0;
                    $sombra_carta = 'sombra_aqua';
                    break;
                case Status::CARTA_FIRMADA :
                    $cartaUrl = \yii\helpers\Url::to('@web/' .GeneralController::limpiarUrl($modelProvider->url_carta_op), true);
                    $cartaPor = 100;
                    $cartaTer = 1;
                    $visible_carta = 1;
                    $visible = 1;
                    $sombra_carta = 'sombra_aqua';
                    break;

            }
        }

        $data = (Object)[
            'arriba' => [
                'Perfil' => [
                    'porcentaje' => $p_clientes_contratos,
                    'icono' => 'curriculum',
                    'terminado' => $perfilT,
                    'visible' => 1,
                    'url' => $url_perfil,
                    'title' => ['title' => 'Mi perfil'],
                    'sombra' => 'sombra_verde',
                    'rechazo' => 0,
                    'pendiente' => 0,
                    'enviado' => 0,
                ],
                'Actividad Económica' => [
                    'porcentaje' => $p_hacienda,
                    'icono' => 'datos_hacienda',
                    'terminado' => $terminadoE,
                    'visible' => 1,
                    'title' => ['title' => 'Actividad Económica'],
                    'url' => '/op/economica/view',
                    'sombra' => 'sombra_roja',
                    'rechazo' => 0,
                    'pendiente' => 0,
                    'enviado' => 0,
                ],
                'Legales' => [
                    'porcentaje' => $p_legales,
                    'icono' => 'legales',
                    'terminado' => $terminadoLegales,
                    'visible' => 1,
                    'title' => ['title' => 'Legales'],
                    'url' => '/op/legales/view',
                    'sombra' => 'sombra_azul',
                    'rechazo' => 0,
                    'pendiente' => 0,
                    'enviado' => 0,
                ],
                'Metodo de pago' => [
                    'porcentaje' => intval($pago),
                    'icono' => 'metodo_pago',
                    'terminado' => ($modelPago->status_op == Status::STATUS_VALIDADO) ? 1 : 0,
                    'visible' => 1,
                    'url' => '/banco/view',
                    'title' => ['title' => 'Metodo de pago'],
                    'sombra' => 'sombra_amarilla',
                    'rechazo' => 0,
                    'pendiente' => 0,
                    'enviado' => 0,
                ],
                'Ubicación' => [
                    'porcentaje' => $p_ubicacion,
                    'icono' => 'datos_propiedad',
                    'terminado' => $ubicacionT,
                    'visible' => 1,
                    'url' => $url_ubicacion,
                    'title' => ['title' => 'Ubicación'],
                    'sombra' => 'sombra_naranja',
                    'rechazo' => 0,
                    'pendiente' => 0,
                    'enviado' => 0,
                ],

            ],
            'abajo' => [

                'Fotografía' => [
                    'porcentaje' => $p_fotografia,
                    'icono' => 'fotografia',
                    'terminado' => $fotografiaT,
                    'visible' => 1,
                    'url' => $url_fotografia,
                    'title' => ['title' => 'Fotografía'],
                    'sombra' => 'sombra_arena',
                    'rechazo' => 0,
                    'pendiente' => 0,
                    'enviado' => 0,
                ],

                'Datos Técnicos' => [
                    'porcentaje' =>  intval($tecnicos),
                    'icono' => 'personal',
                    'terminado' => intval($tecnicoEnabledT) == 100? 1 : 0,
                    'visible' => 1,
                    'url' => '/op/tecnicos/index',
                    'title' => ['title' => 'Datos Técnicos'],
                    'sombra' => 'sombra_verde_claro',
                    'rechazo' => 0,
                    'pendiente' => 0,
                    'enviado' => 0,
                ],
                'Maquinaria' => [
                    'porcentaje' => intval($maquinaria),
                    'icono' => 'maquinaria',
                    'terminado' =>intval($maquinaria_Enabled) == 100 ? 1 : 0,
                    'visible' =>1,
                    'url' => '/op/maquinaria/index',
                    'title' => ['title' => 'Maquinaria'],
                    'sombra' => 'sombra_verde_oscuro',
                    'rechazo' => 0,
                    'pendiente' => 0,
                    'enviado' => 0,
                ],
                'Experiencia' => [
                    'porcentaje' => intval($experiencia),
                    'icono' => 'experiencia',
                    'terminado' => intval($experiencia_Enabled) == 100 ? 1 : 0,
                    'visible' => 1,
                    'url' => '/op/experiencia/index',
                    'title' => ['title' => 'Experiencia'],
                    'sombra' => 'sombra_azul_oscuro',
                    'rechazo' => 0,
                    'pendiente' => 0,
                    'enviado' => 0,
                ],
                'Datos Financieros' => [
                    'porcentaje' => intval($financieros),
                    'icono' => 'financieros',
                    'terminado' => $modelEF->status_op == Status::STATUS_VALIDADO ? 1 :0,
                    'visible' => 1,
                    'url' => '/op/financieros/view',
                    'title' => ['title' => 'Datos Financieros'],
                    'sombra' => 'sombra_rojo_claro',
                    'rechazo' => 0,
                    'pendiente' => 0,
                    'enviado' => 0,
                ],

                'Carta Protesta' => [
                    'porcentaje' => $cartaPor,
                    'icono' => 'acta_const',
                    'terminado' => $cartaTer,
                    'visible' => $visible_carta,
                    'url' => $cartaUrl,
                    'title' => ['title' => 'Carta Protesta'],
                    'sombra' => $sombra_carta,
                    'rechazo' => 0,
                    'pendiente' => 0,
                    'enviado' => 0,
                ]
            ]
        ];

        return $data;
    }


    public function actionOpTest($id = null){
        if ($id != null) {
            if (($provider = \app\models\Provider::find()->where(['provider_id' => $id])->one()) !== null) {
                $provider_id = $provider->provider_id;
            } else {
                return $this->goHome();
            }
        } else {
            $provider = \app\models\Provider::find()->where(['user_id' => Yii::$app->user->getId()])->one();
            $provider_id = $provider->provider_id;
        }

        $this->getDashboardDataOp($id);
    }



    private function getDataOPNew($id = null)
    {
        if ($id != null) {
            if (($provider = \app\models\Provider::find()->where(['provider_id' => $id])->one()) !== null) {
                $provider_id = $provider->provider_id;
            } else {
                return $this->goHome();
            }
        } else {
            $provider = \app\models\Provider::find()->where(['user_id' => Yii::$app->user->getId()])->one();
            $provider_id = $provider->provider_id;
        }

        /*legales*/

        $modelRfc = Rfc::find()->where(['provider_id' => $provider_id])->one();

        $status_global = 'status_op';

        /*Perfil*/

        $statusPer = Perfil::find()->where(['provider_id' => $provider_id])->one();
        $modelDomicilio = ComprobanteDomicilio::find()->where(['provider_id' => $provider_id])->one();
        $p_perfilPre = $this->porcentajeModelo($modelDomicilio);
        $p_cl = $this->porcentajeTablaClientes('ClientesContratos', 'status_op', $id);
        $p_clientes_contratos = (intval($p_cl) + $p_perfilPre) / 2;
        $url_perfil = '/op/perfil/index';

        /*actividad economica*/

        $terminadoE = 0;

        $porRepLegal = 100;
        $porRepLegalCarta = 100;
        $modelGiro = Giro::find()->where(['provider_id' => $provider_id])->one();

        $status_datos_hacienda = AltaHacienda::find()->select($status_global)->where(['provider_id' => $provider_id])->one()[$status_global];

        /*if ($provider->tipo_persona == 'Persona moral') {
            $porRepLegal = $this->porcentajeTabla('RepresentanteLegal', 'status_op',$id);
            $porRepLegalCarta = $this->porcentajeTablaCarta('RepresentanteLegal', 'status_op',$id);

            $p_hacienda = ($modelGiro)?
                intval(($this->porcentajeModelo($modelGiro) + $porRepLegal) / 2) :($porRepLegal>0?$porRepLegal/2:0);
        } else {*/
        $pRfcProv = !empty($modelRfc->url_rfc) ? 100 : 0;
        $p_hacienda = ($modelGiro) ?
            (intval($this->porcentajeModelo($modelGiro)) + $pRfcProv) / 2 :
            0;
        //}


        if ($status_datos_hacienda == Status::STATUS_VALIDADO/* && $porRepLegalCarta == 100*/) {
            $terminadoE = 1;
        }

        $url_hacienda = "/bys/economica/view";

        if ($provider->tipo_persona == 'Persona moral') {
            $modelacta = ActaConstitutiva::find()->where(['provider_id' => $provider_id])->one();
            $modelImss = RegistroImss::find()->where(['provider_id' => $provider_id])->one();
            $modelEscritura = EscrituraPublica::find()->where(['provider_id' => $provider_id])->one();
            $modelLegal = RegistroPublicoPropiedad::find()->where(['provider_id' => $provider_id])->one();

            $p_modA = $this->porcentajeTabla('ModificacionActa', 'status_op', $id);
            $p_modActa = $p_modA === null ? 100 : $p_modA;

            $p_relAcc = $this->porcentajeTabla('RelacionAccionistas', 'status_op', $id);

            $porRepLegal = $this->porcentajeTabla('RepresentanteLegal', 'status_op', $id);

            $p_legales = 0;

            if ($modelLegal && $modelacta && $modelImss && $modelEscritura) {
                $pLegales1 = intval(($this->porcentajeModelo($modelLegal) + $this->porcentajeModelo($modelacta) + $this->porcentajeModelo($modelImss)
                        + $this->porcentajeModelo($modelEscritura)) / 4);
                $p_legales = intval($pLegales1 > 15 ? ($pLegales1 + intval($p_modActa) + intval($p_relAcc) + intval($porRepLegal)) / 4 : $pLegales1);
            }

            $porMActa = $this->porcentajeTablaCarta('ModificacionActa', 'status_op', $id);
            $porRelAc = $this->porcentajeTablaCarta('RelacionAccionistas', 'status_op', $id);
            $porRepLegalCarta = $this->porcentajeTablaCarta('RepresentanteLegal', 'status_op', $id);
        } else {
            $modelCurp = Curp::find()->where(['provider_id' => $provider_id])->one();
            $modelOficial = IdOficial::find()->where(['provider_id' => $provider_id])->one();
            $p_legales = ($modelCurp && $modelOficial) ?
                intval(($this->porcentajeModelo($modelCurp)
                        + $this->porcentajeModelo($modelOficial)) / 2) :
                10;

            $porMActa = 100;
            $porRelAc = 100;
            $porRepLegalCarta = 100;
        }

        $terminadoLegales = 0;

        if ($modelRfc->$status_global == Status::STATUS_VALIDADO && ($porMActa === null || $porMActa == 100) && $porRelAc == 100 && $porRepLegalCarta >= 100) {
            $terminadoLegales = 1;
        }
        $modelEF = EstadoFinanciero::find()->where(['provider_id' => $provider_id])->one();

        if ($modelEF == null) {
            $modelEF = new EstadoFinanciero();
            $modelEF->provider_id = $provider_id;
            $modelEF->save();
        }

        $modelCC = CapacidadContratacion::find()->where(['provider_id' => $provider_id])->one();
        $modelDA = DeclaracionIsr::find()->where(['provider_id' => $provider_id])->one();
        $financieros = ($modelEF && $modelCC && $modelDA) ? (($this->porcentajeModelo($modelEF) + $this->porcentajeModelo($modelDA) + $this->porcentajeModelo($modelCC)) / 3) : 10;

        $modelPerfil = Curriculum::find()->where(['provider_id' => $provider_id])->one();
        $perfil = ($modelPerfil) ? ($this->porcentajeModelo($modelPerfil)) : 80;


        $maquinaria = $this->porcentajeTabla('MaquinariaEquipos', 'status_op', $id);
        $maquinaria_Enabled = $this->porcentajeTablaCarta('MaquinariaEquipos', 'status_op', $id);

        $experiencia = $this->porcentajeTabla('Experiencia', 'status_op', $id);
        $experiencia_Enabled = $this->porcentajeTablaCarta('Experiencia', 'status_op', $id);

        $personal = $this->porcentajeTabla('PersonalTecnico', 'status_op', $id);
        $personal_Enabled = $this->porcentajeTablaCarta('PersonalTecnico', 'status_op', $id);


        $modelOrg = Organigrama::find()->select('status_op')->where(['provider_id' => $provider_id])->one()['status_op'];
        $arrOrg = ['VALIDADO', 'POR VALIDAR'];
        $organigrama = in_array($modelOrg, $arrOrg) ? 100 : 10;

        $organigramaTer = in_array($modelOrg, ['VALIDADO']) ? 100 : 10;
        $tecnicos = ($personal + $organigrama) / 2;
        $tecnicoEnabledT = ($personal_Enabled + $organigramaTer) / 2;


        $modelPago = IntervencionBancaria::find()->where(['provider_id' => $provider_id])->one();
        $pago = ($modelPago) ? ($this->porcentajeModelo($modelPago)) : 10;


        /*ubicacion*/

        $p_ub = $this->porcentajeTabla('Ubicacion', 'status_op', $id);
        $p_ubEnabled = $this->porcentajeTablaCarta('Ubicacion', 'status_op', $id);

        $entidad = Ubicacion::find()->select('state_fiscal')->where(['and', ['provider_id' => $provider_id], ['type_address_prov' => 'DOMICILIO FISCAL']])->orderBy(['ubicacion_id' => SORT_DESC])->one()['state_fiscal'];
        //$entidad = CatMunicipios::find()->select('entidad_id')->where(['municipio_id' => $city])->one()['entidad_id'];
        $porcentrajeDirNL = 100;
        $statusDirNl = 'VALIDADO';
        $dirNl = false;
        if (isset($entidad) && $entidad != 19 || $entidad == null) {
            $dirNl = true;
            $modelDirNL = Ubicacion::find()->where(['and', ['provider_id' => $provider_id], ['type_address_prov' => 'NL']])->orderBy(['ubicacion_id' => SORT_DESC])->one();
            $statusDirNl = 'EN EDICION';
            $porcentrajeDirNL = 0;
            if ($modelDirNL != null) {
                $statusDirNl = $modelDirNL->status_op;

                $porcentrajeDirNL = $this->porcentajeModelo($modelDirNL);
            }


        }


        $p_fo = $this->porcentajeTabla('FotografiaNegocio', 'status_op', $id);
        $p_foEnabled = $this->porcentajeTablaCarta('FotografiaNegocio', 'status_op', $id);

        $p_ub = $p_ub === null && $p_fo >= 100 ? 100 : $p_ub;
        $p_ubicacion = $p_ub == null && !$dirNl ? 0 : ($dirNl ? ($porcentrajeDirNL + $p_ub) / 2 : $p_ub);
        $ubicacionT = (((intval($p_ubEnabled) >= 100 || $p_ubEnabled === null) && $p_foEnabled >= 100) && $statusDirNl == 'VALIDADO') ? 1 : 0;
        $url_ubicacion = '/op/ubicacion/index';
        $img_ubicacion = 'datos_propiedad';

        /*Fotografia*/


        $p_fotografia = $p_fo == null ? 0 : $p_fo;
        $fotografiaT = (intval($p_foEnabled) == 100) ? 1 : 0;
        $url_fotografia = '/op/fotografia/index';
        $img_fotografia = 'fotografia';


        $cartaUrl = '#';
        $cartaPor = 0;
        $cartaTer = 0;
        $visible = 0;
        $visible_carta = 0;
        $sombra_carta = '';

        $p_clCarta = $this->porcentajeTablaClientesCarta('ClientesContratos', 'status_op', $id);
        $p_clientes_contratosCarta = $p_clCarta == null ? 0 : $p_clCarta;
        $perfilT = (intval($p_clientes_contratosCarta) >= 100) && $statusPer->$status_global == Status::STATUS_VALIDADO ? 1 : 0;
        if ($perfilT && $p_hacienda >= 100 && $p_legales >= 100 && $p_clientes_contratosCarta >= 100) {

            $modelProvider = Provider::find()->where(['provider_id' => $provider_id])->one();
            switch ($modelProvider->status_carta_op) {
                case Status::CARTA_PENDIENTE :
                    $cartaUrl = '/carta/generacartaprotesta';
                    $cartaPor = 33;
                    $visible_carta = 1;
                    $visible = 0;
                    $sombra_carta = 'sombra_aqua';
                    break;
                case Status::CARTA_GENERADA:
                    $cartaUrl = '/carta/generacartaprotesta';
                    $cartaPor = 66;
                    $visible_carta = 1;
                    $visible = 0;
                    $sombra_carta = 'sombra_aqua';
                    break;
                case Status::CARTA_FIRMADA :
                    $cartaUrl = \yii\helpers\Url::to('@web/' . GeneralController::limpiarUrl($modelProvider->url_carta_op) , true);
                    $cartaPor = 100;
                    $cartaTer = 1;
                    $visible_carta = 1;
                    $visible = 1;
                    $sombra_carta = 'sombra_aqua';
                    break;

            }
        }

        $data = (Object)[
            'arriba' => [
                'Perfil' => [
                    'porcentaje' => $p_clientes_contratos,
                    'icono' => 'curriculum',
                    'terminado' => $perfilT,
                    'visible' => 1,
                    'url' => $url_perfil,
                    'rechazo' => 0,
                    'pendiente' => 0,
                    'enviado' => 0,
                ],
                'Actividad Económica' => [
                    'porcentaje' => $p_hacienda,
                    'icono' => 'datos_hacienda',
                    'terminado' => $terminadoE,
                    'visible' => 1,
                    'url' => '/op/economica/view',
                    'sombra' => 'sombra_roja',
                    'rechazo' => 0,
                    'pendiente' => 0,
                    'enviado' => 0,
                ],
                'Legales' => [
                    'porcentaje' => $p_legales,
                    'icono' => 'legales',
                    'terminado' => $terminadoLegales,
                    'visible' => 1,
                    'url' => '/op/legales/view',
                    'sombra' => 'sombra_azul',
                    'rechazo' => 0,
                    'pendiente' => 0,
                    'enviado' => 0,
                ],
                'Metodo de pago' => [
                    'porcentaje' => intval($pago),
                    'icono' => 'metodo_pago',
                    'terminado' => ($modelPago->status_op == Status::STATUS_VALIDADO) ? 1 : 0,
                    'visible' => 1,
                    'url' => '/banco/view',
                    'sombra' => 'sombra_amarilla',
                    'rechazo' => 0,
                    'pendiente' => 0,
                    'enviado' => 0,
                ],

                'Carta Protesta' => [
                    'porcentaje' => $cartaPor,
                    'icono' => 'acta_const',
                    'terminado' => $cartaTer,
                    'visible' => $visible_carta,
                    'url' => $cartaUrl,
                    'sombra' => $sombra_carta,
                    'rechazo' => 0,
                    'pendiente' => 0,
                    'enviado' => 0,
                ],
            ],
            'abajo' => [
                'Ubicación' => [
                    'porcentaje' => $p_ubicacion,
                    'icono' => 'datos_propiedad',
                    'terminado' => $ubicacionT,
                    'visible' => $visible,
                    'url' => ($visible == 0) ? '#': $url_ubicacion,
                    'sombra' => 'sombra_naranja',
                    'rechazo' => 0,
                    'pendiente' => 0,
                    'enviado' => 0,
                ],
                'Fotografía' => [
                    'porcentaje' => $p_fotografia,
                    'icono' => 'fotografia',
                    'terminado' => $fotografiaT,
                    'visible' => $visible,
                    'url' => ($visible == 0) ? '#': $url_fotografia,
                    'sombra' => 'sombra_arena',
                    'rechazo' => 0,
                    'pendiente' => 0,
                    'enviado' => 0,
                ],
                'Datos Técnicos' => [
                    'porcentaje' => ($visible == 1) ? intval($tecnicos) : 0,
                    'icono' => 'personal',
                    'terminado' => ($visible == 1) ? (intval($tecnicoEnabledT) == 100) ? 1 : 0 : 0,
                    'visible' => $visible,
                    'url' => ($visible == 0) ? '#': '/op/tecnicos/index',
                    'sombra' => 'sombra_verde_claro',
                    'rechazo' => 0,
                    'pendiente' => 0,
                    'enviado' => 0,
                ],
                'Maquinaria' => [
                    'porcentaje' => ($visible == 1) ? intval($maquinaria) : 0,
                    'icono' => 'maquinaria',
                    'terminado' => ($visible == 1) ? (intval($maquinaria_Enabled) == 100) ? 1 : 0 : 0,
                    'visible' => $visible,
                    'url' => ($visible == 0) ? '#': '/op/maquinaria/index',
                    'sombra' => 'sombra_verde_oscuro',
                    'rechazo' => 0,
                    'pendiente' => 0,
                    'enviado' => 0,
                ],
                'Experiencia' => [
                    'porcentaje' => ($visible == 1) ? intval($experiencia) : 0,
                    'icono' => 'experiencia',
                    'terminado' => ($visible == 1) ? (intval($experiencia_Enabled) == 100) ? 1 : 0 : 0,
                    'visible' => $visible,
                    'url' => ($visible == 0) ? '#': '/op/experiencia/index',
                    'sombra' => 'sombra_azul_oscuro',
                    'rechazo' => 0,
                    'pendiente' => 0,
                    'enviado' => 0,
                ],
                'Datos Financieros' => [
                    'porcentaje' => ($visible == 1) ? intval($financieros) : 0,
                    'icono' => 'financieros', 
                    'terminado' => ($visible == 1) ? ($modelEF->status_op == Status::STATUS_VALIDADO) ? 1 : 0 : 0,
                    'visible' => $visible,
                    'url' => ($visible == 0) ? '#': '/op/financieros/view',
                    'sombra' => 'sombra_rojo_claro',
                    'rechazo' => 0,
                    'pendiente' => 0,
                    'enviado' => 0,
                ],
            ]
        ];

        return $data;
    }

    private function pendientesPorModulo($model,$id,$status = 'status_bys'){
        $modelo = "app\models\\" . $model;
        $data = $modelo::find()->select($status)->distinct()
            ->where(['and',['provider_id'=>$id],['!=',$status,Status::STATUS_VALIDADO],['activo'=>true]])
            ->asArray()->all();
        $data= ArrayHelper::getColumn($data,$status);
        return $data;
    }

    //Nuevo metodo con la logica de consulta de modulos completos
    public function actionPuedeValidarBys(){
        $respuesta = [ "provider_id" => null, "url" => null ];
        $provider = \app\models\Provider::find()->where(['user_id' => Yii::$app->user->getId()])->one();
        $provider_id = $provider->provider_id;
        $tipo_persona = $provider->tipo_persona;
        $modulos_rechazados = Module::getModulesByStatus($provider_id, "status_bys", Status::STATUS_RECHAZADO);
        if( count($modulos_rechazados) > 0 ){ return json_encode($respuesta); }
        $modulos = Module::getModulesByStatus($provider_id, "status_bys", Status::STATUS_ENEDICION);
        $modulos_opciones = GeneralController::defaultModulosOpciones();

        if($modulos == null || count($modulos) == 0 ){ return json_encode($respuesta); }
        foreach($modulos as $modulo){
            $opciones_modulo = isset( $modulos_opciones[$modulo->model] ) ? $modulos_opciones[$modulo->model] : null; 
            $isComplete = GeneralController::isModuloCompleto($provider_id, $tipo_persona, $modulo->model, $opciones_modulo);
            if($modulo->model == "bys_domicilio"){
                $ubicacion_nl = UbicacionController::obtenerPorcentajeDireccionNL(Modulos::MODULO_DOMICILIO, $provider_id);
                $direccion_fisica = UbicacionController::obtenerPorcentajeDireccionFiscal(Modulos::MODULO_DOMICILIO, $provider_id);
                $direccion_notificaciones = Ubicacion::find()->where(['and', ['provider_id' => $provider_id], ['notifications' => true], ["activo" => true] ])->exists();
                $isComplete = $isComplete && $ubicacion_nl == 100 && $direccion_fisica == 100 && $direccion_notificaciones;
            }
            if(!$isComplete){ return json_encode($respuesta); }
        }

        $pending = Asignacion::find()->where(['and',['id_proveedor'=>$provider_id],['activo'=>true]])->one();

        if($provider->status_carta_bys != Status::CARTA_FIRMADA || $pending) //solo cuando ya haya firmado puede enviar a validar
            return json_encode($respuesta);

        $respuesta["url"] = "/provider/validar-modulos-bys";
        $respuesta["provider_id"] = $provider_id;

        return json_encode($respuesta);
    }

    //Cambia el status de los modulos a POR_VALIDAR
    public function actionValidarModulosBys(){
        $user_id = Yii::$app->user->getId();
        $provider = \app\models\Provider::find()->where(['user_id' => $user_id])->one();
        $provider_id = $provider->provider_id;
        $fecha_actual = date('Y-m-d H:i:s');
        $modulos = Module::getModulesByStatus($provider_id, "status_bys", Status::STATUS_ENEDICION);
        Status::updateAll(['status_bys'=>Status::STATUS_TERMINADO_PRO],['and',['provider_id'=>$provider_id],['status_bys'=>Status::STATUS_RECHAZADO]]);
        if($modulos != null && count($modulos) > 0 ){ 
            foreach($modulos as $modulo){
                $modulo->status_bys = Status::STATUS_PORVALIDAR;
                $modulo->last_modification_at = $fecha_actual;
                $modulo->last_modification_by = $provider->user_id;
                $modulo->save();
                GeneralController::updateLastSendValidation($provider_id, $modulo->model,$modulo->model, "bys", Status::STATUS_PENDIENTE, $fecha_actual);
            }

            $modulos_complete = ModulesComplete::find()->where(["provider_id" => $provider_id])->one();
            if($modulos_complete == null){
                $modulos_complete = new ModulesComplete();
                $modulos_complete->provider_id = $provider_id;
                $modulos_complete->modules = 12;
            }
            $modulos_complete->created_date = $fecha_actual;
            $modulos_complete->save();
        }
        //Manda a asignar al validador activo con menos asignaciones
        $asignacion_activa = Asignacion::find()->where(['and',["id_proveedor" => $provider_id], ['activo' => true]])->one();
        if($asignacion_activa == null){
            Asignacion::asignaValidador($provider_id);

            GeneralController::pushNotification($user_id, "¡Tu expediente ha sido enviado a revisión!");
    
            try{
                $subject = "Tu expediente fue enviado a revisión";
                $correoUsuario = $provider->email; //Para recibir notificaciones
                GeneralController::sendEmail('/provider/correos/envio_revision',null, $correoUsuario, $subject,['tipo_provider' => 'bys']);
            }catch(\Exception $e){
                //var_dump($e);
            }
        }
        
        $this->redirect('dashboard');
    }


    /* public function actionTestEmail(){
        $correoUsuario = "<EMAIL>";
        $tipo_provider = 'op';
        /* $subject_bienvenida = $tipo_provider == 'bys' ? 'Bienvenido al Padrón de Proveedores' : 'Bienvenido al Padrón de Contratistas';
        $plantilla_bienvenida = $tipo_provider == 'bys' ? '/provider/correos/bienvenida' : '/provider/correos/bienvenida'; //poner plantilla para OP
        GeneralController::sendEmail($plantilla_bienvenida,null,$correoUsuario, $subject_bienvenida,['tipo_provider' => $tipo_provider]); */
        /* $subject = "Confirmación de cuenta";
        $modelToken = new \app\models\Token();
        $modelToken->user_id = 5040;
        $modelToken->type = 0;
        $modelToken->save();
        $token = $modelToken->code;
        GeneralController::sendEmail('/provider/correos/confirmacion',null,$correoUsuario,$subject,['token'=>$token,'id'=>5040,'tipo_provider' => $tipo_provider]); */
        /* $arr_rechazados = ["Mi Perfil", "Capacidad Económica", "Datos bancarios"];
        $subject = "Datos de módulos rechazados";
        $tipo_provider = 'bys';
        GeneralController::sendEmail('/provider/correos/rechazos',null,$correoUsuario,$subject,['tipo_provider' => $tipo_provider, 'rechazados' => $arr_rechazados]);  */
        /* $subject = "Solicitud de corrección de datos a módulo";
        $modulo = "Datos bancarios";
        $motivo = "Se muestran datos sensibles en el documento, favor de ocultarlos y anexar de nuevo";
        GeneralController::sendEmail('/provider/correos/rechazo',null,$correoUsuario,$subject,['tipo_provider' => $tipo_provider, 'nombre_modulo' => $modulo, 'motivo' => $motivo]);  */
        /* $subject = "Tu expediente fue enviado a revisión";
        $tipo_provider = 'bys';
        GeneralController::sendEmail('/provider/correos/envio_revision',null,$correoUsuario,$subject,['tipo_provider' => $tipo_provider]);  */
        /* $subject = "El módulo “Fotografias” fue enviado a revisión";
        $tipo_provider = 'op';
        GeneralController::sendEmail('/provider/correos/envio_modulo_revision',null,$correoUsuario,$subject,['tipo_provider' => $tipo_provider, 'modulo' => 'Fotografias']);  */
        /* $subject = "¡Felicidades! ya formas parte del Padrón de Proveedores ";
        $tipo_provider = "bys"; */
        /* $subject = "¡Felicidades! ya formas parte del Registro Estatal de Contratistas";
        $tipo_provider = "op";
        GeneralController::sendEmail('/provider/correos/certificacion',null,$correoUsuario,$subject,['tipo_provider' => $tipo_provider ]);
        $subject = "Acreditación de “Curso de prevención y concientización sobre faltas administrativas y hechos de corrupción”";
        $tipo_provider = "bys";
        GeneralController::sendEmail('/provider/correos/constancia',null,$correoUsuario,$subject,['tipo_provider' => $tipo_provider ]);
    } */


    //Devuelve valores para llenar el dashboard
    public function getDashboardDataBys($id = null){

        if(!$id && \Yii::$app->user->isGuest){
            return [];
        }

        $data_arriba = [];
        $data_abajo = [];

        $condicion = $id == null ? ['user_id' => Yii::$app->user->getId()] : ['provider_id' => $id];
        $provider = \app\models\Provider::find()->where($condicion)->one();
        $provider_id = $provider->provider_id;
        $tipo_persona = $provider->tipo_persona;

        $modulos_opciones = GeneralController::defaultModulosOpciones();

        $modulos_arriba = [
            [ 'icono' => 'curriculum', 'visible' => 1, 'url' => '/bys/perfil/index', 'title' => ['title' => 'Mi Perfil'], 'sombra' => 'sombra_verde', 'modulo' => 'bys_perfil'],
            [ 'icono' => 'legales', 'visible' => 1, 'url' => '/bys/legales/view', 'title' => ['title' => 'Legales'], 'sombra' => 'sombra_azul', 'modulo' => 'bys_legales'],
            [ 'icono' => 'bienes_servicios', 'visible' => 1, 'url' => '/bys/economica/view', 'title' => ['title' => 'Bienes y/o Servicios'], 'sombra' => 'sombra_naranja', 'modulo' => 'bys_bys'],
            [ 'icono' => 'experiencia_comercial', 'visible' => 1, 'url' => '/bys/experiencia/view', 'title' => ['title' => 'Experiencia Comercial'], 'sombra' => 'sombra_rojo_claro', 'modulo' => 'bys_experiencia'],
        ];

        $modulos_abajo = [
            [ 'icono' => 'datos_hacienda', 'visible' => 1, 'url' => '/bys/financieros/view', 'title' => ['title' => 'Capacidad Económica'], 'sombra' => 'sombra_roja', 'modulo' => 'bys_economica'],
            [ 'icono' => 'datos_propiedad', 'visible' => 1, 'url' => '/bys/ubicacion/index', 'title' => ['title' => 'Establecimientos'], 'sombra' => 'sombra_naranja', 'modulo' => 'bys_domicilio'],
            [ 'icono' => 'metodo_pago', 'visible' => 1, 'url' => '/banco/viewbys', 'title' => ['title' => 'Datos Bancarios'], 'sombra' => 'sombra_amarilla', 'modulo' => 'bys_bancos'],
        ];

        foreach($modulos_arriba as $key => $modulo){
            $modulo_atributos = GeneralController::getModuloAtributos($modulo['modulo'], $tipo_persona);
            $modulo_opciones = isset($modulos_opciones[$modulo['modulo']]) ? $modulos_opciones[$modulo['modulo']] : null;
            $datos_modulo = GeneralController::getInformacionModuloBys($modulo['modulo'], $modulo_atributos, $modulo_opciones, $provider_id);
            $data_arriba[$key] = array_merge($modulos_arriba[$key], $datos_modulo);
        }

        foreach($modulos_abajo as $key => $modulo){
            $modulo_atributos = GeneralController::getModuloAtributos($modulo['modulo'], $tipo_persona);
            $modulo_opciones = isset($modulos_opciones[$modulo['modulo']]) ? $modulos_opciones[$modulo['modulo']] : null;
            $datos_modulo = GeneralController::getInformacionModuloBys($modulo['modulo'], $modulo_atributos, $modulo_opciones, $provider_id);
            $data_abajo[$key] = array_merge($modulos_abajo[$key], $datos_modulo);
        }

        //Logica de ubicacion para Ubicacion NL y Domicilio Fiscal
        $data_abajo[1] = UbicacionController::datosModuloUbicacion(Modulos::MODULO_DOMICILIO,$data_abajo[1], $provider_id);

        $carta_protesta = [ 
            'url' => '#',
            'title' => ['title' => 'Carta Protesta'],
            'icono' => 'acta_const',
            'sombra' => 'sombra_aqua',
            'visible' => 1, 
            'porcentaje' => 0,
            'terminado' => 0,
            'rechazo' => 0,
            'enviado' => 0,
            'pendiente' => 0  ];
        //if($this->isModulosCompletos($data_arriba) && $this->isModulosCompletos($data_abajo)){
            switch($provider->status_carta_bys){
                case Status::CARTA_PENDIENTE :
                    $carta_protesta['url'] = '/carta/generacartaprotesta';
                    //$carta_protesta['pendiente'] = 1;
                    break;
                case Status::CARTA_GENERADA :
                    $carta_protesta['url'] = '/carta/generacartaprotesta';
                    //$carta_protesta['enviado'] = 1;
                    $carta_protesta['porcentaje'] = 50;
                    break;
                case Status::CARTA_FIRMADA : 
                    $carta_protesta['url'] = '/'. GeneralController::limpiarUrl($provider->url_carta_bys);
                    $carta_protesta['terminado'] = 1;
                    $carta_protesta['porcentaje'] = 100;
                    break;
            }
        //}

        array_push($data_abajo, $carta_protesta);

        $respuesta_final = (Object)[ "arriba" => $data_arriba, "abajo" => $data_abajo];

        return $respuesta_final;
    }

    private function isModulosCompletos($datos){
        foreach($datos as $key => $modulo){
            if($modulo['terminado']==0){ return false; }
        }
        return true;
    }

    public function actionGetDetalleModuloProveedor($provider_id){
        $provider = Provider::find()->where(['provider_id' => $provider_id])->one();
        $provider_id = $provider->provider_id;
        $tipo_persona = $provider->tipo_persona;

        $modulos_opciones = [
            'bys_legales' =>[
                "ModificacionActa" => [ "opciones" => ["and", ["activo" => true]] ],
                "RepresentanteLegal" => [ "opciones" => ["and", ["activo" => true]] ],
                "RelacionAccionistas" => [ "opciones" => ["and", ["activo" => true]] ]
            ],
            "bys_experiencia" => [ 
                "ClientesContratos" =>[ "opciones" => ["and", ["activo" => true], ["tipo" => "bys"] ] ],
                "Certificacion" => [ "opciones" => [ "and", ["activo" => true] ] ]
            ],
            "bys_domicilio" => [
                "Ubicacion" =>[ "opciones" => ["and", ["activo" => true] ] ],
                "FotografiaNegocio" =>[ "opciones" => ["and", ["activo" => true]] ]
            ],
            "bys_bys"=>[
                "Giro" => [ "opciones" => ["and", ["active" => true]] ],
                "ProviderGiro" => [ "opciones" => ["and", ["active" => true]] ]
            ]
        ];

        $modulos = [
            [ 'modulo' => 'bys_perfil', 'porcentaje' => 0, 'status' => null, 'atributos' => [] ],
            [ 'modulo' => 'bys_legales', 'porcentaje' => 0, 'status' => null, 'atributos' => [] ],
            [ 'modulo' => 'bys_bys', 'porcentaje' => 0, 'status' => null, 'atributos' => [] ],
            [ 'modulo' => 'bys_experiencia', 'porcentaje' => 0, 'status' => null, 'atributos' => [] ],
            [ 'modulo' => 'bys_economica', 'porcentaje' => 0, 'status' => null, 'atributos' => [] ],
            [ 'modulo' => 'bys_domicilio', 'porcentaje' => 0, 'status' => null, 'atributos' => [] ],
            [ 'modulo' => 'bys_bancos', 'porcentaje' => 0, 'status' => null, 'atributos' => [] ],
        ];

        foreach($modulos as $key => $modulo){
            $modulo_atributos = $this->getModuloAtributos($modulo['modulo'], $tipo_persona);
            $modulo_opciones = isset($modulos_opciones[$modulo['modulo']]) ? $modulos_opciones[$modulo['modulo']] : null;
            $modulos[$key] = GeneralController::debugInformacionModuloBys($modulo, $modulo_atributos, $modulo_opciones, $provider_id);
        }
        return json_encode($modulos);
    }

    /* Este metodo ya no se utiliza para obtener los datos del dashboard 16/03/2022 */
    private function getDataBys($id = null)
    {
        if ($id != null) {
            if (($provider = \app\models\Provider::find()->where(['provider_id' => $id])->one()) !== null) {
                $provider_id = $provider->provider_id;
            } else {
                return $this->goHome();
            }
        } else {
            $provider = \app\models\Provider::find()->where(['user_id' => Yii::$app->user->getId()])->one();
            $provider_id = $provider->provider_id;
        }

        /* legales */
        $modelRfc = Rfc::find()->where(['provider_id' => $provider_id])->one();

        $status_global = 'status_bys';

        /*perfil*/

        $p_cl = $this->porcentajeTablaClientes('ClientesContratos', 'status_bys', $id);
        $modelDomicilio = ComprobanteDomicilio::find()->where(['provider_id' => $provider_id])->one();
        $statusPer = Perfil::find()->where(['provider_id' => $provider_id])->one();
        $p_perfilPre = $this->porcentajeModelo($modelDomicilio);
        $p_clientes_contratos = (intval($p_cl) + $p_perfilPre) / 2;
        $url_perfil = '/bys/perfil/index';

        /*actividad economica*/



        $modelGiro = Giro::find()->where(['provider_id' => $provider_id])->one();

        $modelProductos = $this->findModelProviderGiro($provider_id);
        $modelProductosPorcentaje = isset($modelProductos[0]['producto_id']) && !empty($modelProductos[0]['producto_id']) ? 100 : 0;

        //$status_datos_hacienda = AltaHacienda::find()->select($status_global)->where(['provider_id' => $provider_id])->one()[$status_global];

        $modelHacienda = AltaHacienda::find()->where(['provider_id' => $provider_id])->one();
        $terminadoE = $modelHacienda->status_bys == Status::STATUS_VALIDADO ? 1: 0;

        $porRepLegalCarta = 100;


        /*if ($provider->tipo_persona == 'Persona moral') {
            $porRepLegal = $this->porcentajeTabla('RepresentanteLegal', 'status_bys',$id);
            $porRepLegalCarta = $this->porcentajeTablaCarta('RepresentanteLegal', 'status_bys',$id);

            $p_hacienda = ($modelGiro)?
                intval(($this->porcentajeModelo($modelGiro) + $porRepLegal) / 2) :($porRepLegal>0?$porRepLegal/2:0);
        } else {*/

        $pRfcProv = !empty($modelRfc->url_rfc) ? 100 : 0;

        $p_hacienda = ($modelGiro) ?
            (intval($this->porcentajeModelo($modelGiro)) + $pRfcProv + $modelProductosPorcentaje) / 3 :
            0;
        //}

        //if ($status_datos_hacienda == Status::STATUS_VALIDADO/*&& $porRepLegalCarta == 100*/) {
         //   $terminadoE = 1;
        //}


        $url_hacienda = "/bys/economica/view";


        /*legales*/


        $modelCurp = Curp::find()->where(['provider_id' => $provider_id])->one();
        $modelOficial = IdOficial::find()->where(['provider_id' => $provider_id])->one();


        if ($provider->tipo_persona == 'Persona moral') {
            $p_modA = $this->porcentajeTabla('ModificacionActa', 'status_bys', $id);
            $p_modActa = $p_modA === null ? 100 : $p_modA;

            $actaCons = ActaConstitutiva::find()->where(['provider_id' => $provider_id])->one();
            $p_relAcc = $this->porcentajeTabla('RelacionAccionistas', 'status_bys', $id);

            $porRepLegal = $this->porcentajeTabla('RepresentanteLegal', 'status_bys', $id);

            $p_legales = 0;

            // if ($modelDomicilio) {
            //   $pLegales1 = intval($this->porcentajeModelo($modelDomicilio));
            $pactaConst = intval($this->porcentajeModelo($actaCons));
            $p_legales = (intval($p_modActa) + intval($porRepLegal) + intval($p_relAcc) + $pactaConst) / 4;
            //}

            $porMActa = $this->porcentajeTablaCarta('ModificacionActa', 'status_bys', $id);
            $porRelAc = $this->porcentajeTablaCarta('RelacionAccionistas', 'status_bys', $id);
            $porRepLegalCarta = $this->porcentajeTablaCarta('RepresentanteLegal', 'status_bys', $id);
        } else {
            $p_legales = ($modelCurp && $modelOficial) ?
                intval(($this->porcentajeModelo($modelCurp)
                        + $this->porcentajeModelo($modelOficial)) / 2) :
                0;

            $porMActa = 100;
            $porRelAc = 100;
            $porRepLegalCarta = 100;
        }

        $url_legales = "/bys/legales/view";
        $terminadoLegales = 0;

        if ($modelRfc->$status_global == Status::STATUS_VALIDADO && ($porMActa === null || $porMActa == 100) && $porRelAc == 100 & $porRepLegalCarta >= 100) {
            $terminadoLegales = 1;
        }


        /*Exdesmadre Intervencion*/

        $url_intervencion = "/banco/view";
        $modelIntervencion = IntervencionBancaria::find()->where(['provider_id' => $provider_id])->one();
        $p_intervencion = ($modelIntervencion) ? $this->porcentajeModelo($modelIntervencion) : 10;
        $terminadoMetodoPago = 0;
        if ($modelIntervencion->$status_global == 'VALIDADO') {
            $terminadoMetodoPago = 1;
        }


        $visibleIconosAbajo = 0;
        $ubicacionT = 0;
        $fotografiaT = 0;
        $financierosT = 0;
        $credencialesT = 0;
        $titleUbicacion = ['title' => 'Ubicación'];
        $titleFotografia = ['title' => 'Fotografía'];
        $titleCredenciales = ['title' => 'Permisos - Certificados'];
        $titleFiancieros = ['title' => 'Financieros'];
        $titlePerfil = ['title' => 'Mi perfil'];
        $visibleIconosAbajo = 1;

        /*ubicacion*/

        $p_ub = $this->porcentajeTabla('Ubicacion', 'status_bys', $id);
        $p_ubEnabled = $this->porcentajeTablaCarta('Ubicacion', 'status_bys', $id);

        $entidad = Ubicacion::find()->select('state_fiscal')->where(['and', ['provider_id' => $provider_id], ['type_address_prov' => 'DOMICILIO FISCAL']])->orderBy(['ubicacion_id' => SORT_DESC])->one()['state_fiscal'];
        $porcentrajeDirNL = 100;
        $statusDirNl = 'VALIDADO';
        $dirNl = false;
        if (isset($entidad) && $entidad != 19 || $entidad == null) {
            $dirNl = true;
            $modelDirNL = Ubicacion::find()->where(['and', ['provider_id' => $provider_id], ['type_address_prov' => 'NL']])->orderBy(['ubicacion_id' => SORT_DESC])->one();
            $statusDirNl = 'EN EDICION';
            $porcentrajeDirNL = 0;
            if ($modelDirNL != null) {
                $statusDirNl = $modelDirNL->status_bys;

                $porcentrajeDirNL = $this->porcentajeModelo($modelDirNL);
            }


        }

        $p_fo = $this->porcentajeTabla('FotografiaNegocio', 'status_bys', $id);
        $p_foEnabled = $this->porcentajeTablaCarta('FotografiaNegocio', 'status_bys', $id);


        $p_ub = $p_ub === null && $p_fo >= 100 ? 100 : $p_ub;
        $p_ubicacion = $p_ub === null && !$dirNl ? 0 : ($dirNl ? ($porcentrajeDirNL + $p_ub) / 2 : $p_ub);

        $ubicacionT = (((intval($p_ubEnabled) >= 100 || $p_ubEnabled === null) && $p_foEnabled >= 100) && $statusDirNl == 'VALIDADO') ? 1 : 0;
        $url_ubicacion = '/bys/ubicacion/index';
        $img_ubicacion = 'datos_propiedad';

        /*Fotografia*/


        $p_fotografia = $p_fo == null ? 0 : $p_fo;
        $fotografiaT = (intval($p_foEnabled) == 100) ? 1 : 0;
        $url_fotografia = '/bys/fotografia/index';
        $img_fotografia = 'fotografia';
        $fot_pendientes = $this->pendientesPorModulo('FotografiaNegocio',$id);


        /*Credenciales*/
        $p_cer = $this->porcentajeTabla('Certificacion', 'status_bys', $id);
        $p_cerEnabled = $this->porcentajeTablaCarta('Certificacion', 'status_bys', $id);
        $cer_pendientes = $this->pendientesPorModulo('Certificacion',$id);


        $p_certificacion = $p_cer == null ? 0 : $p_cer;
        $credencialesT = (intval($p_cerEnabled) == 100) ? 1 : 0;
        $url_credenciales = '/bys/credenciales/index';
        $img_credenciales = 'certificacion';


        /*Financieros*/

        $modelUltimaDeclaracion = UltimaDeclaracion::find()->where(['provider_id' => $provider->provider_id])->one();
        if ($modelUltimaDeclaracion == null) {
            $modelUltimaDeclaracion = new UltimaDeclaracion();
            $modelUltimaDeclaracion->provider_id = $provider->provider_id;
            $modelUltimaDeclaracion->save();
        }
        $p_ultima_declaracion = $this->porcentajeModelo($modelUltimaDeclaracion);
        $url_ultima_declaracion = ($p_ultima_declaracion > 0) ? '/bys/experiencia/view' : '/bys/experiencia/update';


        if ($modelUltimaDeclaracion->$status_global == Status::STATUS_VALIDADO) {
            $financierosT = 1;
        }
        $img_financieros = 'financieros';

        /* Perfil*/


        /*carta firma*/
        $p_carta = 0;
        $url_carta = null;
        $terminadoCarta = 0;
        $img_carta = 'acta_const';
        $sombra_carta = '';
        $visible_carta = 0;
        $titulo_target = ['title' => 'Carta protesta'];

        $p_clCarta = $this->porcentajeTablaClientesCarta('ClientesContratos', 'status_bys', $id);
        $p_clientes_contratosCarta = $p_clCarta == null ? 0 : $p_clCarta;
        $perfilT = (intval($p_clientes_contratosCarta) >= 100) && $statusPer->$status_global == Status::STATUS_VALIDADO ? 1 : 0;

        if ($perfilT == 1 && $terminadoLegales == 1 && $terminadoE == 1 && $ubicacionT == 1 && $fotografiaT == 1 && $credencialesT == 1 && $financierosT == 1 && $terminadoMetodoPago) {
            $modelProvider = Provider::find()->where(['provider_id' => $provider_id])->one();
            switch ($modelProvider->status_carta_bys) {
                case Status::CARTA_PENDIENTE :
                    $url_carta = '/carta/generacartaprotesta';
                    $titulo_target = ['title' => 'Carta protesta'];
                    $terminadoCarta = 0;
                    $img_carta = 'acta_const';
                    $sombra_carta = 'sombra_aqua';
                    $visible_carta = 1;
                    break;
                case Status::CARTA_GENERADA:
                    $titulo_target = ['title' => 'Carta protesta'];
                    $url_carta = '/carta/generacartaprotesta';
                    $img_carta = 'acta_const';
                    $terminadoCarta = 0;
                    $sombra_carta = 'sombra_aqua';
                    $visible_carta = 1;
                    break;
                case Status::CARTA_FIRMADA :
                    $url_carta = \yii\helpers\Url::to('@web/' . GeneralController::limpiarUrl($modelProvider->url_carta_bys), true);
                    $p_carta = 100;
                    $terminadoCarta = 1;
                    $img_carta = 'acta_const';
                    $sombra_carta = 'sombra_aqua';
                    $titulo_target = ['title' => 'Carta protesta', 'target' => '_blank'];
                    $visible_carta = 1;
                    break;

            }
        }

        $data = (Object)[
            'arriba' => [
                [
                    'porcentaje' => $p_clientes_contratos,
                    'icono' => 'curriculum',
                    'terminado' => $perfilT,
                    'visible' => 1,
                    'url' => $url_perfil,
                    'title' => $titlePerfil,
                    'sombra' => 'sombra_verde',
                    'rechazo' => 0,
                    'pendiente' => 0
                ],
                [
                    'porcentaje' => intval($p_hacienda),
                    'icono' => 'datos_hacienda',
                    'terminado' => $terminadoE,
                    'visible' => 1,
                    'url' => $url_hacienda,
                    'title' => ['title' => 'Actividad Económica'],
                    'sombra' => 'sombra_roja',
                    'rechazo' => 0,
                    'pendiente' => 0,
                    //'rechazo' => $modelHacienda->status_bys == Status::STATUS_RECHAZADO && intval($p_hacienda) == 100 ? 1 :0,
                    //'pendiente' => $modelHacienda->status_bys == Status::STATUS_ENEDICION && intval($p_hacienda) == 100 ? 1 :0,
                ],
                [
                    'porcentaje' => $p_legales,
                    'icono' => 'legales',
                    'terminado' => $terminadoLegales,
                    'visible' => 1,
                    'url' => $url_legales,
                    'title' => ['title' => 'Legales'],
                    'sombra' => 'sombra_azul',
                    'rechazo' => 0,
                    'pendiente' => 0
                ],
                [
                    'porcentaje' => $p_ubicacion,
                    'icono' => $img_ubicacion,
                    'terminado' => $ubicacionT,
                    'visible' => 1,
                    'url' => $url_ubicacion,
                    'title' => $titleUbicacion,
                    'sombra' => 'sombra_naranja',
                    'rechazo' => 0,//Pendiente multiple
                    'pendiente' => 0//Pendiente multiple
                ],
               /*  [
                    'porcentaje' => $p_fotografia,
                    'icono' => $img_fotografia,
                    'terminado' => $fotografiaT,
                    'visible' => 1,
                    'url' => $url_fotografia,
                    'title' => $titleFotografia,
                    'sombra' => 'sombra_arena',
                    'rechazo' => 0,
                    'pendiente' => 0,
                    //'rechazo' => in_array(Status::STATUS_RECHAZADO,$fot_pendientes) && $p_fotografia == 100 ? 1 :0,
                    //'pendiente' => in_array(Status::STATUS_PENDIENTE,$fot_pendientes) && $p_fotografia == 100 ? 1 :0,

                ], */

            ],
            'abajo' => [

                [
                    'porcentaje' => $p_ultima_declaracion,
                    'icono' => $img_financieros,
                    'terminado' => $financierosT,
                    'visible' => 1,
                    'url' => $url_ultima_declaracion,
                    'title' => $titleFiancieros,
                    'sombra' => 'sombra_rojo_claro',
                    'rechazo' => 0,
                    'pendiente' => 0,
                    //'rechazo' => $modelUltimaDeclaracion->status_bys == Status::STATUS_RECHAZADO && $p_ultima_declaracion == 100 ? 1 :0,
                    //'pendiente' => $modelUltimaDeclaracion->status_bys == Status::STATUS_ENEDICION && $p_ultima_declaracion == 100 ? 1 :0,

                ],
                [
                    'porcentaje' => $p_certificacion,
                    'icono' => $img_credenciales,
                    'terminado' => $credencialesT,
                    'visible' => 1,
                    'url' => $url_credenciales,
                    'title' => $titleCredenciales,
                    'sombra' => 'sombra_celeste',
                    'rechazo' => 0,
                    'pendiente' => 0,
                    //'rechazo' => in_array(Status::STATUS_RECHAZADO,$cer_pendientes) && $p_certificacion == 100 ? 1 :0,
                    //'pendiente' => in_array(Status::STATUS_PENDIENTE,$cer_pendientes) && $p_certificacion == 100 ? 1 :0
                ],

                [
                    'porcentaje' => $p_intervencion,
                    'icono' => 'metodo_pago',
                    'terminado' => $terminadoMetodoPago,
                    'visible' => 1,
                    'url' => $url_intervencion,
                    'title' => ['title' => 'Metodo de pago'],
                    'sombra' => 'sombra_amarilla',
                    'rechazo' => 0,
                    'pendiente' => 0
                    //'rechazo' => $modelIntervencion->status_bys == Status::STATUS_RECHAZADO && $p_intervencion == 100 ? 1 :0,
                    //'pendiente' => $modelIntervencion->status_bys == Status::STATUS_ENEDICION && $p_intervencion == 100 ? 1 :0
                ],
                [
                    'porcentaje' => $p_carta,
                    'icono' => $img_carta,
                    'terminado' => $terminadoCarta,
                    'visible' => $visible_carta,
                    'url' => $url_carta,
                    'title' => $titulo_target,
                    'sombra' => $sombra_carta,
                    'rechazo' => 0,
                    'pendiente' => 0
                ],
            ]
        ];

        return $data;
    }


    public function actionProveedor()
    {
        $this->layout = 'nomain';
        return $this->render('proveedor');
    }

    public function actionIndexDatosFinancieros()
    {
        if (Yii::$app->user->can(Usuarios::ROLE_PROVIDER)) {

            $provider = Provider::find()->where(['user_id' => Yii::$app->user->identity->user_id])->one();

            switch ($provider->tipo_provider) {

                case 'bys' :
                {
                    $ultima = UltimaDeclaracion::find()->where("provider_id = $provider->provider_id and (status = 'PENDIENTE' or status = 'RECHAZADO')")->one();
                    $balance = BalanceEstado::find()->where("provider_id = $provider->provider_id and (status = 'PENDIENTE' or status = 'RECHAZADO')")->one();
                    $iva = DeclaracionIva::find()->where("provider_id = $provider->provider_id and (status = 'PENDIENTE' or status = 'RECHAZADO')")->one();
                    $isr = Isr::find()->where("provider_id = $provider->provider_id and (status = 'PENDIENTE' or status = 'RECHAZADO')")->one();
                    return $this->render('index-datos-financieros', [
                        'tipo' => 'bys',
                        'ultima' => $ultima,
                        'balance' => $balance,
                        'iva' => $iva,
                        'isr' => $isr
                    ]);
                }

                case 'op' :
                {
                    $isr = Isr::find()->where("provider_id = $provider->provider_id and (status = 'PENDIENTE' or status = 'RECHAZADO')")->one();
                    $capacidad = CapacidadContratacion::find()->where("provider_id = $provider->provider_id and (status = 'PENDIENTE' or status = 'RECHAZADO')")->one();
                    $estado = EstadoFinanciero::find()->where("provider_id = $provider->provider_id and (status = 'PENDIENTE' or status = 'RECHAZADO')")->one();
                    return $this->render('index-datos-financieros', [
                        'tipo' => 'op',
                        'isr' => $isr,
                        'capacidad' => $capacidad,
                        'estado' => $estado
                    ]);
                }
            }
        }
    }


    public function actionIndexDatosTecnicos()
    {
        $user_id = Provider::find()->select('provider_id')->where(['user_id' => Yii::$app->user->getId()])->one()->provider_id;
        // var_dump($user_id);exit();
        $provider_tipo = Provider::find()->select('tipo_provider')->where(['provider_id' => $user_id])->one()->tipo_provider;

        $capacidad_pro = CapacidadProduccion::find()->select(['capacidad_produccion_id', 'status'])->where(['provider_id' => $user_id])
            ->orderBy(['capacidad_produccion_id' => SORT_DESC])->limit(1)->one();

        $organigrama = Organigrama::find()->select(['organigrama_id', 'status'])->where(['provider_id' => $user_id])
            ->orderBy(['organigrama_id' => SORT_DESC])->limit(1)->one();

        $cv = Curriculum::find()->select(['curriculum_id', 'status'])->where(['provider_id' => $user_id])
            ->orderBy(['curriculum_id' => SORT_DESC])->limit(1)->one();

        return $this->render('index-datos-tecnicos', [
            'capacidad_pro' => $capacidad_pro['status'],
            'provider_id' => $user_id,
            'capacidad_prod_id' => $capacidad_pro['capacidad_produccion_id'],
            'proveedor_contratista' => $provider_tipo,
            'cv' => $cv['status'],
            'cv_id' => $cv['curriculum_id'],
            'organigrama' => $organigrama['status'],
            'organigrama_id' => $organigrama['organigrama_id']
        ]);
    }


    public function actionIndexDatosLegales()
    {
        $id = Provider::find()->select('provider_id')->where(['user_id' => Yii::$app->user->id])->one()->provider_id;
        $this->layout = 'home';
        return $this->render('index-datos-legales', [
            'id' => $id,

        ]);
    }

    /**
     * @return string|\yii\web\Response
     */
    public function actionCreate($tipoProvider = null,$id=null)
    {

        if ($tipoProvider != null) {
            $tipoProvider = base64_decode($tipoProvider);
            if (in_array($tipoProvider, ['bys', 'op'])) {
                setcookie('provider_type', $tipoProvider, time() + 1 * 24 * 60 * 60);
            }
        }
        if (Provider::existe()) {
            return $this->redirect(['view', 'id' => Provider::getProvider()['provider_id']]);
        }

        $model = Provider::find()->where(['user_id'=>Yii::$app->user->getId()])->One();
        $exist = true;
        if(!$model){
            $exist = false;
            $model = new Provider();
            $model->user_id = Yii::$app->user->getId();
            $model->tipo_persona = $tipo = Yii::$app->user->identity->tipo_persona;
            $model->rfc = $rfc = Yii::$app->user->identity->rfc;
        }

        if (Yii::$app->user->identity->tipo_persona == 'Persona física') {
            $model->scenario = Provider::SCENARIO_CREATE_FISICA;
        } else {
            $model->scenario = Provider::SCENARIO_CREATE_MORAL;
        }

        $tra = Yii::$app->db->beginTransaction();
        if ($model->load(Yii::$app->request->post()) /*&& $modelUbi->load(Yii::$app->request->post()) && $model_rfc->load(Yii::$app->request->post()) && $model_comprobante_domicilio->load(Yii::$app->request->post())*/) {
            $model->email = Yii::$app->user->identity->email;


            $model->tipo_provider = $model->tipo_provider == 'bys' || $model->tipo_provider == 'op' ? $model->tipo_provider : (isset($_COOKIE['provider_type']) ? $_COOKIE['provider_type'] : 'bys');
            if ($model->save()) {
                if(!$exist){
                    $model_rfc = new Rfc();
                    $model_rfc->rfc = Yii::$app->user->identity->rfc;
                    $this->saveModelProv($model_rfc,$model);

                    $this->saveModelProv(new IntervencionBancaria(),$model );
                    $this->saveModelProv(new ComprobanteDomicilio(),$model );

                    if ($model->tipo_persona == 'Persona física') {
                        $this->saveModelProv(new Curp(),$model);
                        $this->saveModelProv(new IdOficial(),$model);
                    }
                    if ($model->tipo_persona == 'Persona moral') {
                        $this->saveModelProv(new ActaConstitutiva(),$model);
                    }

                    if ($model->tipo_persona == 'Persona moral' && $model->tipo_provider == 'op') {
                        $this->saveModelProv(new EscrituraPublica(),$model);
                        $this->saveModelProv(new RegistroPublicoPropiedad(),$model);
                        $this->saveModelProv(new RegistroImss(),$model);
                        $this->saveModelProv(new ActaConstitutiva(),$model);
                    }
                    $this->saveModelProv(new DeclaracionIsr(),$model);
                    $this->saveModelProv(new AltaHacienda(),$model);

                    switch ($model->tipo_provider) {
                        case 'op':
                        {
                            $this->saveModelProv(new EstadoFinanciero(),$model);
                            $this->saveModelProv(new CapacidadContratacion(),$model);
                            break;
                        }
                        case 'bys' :
                        {
                            $this->saveModelProv(new UltimaDeclaracion(),$model);
                            $this->saveModelProv(new BalanceEstado(),$model);
                            $this->saveModelProv(new DeclaracionIva(),$model);
                            break;
                        }
                    }
                }
                //crea el listado de modulos para el proveedor
                Module::setModules($model->provider_id);

                //asigna la razon social al perfil y al veneficiario de la cuenta

                $modelUsuario = Usuarios::findOne(Yii::$app->user->id);
                $loginForm = new LoginForm();
                $data = $loginForm->getDataFromFIEL($modelUsuario->fiel_data);
                $razonSocial = $data['CN'];


                //traduce
                if($model->tipo_persona == 'Persona moral'){
                    $model->name_razon_social = $this->traduce($razonSocial);
                }
                //var_dump($data).exit();


                if($model->tipo_provider == 'bys')
                    $model->status_carta_bys = Status::CARTA_GENERADA;  //habilita el video y el firmado antes de enviar a validar para bys
                $model->save();

                $modelBancos = IntervencionBancaria::find()->where(['provider_id'=>$model->provider_id])->one();
                if(!$modelBancos)
                    $modelBancos = new IntervencionBancaria();

                $modelBancos->nombre_titular_cuenta = $razonSocial;
                $modelBancos->save(false);

                if($model->tipo_persona == 'Persona física'){ //Asigna curp si es persona fisica
                    $modelCurp = Curp::find()->where(['provider_id'=>$model->provider_id])->one();
                    if(!$modelCurp){
                        $modelCurp = new Curp();
                        $modelCurp->provider_id = $model->provider_id;
                    }
                    $modelCurp->curp = $data['SN'];
                    $modelCurp->save(false);
                }


                $tra->commit();
                return $this->redirect(['view', 'id' => $model->provider_id]);
            } else {
                $tra->rollBack();
                return json_encode($model->errors);exit();
            }
        }

        $this->layout = 'limpio';

        return $this->render('create', [
            'model' => $model,
            'tipo' => $tipoProvider,
            'pais' => Provider::findPais(),
        ]);
    }

    private function saveModelProv($model,$value){
        $model->provider_id = $value->provider_id;
        if(!$model->save(false))
            var_dump($model->errors).exit();
    }

    public function traduce($razonSocial){
        $sociedades = SociedadMercantil::find()->where(['activo'=>true])->asArray()->all();
        usort($sociedades, function($a,$b){ return strlen($a['abreviatura']) < strlen($b['abreviatura']); });
        foreach($sociedades as $sociedad){
            $razonSocial = str_replace(' '.strtoupper($sociedad['traducir']).' ',' '.strtoupper($sociedad['abreviatura']).' ',strtoupper($razonSocial));
        }
        return $razonSocial;
    }


    public function actionUpdate($id = null)
    {

        $this->layout = 'nomain';
        $userId = Yii::$app->user->getId();
        $model = $this->findModel($id);
        if ($model->tipo_persona == 'Persona física') {
            $model->scenario = Provider::SCENARIO_CREATE_FISICA;
        } else {
            $model->scenario = Provider::SCENARIO_CREATE_MORAL;
        }
        $modelUbi = $this->findModelUbicacionProv($id);
        $model_rfc = $this->findModelRfcProv($id);
        $model_rfc->scenario = Rfc::SCENARIO_CREATE;
        $model_comprobante_domicilio = $this->findModelDomicilioProv($id);
        $modelUbi->scenario = Ubicacion::SCENARIO_CREATE;
        $model_comprobante_domicilio->scenario = Ubicacion::SCENARIO_CREATE;
        $urlComprobanteDomicilioOld = $model_comprobante_domicilio->url_comprobante_domicilio;
        $urlRfcOld = $model_rfc->url_rfc;
        $estado = CatEntidades::findState();
        $grupoProducto = ArrayHelper::map(GrupoProducto::find()->orderBy(['nombre' => 'ASC'])->all(), 'grupo_producto_id', 'nombre');
        $model->user_id = $userId;
        $pais = Provider::findPais();


        if (Provider::registro_completo()) {
            return $this->redirect(['view', 'id' => $id]);
        } else {
            $ubuSave = true;
            $trans = Yii::$app->db->beginTransaction();
            if ($model->load(Yii::$app->request->post()) && $modelUbi->load(Yii::$app->request->post()) && $model_rfc->load(Yii::$app->request->post()) && $model_comprobante_domicilio->load(Yii::$app->request->post())) {

                if (!empty($modelUbi->state_fiscal)) {
                    $statefiscal = $modelUbi->estado->nombre;

                }


                $modelUbi->tipo = 'DOMICILIO FISCAL';
                $modelUbi->descripcion = 'DOMICILIO FISCAL';
                $modelUbi->type_address_prov = 'DOMICILIO FISCAL';
                $modelUbi->correo = $model->email;
                $modelUbi->telefono = $model->telfono;
                $modelUbi->provider_id = $id;
                $modelUbi->status_bys = 'VALIDADO';
                $modelUbi->status_op = 'VALIDADO';

                if (!empty($modelUbi->geo_ubicacion)) {
                    $location = json_decode($modelUbi->geo_ubicacion);
                    if (isset($location[0]) && !empty($location[0]) && isset($location[1]) && !empty($location[1])) {
                        $val_geo = Ubicacion::findBySql("select ST_MakePoint(" . $location[0] . "," . $location[1] . ")")->asArray()->all();
                        $modelUbi->geo_ubicacion = $val_geo[0]['st_makepoint'];
                    }
                }

                $model->estaLleno = $this->estaLlenoProvider($modelUbi->getAttributes());


                $ubuSave = $ubuSave && $modelUbi->save();


                $this->makeDir($model_rfc->path);


                if (!empty($model_rfc->url_rfc) && $model_rfc->url_rfc != $urlRfcOld) {
                    $new_nameRfc = str_replace('archivos_tmp/', $model_rfc->path . '/', $model_rfc->url_rfc);
                    $this->copyFile($model_rfc->url_rfc, $new_nameRfc);
                    $model_rfc->url_rfc = $new_nameRfc;
                }

                $this->makeDir($model_comprobante_domicilio->path_comprobante_domicilio);

                if (!empty($model_comprobante_domicilio->url_comprobante_domicilio) && $model_comprobante_domicilio->url_comprobante_domicilio != $urlComprobanteDomicilioOld) {
                    $new_nameCd = str_replace('archivos_tmp/', $model_comprobante_domicilio->path_comprobante_domicilio . '/', $model_comprobante_domicilio->url_comprobante_domicilio);
                    $this->copyFile($model_comprobante_domicilio->url_comprobante_domicilio, $new_nameCd);
                    $model_comprobante_domicilio->url_comprobante_domicilio = $new_nameCd;
                }

                $model->estaLlenoRfc = $this->estaLlenoProvider($model_rfc->getAttributes());
                $model->estaLlenoCd = $this->estaLlenoProvider($model_comprobante_domicilio->getAttributes());
                if ($model->save() && $ubuSave && $model_rfc->save() && $model_comprobante_domicilio->save()) {

                    $trans->commit();
                    return $this->redirect(['view', 'id' => $model->provider_id]);
                } else {
                    $trans->rollBack();
                }
            }
            $cat_vialidad = Provider::getVialidad();
            $model->proc = ArrayHelper::getColumn($model->giro, 'grupo_producto_id');
            $list_municipios = [];
            $list_localidades = [];
            $list_asentamientos = [];
            if (!empty($modelUbi->state_fiscal)) {
                $list_municipios = Provider::getListMun($modelUbi->state_fiscal);
            }

            if (!empty($modelUbi->city_fiscal) && !empty($modelUbi->state_fiscal)) {
                $list_localidades = Provider::getListLoc($modelUbi->state_fiscal, $modelUbi->city_fiscal);
                $list_asentamientos = Provider::getListAsen($modelUbi->state_fiscal, $modelUbi->city_fiscal);
            }

            return $this->render('update', [
                'model' => $model,
                'estado' => $estado,
                'grupoProducto' => $grupoProducto,
                'pais' => $pais,
                'cat_vialidad' => $cat_vialidad,
                'list_municipios' => $list_municipios,
                'list_localidades' => $list_localidades,
                'list_asentamientos' => $list_asentamientos,
                'modelUbi' => $modelUbi,
                'model_rfc' => $model_rfc,
                'model_comprobante_domicilio' => $model_comprobante_domicilio
            ]);

        }
    }


    public function actionDelete($id)
    {
        $this->findModel($id)->delete();

        return $this->redirect(['index']);
    }

    public function actionIndexVentanilla()
    {
        $searchModel = new ProviderSearch();
        $dataProvider = $searchModel->search(Yii::$app->request->queryParams, 'ventanillaFilter');
        if (Yii::$app->request->post('hasEditable')) {
            $id = Yii::$app->request->post('editableKey');
            $model = Provider::findOne($id);
            $posted = current($_POST['Provider']);
            if ($posted) {
                if (isset($posted['id_sire'])) {
                    $model->id_sire = $posted['id_sire'];
                }
                $model->Update();
            }

            return '{"output":"","message":""}';
        }
        return $this->render('index-ventanilla', [
            'searchModel' => $searchModel,
            'dataProvider' => $dataProvider,
        ]);
    }


    public function actionCertificado($id)
    {
        $model = $this->findModel($id);
        $datos = $this->findModelUser($model->user_id);
        $dato = Datos::find()->select('dato')->where(['user_id' => $model->user_id])->one()->dato;
        $id_sire = $model->id_sire;
        if ($id_sire != null) {

            $data_post = [
                'key_sap' => $model->id_sire,
                'razon_social' => $model->name_razon_social,
                'nombre' => $model->name_comercial,
                'rfc' => $model->rfc,
                'tipo_persona' => $model->tipo_persona,
                'cp' => $model->cp_fiscal,
                'calle' => $model->calle_fiscal,
                'numero' => $model->numero_fiscal,
                'telefono' => $model->telfono,
                'user_name' => $datos->username,
                'password' => $dato,

            ];

            $ch = curl_init("http://*************:8080/pruebas/webservice/inserta_proveedores.php");
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_CUSTOMREQUEST, "POST");
            curl_setopt($ch, CURLOPT_POSTFIELDS, $data_post);
            $response = curl_exec($ch);
            curl_close($ch);
            $res = json_decode($response);
            $fecha = date('d-m-y');
            $res = true;
            if ($res) {
                if ($model->tipo_provider == 'op') {
                    $status_search = 'status_op';
                    $fecha_cer = 'fecha_certificado_op';
                } else {
                    $status_search = 'status_bys';
                    $fecha_cer = 'fecha_certificado_bys';
                }
                $etapa = Yii::$app->db->createCommand();
                $etapa->update('provider', [$status_search => 'PROVEEDOR_ETAPA_4', $fecha_cer => $fecha], 'provider_id =' . $id);
                $etapa->execute();
                $registro = yii::$app->db->createCommand();
                $registro->delete('datos', 'user_id =' . $datos->user_id);
                $registro->execute();

                $cabecera = $this->renderPartial('pdf/header');
                $content = $this->renderPartial('pdf/pdf3', ['model' => $this->findModel($id), 'model_user' => $this->findModelUser($model->user_id)]);
                $pie = $this->renderPartial('pdf/footer');
                $mpdf = new mPDF();
                $mpdf->SetTitle('Certificado');
                $stylesheet = file_get_contents('css/pdf.css');
                $mpdf->SetHTMLHeader($cabecera);
                $mpdf->SetHTMLFooter($pie);
                $mpdf->WriteHTML($stylesheet, 1);
                $mpdf->WriteHTML($content, 2);
                $mpdf->showImageErrors = true;
                $mpdf->Output('Certificado.pdf', 'D');

                Yii::$app->session->setFlash('success', 'Datos Guardados Correctamente');
            } else {
                Yii::$app->session->setFlash('error', $res->detalle);
            }
        } else {
            Yii::$app->session->setFlash('error', 'Id SIRE no puede estar vacio¡');
        }
        return $this->redirect(['/provider/index-ventanilla']);

    }

    public function actionIndexAdminProvider(){
        $providerSearch = new ProviderSearch();
        $dataProvider = $providerSearch->search(Yii::$app->request->queryParams);

        $dataEtapa1 = [];
        $captura_contratistas = [];
        $enCaptura = [];
        $dataEtapa3 = [];
        $dashboard = [];
        $dashboardFull = [];

        if(Yii::$app->user->can(Usuarios::ROLE_ADMIN_CONTRATISTAS)){
            //Contratista en curso de validacion y validados
            $captura_contratistas = $providerSearch->search(Yii::$app->request->queryParams, 'contratistas_captura');
        }

        if(Yii::$app->user->can(Usuarios::ROLE_VALIDADOR_PROVEEDORES) || Yii::$app->user->can(Usuarios::ROLE_ADMIN_PROVIDER)){
            $enCaptura = $providerSearch->search(Yii::$app->request->queryParams, 'enCaptura');
            $dataEtapa1 = $providerSearch->search(Yii::$app->request->queryParams, 'etapa1Filter');
            $dashboardFull = $providerSearch->search(Yii::$app->request->queryParams, 'dashboardFullFilter');
            $dataEtapa3 = $providerSearch->search(Yii::$app->request->queryParams, 'etapa3Filter');
            $dashboard = $providerSearch->search(Yii::$app->request->queryParams, 'dashboardFilter');
        }

        return $this->render('index-admin-provider', [
            'searchModel' => $providerSearch,
            'dataProvider' => $dataProvider,
            'dataEtapa1' => $dataEtapa1,
            'captura_contratistas' => $captura_contratistas,
            'dataEtapa3' => $dataEtapa3,
            'dashboard' => $dashboard,
            'enCaptura' => $enCaptura,
            'dashboardFull' => $dashboardFull
        ]);
    }

    public function actionEditprov($id){
        $model = Provider::findOne($id);

        if($model->load(Yii::$app->request->post())){
            $model->save();
            return $this->redirect('index-admin-provider');
        }
        return $this->renderAjax('editprov',['model'=>$model]);

    }

    public function actionReasignacion($id=null){
        if(!$id)
            return $this->goHome();

        $expSelect = new Expression("pr.provider_id, case when tipo_persona = 'Persona moral' then concat_ws(' - ',pr.name_razon_social, pr.rfc) else concat_ws(' ',pr.pf_nombre, pr.pf_ap_paterno, pr.pf_ap_materno,'-', pr.rfc) end as proveedor");
        $asignados = Asignacion::find()
            ->select($expSelect)
            ->innerJoin(['pr'=>'provider'],'pr.provider_id = asignacion.id_proveedor')
            ->where(['and',['asignacion.id_validador'=>$id],['asignacion.activo'=>true],['modo'=>'REV']])->asArray()->all();
        $data['asignados'] = array_values(array_column($asignados,'provider_id'));

        if(Yii::$app->request->isPost){
            $form = isset(Yii::$app->request->post()['pendientes'])?Yii::$app->request->post()['pendientes']:[];
            $quitar = array_diff($data['asignados'],$form);
            $agregar = array_diff($form,$data['asignados']);

            Asignacion::updateAll(['id_validador'=>$id,'prioridad'=>true],['id_proveedor'=>$agregar]);
            Asignacion::updateAll(['id_validador'=>Yii::$app->user->id],['id_proveedor'=>$quitar]);

            //todo agregar a historico reasignacion los movimientos

            return $this->redirect('/usuarios/index');
        }

        $pendientes = Asignacion::find()
            ->select($expSelect)
            ->innerJoin(['pr'=>'provider'],'pr.provider_id = asignacion.id_proveedor')
            ->where(['and',['asignacion.activo'=>true],['modo'=>'REV']])
            ->orderBy(" asignacion.created_at asc ")
            ->asArray()->all();

        $data['pendientes'] = ArrayHelper::map($pendientes,'provider_id','proveedor');
        $data['usuario'] =  Usuarios::findOne($id);

        return $this->renderAjax('reasignacion',$data);
    }

    public function actionCompletosAsingar($id=null,$des=null){
        if(!$id)
            return $this->goHome();

        $model = Asignacion::find()->where(['and',['id_proveedor'=>$id],['activo'=>true]])->one();
        if(!$model)
            $model = new Asignacion();
        if($des){
            //$model->activo = false;
            $model->last_updated_by = Yii::$app->user->getId();
            $model->last_updated_at = date('Y-m-d H:i:s');
            if(isset(Yii::$app->request->post()['prioridad'])){
                $model->prioridad = Yii::$app->request->post()['prioridad'];
            }
            $model->save();
            $newVal = Asignacion::asignavalidador($id,$model->id_validador,$model->id_asignacion); //Asigna un nuevo validador

            //historicoReasignacion
            $this->reasignacionHistorico($id,$model->id_validador,$newVal);
            return $this->redirect('index-admin-provider');
        }

        $auxAnt = $model->id_validador;
        if($model->load(Yii::$app->request->post())){
            $this->reasignacionHistorico($id,$auxAnt,$model->id_validador);
            $model->created_by = Yii::$app->user->getId();
            if(!$model->save())
                var_dump($model->errors).exit();
            return $this->redirect('index-admin-provider');
        }

        $proveedor = Provider::findOne($id);
        $validadores = ArrayHelper::map(Usuarios::find()->select(['user_id',"concat_ws(' ',nombre, primer_apellido, segundo_apellido,'(',email,')') as validador"])
            ->where(['and',['role'=>Usuarios::ROLE_VALIDADOR_PROVEEDORES],['status'=>'ACTIVO']])->
            asArray()->all(),'user_id','validador');

        return $this->renderAjax('completos-asignar',[
           'model'=>$model,
           'proveedor'=>$proveedor,
           'validadores'=>$validadores
        ]);
    }

    public function reasignacionHistorico($prov,$ant,$desp){
        $hr = new HistoricoReasignacion();
        $hr->provider_id = $prov;
        $hr->created_by = Yii::$app->user->getId();
        $hr->revisor_id_ant = $ant;
        $hr->revisor_id_pos =$desp;
        if(!$hr->save())
            var_dump($hr->errors).exit();
    }


    protected function findModel($id)
    {
        if (($model = Provider::find()->where(['provider_id' => $id])->one()) !== null) {
            return $model;
        } else {
            return new Provider();
        }
    }

    protected function findProvider($user_id){
        $model = Provider::find()->where(['user_id' => $user_id])->one();
        return $model !== null ? $model : new Provider();
    }

    protected function findModelProv($id)
    {
        $model = Provider::find()->where(['provider_id' => $id])->one();
        if ($model == null || empty($model)) {
            throw new NotFoundHttpException('The requested page does not exist.');
        }
        return $model;
    }

    public function findModelUser($id)
    {
        $model = Usuarios::find()->where(['user_id' => $id])->one();

        return $model;
    }

    public function findModelRepresentante($id)
    {
        $model = RepresentanteLegal::find()->where(['and', ['provider_id' => $id], ['activo' => true]])->one();

        return $model;
    }


    public function actionProviderState($id)
    {
        $city = ArrayHelper::map(CatMunicipios::findCity($id), 'municipio_id', 'nombre');
        return json_encode($city);
    }


    public function actionLocalidades($state, $mun)
    {
        $localidades = ArrayHelper::map(CatLocalidades::find()->select(['localidad_id', 'nombre'])
            ->where(['and', ['entidad_id' => $state], ['municipio_id' => $mun]])->asArray()->orderBy(['nombre' => 'ASC'])->all(), 'localidad_id', 'nombre');

        $con = '';
        $con .= "<option value=''>Localidad...</option>";
        if ($localidades) {
            foreach ($localidades as $key => $value) {
                $con .= "<option value='$key'>" . $value . "</option>";
            }
        }
        return $con;
    }


    public function actionAsentamientos($state, $mun)
    {

        $asentamientos = ArrayHelper::map(CatAsentamientos::find()->select(['asentamiento_id', 'nombre'])
            ->where(['and', ['entidad_id' => $state], ['municipio_id' => $mun]])->asArray()->orderBy(['nombre' => 'ASC'])->all(), 'asentamiento_id', 'nombre');

        $con = '';
        $con .= "<option value=''>Asentamiento...</option>";
        if ($asentamientos) {
            foreach ($asentamientos as $key => $value) {
                $con .= "<option value='$key'>" . $value . "</option>";
            }
        }
        return $con;

    }


    public function actionProviderProducto($id)
    {
        $prodcuto = ArrayHelper::map(Producto::find()->where(['grupo_producto_id' => $id])->all(), 'producto_id', 'nombre');
        return json_encode($prodcuto);
    }


    public function actionProviderColonia($id, $tipo)
    {
        $colonias = CatAsentamientos::findcoloniaPorcp($id);
        return json_encode($colonias);
    }


    public function actionProviderStateCityPorcp($id)
    {
        $city = CatAsentamientos::findStateandCity($id);
        return json_encode($city);
    }


    public function actionValidacionOld($id){



        $provider = Provider::findOne(['provider_id' => $id]);


        return $this->renderAjax('detalle-consulta-permisos', [
            'nombre_provider' => $nombre_provider,
            'provider_data' => $provider,
            'rfc_data' => $rfc_data,
            'acta_data' => $acta_data,
            'mod_acta_data' => $mod_acta_data,
            'model_acta_constitutiva' => $acta_constitutiva,
            'representante_data' => $representante_data,
            'accionistas_data' => $accionistas_data,
            'actividades_data' => $actividades_data,
            'productos_data' => $productos_data['model'],
            'modelProductosTipo' => $productos_data['tipo'],
            'ult_declaracion_data' => $ult_declaracion_data,
            'clientes_data' => $clientes_data,
            'certificados_data' => $certificados_data,
            'ubicacion_fiscal_data' => $ubicacion_fiscal_data,
            'ubicacion_nl_data' => $ubicacion_nl_data,
            'ubicaciones_data' => $ubicaciones_data,
            "ubicaciones_arr" => $array_ubicaciones,
            'pago_data' => $pago_data,
            'curp_data' => $curp_data,
            'idOf_data' => $idOf_data,
            'cartas_data' => $dataProviderCarta,
            'modulos_permisos' => $modulos_permisos,
        ]);





    }

    public static function getLocation($street, $num, $suburb, $zip, $city, $state)
    {
        //$zip = '';
        $address = $street . ' ' . $num . ' ' . $suburb . ' ' . $zip . ' ' . $city . ', ' . $state;
        $dir = urlencode($address);
        $url = "https://maps-api-ssl.google.com/maps/api/geocode/json?address=$dir&sensor=false&key=AIzaSyAxWfwRmQG4PfsVQfD96np1SZMqklYIo2k";

        $curl = curl_init();
        curl_setopt($curl, CURLOPT_URL, $url);
        curl_setopt($curl, CURLOPT_HEADER, false);
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($curl, CURLOPT_ENCODING, "");
        $curlData = curl_exec($curl);
        curl_close($curl);

        $address = json_decode($curlData, true);
        $location = [];
        if ($address && isset($address['results'][0]['geometry']['location'])) {
            $location = [$address['results'][0]['geometry']['location']['lat'], $address['results'][0]['geometry']['location']['lng']];
        }
        return $location;
    }


    public static function getStateName($state)
    {
        return CatEntidades::find()->select('nombre')->where(['entidad_id' => $state])->asArray()->one();
    }

    public function findModelAct($id)
    {
        $model = Giro::find()->where(['and',['provider_id' => $id, 'active' => true]])->all();
        if ($model == null || empty($model)) {
            $model = [new Giro()];
        }
        return $model;
    }

    public function findModelActConsulta($id)
    {
        $modelHistory = Historico::find()->where(['and',['modelo' => 'Giro'],['provider_id' => $id]])->orderBy(['fecha_validacion' => SORT_DESC])->limit(1)->one();

        if(isset($modelHistory['data']) && !empty($modelHistory['data'])){
            $model_data = $modelHistory['data'];
            $model = [];
            foreach ($model_data as $v){
                $model[] = new Giro($v);
            }
            Model::loadMultiple($model, $model_data);
        }else{
            $model = [new Giro()];
        }

        return $model;
    }


    public function findModelUbicacion($id)
    {
        $stutus = Yii::$app->user->can(Usuarios::ROLE_USUARIO_CONSULTA_BYS) || Yii::$app->user->can(Usuarios::ROLE_ADMIN_PROVIDER) || Yii::$app->user->can(Usuarios::ROLE_VALIDADOR_PROVEEDORES) ? 'bys' : 'op';
        $model = Ubicacion::find()->where(['and', ['provider_id' => $id], ['status_' . $stutus => 'VALIDADO'], ['activo' => true]])->all();
        if ($model == null || empty($model)) {
            $model = [new Ubicacion()];
        }
        return $model;
    }

    public function findModelUbicacionAuditor($id)
    {
        $model = Ubicacion::find()->where(['and', ['provider_id' => $id], ['activo' => true]])->all();
        if ($model == null || empty($model)) {
            $model = [new Ubicacion()];
        }
        return $model;
    }


    public function findModelFotografia($id)
    {
        $stutus = Yii::$app->user->can(Usuarios::ROLE_USUARIO_CONSULTA_BYS) || Yii::$app->user->can(Usuarios::ROLE_ADMIN_PROVIDER) || Yii::$app->user->can(Usuarios::ROLE_VALIDADOR_PROVEEDORES) ? 'bys' : 'op';
        $model = FotografiaNegocio::find()->where(['and', ['provider_id' => $id], ['status_' . $stutus => 'VALIDADO'], ['activo' => true]])->all();
        if ($model == null || empty($model)) {
            $model = [new FotografiaNegocio()];
        }
        return $model;
    }

    public function findModelFotografiaAuditor($id)
    {
        $model = FotografiaNegocio::find()->where(['and', ['provider_id' => $id], ['activo' => true]])->all();
        if ($model == null || empty($model)) {
            $model = [new FotografiaNegocio()];
        }
        return $model;
    }

    public function findModelClientesContratos($id,$bys=false)
    {
        $stutus = Yii::$app->user->can(Usuarios::ROLE_USUARIO_CONSULTA_BYS) || Yii::$app->user->can(Usuarios::ROLE_ADMIN_PROVIDER) || Yii::$app->user->can(Usuarios::ROLE_VALIDADOR_PROVEEDORES) ? 'bys' : 'op';

        $model = ClientesContratos::find()->where(['and', ['provider_id' => $id], ['status_' . $stutus => 'VALIDADO'], ['activo' => true]])->all();

        if($bys)
        $model = ClientesContratos::find()->where(['and', ['provider_id' => $id], ['activo' => true], ['tipo' => 'bys']])->all();
        if ($model == null || empty($model)) {
            $model = [new ClientesContratos()];
        }
        return $model;
    }


    public function findModelClientesContratosAuditor($id)
    {
        $model = ClientesContratos::find()->where(['and', ['provider_id' => $id], ['activo' => true]])->all();
        if ($model == null || empty($model)) {
            $model = [new ClientesContratos()];
        }
        return $model;
    }


    public function findModelExperiencia($id)
    {

        $model = Experiencia::find()->where(['and', ['provider_id' => $id], ['status_op' => 'VALIDADO'], ['activo' => true]])->all();
        if ($model == null || empty($model)) {
            $model = [new Experiencia()];
        }
        return $model;
    }

    public function findModelTecnicos($id)
    {

        $model = PersonalTecnico::find()->where(['and', ['provider_id' => $id], ['status_op' => 'VALIDADO'], ['activo' => true]])->all();
        if ($model == null || empty($model)) {
            $model = [new PersonalTecnico()];
        }
        return $model;
    }

    public function findModelMaquinaria($id)
    {
        $model = MaquinariaEquipos::find()->where(['and', ['provider_id' => $id], ['status_op' => 'VALIDADO'], ['activo' => true]])->all();

        if ($model == null || empty($model)) {
            $model = [new MaquinariaEquipos()];
        }
        return $model;
    }


    protected function findModelEF($id)
    {
        $model = EstadoFinanciero::find()->where(['provider_id' => $id])->one();
        if (!$model) {
            $model = new EstadoFinanciero();
        }
        return $model;
    }

    protected function findModelDA($id)
    {
        $model = DeclaracionIsr::find()->where(['provider_id' => $id])->one();
        if (!$model) {
            $model = new DeclaracionIsr();
        }
        return $model;
    }


    protected function findModelCC($id)
    {
        $model = CapacidadContratacion::find()->where(['provider_id' => $id])->one();
        if (!$model) {
            $model = new CapacidadContratacion();
        }
        return $model;
    }


    public function findModelCertificacion($id,$bys=false)
    {
        $model = Certificacion::find()->where(['and', ['provider_id' => $id], ['status_bys' => 'VALIDADO'], ['activo' => true]])->all();
        if($bys)

            $model = Certificacion::find()->where(['and', ['provider_id' => $id], ['activo' => true]])->all();
        if ($model == null || empty($model)) {
            $model = [new Certificacion()];
        }
        return $model;
    }

    public function findModelCertificacionAuditor($id)
    {
        $model = Certificacion::find()->where(['and', ['provider_id' => $id], ['activo' => true]])->all();
        if ($model == null || empty($model)) {
            $model = [new Certificacion()];
        }
        return $model;
    }

    public function findModelAccionistas($id, $bys=false)
    {
        $stutus = Yii::$app->user->can(Usuarios::ROLE_USUARIO_CONSULTA_BYS) || Yii::$app->user->can(Usuarios::ROLE_ADMIN_PROVIDER) || Yii::$app->user->can(Usuarios::ROLE_VALIDADOR_PROVEEDORES) ? 'bys' : 'op';

        $model = RelacionAccionistas::find()->where(['and', ['provider_id' => $id], ['status_' . $stutus => 'VALIDADO'], ['activo' => true]])->all();

        if($bys)
            $model = RelacionAccionistas::find()->where(['and', ['provider_id' => $id], ['activo' => true]])->all();


        if ($model == null || empty($model)) {
            $model = [new RelacionAccionistas()];
        }
        return $model;
    }

    public function findModelAccionistasAuditor($id)
    {

        $model = RelacionAccionistas::find()->where(['and', ['provider_id' => $id], ['activo' => true]])->all();
        if ($model == null || empty($model)) {
            $model = [new RelacionAccionistas()];
        }
        return $model;
    }

    public function findModelModificacionActa($id,$bys=false)
    {
        $stutus = Yii::$app->user->can(Usuarios::ROLE_USUARIO_CONSULTA_BYS) ||Yii::$app->user->can(Usuarios::ROLE_ADMIN_PROVIDER) || Yii::$app->user->can(Usuarios::ROLE_VALIDADOR_PROVEEDORES) ? 'bys' : 'op';

        $model = ModificacionActa::find()->where(['and', ['provider_id' => $id], ['status_' . $stutus => 'VALIDADO'], ['activo' => true]])->orderBy(['fecha_acta' => SORT_DESC])->all();
        if($bys)

            $model = ModificacionActa::find()->where(['and', ['provider_id' => $id],  ['activo' => true]])->orderBy(['fecha_acta' => SORT_DESC])->all();
        if ($model == null || empty($model)) {
            $model = [new ModificacionActa()];
        }

        return $model;
    }


    public function findModelModificacionActaAuditor($id)
    {

        $model = ModificacionActa::find()->where(['and', ['provider_id' => $id], ['activo' => true]])->all();
        if ($model == null || empty($model)) {
            $model = [new ModificacionActa()];
        }

        return $model;
    }

    public function findModelRegistroActas($id)
    {
        $model = ModificacionActa::find()->where(['provider_id' => $id])->one();
        if ($model == null || empty($model)) {
            $model = new ModificacionActa();
        }
        return $model;
    }

    public function findModelRepresentanteLegal($id,$bys=false)
    {
        $stutus = Yii::$app->user->can(Usuarios::ROLE_USUARIO_CONSULTA_BYS) || Yii::$app->user->can(Usuarios::ROLE_ADMIN_PROVIDER) || Yii::$app->user->can(Usuarios::ROLE_VALIDADOR_PROVEEDORES) ? 'bys' : 'op';

        $model = RepresentanteLegal::find()->where(['and', ['provider_id' => $id], ['activo' => true], ['status_' . $stutus => 'VALIDADO']])->all();

        if($bys)
            $model = RepresentanteLegal::find()->where(['and', ['provider_id' => $id], ['activo' => true]])->all();

        if ($model == null || empty($model)) {
            $model = [new RepresentanteLegal()];
        }
        return $model;
    }

    public function findModelRepresentanteLegalAuditor($id)
    {

        $model = RepresentanteLegal::find()->where(['and', ['provider_id' => $id], ['activo' => true]])->all();
        if ($model == null || empty($model)) {
            $model = [new RepresentanteLegal()];
        }
        return $model;
    }

    public function findModelRepresentanteLegalCon($id)
    {
        $stutus = Yii::$app->user->can(Usuarios::ROLE_ADMIN_PROVIDER) || Yii::$app->user->can(Usuarios::ROLE_VALIDADOR_PROVEEDORES) ? 'bys' : 'op';

        $model = RepresentanteLegal::find()->where(['and', ['provider_id' => $id], ['activo' => true]])->all();
        if ($model == null || empty($model)) {
            $model = [new RepresentanteLegal()];
        }
        return $model;
    }

    public function actionIndexDetalleProvider()
    {
        $this->layout = 'home';
        $searchModel = new ProviderSearch();

        $dataProvider = $searchModel->search(Yii::$app->request->queryParams, 'consultaFilter');
        return $this->render('index-detalle-provider', [
            'searchModel' => $searchModel,
            'dataProvider' => $dataProvider,
        ]);
    }

    public function actionIndexDetalleProviderPermiso(){
        $this->layout = 'home';

        self::setTempHistoricoProviderGiro('bys');

        $params = Yii::$app->request->queryParams;
        $data = ProviderQuery::getAllDataConsulta($params);

        $user_id = Yii::$app->user->identity->getId();

        $acuerdo = UsuariosAcuerdos::obtenerAcuerdo($user_id, 'provider/index-detalle-provider-permiso', UsuariosAcuerdos::TYPE_AVISO_PRIVACIDAD);

        return $this->render('index-detalle-provider-permiso', [
            'dataProvider' => $data,
            'acuerdo' => $acuerdo
        ]);
    }

    public function findModelHistorico($id, $modelo)
    {
        $date = Yii::$app->db->createCommand("select fecha_validacion::date as fecha from provider.historico
            where provider_id =  :id and modelo = :mod order by fecha_validacion DESC limit 1", [':id' => $id, ':mod' => $modelo])->queryOne()['fecha'];

        $model = Yii::$app->db->createCommand("select data, fecha_validacion::date as fecha from provider.historico where provider_id = :id
          and modelo = :mod and fecha_validacion::date< :date order by fecha_validacion DESC", [':id' => $id, ':mod' => $modelo, ':date' => $date])->queryAll();

        if ($model == null || empty($model)) {
            $model = [];
        }
        return $model;
    }


    public function actionDetalleProvider($user_id)
    {
        if (!Yii::$app->user->can(Usuarios::ROLE_USUARIO_CONSULTA_BYS) && !Yii::$app->user->can(Usuarios::ROLE_ADMIN_CONCURSOS)
            && !Yii::$app->user->can(Usuarios::ROLE_ADMIN_PROVIDER)) {
            $this->msgError();
        }
        $provider = \app\models\Provider::find()->where(['user_id' => $user_id])->one();
        $id = $provider->provider_id;

        $tipo_persona = $provider->tipo_persona;
        $tipo_provider = $provider->tipo_provider;
        $model = \app\models\Rfc::find()->where(['provider_id' => $id])->one();
        $representante = $this->findModelRepresentanteLegal($id);

        $comprobante_domicilio = \app\models\ComprobanteDomicilio::find()->select('provider_id')->where(['provider_id' => $id])->one();
        $curp = \app\models\Curp::find()->where(['provider_id' => $id])->one();
        $idoficial = \app\models\IdOficial::find()->where(['provider_id' => $id])->one();
        $metodo_pago = \app\models\IntervencionBancaria::find()->where(['provider_id' => $id])->one();
        $ultima_declaracion = \app\models\UltimaDeclaracion::find()->where(['provider_id' => $id])->one();
        $model_ubicacionNl = Ubicacion::find()->where(['and', ['provider_id' => $id], ['status_' . $tipo_provider => 'VALIDADO'], ['type_address_prov' => 'NL']])->one();

        $acta_constitutiva = $this->findModelActaConstitutiva($id);

        return $this->renderAjax('detalle-provider', [
            'id' => $id,
            'tipo_persona' => $tipo_persona,
            'model' => $model,
            'representante' => $representante,
            'model_curp' => $curp,
            'model_comprobante_domicilio' => $comprobante_domicilio,
            'model_idoficial' => $idoficial,
            'model_act' => $this->findModelAct($id),
            'model_registro_acta' => $this->findModelRegistroActas($id),
            'model_mpago' => $metodo_pago,
            'fotografia' => $this->findModelFotografia($id),
            'model_ubicacion' => $this->findModelUbicacion($id),
            'clientes_contratos' => $this->findModelClientesContratos($id),
            'certificacion' => $this->findModelCertificacion($id),
            'accionistas' => $this->findModelAccionistas($id),
            'ultima_declaracion' => $ultima_declaracion,
            'modelProductos' => $this->findModelProviderGiro($id),
            'mod_acta_constitutiva' => $acta_constitutiva,
            'tipo_provider' => 'rep_' . $tipo_provider,
            'model_ubicacionNl' => $model_ubicacionNl,
            'modActa' => $this->findModelModificacionActa($id),
        ]);
    }


    public function actionDetalleConsultaPermisos($user_id, $tipo = 'bys'){
            
        if (!Yii::$app->user->can(Usuarios::ROLE_USUARIO_CONSULTA_BYS) && !Yii::$app->user->can(Usuarios::ROLE_USUARIO_CONSULTA_OP) ) { $this->msgError(); }
        $permisos = Yii::$app->db->createCommand("
            SELECT p.* FROM permission_view_module p join permission_view_module_user pu on pu.permission_view_module_id = p.permission_view_module_id
            where pu.user_id = :id", [':id' => Yii::$app->user->getId()])->queryAll();
        
        /* if($permisos){ */
            $provider = Provider::findOne(['user_id' => $user_id]);
            $provider_id = $provider->provider_id;
            $tipo_persona = $provider->tipo_persona;
            $tipo_provider = $provider->tipo_provider;

            $modulos_permisos = array_unique(ArrayHelper::getColumn($permisos,'module'));

            $parametros_tipo_fiscal = $tipo == 'bys' ? [["type_address_prov" => "DOMICILIO FISCAL"]] : [["status_op" => STATUS::STATUS_VALIDADO], ["type_address_prov" => "DOMICILIO FISCAL"]];
            $parametros_nl = $tipo == 'bys' ? [['type_address_prov' => 'NL']] : [["status_op" => STATUS::STATUS_VALIDADO], ['type_address_prov' => 'NL']];

            $nombre_provider = $this->getNameProvider($provider_id);
            $curp_data = $this->findModelCurp($provider_id);
            $idOf_data = $this->findModelIdoficial($provider_id);
            $rfc_data = $this->findRfc($provider_id);
            $actividades_data = $this->findModelAct($provider_id);
            $acta_data = $this->findModelActaConstitutiva($provider_id);

            $representante_data = $this->findModelRepresentanteLegal($provider_id,true); //VALIDAR PARA OP, ya que trae todos
            $accionistas_data = $this->findModelAccionistas($provider_id,true); //VALIDAR PARA OP, ya que trae todos
            $mod_acta_data = $this->findModelModificacionActa($provider_id,true); //VALIDAR PARA OP, ya que trae todos
            $acta_constitutiva = $this->findModelActaConstitutiva($provider_id);

            $pago_data = $this->findDatosBancarios($provider_id);
            $ubicacion_nl_data = UbicacionController::findModelDireccionFotografia($provider_id, $parametros_nl);
            $ubicaciones_data = UbicacionController::findDireccionesArray($provider_id, 'bys');

            $searchModelCarta = new \app\models\HistoricoCartaProtestaSearch();
            $searchModelCarta->provider_id = $provider->provider_id;
            $dataProviderCarta = $searchModelCarta->search(Yii::$app->request->queryParams,'consultaCartas');

            $searchModelCertificado = new \app\models\HistoricoCertificadosSearch();
            $searchModelCertificado->provider_id = $provider->provider_id;

            if($tipo == 'bys'){
                $productos_data = $this->findModelProviderGiroAud($provider_id);
                $ult_declaracion_data = UltimaDeclaracion::find()->where(['provider_id' => $provider_id])->one();
                $ult_declaracion_data = ($ult_declaracion_data == null || empty($ult_declaracion_data)) ? new UltimaDeclaracion() : $ult_declaracion_data;
                $clientes_data = $this->findModelClientesContratos($provider_id,true);
                $certificados_data = $this->findModelCertificacion($provider_id,true);
                $ubicacion_fiscal_data = UbicacionController::findModelDireccionFotografia($provider_id, $parametros_tipo_fiscal, true);
                $array_ubicaciones = UbicacionController::getArrayUbicaciones($provider_id);
                //$cartas_data = HistoricoCartaProtesta::find()->where(['and', ['provider_id' => $provider_id], ['is not','firma_bys',null]])->all();

                
                $dataProviderCertificado = $searchModelCertificado->search(Yii::$app->request->queryParams, 'validCertificados');
                $dataProviderCursos = $searchModelCertificado->search(Yii::$app->request->queryParams, 'validCursos');
                return $this->renderAjax('detalle-consulta-permisos', [
                    'nombre_provider' => $nombre_provider, 
                    'provider_data' => $provider,
                    'rfc_data' => $rfc_data,
                    'acta_data' => $acta_data,
                    'mod_acta_data' => $mod_acta_data,
                    'model_acta_constitutiva' => $acta_constitutiva,
                    'representante_data' => $representante_data,
                    'accionistas_data' => $accionistas_data,
                    'actividades_data' => $actividades_data,
                    'productos_data' => $productos_data['model'],
                    'modelProductosTipo' => $productos_data['tipo'],
                    'ult_declaracion_data' => $ult_declaracion_data,
                    'clientes_data' => $clientes_data,
                    'certificados_data' => $certificados_data,
                    'ubicacion_fiscal_data' => $ubicacion_fiscal_data,
                    'ubicacion_nl_data' => $ubicacion_nl_data,
                    'ubicaciones_data' => $ubicaciones_data,
                    "ubicaciones_arr" => $array_ubicaciones,
                    'pago_data' => $pago_data,
                    'curp_data' => $curp_data,
                    'idOf_data' => $idOf_data,
                    'cartas_data' => $dataProviderCarta,
                    'historico_cursos' => $dataProviderCursos,
                    'historico_certificados_data' => $dataProviderCertificado,
                    'modulos_permisos' => $modulos_permisos,
                ]);
            }else if($tipo == 'op'){
                $dom_fiscal_data = $this->findModelUbicacionProv($provider_id);
                $comprobante_data = $this->findModelComprobanteDomicilio($provider_id);
                $curriculum_data = $this->findModelCurriculum($provider_id);
                $referencias_data = $this->findReferenciasOp($provider_id);
                $imss_data = $this->findModelRegistroImss($provider_id);
                $propiedad_data = $this->findModelEscrituraPublica($provider_id);
                $registro_data = $this->findModelRegistroPublico($provider_id);
                $fotografias_data = $this->findFotografiasOp($provider_id);
                $organigrama_data = $this->findModelOrganigrama($provider_id);
                $personal_tec_data = $this->findPersonalTecnico($provider_id);
                $maquinaria_data = $this->findMaquinarias($provider_id);
                $experiencia_data = $this->findExperiencia($provider_id);
                $estado_financiero_data = $this->findModelEF($provider_id);
                $declaracion_isr_data = $this->findModelDA($provider_id);
                $capacidad_contratacion_data = $this->findModelCC($provider_id);
                $ubicaciones_data =  $this->findModelUbicacion($provider_id);
                $model_ubicacionNl = Ubicacion::find()->where(['and', ['provider_id' => $provider_id], ['status_op' => 'VALIDADO'], ['type_address_prov' => 'NL']])->one();

                $dataProviderCertificado = $searchModelCertificado->search(Yii::$app->request->queryParams, 'validCertificadosConsultaOp');
                $dataProviderCursos = $searchModelCertificado->search(Yii::$app->request->queryParams, 'validCursosOp');

                $searchVisitas = new VisitSearch();
                $dataVisitas = $searchVisitas->getConsulta($provider_id);
                
                return $this->renderAjax('detalle-consulta-permisos-op', [ 
                    'nombre_provider' => $nombre_provider, 
                    'provider_data' => $provider,
                    'dom_fiscal_data' => $dom_fiscal_data,
                    'comprobante_data' => $comprobante_data,
                    'curriculum_data' => $curriculum_data,
                    'referencias_data' => $referencias_data,
                    'curp_data' => $curp_data,
                    'idOf_data' => $idOf_data,
                    'rfc_data' => $rfc_data,
                    'actividades_data' => $actividades_data,
                    'model_acta_constitutiva' => $acta_constitutiva,
                    'acta_data' => $acta_data,
                    'imss_data' => $imss_data,
                    'propiedad_data' => $propiedad_data,
                    'registro_data' => $registro_data,
                    'representante_data' => $representante_data,
                    'accionistas_data' => $accionistas_data,
                    'mod_acta_data' => $mod_acta_data,
                    'pago_data' => $pago_data,
                    'ubicacion_nl_data' => $model_ubicacionNl,
                    'ubicaciones_data' => $ubicaciones_data,
                    'fotografias_data' => $fotografias_data,
                    'organigrama_data' => $organigrama_data,
                    'personal_tec_data' => $personal_tec_data,
                    'maquinaria_data' => $maquinaria_data,
                    'experiencia_data' => $experiencia_data,
                    'estado_financiero_data' => $estado_financiero_data,
                    'declaracion_isr_data' => $declaracion_isr_data,
                    'capacidad_contratacion_data' => $capacidad_contratacion_data,
                    'cartas_data' => $dataProviderCarta,
                    'historico_certificados_data' => $dataProviderCertificado,
                    'historico_visitas_data' => $dataVisitas,
                    'historico_cursos' => $dataProviderCursos,
                    'modulos_permisos' => $modulos_permisos,
                ]);
            }

    }

     //Utiliza la misma vista de permisos pero con la ultima validacion enviada en la tabla provider.historico
     public function actionDetalleConsultaPermisosHistorico($user_id, $tipo = 'bys'){
        if (!Yii::$app->user->can(Usuarios::ROLE_USUARIO_CONSULTA_BYS) 
        && !Yii::$app->user->can(Usuarios::ROLE_USUARIO_CONSULTA_OP)
        && !Yii::$app->user->can(Usuarios::ROLE_ADMIN_PROVIDER)
        && !Yii::$app->user->can(Usuarios::ROLE_ADMIN_CONCURSOS)
        && !Yii::$app->user->can(Usuarios::ROLE_ADMIN_CONTRATISTAS)) { $this->msgError(); }

        $permisos = Yii::$app->db->createCommand("
            SELECT p.* FROM permission_view_module p join permission_view_module_user pu on pu.permission_view_module_id = p.permission_view_module_id
            where pu.user_id = :id", [':id' => Yii::$app->user->getId()])->queryAll();

        if(Yii::$app->user->can(Usuarios::ROLE_ADMIN_PROVIDER) || Yii::$app->user->can(Usuarios::ROLE_ADMIN_CONTRATISTAS) 
            || Yii::$app->user->can(Usuarios::ROLE_ADMIN_CONCURSOS) ){ 
            $permisos = Yii::$app->db->createCommand("SELECT distinct(module) FROM permission_view_module")->queryAll();
        }
        
        $provider = Provider::findOne(['user_id' => $user_id]);
        $provider_id = $provider->provider_id;

        $modulos_permisos = array_unique(ArrayHelper::getColumn($permisos,'module'));

        $nombre_provider = $this->getNameProvider($provider_id);

        $searchModelCarta = new \app\models\HistoricoCartaProtestaSearch();
        $searchModelCarta->provider_id = $provider->provider_id;
        $dataProviderCarta = $searchModelCarta->search(Yii::$app->request->queryParams,'consultaCartas');

        $searchModelCertificado = new \app\models\HistoricoCertificadosSearch();
        $searchModelCertificado->provider_id = $provider->provider_id;


        if($tipo == 'bys'){
            $dataProviderCertificado = $searchModelCertificado->search(Yii::$app->request->queryParams, 'validCertificados');
            $dataProviderCursos = $searchModelCertificado->search(Yii::$app->request->queryParams, 'validCursos');
            
            return $this->renderAjax('detalle-consulta-permisos', [
                'perfil'=>Perfil::find()->where(['provider_id'=>$provider_id])->one(),
                'nombre_provider' => $nombre_provider, 
                'provider_data' => $provider,
                'rfc_data' => self::getHistoricoModelo('Rfc', $provider_id),
                'acta_data' => self::getHistoricoModelo('ActaConstitutiva', $provider_id),
                'mod_acta_data' => self::getArrayHistorico('ModificacionActa', $provider_id), //self::getHistoricoModelo('ModificacionActa', $provider_id, true),
                'model_acta_constitutiva' => self::getHistoricoModelo('ActaConstitutiva', $provider_id),
                'representante_data' => self::getArrayHistorico('RepresentanteLegal', $provider_id), //self::getHistoricoModelo('RepresentanteLegal', $provider_id, true),
                'accionistas_data' => self::getArrayHistorico('RelacionAccionistas', $provider_id), //self::getHistoricoModelo('RelacionAccionistas', $provider_id, true),
                'actividades_data' => self::getHistoricoGiro($provider_id),
                'productos_data' => self::getHistoricoProviderGiro($provider_id),
                'ult_declaracion_data' => self::getHistoricoModelo('UltimaDeclaracion', $provider_id),
                'clientes_data' => self::getArrayHistorico('ClientesContratos', $provider_id), //self::getHistoricoModelo('ClientesContratos', $provider_id, true),
                'certificados_data' => self::getArrayHistorico('Certificacion', $provider_id), //self::getHistoricoModelo('Certificacion', $provider_id, true),
                'ubicacion_fiscal_data' => self::getUbicacionUnicaHistorico($provider_id, 'DOMICILIO FISCAL', true),
                'ubicacion_nl_data' => self::getUbicacionUnicaHistorico($provider_id, 'DIRECCIÓN NUEVO LEÓN'),
                'ubicaciones_data' => self::getUbicacionesHistorico($provider_id),
                'ubicaciones_arr' => self::getArrayHistoricoCordUbicaciones($provider_id),
                'pago_data' => self::getHistoricoModelo('IntervencionBancaria', $provider_id),
                'curp_data' => self::getHistoricoModelo('Curp', $provider_id),
                'idOf_data' => self::getHistoricoModelo('IdOficial', $provider_id),
                'cartas_data' => $dataProviderCarta,
                'historico_cursos' => $dataProviderCursos,
                'historico_certificados_data' => $dataProviderCertificado,
                'modulos_permisos' => $modulos_permisos,
                'type' => ''
            ]);
        }
        if($tipo == 'op'){
            $dataProviderCertificado = $searchModelCertificado->search(Yii::$app->request->queryParams, 'validCertificadosConsultaOp');
            $dataProviderCursos = $searchModelCertificado->search(Yii::$app->request->queryParams, 'validCursosOp');

            $searchVisitas = new VisitSearch();
            $dataVisitas = $searchVisitas->getConsulta($provider_id);
            $modelDT = EstadoFinancieroTipo::find()->where(['provider_id' => $provider_id])->one();

            return $this->renderAjax('detalle-consulta-permisos-op', [ 
                'nombre_provider' => $nombre_provider, 
                'provider_data' => $provider,
                'comprobante_data' => self::getHistoricoModelo('ComprobanteDomicilio', $provider_id, false, 'op'),
                'referencias_data' => self::getArrayHistorico('ClientesContratos', $provider_id, 'op'), //self::getHistoricoModelo('ClientesContratos', $provider_id, true, 'op'),
                'curp_data' => self::getHistoricoModelo('Curp', $provider_id, false, 'op'),
                'idOf_data' => self::getHistoricoModelo('IdOficial', $provider_id, false, 'op'),
                'rfc_data' => self::getHistoricoModelo('Rfc', $provider_id, false, 'op'),
                'actividades_data' => self::getHistoricoGiro($provider_id, 'op'),
                'model_acta_constitutiva' => self::getHistoricoModelo('ActaConstitutiva', $provider_id, false, 'op'),
                /* 'acta_data' => self::getHistoricoModelo('ActaConstitutiva', $provider_id, false, 'op'), */
                'imss_data' => self::getHistoricoModelo('RegistroImss', $provider_id, false, 'op'),
                'propiedad_data' => self::getHistoricoModelo('EscrituraPublica', $provider_id, false, 'op'),
                'registro_data' => self::getHistoricoModelo('RegistroPublicoPropiedad', $provider_id, false, 'op'),
                'representante_data' => self::getArrayHistorico('RepresentanteLegal', $provider_id, 'op'), //self::getHistoricoModelo('RepresentanteLegal', $provider_id, true, 'op'),
                'accionistas_data' => self::getArrayHistorico('RelacionAccionistas', $provider_id, 'op'), //self::getHistoricoModelo('RelacionAccionistas', $provider_id, true, 'op'),
                'mod_acta_data' => self::getArrayHistorico('ModificacionActa', $provider_id, 'op'), //self::getHistoricoModelo('ModificacionActa', $provider_id, true, 'op'),
                'ubicaciones_data' => self::getHistoricoModelo('Ubicacion', $provider_id, true, 'op'),
                'fotografias_data' => self::getArrayHistorico('FotografiaNegocio', $provider_id, 'op'), //self::getHistoricoModelo('FotografiaNegocio', $provider_id, true, 'op'),
                'experiencia_data' => self::getArrayHistorico('Experiencia', $provider_id, 'op'), //self::getHistoricoModelo('Experiencia', $provider_id, true, 'op'),
                'estado_financiero_data' => self::getHistoricoModelo('EstadoFinanciero', $provider_id, false, 'op'),
                'declaracion_isr_data' => self::getHistoricoModelo('DeclaracionIsr', $provider_id, false, 'op'),
                'capacidad_contratacion_data' => self::getHistoricoModelo('CapacidadContratacion', $provider_id, false, 'op'),
                'personal_tec_data' => self::getArrayHistorico('PersonalTecnico', $provider_id, 'op'), //self::getHistoricoModelo('PersonalTecnico', $provider_id, true, 'op'),
                'maquinaria_data' => self::getArrayHistorico('MaquinariaEquipos', $provider_id, 'op'), //self::getHistoricoModelo('MaquinariaEquipos', $provider_id, true, 'op'),
                'pago_data' => self::getHistoricoModelo('IntervencionBancaria', $provider_id, false, 'op'),
                'cartas_data' => $dataProviderCarta,
                'historico_certificados_data' => $dataProviderCertificado,
                'historico_visitas_data' => $dataVisitas,
                'historico_cursos' => $dataProviderCursos,
                'modulos_permisos' => $modulos_permisos,
                'type' => isset($modelDT->type)?$modelDT->type:''
            ]);
        }

    }

    public function actionIndexDetalleProviderPermisoOp(){
        $this->layout = 'home';
        $searchModel = new ProviderSearch();
        $searchModel->auditoria_id = Yii::$app->user->getId();
        $dataProvider = $searchModel->search(Yii::$app->request->queryParams, 'AsignadosCon');

        $searchModelVisit = new VisitSearch();
        $searchModelVisit->statusV = false;
        $dataProviderVisit = $searchModelVisit->search(Yii::$app->request->queryParams);

        $user_id = Yii::$app->user->identity->getId();

        $acuerdo = UsuariosAcuerdos::obtenerAcuerdo($user_id, 'provider/index-detalle-provider-permiso-op', UsuariosAcuerdos::TYPE_AVISO_PRIVACIDAD );


        return $this->render('index-detalle-provider-permiso-op', [
            'searchModel' => $searchModel,
            'dataProvider' => $dataProvider,
            'dataProviderVisit' => $dataProviderVisit,
            'searchModelVisit' => $searchModelVisit,
            'acuerdo' => $acuerdo
        ]);
    }


    public function actionDetalleConsultaPermisosOp($user_id){
        if (Yii::$app->request->isAjax) {
            if (!Yii::$app->user->can(Usuarios::ROLE_USUARIO_CONSULTA_OP)) {
                $this->msgError();
            }

            $permisosUser = Yii::$app->db->createCommand("select p.* from permission_view_module p
                    join permission_view_module_user pu on pu.permission_view_module_id = p.permission_view_module_id
                    where pu.user_id = :id",[':id' => Yii::$app->user->getId()])->queryAll();

            $val = ArrayHelper::getColumn(Usuarios::find()->select('user_id')->where(['in','role',['VALIDADOR CONTRATISTAS','ADMIN CONTRATISTAS']])->all(),'user_id');

            if($permisosUser){

                $provider = \app\models\Provider::find()->where(['user_id' => $user_id])->one();
                $id = $provider->provider_id;
                $id = $provider->provider_id;
                $tipo_persona = $provider->tipo_persona;
                $tipo_provider = $provider->tipo_provider;

                $permissions_view = array_unique(ArrayHelper::getColumn($permisosUser,'permission'));

                $perfilData = [];

                $model_comprobante_domicilio = [];

                if(in_array('Perfil',$permissions_view)){
                    $perfilData = $provider;
                    $model_comprobante_domicilio = $this->findModelComprobanteDomicilioConsulta($id,$val);
                }

                $clientes_contratos = [];
                if(in_array('Referencias comerciales',$permissions_view)){
                    $clientes_contratos = $this->findModelClientesContratos($id);

                }

                //actividad economica

                $model = [];
                $model_act = [];
                $modelProductos = [];

                if(in_array('Actividad Económica',$permissions_view)){
                    $modelHistory = Historico::find()->where(['and',['modelo' => 'Rfc'],['provider_id' => $id],['in','validador_id',$val]])->orderBy(['fecha_validacion' => SORT_DESC])->limit(1)->one();

                    if(isset($modelHistory['data']) && !empty($modelHistory['data'])){
                        $model_Rfc = $modelHistory['data'];
                        $model = new Rfc();
                        $model->load($model_Rfc, '');
                    }else{
                        $model = new Rfc();
                    }
                    //$model = \app\models\Rfc::find()->where(['provider_id' => $id])->one();
                    $model_act = $this->findModelActConsulta($id);
                }

                //legales

                $model_curp = [];
                $model_idoficial = [];
                $acta_constitutiva = [];

                $models = [];
                if(in_array('Legales',$permissions_view)){
                    $model_curp = $this->findModelCurpConsulta($id,$val);
                    $model_idoficial = $this->findModelIdoficialConsulta($id,$val);
                    $acta_constitutiva = $this->findModelActaConstitutivaConsulta($id,$val);

                    $models = (Object)[
                        'imss' => $this->findModelRegistroImssConsulta($id),
                        'escritura' => $this->findModelEscrituraPublicaConsulta($id),
                        'registro' => $this->findModelRegistroPublicoConsulta($id),
                    ];

                }

                $modActa = [];
                if(in_array('Modificación de Acta',$permissions_view)){
                    $modActa = $this->findModelModificacionActa($id);
                }

                $representante = [];
                if(in_array('Representante Legal',$permissions_view)){
                    $representante = $this->findModelRepresentanteLegal($id);
                }

                $accionistas = [];
                if(in_array('Relación accionistas',$permissions_view)){
                    $accionistas = $this->findModelAccionistas($id);
                }


                //ubicacion

                $model_ubicacionNl = [];
                $model_ubicacion = [];
                if(in_array('Ubicación',$permissions_view)){

                    //$model_ubicacionNl = Ubicacion::find()->where(['and', ['provider_id' => $id], ['status_' . $tipo_provider => 'VALIDADO'], ['type_address_prov' => 'NL']])->one();
                    $modelHistory = Historico::find()->where(['and',['modelo' => 'DireccionNl'],['provider_id' => $id],['in','validador_id',$val]])->orderBy(['fecha_validacion' => SORT_DESC])->limit(1)->one();

                    if(isset($modelHistory['data']) && !empty($modelHistory['data'])){
                        $model_ubinl = $modelHistory['data'];
                        $model_ubicacionNl = new Ubicacion();
                        $model_ubicacionNl->load($model_ubinl, '');
                    }else{
                        $model_ubicacionNl = new Ubicacion();
                    }

                    $model_ubicacion = $this->findModelUbicacion($id);
                }

                //fotografia

                $fotografia = [];
                if(in_array('Fotografía de Negocio',$permissions_view)){
                    $fotografia = $this->findModelFotografia($id);
                }

                //Permisos-Certificados

                $certificacion = [];
                if(in_array('Permisos',$permissions_view)){
                    $certificacion = $this->findModelCertificacion($id);
                }

                //financieros


                //metodo de pago

                $metodo_pago = [];
                if(in_array('Método de Pago',$permissions_view)){

                    $modelHistory = Historico::find()->where(['and',['modelo' => 'IntervencionBancaria'],['provider_id' => $id],['in','validador_id',$val]])->orderBy(['fecha_validacion' => SORT_DESC])->limit(1)->one();

                    if(isset($modelHistory['data']) && !empty($modelHistory['data'])){
                        $model_metPago = $modelHistory['data'];
                        $metodo_pago = new IntervencionBancaria();
                        $metodo_pago->load($model_metPago, '');
                    }else{
                        $metodo_pago = new IntervencionBancaria();
                    }
                    //$metodo_pago = \app\models\IntervencionBancaria::find()->where(['provider_id' => $id])->one();
                }


                //Estado Financiero


                $modelEF = [];
                $modelCC= [];
                $modelDA = [];

                if(in_array('Estado Financiero',$permissions_view)){
                    $modelEF = $this->findModelEFConsulta($id);
                    $modelCC = $this->findModelCCConsulta($id);
                    $modelDA = $this->findModelDAConsulta($id);
                }


                //experiencia
                $modelExperiencia = [];

                $registro = $this->findModelRegistroPublico($id);
                if(in_array('Experiencia',$permissions_view)){
                    $modelExperiencia = $this->findModelExperiencia($id);
                }

                if(in_array('Datos Técnicos',$permissions_view)){
                    $modelTecnicos = $this->findModelTecnicos($id);
                }

                if(in_array('Maquinaria',$permissions_view)){
                    $modelMaquinaria = $this->findModelMaquinaria($id);
                }

                return $this->renderAjax('detalle-consulta-permisos-op', [
                    'data'=>$this->getDataOP($provider->provider_id),
                    'id' => $id,
                    'tipo_persona' => $tipo_persona,
                    'model' => $model,
                    'representante' => $representante,
                    'model_comprobante_domicilio' => $model_comprobante_domicilio,
                    'model_act' => $model_act,
                    'model_mpago' => $metodo_pago,
                    'fotografia' => $fotografia,
                    'model_ubicacion' => $model_ubicacion,
                    'clientes_contratos' => $clientes_contratos,
                    'certificacion' => $certificacion,
                    'accionistas' => $accionistas,
                    'modelProductos' => $modelProductos,
                    'modActa' => $modActa,
                    'model_acta_constitutiva' => $acta_constitutiva,
                    'model_curp' => $model_curp,
                    'model_idoficial' => $model_idoficial,
                    'perfilData' => $perfilData,
                    'model_ubicacionNl' => $model_ubicacionNl,
                    'namePro' => $this->getNameProvider($id),
                    'tipo_provider' => 'rep_' . $tipo_provider,
                    'permissions_view' => $permissions_view,
                    'modelEF' => $modelEF,
                    'modelCC' => $modelCC,
                    'modelDA' => $modelDA,
                    'registro' => $registro,
                    'modelExperiencia' => $modelExperiencia,
                    'modelTecnicos' => $modelTecnicos,
                    'modelMaquinaria' =>$modelMaquinaria,
                    'modelos' => $models
                ]);
            }
        }
        return $this->goHome();
    }

    protected function findModelEscrituraPublicaConsulta($id)
    {

        $modelHistory = Historico::find()->where(['and',['modelo' => 'EscrituraPublica'],['provider_id' => $id]])->orderBy(['fecha_validacion' => SORT_DESC])->limit(1)->one();

        if(isset($modelHistory['data']) && !empty($modelHistory['data'])){
            $model_curp = $modelHistory['data'];
            $model = new EscrituraPublica();
            $model->load($model_curp, '');
        }else{
            $model = new EscrituraPublica();
        }

        return $model;
    }

    protected function findModelRegistroPublicoConsulta($id)
    {

        $modelHistory = Historico::find()->where(['and',['modelo' => 'RegistroPublicoPropiedad'],['provider_id' => $id]])->orderBy(['fecha_validacion' => SORT_DESC])->limit(1)->one();

        if(isset($modelHistory['data']) && !empty($modelHistory['data'])){
            $model_curp = $modelHistory['data'];
            $model = new RegistroPublicoPropiedad();
            $model->load($model_curp, '');
        }else{
            $model = new RegistroPublicoPropiedad();
        }

        return $model;
    }

    protected function findModelRegistroImssConsulta($id)
    {


        $modelHistory = Historico::find()->where(['and',['modelo' => 'RegistroImss'],['provider_id' => $id]])->orderBy(['fecha_validacion' => SORT_DESC])->limit(1)->one();

        if(isset($modelHistory['data']) && !empty($modelHistory['data'])){
            $model_curp = $modelHistory['data'];
            $model = new RegistroImss();
            $model->load($model_curp, '');
        }else{
            $model = new RegistroImss();
        }

        return $model;
    }

    public function actionDetalleConsulta($user_id)
    {
        if (!Yii::$app->user->can(Usuarios::ROLE_USUARIO_CONSULTA_BYS) && !Yii::$app->user->can(Usuarios::ROLE_ADMIN_CONCURSOS)
            && !Yii::$app->user->can(Usuarios::ROLE_ADMIN_PROVIDER)) {
            $this->msgError();
        }

        $provider = \app\models\Provider::find()->where(['user_id' => $user_id])->one();
        $id = $provider->provider_id;
        $model = \app\models\Rfc::find()->where(['provider_id' => $id])->one();
        $comprobante_domicilio = \app\models\ComprobanteDomicilio::find()->select('provider_id')->where(['provider_id' => $id])->one();

        $tipo_persona = $provider->tipo_persona;
        $tipo_provider = $provider->tipo_provider;
        $representante = $this->findModelRepresentanteLegalCon($id);
        $idoficial = \app\models\IdOficial::find()->where(['provider_id' => $id])->one();
        $acta_constitutiva = $this->findModelActaConstitutiva($id);

        $curp = \app\models\Curp::find()->where(['provider_id' => $id])->one();
        return $this->renderAjax('detalle-consulta', [
            'id' => $id,
            'model' => $model,
            'model_comprobante_domicilio' => $comprobante_domicilio,
            'tipo_persona' => $tipo_persona,
            'representante' => $representante,
            'model_act' => $this->findModelAct($id),
            'model_registro_acta' => $this->findModelRegistroActas($id),
            'accionistas' => $this->findModelAccionistas($id),
            'mod_acta_constitutiva' => $acta_constitutiva,
            'tipo_provider' => 'rep_' . $tipo_provider,
            'modActa' => $this->findModelModificacionActa($id),
            'model_curp' => $curp,
            'model_idoficial' => $idoficial,
        ]);
    }

    public function actionDetalleAuditor($user_id, $auditor_id ,$tipo = 'bys')
    {
        if (Yii::$app->request->isAjax) {
            $user_id = base64_decode($user_id);

            $provider = Provider::findOne(['user_id' => $user_id]);
            $provider_id = $provider->provider_id;

            $parametros_tipo_fiscal = $tipo == 'bys' ? [["type_address_prov" => "DOMICILIO FISCAL"]] : [["status_op" => STATUS::STATUS_VALIDADO], ["type_address_prov" => "DOMICILIO FISCAL"]];
            $parametros_nl = $tipo == 'bys' ? [['type_address_prov' => 'NL']] : [["status_op" => STATUS::STATUS_VALIDADO], ['type_address_prov' => 'NL']];

            $nombre_provider = $this->getNameProvider($provider_id);
            $curp_data = $this->findModelCurp($provider_id);
            $idOf_data = $this->findModelIdoficial($provider_id);
            $rfc_data = $this->findRfc($provider_id);
            $actividades_data = $this->findModelAct($provider_id);
            $acta_data = $this->findModelActaConstitutiva($provider_id);

            $representante_data = $this->findModelRepresentanteLegal($provider_id,true); //VALIDAR PARA OP, ya que trae todos
            $accionistas_data = $this->findModelAccionistas($provider_id,true); //VALIDAR PARA OP, ya que trae todos
            $mod_acta_data = $this->findModelModificacionActa($provider_id,true); //VALIDAR PARA OP, ya que trae todos
            $acta_constitutiva = $this->findModelActaConstitutiva($provider_id);

            $pago_data = $this->findDatosBancarios($provider_id);
            $ubicacion_nl_data = UbicacionController::findModelDireccionFotografia($provider_id, $parametros_nl);
            $ubicaciones_data = UbicacionController::findDireccionesArray($provider_id, 'bys');

            $productos_data = $this->getDataProviderGiro($provider_id);
            $ult_declaracion_data = UltimaDeclaracion::find()->where(['provider_id' => $provider_id])->one();
            $ult_declaracion_data = ($ult_declaracion_data == null || empty($ult_declaracion_data)) ? new UltimaDeclaracion() : $ult_declaracion_data;
            $clientes_data = $this->findModelClientesContratos($provider_id,true);
            $certificados_data = $this->findModelCertificacion($provider_id,true);
            $ubicacion_fiscal_data = UbicacionController::findModelDireccionFotografia($provider_id, $parametros_tipo_fiscal, true);
            $array_ubicaciones = UbicacionController::getArrayUbicaciones($provider_id);

             return $this->renderAjax('detalle-auditor', [
                'nombre_provider' => $nombre_provider,
                'provider_data' => $provider,
                'rfc_data' => $rfc_data,
                'acta_data' => $acta_data,
                'mod_acta_data' => $mod_acta_data,
                'model_acta_constitutiva' => $acta_constitutiva,
                'representante_data' => $representante_data,
                'accionistas_data' => $accionistas_data,
                'actividades_data' => $actividades_data,
                'productos_data' => $productos_data,
                'ult_declaracion_data' => $ult_declaracion_data,
                'clientes_data' => $clientes_data,
                'certificados_data' => $certificados_data,
                'ubicacion_fiscal_data' => $ubicacion_fiscal_data,
                'ubicacion_nl_data' => $ubicacion_nl_data,
                'ubicaciones_data' => $ubicaciones_data,
                "ubicaciones_arr" => $array_ubicaciones,
                'pago_data' => $pago_data,
                'curp_data' => $curp_data,
                'idOf_data' => $idOf_data,
                'namePro' => $this->getNameProvider($provider_id),
             ]);


        } else {
            Yii::$app->session->setFlash('error', 'Acceso restringido.');
            return $this->redirect('index');
        }

    }

    public function actionConsulta($id = null, $token = null)
    {
        $this->layout = 'request';

        if (!Yii::$app->user->isGuest || ($id == null || $token == null)) {
            return $this->goHome();
        }
        $token = base64_decode($token);
        $t_id = base64_decode($id);
        $model = $this->findModelAuditoria($t_id);
        $searchModel = new ProviderSearch();
        $searchModel->auditoria_id = $t_id;
        $dataProvider = $searchModel->search(Yii::$app->request->queryParams, 'Asignados');
        $cookieVal = Yii::$app->getRequest()->getCookies()->getValue('isLogged');
        if ($model->load(Yii::$app->request->post()) or $cookieVal == 1) {

            $auditoria = Auditoria::find()->where(['auditoria_id' => $t_id])->one();
            $f_actual = date('Y-m-d');
            $f_fin = substr($auditoria->fecha_fin, 0, -9);
            if(!$auditoria->activo){
                $this->msgError("No es posible ver el registro, dado de baja.");
            }
            if ($f_actual > $f_fin) {
                $this->msgError("Actualmente no tiene permisos para ver éste apartado, la fecha del permiso expiró.");
            } else if ($model !== null || $cookieVal == 1) {

                $this->layout = 'home';

                if (!$cookieVal) {
                    $cookie = new Cookie([
                        'name' => 'isLogged',
                        'value' => 1,
                        'expire' => time() + 3600, //se da una hora para que el usuario pueda estar en el sistema
                    ]);
                    Yii::$app->getResponse()->getCookies()->add($cookie);
                }
                return $this->redirect(['index-detalle-auditor', 'id' => base64_encode($t_id), 'token' => base64_encode($token)]);
                /* return $this->render('index-detalle-auditor', [
                    'token' => $token,
                    't_id' => $id,
                    'searchModel' => $searchModel,
                    'dataProvider' => $dataProvider,
                ]); */
            } else {
                Yii::$app->session->setFlash('info', 'No se ha encontrado usuario, favor de revisar sus credenciales.');
            }
        }
        return $this->render('consulta', [
            'model' => $model,
            'searchModel' => $searchModel,
            'dataProvider' => $dataProvider,
        ]);
    }

    public function actionIndexDetalleAuditor($id = null, $token = null)
    {
        $this->layout = 'home';

        $t_id = base64_decode($id);

        $auditoria = Auditoria::find()->where(['auditoria_id' => intval($t_id)])->one();
        if($auditoria){
            $f_actual = date('Y-m-d');
            $f_fin = substr($auditoria->fecha_fin, 0, -9);
            if ($f_actual > $f_fin) {
                $this->msgError();
            }
        }else{
            $this->msgError();
        }


        $searchModel = new ProviderSearch();
        $searchModel->auditoria_id = $t_id;
        $dataProvider = $searchModel->search(Yii::$app->request->queryParams, 'Asignados');

        return $this->render('index-detalle-auditor', [
            't_id' => $id,
            'token' => $token,
            'searchModel' => $searchModel,
            'dataProvider' => $dataProvider,
        ]);
    }

    public function actionDownloadProviderReport($type=''){

        $dataprovider = '';
        $filename = 'empty';

        if($type == 'dashboardFull'){

            $filename = "proveedores_revision";

            $columns = ['Nombre proveedor', 'RFC', 'Ultimo envio', 'Revisor', 'Estatus'];

            $data = Yii::$app->db->createCommand('
                SELECT
                    CASE
                        WHEN "provider"."name_razon_social" = \'\' THEN concat_ws(\' \',provider.pf_nombre,provider.pf_ap_paterno,provider.pf_ap_materno)
                        ELSE "provider"."name_razon_social"
                    END AS nombre_proveedor,
                    "rfc"."rfc" AS "rfc_manual",
                    "a"."created_at"::date AS "fecha",
                    concat_ws(\' \', u.nombre, u.primer_apellido, u.segundo_apellido) AS revisor,
                    "provider"."permanently_disabled" AS estatus
                FROM "provider"
                LEFT JOIN "provider"."rfc" "rfc" ON rfc.provider_id = provider.provider_id
                INNER JOIN "provider"."asignacion" "a" ON a.id_proveedor = provider.provider_id AND a.activo=true
                INNER JOIN "usuarios" "u" ON u.user_id = a.id_validador
                WHERE "a"."modo" = \'REV\'
                ORDER BY "a"."created_at";
            ')->queryAll();
            
            $dataprovider = GeneralController::multiarrayToCVS($data, $columns);

        }
        if($type == 'validaciones'){

            $filename = "proveedores_validacion";

            $columns = ['RFC', 'Razon social', 'Estatus', 'Fecha'];

            $data = Yii::$app->db->createCommand("SELECT p.rfc, CASE WHEN (p.name_razon_social is not null and p.name_razon_social != '') 
                THEN p.name_razon_social else concat_ws(' ',p.pf_nombre,p.pf_ap_paterno,p.pf_ap_materno) end as nombre_proveedor,
                p.permanently_disabled as estatus, CASE WHEN x.fecha_inicial is null THEN pasig.last_updated_at::date 
                    ELSE x.fecha_inicial::date END as fecha
                FROM provider p INNER JOIN provider.asignacion pasig ON pasig.id_proveedor = p.provider_id 
                LEFT JOIN ( SELECT distinct id_proveedor, max(created_at) as fecha_inicial FROM provider.asignacion a 
                    GROUP BY a.id_proveedor ) x ON x.id_proveedor = p.provider_id WHERE pasig.activo is true AND pasig.modo = 'VAL' ORDER BY x.fecha_inicial")->queryAll();
            
            $dataprovider = GeneralController::multiarrayToCVS($data, $columns);

        }
        if($type == 'consultaFuncionarios'){

            $filename = "proveedores_funcionarios";

            $columns = ['RFC Proveedor', 'Razon social', 'Tipo', 'Nombre funcionario', 'RFC funcionario', '¿Es funcionario?'];

            $data = Yii::$app->db->createCommand("SELECT p_rfc, nombre_proveedor, tipo, nombre_completo_funcionario, funcionario_rfc, 
            CASE WHEN es_funcionario is true THEN 'Si' ELSE 'No' END as es_funcionario FROM public.funcionarios_proveedores ORDER BY provider_id")->queryAll();
            
            $dataprovider = GeneralController::multiarrayToCVS($data, $columns);
        }

    $content = chr(239) . chr(187) . chr(191) . $dataprovider; // add BOM
    GeneralController::setHeadersDownloadCSV($filename);
    return $content;
                        

    }

    public function findModelAuditoria($id)
    {
        $model = Auditoria::find()->where(['auditoria_id' => $id])->one();
        if ($model == null || empty($model)) {
            $this->msgError();
            return $this->render('consulta');
        }
        return $model;
    }

    public function msgError($msg = 'Acceso denegado')
    {
        echo $this->render('/site/error', ['name' => 'Lo sentimos...', 'message' => $msg]);

        exit();
    }


    public function actionFotoPerfil()
    {
        $id = Yii::$app->user->identity->getId();
        $model = Provider::find()->where(['user_id' => $id])->one();

        $model->url_perfilURL = UploadedFile::getInstance($model, 'url_perfil');
        $model->url_perfilOLD = $model->url_perfil;

        if ($model->load(Yii::$app->request->post())) {
            $this->makeDir($model->path);

            ($model->url_perfilURL) ?
                $this->saveNewFile($model, 'url_perfil', $model->path) :
                $model->url_perfil = $model->url_perfilOLD;

            if (!$model->save()) {
                return $model->errors;
            }
            return $this->redirect("/" . $model->tipo_provider . '/perfil/index');

        }

        return $this->renderAjax('foto-perfil', [
            'model' => $model
        ]);

    }


    public function actionFotoBanner()
    {
        $id = Yii::$app->user->identity->getId();
        $model = Provider::find()->where(['user_id' => $id])->one();

        $model->url_bannerURL = UploadedFile::getInstance($model, 'url_banner');
        $model->url_bannerOLD = $model->url_banner;

        if ($model->load(Yii::$app->request->post())) {
            $this->makeDir($model->path);

            ($model->url_bannerURL) ?
                $this->saveNewFile($model, 'url_banner', $model->path) :
                $model->url_banner = $model->url_bannerOLD;

            if (!$model->save()) {
                return json_encode($model->errors);
            }
            return $this->redirect("/" . $model->tipo_provider . '/perfil/index');
        }
        return $this->renderAjax('foto-banner', [
            'model' => $model
        ]);
    }

    public function cambioNombrePF($model,$tipo='bys'){
        $antes = $model->oldAttributes['pf_nombre'].' '.$model->oldAttributes['pf_ap_paterno'].' '.$model->oldAttributes['pf_ap_materno'];
        $despues = $model->pf_nombre.' '.$model->pf_ap_paterno.' '.$model->pf_ap_materno;
        $aux = 'status_carta_'.$tipo;
        if($antes!=$despues && $model->tipo_persona == 'Persona física' && $model->$aux == 'FIRMADA'){
            Yii::$app->session->setFlash('warning', 'Debido a su actualización de datos, es necesario volver a firmar su carta protesta.');
            $model->$aux = 'GENERADA';
            $model->name_razon_social = $despues;
            $model->save(false);
        }
    }

    public function actionUpdatePerfilAjax(){
        $isRequest = Yii::$app->request->isAjax;
        $postRequest = Yii::$app->request->post();
        if( $isRequest || $postRequest ){
            $user_id = Yii::$app->user->identity->getId();

            $model = $this->findProvider($user_id);
            $modelPerfil = Perfil::find()->where(['provider_id'=>$model->provider_id])->one();

            $modelUser = Usuarios::findOne($user_id);
            $modelRfc = $this->findModelRfcProv($model->provider_id);
            $modelStatus = Module::getstatusbymodule('bys_perfil',$model->provider_id);

            $modelUser->scenario = Usuarios::SCENARIO_UPDATE_PROFILE;

            $rechazo = $modelStatus->status_bys == Status::STATUS_RECHAZADO  || $modelStatus->status_bys == Status::STATUS_ENEDICION  ?
                Status::find()->getRechazo( 'bys_perfil', $model->provider_id) : null;

            $url_rfcOLD = $modelRfc->url_rfc;

            if ( $isRequest && $model->load($postRequest) && $modelUser->load($postRequest)) {
                Yii::$app->response->format = Response::FORMAT_JSON;
                return array_merge(ActiveForm::validate($model), ActiveForm::validate($modelUser));
            } else if ( $model->load($postRequest) && $modelUser->load($postRequest) && $modelPerfil->load($postRequest) ){

                $modelRfc->load(Yii::$app->request->post());
                $this->cambioNombrePF($model,'bys');

                if (!empty($modelRfc->url_rfc) && $modelRfc->url_rfc != $url_rfcOLD) {
                    $new_nameCd = str_replace('archivos_tmp/',$modelRfc->path.'/', $modelRfc->url_rfc);
                    $this->copyFile($modelRfc->url_rfc, $new_nameCd);
                    $modelRfc->url_rfc = $new_nameCd;


                    //Agrega las actividades de la constancia de situacion fiscal
                    try {
                        $parser = new Parser();
                        $pdf = $parser->parseFile($new_nameCd);
                        $text = $pdf->getText();
                        //var_dump($text);
                        $inicio = strpos($text, 'Actividades Económicas');
                        $fin = strpos($text, 'Regímenes');
                        $tabla = substr($text, $inicio, ($fin - $inicio));
                        $subData = substr($tabla, strpos($tabla, "\n1 "));
                        //var_dump($subData);
                        //var_dump('<br><br>');
                        $matchFecha = "/([0-9]{2})\/([0-9]{2})\/([0-9]{4})/";
                        $x = preg_split($matchFecha, $subData, 0, PREG_SPLIT_DELIM_CAPTURE);
                        //var_dump(json_encode($x)).exit();
                        //var_dump('<br><br>');
                        $all = [];

                        /* //Se quita pro que ya no se utilizarán las actividades economicas
                        for ($i = 0; $i < count($x) - 1; $i += 4) {
                            if($i==0)
                                Giro::deleteAll(['provider_id' => $model->provider_id]);

                            $aux = strlen($x[$i]);

                            $p1 = trim($x[$i][$aux - 4] . $x[$i][$aux - 3] . $x[$i][$aux - 2]);
                            $p2 = trim($x[$i][$aux - 3] . $x[$i][$aux - 2]);
                            $limit = ($x[$i][$aux-4]==" ") ? 7 : 6;
                            $item = [
                                'id' => trim($x[$i][1] . $x[$i][2]),
                                'fecha' => trim($x[$i + 3] . '/' . $x[$i + 2] . '/' . $x[$i + 1]),
                                'porcentaje' => (int)$p1 == 0 ? $p2 : $p1 ,
                                'concepto' => strtoupper(trim(substr($x[$i], 3, ($aux - $limit)))),
                            ];

                            $modelAct = CatActividades::find()->where(['nombre_actividad' => ($item['concepto'])])->one();
                            //var_dump($modelAct);
                            if ($modelAct) {
                                $modelGiro = new Giro();
                                $modelGiro->provider_id = $model->provider_id;
                                $modelGiro->actividad_id = $modelAct->actividad_id;
                                $modelGiro->start_date = $item['fecha'];
                                $modelGiro->porcentaje = $item['porcentaje'];
                                $modelGiro->save(false);
                            }
                            array_push($all, $item);
                        }
                        */

                        $modelDir = Ubicacion::find()->where(['and',['provider_id'=>$model->provider_id],['tipo'=>'DOMICILIO FISCAL'],['activo'=>true]])->one();

                        if(!$modelDir) {

                            $modelDir = new Ubicacion();
                            $inicio = strpos($text, 'Código Postal:');
                            $fin = strpos($text,'Actividades Económicas');
                            $subString = substr($text, $inicio, ($fin-$inicio));

                            //var_dump($subString);
                            //echo "<br><br>";
                            $subString = str_replace('Página [ 1] de [ 3]', '', str_replace('Página [ 2] de [ 3]', '', str_replace('Página [ 3] de [ 3]', '', $subString)));
                            $subString = str_replace('Página [1] de [3]', '', str_replace('Página [2] de [3]', '', str_replace('Página [3] de [3]', '', $subString)));

                            //var_dump($subString);
                            $data = explode(':',$subString);
                            //var_dump(strpos($data[14]," "));

                            $dom = [
                                'CodigoPostal' => isset($data[1]) ? trim(substr($data[1], 0, strpos($data[1],'Tipo ')-1)):'',
                                'TipoVialidad'=> isset($data[2]) ? trim(substr($data[2], 0, strpos($data[2],'Nombre ')-1)):'',
                                'vialidad'=> isset($data[3]) ? trim(substr($data[3], 0, strpos($data[3],'Número E')-1)):'',
                                'exterior'=> isset($data[4]) ? trim(substr($data[4], 0, strpos($data[4],'Número I')-1)):'',
                                'interior'=> isset($data[5]) ? trim(substr($data[5], 0, strpos($data[5],'Nombre ')-1)):'',
                                'colonia'=> isset($data[6]) ? trim(substr($data[6], 0, strpos($data[6],'Nombre ')-1)):'',
                                'localidad'=> isset($data[7]) ? trim(substr($data[7], 0, strpos($data[7],'Nombre ')-1)):'',
                                'municipio'=> isset($data[8]) ? trim(substr($data[8], 0, strpos($data[8],'Nombre ')-1)):'',
                                'estado'=> isset($data[9]) ? trim(substr($data[9], 0, strpos($data[9],'Entre Calle')-1)):'',
                                'correo'=> isset($data[12]) ? trim(substr($data[12], 0, strpos($data[12],'Tel.')-1)):'',
                                'telefono'=> isset($data[13]) && isset($data[14]) ? trim(substr($data[13], 0, strpos($data[13],'Número')-1)).
                                    trim(substr($data[14], 0, 9)):'',
                            ];

                            //var_dump($dom).exit();

                            $vialidad = CatVialidad::find()
                                ->where(['descripcion'=>strtoupper($dom['TipoVialidad'])])->one();
                            $idVial = isset($vialidad['vialidad_id'])?$vialidad['vialidad_id']:'';

                            $edo = CatEntidades::find()
                                ->where(['ilike','UNACCENT(UPPER(nombre))',SiteController::limpiar_cadena(strtoupper($dom['estado']),false)])->one();
                            $idEdo = isset($edo['entidad_id'])?$edo['entidad_id']:'';

                            $mpio = CatMunicipios::find()
                                ->where(['and',['entidad_id'=>"".$idEdo], ['ilike','UNACCENT(UPPER(nombre))',SiteController::limpiar_cadena(strtoupper($dom['municipio']),false)]])->one();
                            $idMpio = isset($mpio['municipio_id'])?$mpio['municipio_id']:'';

                            $modelDir->tipo = 'DOMICILIO FISCAL';
                            $modelDir->calle_fiscal = $dom['vialidad'];
                            $modelDir->num_ext_fiscal = $dom['exterior'];
                            $modelDir->num_int_fiscal = $dom['interior'];
                            //$modelDir->colonia_fiscal = $dom['colonia'];
                            $modelDir->cp_fiscal = $dom['CodigoPostal'];
                            $modelDir->telefono= $dom['telefono'];
                            $modelDir->provider_id = $model->provider_id;
                            $modelDir->correo = $dom['correo'];
                            $modelDir->vialidad_id = $idVial;
                            $modelDir->state_fiscal = $idEdo;
                            $modelDir->city_fiscal = $idMpio;

                            $modelDir->save(false);
                        }

                    }catch (Exception $e){
                        var_dump(json_decode($e)).exit();
                    }
                }

                $modelUser->save();
                $modelPerfil->save();
                self::updateModulesBys($model->provider_id,'bys_perfil');
                Perfil::updateAll(['status_' . $model->tipo_provider => Status::STATUS_ENEDICION], ['provider_id' => $model->provider_id]);
                $model->save();
                self::eliminarCita();
                if (($requi_status = Status::find()->where(['and', ['register_id' => $model->provider_id], ['status_' . $model->tipo_provider => Status::STATUS_PENDIENTE], ['modelo' => 'perfil']])->one()) !== null) {
                    $requi_status->status_bys = Status::STATUS_TERMINADO_PRO;
                    $requi_status->save();
                }
                $modelRfc->save(false);
                return $this->redirect(["/bys/perfil/index"]);
            }

            return $this->renderAjax('update-perfilbys', [
                'model' => $model,
                'modelRfc'=>$modelRfc,
                'modelPerfil'=>$modelPerfil,
                'modelUser'=>$modelUser,
                'rechazo' => $rechazo,
                'pais' => Provider::findPais(),
            ]);
        }
    }


    public function actionUpdatePerfilbys($id = null)
    {
        $id = Yii::$app->user->identity->getId();
        $model = Provider::find()->select(['provider_id', 'tipo_provider'])->where(['user_id' => $id])->one();

        $modelRfc = $this->findModelRfcProv($model->provider_id);

        $model = $this->findModel($model->provider_id);
        $modelUser = Usuarios::findOne($model->user_id);

        $modelStatus = Module::getstatusbymodule('bys_perfil',$model->provider_id);
        $rechazo = $modelStatus->status_bys == Status::STATUS_RECHAZADO  || $modelStatus->status_bys == Status::STATUS_ENEDICION  ?
            Status::find()->getRechazo( 'bys_perfil', $model->provider_id) : null;

        $url_rfcOLD = $modelRfc->url_rfc;

        if ($model->load(Yii::$app->request->post()) && $modelUser->load(Yii::$app->request->post())  ) {
            $modelRfc->load(Yii::$app->request->post());

           $this->cambioNombrePF($model,'bys');

            if (!empty($modelRfc->url_rfc) && $modelRfc->url_rfc != $url_rfcOLD) {
                $new_nameCd = str_replace('archivos_tmp/',$modelRfc->path.'/', $modelRfc->url_rfc);
                $this->copyFile($modelRfc->url_rfc, $new_nameCd);
                $modelRfc->url_rfc = $new_nameCd;


                //Agrega las actividades de la constancia de situacion fiscal
                try {
                    $parser = new Parser();
                    $pdf = $parser->parseFile($new_nameCd);
                    $text = $pdf->getText();
                    //var_dump($text);
                    $inicio = strpos($text, 'Actividades Económicas');
                    $fin = strpos($text, 'Regímenes');
                    $tabla = substr($text, $inicio, ($fin - $inicio));
                    $subData = substr($tabla, strpos($tabla, "\n1 "));
                    //var_dump($subData);
                    //var_dump('<br><br>');
                    $matchFecha = "/([0-9]{2})\/([0-9]{2})\/([0-9]{4})/";
                    $x = preg_split($matchFecha, $subData, 0, PREG_SPLIT_DELIM_CAPTURE);
                    //var_dump($x);
                    //var_dump('<br><br>');
                    $all = [];

                    for ($i = 0; $i < count($x) - 1; $i += 4) {
                        if($i==0)
                            Giro::deleteAll(['provider_id' => $model->provider_id]);

                        $aux = strlen($x[$i]);
                        $item = [
                            'id' => trim($x[$i][1] . $x[$i][2]),
                            'fecha' => trim($x[$i + 3] . '/' . $x[$i + 2] . '/' . $x[$i + 1]),
                            'porcentaje' => trim($x[$i][$aux - 4] . $x[$i][$aux - 3] . $x[$i][$aux - 2]),
                            'concepto' => strtoupper(trim(substr($x[$i], 3, ($aux - 7))))
                        ];

                        $modelAct = CatActividades::find()->where(['nombre_actividad' => $item['concepto']])->one();
                        //var_dump($modelAct);
                        if ($modelAct) {
                            $modelGiro = new Giro();
                            $modelGiro->provider_id = $model->provider_id;
                            $modelGiro->actividad_id = $modelAct->actividad_id;
                            $modelGiro->start_date = $item['fecha'];
                            $modelGiro->porcentaje = $item['porcentaje'];
                            $modelGiro->save(false);
                        }
                        array_push($all, $item);
                    }

                    $modelDir = Ubicacion::find()->where(['and',['provider_id'=>$model->provider_id],['tipo'=>'DOMICILIO FISCAL'],['activo'=>true]])->one();

                    if(!$modelDir) {

                        $modelDir = new Ubicacion();
                        $inicio = strpos($text, 'Código Postal:');
                        $fin = strpos($text,'Actividades Económicas');
                        $subString = substr($text, $inicio, ($fin-$inicio));

                        //var_dump($subString);
                        //echo "<br><br>";
                        $subString = str_replace('Página [ 1] de [ 3]', '', str_replace('Página [ 2] de [ 3]', '', str_replace('Página [ 3] de [ 3]', '', $subString)));
                        $subString = str_replace('Página [1] de [3]', '', str_replace('Página [2] de [3]', '', str_replace('Página [3] de [3]', '', $subString)));

                        //var_dump($subString);
                        $data = explode(':',$subString);
                        //var_dump(strpos($data[14]," "));

                        $dom = [
                            'CodigoPostal' => isset($data[1]) ? trim(substr($data[1], 0, strpos($data[1],'Tipo ')-1)):'',
                            'TipoVialidad'=> isset($data[2]) ? trim(substr($data[2], 0, strpos($data[2],'Nombre ')-1)):'',
                            'vialidad'=> isset($data[3]) ? trim(substr($data[3], 0, strpos($data[3],'Número E')-1)):'',
                            'exterior'=> isset($data[4]) ? trim(substr($data[4], 0, strpos($data[4],'Número I')-1)):'',
                            'interior'=> isset($data[5]) ? trim(substr($data[5], 0, strpos($data[5],'Nombre ')-1)):'',
                            'colonia'=> isset($data[6]) ? trim(substr($data[6], 0, strpos($data[6],'Nombre ')-1)):'',
                            'localidad'=> isset($data[7]) ? trim(substr($data[7], 0, strpos($data[7],'Nombre ')-1)):'',
                            'municipio'=> isset($data[8]) ? trim(substr($data[8], 0, strpos($data[8],'Nombre ')-1)):'',
                            'estado'=> isset($data[9]) ? trim(substr($data[9], 0, strpos($data[9],'Entre Calle')-1)):'',
                            'correo'=> isset($data[12]) ? trim(substr($data[12], 0, strpos($data[12],'Tel.')-1)):'',
                            'telefono'=> isset($data[13]) && isset($data[14]) ? trim(substr($data[13], 0, strpos($data[13],'Número')-1)).
                                trim(substr($data[14], 0, 9)):'',
                        ];

                        //var_dump($dom).exit();

                        $vialidad = CatVialidad::find()
                            ->where(['descripcion'=>strtoupper($dom['TipoVialidad'])])->one();
                        $idVial = isset($vialidad['vialidad_id'])?$vialidad['vialidad_id']:'';

                        $edo = CatEntidades::find()
                            ->where(['ilike','UNACCENT(UPPER(nombre))',SiteController::limpiar_cadena(strtoupper($dom['estado']),false)])->one();
                        $idEdo = isset($edo['entidad_id'])?$edo['entidad_id']:'';

                        $mpio = CatMunicipios::find()
                            ->where(['and',['entidad_id'=>"".$idEdo], ['ilike','UNACCENT(UPPER(nombre))',SiteController::limpiar_cadena(strtoupper($dom['municipio']),false)]])->one();
                        $idMpio = isset($mpio['municipio_id'])?$mpio['municipio_id']:'';

                        $modelDir->tipo = 'DOMICILIO FISCAL';
                        $modelDir->calle_fiscal = $dom['vialidad'];
                        $modelDir->num_ext_fiscal = $dom['exterior'];
                        $modelDir->num_int_fiscal = $dom['interior'];
                        //$modelDir->colonia_fiscal = $dom['colonia'];
                        $modelDir->cp_fiscal = $dom['CodigoPostal'];
                        $modelDir->telefono= $dom['telefono'];
                        $modelDir->provider_id = $model->provider_id;
                        $modelDir->correo = $dom['correo'];
                        $modelDir->vialidad_id = $idVial;
                        $modelDir->state_fiscal = $idEdo;
                        $modelDir->city_fiscal = $idMpio;

                        $modelDir->save(false);
                    }

                }catch (Exception $e){
                    var_dump(json_decode($e)).exit();
                }
            }

            $modelUser->save();
            self::updateModulesBys($model->provider_id,'bys_perfil');
            Perfil::updateAll(['status_' . $model->tipo_provider => Status::STATUS_ENEDICION], ['provider_id' => $model->provider_id]);
            $model->save();
            self::eliminarCita();
            if (($requi_status = Status::find()->where(['and', ['register_id' => $model->provider_id], ['status_' . $model->tipo_provider => Status::STATUS_PENDIENTE], ['modelo' => 'perfil']])->one()) !== null) {
                $requi_status->status_bys = Status::STATUS_TERMINADO_PRO;
                $requi_status->save();
            }
            $modelRfc->save(false);
            return $this->redirect(["/bys/perfil/index"]);
        }
        return $this->renderAjax('update-perfilbys', [
            'model' => $model,
            'modelRfc'=>$modelRfc,
            'modelUser'=>$modelUser,
            'rechazo' => $rechazo,
            'pais' => Provider::findPais(),
            /* 'rechazo' => Status::find()->getStatus($id, 'perfil', $model->tipo_provider), */
        ]);
    }


    public function actionUpdatePerfil($id = null)
    {
        $id = Yii::$app->user->identity->getId();
        $data_pro = Provider::find()->select(['provider_id', 'tipo_provider'])->where(['user_id' => $id])->one();
        $id_provider = $data_pro['provider_id'];
        $tipo_pro = $data_pro['tipo_provider'];


        $model = $this->findModel($id_provider);
        $modelUser = Usuarios::findOne($model->user_id);
        $modelOld = $model->attributes;
        $modelUbi = $this->findModelUbicacionProv($id_provider);
        $modelUbiOld = $modelUbi->attributes;
        $model_rfc = $this->findModelRfcProv($id_provider);
        $model_rfcOld = $model_rfc->attributes;
        $model_comprobante_domicilio = $this->findModelComprobanteDomicilio($id_provider);
        $model_comprobante_domicilioOld = $model_comprobante_domicilio->attributes;
        $urlComprobanteDomicilioOld = $model_comprobante_domicilio->url_comprobante_domicilio;

        $modelUbi->scenario = Ubicacion::SCENARIO_OP_FISCAL;

        $rechazo = Status::find()->getStatus($id, 'perfil', $tipo_pro);
        $statefiscal = '';

        if (!empty($modelUbi->state_fiscal)) {
            $statefiscal = $modelUbi->state_fiscal;
        }

        if ($model->load(Yii::$app->request->post()) && $modelUbi->load(Yii::$app->request->post()) && $model_comprobante_domicilio->load(Yii::$app->request->post()) && $model_rfc->load(Yii::$app->request->post()) && $modelUser->load(Yii::$app->request->post())  ) {

            CartaController::reactivaCartaProtesta($modelUbi);
            CartaController::reactivaCartaProtesta($model);

            $this->cambioNombrePF($model,'op');

            if (!empty($modelUbi->geo_ubicacion)) {
                $location = json_decode($modelUbi->geo_ubicacion);
                if (isset($location[0]) && !empty($location[0]) && isset($location[1]) && !empty($location[1])) {
                    $val_geo = Ubicacion::findBySql("select ST_MakePoint(" . $location[0] . "," . $location[1] . ")")->asArray()->all();
                    $modelUbi->geo_ubicacion = $val_geo[0]['st_makepoint'];
                }
            }

            if(!$model_rfc->save())
                return json_encode($model_rfc->errors).exit();
            if ($tipo_pro == 'bys') {
                $pathImgDom = $model_comprobante_domicilio->path_comprobante_domicilio;
            } else {
                $pathImgDom = $model_comprobante_domicilio->path;
            }

            $this->makeDir($pathImgDom);

            if (!empty($model_comprobante_domicilio->url_comprobante_domicilio) && $model_comprobante_domicilio->url_comprobante_domicilio != $urlComprobanteDomicilioOld) {
                $new_nameCd = str_replace('archivos_tmp/', $pathImgDom . '/', $model_comprobante_domicilio->url_comprobante_domicilio);
                $this->copyFile($model_comprobante_domicilio->url_comprobante_domicilio, $new_nameCd);
                $model_comprobante_domicilio->url_comprobante_domicilio = $new_nameCd;

            }

            $modelUbi->url_comprobante_domicilio = $model_comprobante_domicilio->url_comprobante_domicilio;
            $modelUbi->provider_id = $id_provider;
            $modelUbi->type_address_prov = 'DOMICILIO FISCAL';
            $modelUbi->tipo = 'DOMICILIO FISCAL';
            $modelUbi->status_op = 'VALIDADO';
            $modelUbi->departamento = '-';


            if(!$modelUbi->save()){
                //var_dump($modelUbi->errors).exit();
            }

            $model_comprobante_domicilio->save(false);

            if ($tipo_pro == 'bys') {

                $countValCert = $this->verifyProviderCert($id_provider, 'bys');
                $countValRec = self::verifyValRec('perfil', $model->provider_id);
                $countValRecRfc = self::verifyValRec('rfc', $model->provider_id);
                $countValRecUbi = self::verifyValRec('ubicacion', $model->provider_id);
                $countValRecCom = self::verifyValRec('comprobante_domicilio', $model->provider_id);
                if ($countValCert > 0 || $countValRec > 0 || $countValRecRfc > 0 || $countValRecUbi > 0 || $countValRecCom > 0) {
                    $trueFalse = $countValCert > 0 ? true : false;
                    $this->compareModels($modelOld, $model->attributes, $model->provider_id, 'perfil_provider', $id_provider, $trueFalse);
                    $this->compareModels($model_rfcOld, $model_rfc->attributes, $model->provider_id, 'perfil_rfc', $id_provider, $trueFalse);
                    $this->compareModels($modelUbiOld, $modelUbi->attributes, $model->provider_id, 'perfil_ubicacion', $id_provider, $trueFalse);
                    $this->compareModels($model_comprobante_domicilioOld, $model_comprobante_domicilio->attributes, $model->provider_id, 'perfil_comprobante_domicilio', $id_provider, $trueFalse);
                }
            }

            $modelUser->save();

            Perfil::updateAll(['status_' . $tipo_pro => Status::STATUS_ENEDICION], ['provider_id' => $id_provider]);
            $model->save();
            self::eliminarCita();
            if (($requi_status = Status::find()->where(['and', ['register_id' => $id_provider], ['status_' . $tipo_pro => Status::STATUS_PENDIENTE], ['modelo' => 'perfil']])->one()) !== null) {
                $requi_status->status_bys = Status::STATUS_TERMINADO_PRO;
                $requi_status->save();
            }

            //var_dump(json_encode($modelUbi->getAttributes())).exit();
            return $this->redirect(["/$tipo_pro/perfil/index"]);


        }


        $pais = Provider::findPais();
        $estado = CatEntidades::findState();
        $cat_vialidad = Provider::getVialidad();
        $list_municipios = [];
        $list_localidades = [];
        $list_asentamientos = [];
        if (!empty($statefiscal)) {
            $list_municipios = Provider::getListMun($statefiscal);
        }

        if (!empty($modelUbi->city_fiscal) && !empty($statefiscal)) {
            $list_localidades = Provider::getListLoc($statefiscal, $modelUbi->city_fiscal);
            $list_asentamientos = Provider::getListAsen($statefiscal, $modelUbi->city_fiscal);
        }


        return $this->renderAjax('update-perfil', [
            'model' => $model,
            'modelUser'=>$modelUser,
            'modelUbi' => $modelUbi,
            'pais' => $pais,
            'estado' => $estado,
            'statefiscal' => $statefiscal,
            'cat_vialidad' => $cat_vialidad,
            'list_municipios' => $list_municipios,
            'list_localidades' => $list_localidades,
            'list_asentamientos' => $list_asentamientos,
            'model_rfc' => $model_rfc,
            'model_comprobante_domicilio' => $model_comprobante_domicilio,
            'rechazo' => $rechazo,
        ]);


    }


    public function actionBienvenida()
    {
        $this->layout = 'nomain';

        return $this->render('correos/bienvenida');
    }

    /**
     * @param $id
     * @return string
     */
    public function actionCambioPassword()
    {
        $this->layout = 'nomain';

        return $this->render('correos/cambio_password');
    }

    /**
     * @param $id
     * @return string
     */
    public function actionConfirmacion()
    {
        $this->layout = 'nomain';

        return $this->render('correos/confirmacion');
    }

    /**
     * @param $id
     * @return string
     */
    public function actionRechazo()
    {
        $this->layout = 'nomain';

        return $this->render('correos/rechazo');
    }

    /**
     * @param $id
     * @return string
     */
    public function actionValidado()
    {
        $this->layout = 'nomain';

        return $this->render('correos/validado');
    }

    /**
     * @param $id
     * @return string
     */
    public function actionCertificacion()
    {
        $this->layout = 'nomain';

        return $this->render('correos/certificacion');
    }

    /**
     * @param $id
     * @return string
     */
    public function actionRenovacion()
    {
        $this->layout = 'nomain';

        return $this->render('correos/renovacion');
    }

    /**
     * @param $id
     * @return string
     */
    public function actionSancion()
    {
        $this->layout = 'nomain';

        return $this->render('correos/sancion');
    }

    /**
     * @param $id
     * @return string
     */
    public function actionNuevo()
    {
        $this->layout = 'nomain';

        return $this->render('correos/nuevo');
    }


    /**
     * @param $id
     * @return string
     */
    public function actionPendienteAviso()
    {
        $this->layout = 'nomain';

        return $this->render('correos/pendiente_aviso');
    }

    /**
     * @param $id
     * @return string
     */
    public function actionAvisoSeis()
    {
        $this->layout = 'nomain';

        return $this->render('correos/aviso_seis');
    }

    /**
     * @param $id
     * @return string
     */
    public function actionAvisoExpirado()
    {
        $this->layout = 'nomain';

        return $this->render('correos/aviso_expirado');
    }

    /**
     * @param $id
     * @return string
     */
    public function actionAuditoria()
    {
        $this->layout = 'nomain';

        return $this->render('correos/auditoria');
    }

    public function findModelProviderGiro($id)
    {

        $model = ProviderGiro::find()
            ->select("p.concepto_grupo_id as grupo")
            ->addSelect(new \yii\db\Expression("string_agg(provider_giro.producto_id::text,',') as producto_id"))
            ->from('provider_giro')
            ->innerJoin('productos_servicios.concepto_linea p','p.concepto_linea_id = provider_giro.producto_id')
            ->groupBy("concepto_grupo_id")->where(['provider_id' => $id])->asArray()->all();

        if ($model == null || empty($model)) {
            $model = [new ProviderGiro()];
        }
        return $model;
    }


    public function findModelProviderGiroAud($id)
    {

        $tipo = 'clase';

        $model = [];

        if((Module::find()->where(['and',['provider_id' => $id],['model' => 'bys_bys'],['!=','status_bys','EN EDICION']])->one())!==null){
            $model = ProviderGiro::find()->select(["pg.provider_id","d.division_id as familia", "g.grupo_id as grupo", "c.clase_id as clase", "MAX(pg.factura::text) as factura", "MAX(pg.documento::text) as documento", "MAX(pg.vigencia) as vigencia", "coalesce(bool_or(pg.especialidad), false)  as especialidad", "coalesce(bool_or(pg.indefinido), false) as indefinido"])
                ->addSelect(new yii\db\Expression("string_agg(pg.producto_id::text, ',') as producto_id"))->from('provider_giro pg')
                ->innerJoin('productos.producto p', 'p.producto_id = pg.producto_id')
                ->innerJoin('productos.clase c', 'c.clase_id = p.clase_id')
                ->innerJoin('productos.grupo g', 'g.grupo_id = c.grupo_id')
                ->innerJoin('productos.division d', 'd.division_id = g.division_id')
                ->groupBy(["pg.provider_id", "d.division_id", "g.grupo_id", "c.clase_i"])->where(['and',['provider_id' => $id, 'active' => true]])->asArray()->all();
        }

        if ($model == null || empty($model)) {

            $model = ProviderGiroRespaldo::find()
                ->select("p.concepto_grupo_id as grupo")
                ->addSelect(new \yii\db\Expression("string_agg(provider_giro_respaldo.producto_id::text,',') as producto_id"))
                ->from('provider_giro_respaldo')
                ->innerJoin('productos_servicios.concepto_linea p', 'p.concepto_linea_id = provider_giro_respaldo.producto_id')
                ->groupBy("concepto_grupo_id")->where(['provider_id' => $id])->asArray()->all();

            if ($model == null || empty($model)) {
                $model = ProviderGiroRespaldo::find()->select("p.grupo_producto_id as grupo_producto")
                    ->addSelect(new \yii\db\Expression("string_agg(provider_giro_respaldo.producto_id::text,',') as producto_id"))
                    ->from('provider_giro_respaldo')->innerJoin('producto p', 'p.producto_id = provider_giro_respaldo.producto_id')
                    ->groupBy("grupo_producto_id")->where(['provider_id' => $id])->asArray()->all();

                if ($model == null || empty($model)) {
                    $model = [new ProviderGiro()];
                } else {
                    $tipo = 'grupo';
                }
            }else{
                $tipo = 'familia';
            }
        }
        return ['model' => $model, 'tipo' => $tipo];
    }

    public function getDataProviderGiro($provider_id){
        $model = ProviderGiro::find()->select(["pg.provider_id","d.division_id as familia", "g.grupo_id as grupo", "c.clase_id as clase", "MAX(pg.factura::text) as factura", "MAX(pg.documento::text) as documento", "MAX(pg.vigencia) as vigencia", "coalesce(bool_or(pg.especialidad), false)  as especialidad", "coalesce(bool_or(pg.indefinido), false) as indefinido"])
                ->addSelect(new yii\db\Expression("string_agg(pg.producto_id::text, ',') as producto_id"))->from('provider_giro pg')
                ->innerJoin('productos.producto p', 'p.producto_id = pg.producto_id')
                ->innerJoin('productos.clase c', 'c.clase_id = p.clase_id')
                ->innerJoin('productos.grupo g', 'g.grupo_id = c.grupo_id')
                ->innerJoin('productos.division d', 'd.division_id = g.division_id')
                ->groupBy(["pg.provider_id", "d.division_id", "g.grupo_id", "c.clase_id"])->where(['and', ['provider_id' => $provider_id, 'active' => true]])->asArray()->all();

        if($model == null || empty($model)){
            $model = [new ProviderGiro()];
        }
        return $model;
    }

    public function getHistoricoProviderGiro($provider_id, $tipo = 'bys'){
        $response = [];
        self::setTempHistoricoProviderGiroParticular($provider_id, $tipo);
        $respuesta_new = self::getNewProviderGiroHistorico();
        $respuesta_old = self::getOldProviderGiroHistorico();
        $respuesta_first = self::getFirstProductosHistorico();
        $response = array_merge($respuesta_new, $respuesta_old, $respuesta_first);

        return $response;
    }

    public function getHistoricoGiro($provider_id, $tipo = 'bys'){
        $response = [];
        self::setTempHistoricoGiro($provider_id, $tipo);

        $model = Giro::find()->from('temp_giro_historico')->where(['provider_id' => $provider_id])->all();
        $response = ($model == null || empty($model)) ? [new Giro()] : $model;

        return $response;
    }

    public function setTempHistoricoGiro($provider_id, $tipo){
        Yii::$app->db->createCommand("DROP TABLE IF EXISTS temp_giro_historico")->execute();

        Yii::$app->db->createCommand(
            "CREATE TEMPORARY TABLE temp_giro_historico(
                giro_id bigint, actividad_id bigint,
                provider_id bigint, porcentaje smallint,
                start_date date,
                url_factura_c_c text
            )" )->execute();

        //EL JSON de la columna data en Historicos la convierte en un solo array de objetos con el fin de pasarlos despues a registros en la DB
        //Agrega un nuevo campo al json object que indica si el producto es legacy de acuerdo a la fecha de validacion
        $query_json_line = "SELECT jsonb_agg( arr.item_object ) FROM provider.historico, json_array_elements(data)
                            with ordinality arr(item_object) WHERE modelo = 'Giro' AND provider_id = $provider_id AND tipo = '$tipo' 
                            GROUP BY fecha_validacion ORDER BY fecha_validacion DESC LIMIT 1";

        //EL JSON array se convierte en registros para poder ser manipulados con SQL
        $query_json_record = "SELECT * FROM jsonb_to_recordset( ($query_json_line) ) as x(
                    giro_id bigint, actividad_id bigint, provider_id bigint, porcentaje smallint, start_date date, url_factura_c_c text ) ";

        //Se insertan los registros en una tabla temporal para manipularlos con querys
        $query_json_insert_temp = "INSERT INTO temp_giro_historico ($query_json_record)";

        Yii::$app->db->createCommand($query_json_insert_temp)->execute();
    }

    public function getNewProviderGiroHistorico(){
        $model = ProviderGiro::find()->select(["pg.mode","pg.provider_id","d.division_id as familia", "g.grupo_id as grupo", "c.clase_id as clase","MAX(pg.factura::text) as factura","MAX(pg.documento::text) as documento", "MAX(pg.vigencia) as vigencia", "coalesce(bool_or(pg.especialidad), false)  as especialidad", "coalesce(bool_or(pg.indefinido), false) as indefinido"])
                ->addSelect(new yii\db\Expression("string_agg(distinct(pg.producto_id)::text, ',') as producto_id"))->from('temp_productos_historico_provider pg')
                ->innerJoin('productos.producto p', 'p.producto_id = pg.producto_id')
                ->innerJoin('productos.clase c', 'c.clase_id = p.clase_id')
                ->innerJoin('productos.grupo g', 'g.grupo_id = c.grupo_id')
                ->innerJoin('productos.division d', 'd.division_id = g.division_id')
                ->groupBy(["pg.provider_id", "d.division_id", "g.grupo_id", "c.clase_id", "pg.mode"])->where(['pg.mode' => 0 ])->asArray()->all();

        if($model == null || empty($model)){ $model = []; }
        return $model;

    }

    public function setTempHistoricoProviderGiroParticular($provider_id, $tipo){
        Yii::$app->db->createCommand("DROP TABLE IF EXISTS temp_productos_historico_provider")->execute();

        Yii::$app->db->createCommand(
            "CREATE TEMPORARY TABLE temp_productos_historico_provider(
                provider_id INT, producto_id INT,
                factura text, documento text, vigencia date,
                especialidad bool default false,
                indefinido bool default false,
                mode INT default 0
            )" )->execute();

        //EL JSON de la columna data en Historicos la convierte en un solo array de objetos con el fin de pasarlos despues a registros en la DB
        //Agrega un nuevo campo al json object que indica si el producto es legacy de acuerdo a la fecha de validacion
        /* $query_json_line = "SELECT jsonb_agg( jsonb_set(arr.item_object::jsonb, '{is_legacy}',
                            (CASE WHEN fecha_validacion::date < '2022-07-01'::date THEN 'true'
                            ELSE 'false' END)::jsonb, true )) FROM provider.historico, json_array_elements(data)
                            with ordinality arr(item_object) WHERE modelo = 'ProviderGiro' AND provider_id = $provider_id AND tipo = '$tipo'"; */

        //Se modifico el Query para obtener el registro mas reciente del historico
        // Mode = 0 => productos.producto | Mode = 1 => productos_servicios.concepto_linea | Mode = 2 => public.productos
        $query_json_filter = "SELECT jsonb_agg(jsonb_set(arr.item_object::jsonb, '{mode}', 
				            to_jsonb(CASE WHEN h.fecha_validacion::date <= '2021-09-12'::date THEN 2
                            WHEN h.fecha_validacion::date > '2021-09-12' AND h.fecha_validacion < '2022-07-01' THEN 1 ELSE 0 END), true))
                            from provider.historico h,  json_array_elements(h.data) With ordinality arr(item_object) 
                            WHERE h.modelo = 'ProviderGiro' AND h.tipo = '$tipo' AND h.provider_id = $provider_id
                            GROUP BY h.fecha_validacion ORDER BY h.fecha_validacion DESC LIMIT 1";
    

        //EL JSON array se convierte en registros para poder ser manipulados con SQL
        $query_json_record = "SELECT * FROM jsonb_to_recordset( ($query_json_filter) ) as x(
            provider_id int, producto_id int, factura text, documento text, vigencia date, especialidad bool, indefinido bool,  mode int ) ";

        //Se insertan los registros en una tabla temporal para manipularlos con querys
        $query_json_insert_temp = "INSERT INTO temp_productos_historico_provider ($query_json_record)";

        Yii::$app->db->createCommand($query_json_insert_temp)->execute();
    }

    /* public function setTempHistoricoProviderGiro($provider_id, $tipo) */
    public function setTempHistoricoProviderGiro($tipo){ //Se cambio el metodo de SET de datos personal a global

        Yii::$app->db->createCommand("DROP TABLE IF EXISTS temp_productos_historico")->execute();

        Yii::$app->db->createCommand(
            "CREATE TEMPORARY TABLE temp_productos_historico(
                provider_id INT, producto_id INT,
                factura text, documento text, vigencia date,
                especialidad bool default false,
                indefinido bool default false,
                mode INT default 0
            )" )->execute();//Se cambio de legacy a mode para implementar las 3 logicas

        //EL JSON de la columna data en Historicos la convierte en un solo array de objetos con el fin de pasarlos despues a registros en la DB
        //Agrega un nuevo campo al json object que indica si el producto es legacy de acuerdo a la fecha de validacion
        /* $query_json_line = "SELECT jsonb_agg( jsonb_set(arr.item_object::jsonb, '{is_legacy}',
                            (CASE WHEN fecha_validacion::date < '2022-07-01'::date THEN 'true'
                            ELSE 'false' END)::jsonb, true )) FROM provider.historico, json_array_elements(data)
                            with ordinality arr(item_object) WHERE modelo = 'ProviderGiro' AND provider_id = $provider_id AND tipo = '$tipo'"; */

        //Se modifico el Query para obtener el registro mas reciente del historico
        // Mode = 0 => productos.producto | Mode = 1 => productos_servicios.concepto_linea | Mode = 2 => public.productos
        $query_json_filter = "SELECT jsonb_agg(jsonb_set(arr.item_object::jsonb, '{mode}', 
				            to_jsonb(CASE WHEN h.fecha_validacion::date <= '2021-09-12'::date THEN 2
                            WHEN h.fecha_validacion::date > '2021-09-12' AND h.fecha_validacion < '2022-07-01' THEN 1 ELSE 0 END), true)) 
                            from provider.historico h,  json_array_elements(h.data) With ordinality arr(item_object) 
                            WHERE h.modelo = 'ProviderGiro' AND h.tipo = '$tipo' AND h.provider_id = p.provider_id 
                            GROUP BY h.fecha_validacion ORDER BY h.fecha_validacion DESC  LIMIT 1";
        
        //Filtra los datos nulos y concatena los array elements en un solo array element para posterior convertirlo a registro
        $query_json_historicos_provider = "SELECT jsonb_agg(producto.item_object) FROM (
                            SELECT ($query_json_filter) as first_hist_data FROM provider p
                        ) filtered_historico, jsonb_array_elements(first_hist_data) 
                        with ordinality producto(item_object) WHERE first_hist_data is not null";

        //EL JSON array se convierte en registros para poder ser manipulados con SQL
        /* $query_json_record = "SELECT * FROM jsonb_to_recordset( ($query_json_line) ) as x(
                    provider_id int, producto_id int, documento text, vigencia date, especialidad bool, indefinido bool, is_legacy bool ) "; */
        
        $query_json_record = "SELECT * FROM jsonb_to_recordset( ($query_json_historicos_provider) ) as x(
            provider_id int, producto_id int, factura text, documento text, vigencia date, especialidad bool, indefinido bool, mode int ) ";

        //Se insertan los registros en una tabla temporal para manipularlos con querys
        $query_json_insert_temp = "INSERT INTO temp_productos_historico ($query_json_record)";

        Yii::$app->db->createCommand($query_json_insert_temp)->execute();

    }

    public function getOldProviderGiroHistorico(){
        $model = ProviderGiro::find()->select("temp_productos_historico_provider.provider_id, mode, p.concepto_grupo_id as grupo")
            ->addSelect(new \yii\db\Expression("string_agg(distinct(temp_productos_historico_provider.producto_id)::text,',') as producto_id"))
            ->from('temp_productos_historico_provider')->innerJoin('productos_servicios.concepto_linea p','p.concepto_linea_id = temp_productos_historico_provider.producto_id')
            ->groupBy("concepto_grupo_id,temp_productos_historico_provider.provider_id, mode")->where(['mode' => 1])->asArray()->all();

        if($model == null || empty($model)){ $model = []; }
        return $model;
    }

    public function getFirstProductosHistorico(){
        $model = Producto::find()->select("producto.*, tph.mode, um.nombre as nombre_unidad, gp.nombre as descripcion_grupo")
        ->innerJoin('temp_productos_historico_provider tph', 'tph.producto_id = producto.producto_id')
        ->innerJoin('grupo_producto gp', 'gp.grupo_producto_id = producto.grupo_producto_id')
        ->innerJoin('unidad_medida um', 'um.unidad_medida_id = producto.unidad_medida_id')
        ->where(['tph.mode' => 2])->asArray()->all();

        if($model == null || empty($model)){ $model = []; }
        return $model;
    }

    //Funcion compatible con logicas de guardado en historico V3 
    //Se obtiene el ultimo registro de validacion
    public function getUbicacionUnicaHistorico($provider_id, $tipoUbicacion, $isRequired = false, $tipo = 'bys'){
        $response = ["ubicacion" => $isRequired ? new Ubicacion() : null, "fotografia" => $isRequired ? [ new FotografiaNegocio() ] : null ];
        $ubicacion_json = Yii::$app->db->createCommand("SELECT arr.item_object FROM provider.historico, json_array_elements(data) WITH ordinality arr(item_object)
                    WHERE modelo = 'Ubicacion' AND provider_id = :provider_id AND tipo = :tipo AND arr.item_object::jsonb->>'tipo' = :tipo_ubicacion 
                    AND json_typeof(data) = 'array' ORDER BY fecha_validacion DESC",
                    ['provider_id' => $provider_id, 'tipo' => $tipo, 'tipo_ubicacion' => $tipoUbicacion])->queryOne();
        if( empty($ubicacion_json['item_object']) ){
            $model = Historico::findBySql("SELECT * FROM provider.historico WHERE modelo = 'Ubicacion' AND provider_id = $provider_id 
                    AND tipo = '$tipo' AND data::json->>'tipo' = '$tipoUbicacion' AND json_typeof(data) = 'object'")
                    ->orderBy(['fecha_validacion' => SORT_DESC])->one();
            if( !empty($model) ){
                $response["ubicacion"] = self::setAtributesModel("app\models\\Ubicacion", $model->data);
                $histFotografia = Historico::findBySql("SELECT * FROM provider.historico WHERE modelo = 'FotografiaNegocio' 
                    AND provider_id = $provider_id AND tipo = '$tipo' AND data::json->>'ubicacion_id' = :ubicacion_id AND json_typeof(data) = 'object'",
                    ['ubicacion_id' => $response["ubicacion"]->ubicacion_id])->orderBy(['fecha_validacion' => SORT_DESC])->all();
                if( !empty($histFotografia) ){ 
                        $auxFoto = [];
                    foreach($histFotografia as $fotoHist){
                        array_push($auxFoto, self::setAtributesModel("app\models\\FotografiaNegocio", $fotoHist->data));
                    }
                     $response["fotografia"] = $auxFoto; 
                }
            }
        }else{
            $response["ubicacion"] = self::setAtributesModel("app\models\\Ubicacion", json_decode($ubicacion_json['item_object']));
            $fotografia_json = Yii::$app->db->createCommand("SELECT arr.item_object FROM provider.historico, json_array_elements(data) WITH ordinality arr(item_object)
                WHERE modelo = 'FotografiaNegocio' AND provider_id = :provider_id AND tipo = :tipo AND arr.item_object::jsonb->>'ubicacion_id' = :ubicacion_id
                AND json_typeof(data) = 'array' ORDER BY fecha_validacion DESC",
                ['provider_id' => $provider_id, 'tipo' => $tipo, 'ubicacion_id' => $response["ubicacion"]->ubicacion_id])->queryOne();
            if( !empty($fotografia_json['item_object']) ){ $response["fotografia"] = [ self::setAtributesModel("app\models\\FotografiaNegocio", json_decode($fotografia_json['item_object'])) ]; } 
        }

        return $response;
    }

    //Funcion compatible con logicas de guardado en historico V3 
    //Se obtiene el ultimo registro de validacion
    public function getUbicacionesHistorico($provider_id, $tipo = 'bys'){
        $direcciones = [];
        $ubicaciones_json = Yii::$app->db->createCommand("SELECT arr.item_object FROM provider.historico, json_array_elements(data) WITH ordinality arr(item_object)
                WHERE modelo = 'Ubicacion' AND provider_id = :provider_id AND tipo = :tipo AND arr.item_object::jsonb->>'type_address_prov' is null
                AND json_typeof(data) = 'array' ORDER BY fecha_validacion  DESC", ['provider_id' => $provider_id, 'tipo' => $tipo])->queryAll();
        if( count($ubicaciones_json) > 0 ){
            foreach($ubicaciones_json as $registro){
                if( !empty($registro['item_object']) ){
                    $response = ["ubicacion" => new Ubicacion(), "fotografia" => new FotografiaNegocio()];
                    $response["ubicacion"] = self::setAtributesModel("app\models\\Ubicacion", json_decode($registro['item_object']));
                    $fotografia_json = Yii::$app->db->createCommand("SELECT arr.item_object FROM provider.historico, json_array_elements(data) WITH ordinality arr(item_object)
                        WHERE modelo = 'FotografiaNegocio' AND provider_id = :provider_id AND tipo = :tipo AND arr.item_object::jsonb->>'ubicacion_id' = :ubicacion_id
                        AND json_typeof(data) = 'array' ORDER BY fecha_validacion DESC",
                        ['provider_id' => $provider_id, 'tipo' => $tipo, 'ubicacion_id' => $response["ubicacion"]->ubicacion_id])->queryOne();
                    if( !empty($fotografia_json['item_object']) ){ $response["fotografia"] = self::setAtributesModel("app\models\\FotografiaNegocio", json_decode($fotografia_json['item_object'])); }
                    array_push($direcciones, $response);
                }
            }
        }else{
            $model = Historico::findBySql("SELECT * FROM provider.historico WHERE modelo = 'Ubicacion' AND provider_id = $provider_id AND tipo = '$tipo' 
                AND data::json->>'type_address_prov' is null AND json_typeof(data) = 'object' ")
                ->orderBy(['fecha_validacion' => SORT_DESC])->all();
            if( count($model) > 0 ){
                foreach($model as $historico){
                    $response = ["ubicacion" => new Ubicacion(), "fotografia" => new FotografiaNegocio()];
                    $response["ubicacion"] = self::setAtributesModel("app\models\\Ubicacion", $historico->data);
                    $histFotografia = Historico::findBySql("SELECT * FROM provider.historico WHERE modelo = 'FotografiaNegocio' AND provider_id = $provider_id AND tipo = '$tipo' 
                            AND data::json->>'ubicacion_id' = :ubicacion_id AND json_typeof(data) = 'object'", ['ubicacion_id' => $response["ubicacion"]->ubicacion_id])
                            ->orderBy(['fecha_validacion' => SORT_DESC])->one(); //Busca datos legacy primeramente en caso de migracion
                    if( !empty($histFotografia->data) ){ $response["fotografia"] = self::setAtributesModel("app\models\\FotografiaNegocio", $histFotografia->data); }
                    array_push($direcciones, $response);
                }
            }
        }
        return $direcciones;
    }

    //Funcion compatible con logicas de guardado en historico V3 
    //Se obtiene el ultimo registro de validacion
    public function getArrayHistoricoCordUbicaciones($provider_id, $tipo = 'bys'){
        $arr_response = [];
        $models = Historico::findBySql("SELECT * FROM provider.historico WHERE modelo = 'Ubicacion' AND provider_id = $provider_id 
                    AND tipo = '$tipo' AND json_typeof(data) = 'array'")->orderBy(['fecha_validacion' => SORT_DESC])->one();
        if( !is_null($models) && !empty($models) ){
            foreach( (array) $models->data as $registro){
                $formato = ['coords' => null, 'titulo' => null];
                $ubicacion = self::setAtributesModel("app\models\\Ubicacion", $registro);
                if( !is_null($ubicacion->geo_ubicacion) && !empty($ubicacion->geo_ubicacion) ){
                    $location = json_encode($ubicacion->geo_ubicacion);
                    $formato['coords'] = $location;
                    $formato['titulo'] = $ubicacion->tipo;
                    $colonia = !empty($ubicacion->colonia_fiscal) ? CatAsentamientos::find()->where(['asentamiento_id' => $ubicacion->colonia_fiscal])->one()->nombre : null;
                    $ciudad = !empty($ubicacion->city_fiscal) ? CatMunicipios::find()->where(['municipio_id' => $ubicacion->city_fiscal])->one()->nombre : null;
                    $estado = !empty($ubicacion->state_fiscal) ? CatEntidades::find()->where(['entidad_id' => $ubicacion->state_fiscal])->one()->nombre : null;
                    $formato['desc'] = "$ubicacion->calle_fiscal, $colonia $ubicacion->cp_fiscal, $ciudad, $estado";
    
                    array_push($arr_response, $formato);
                }
            }
        }else{
            $model = Historico::findBySql("SELECT * FROM provider.historico WHERE modelo = 'Ubicacion' AND provider_id = $provider_id 
                    AND tipo = '$tipo' AND json_typeof(data) = 'object'")->orderBy(['fecha_validacion' => SORT_DESC])->all();
            if( count($model) > 0 ){
                foreach($model as $historico){
                    $formato = ['coords' => null, 'titulo' => null];
                    $ubicacion = self::setAtributesModel("app\models\\Ubicacion", $historico->data);
                    if( !is_null($ubicacion->geo_ubicacion) && !empty($ubicacion->geo_ubicacion) ){
                        $location = json_encode($ubicacion->geo_ubicacion);
                        $formato['coords'] = $location;
                        $formato['titulo'] = $ubicacion->tipo;
                        $colonia = !empty($ubicacion->colonia_fiscal) ? CatAsentamientos::find()->where(['asentamiento_id' => $ubicacion->colonia_fiscal])->one()->nombre : null;
                        $ciudad = !empty($ubicacion->city_fiscal) ? CatMunicipios::find()->where(['municipio_id' => $ubicacion->city_fiscal])->one()->nombre : null;
                        $estado = !empty($ubicacion->state_fiscal) ? CatEntidades::find()->where(['entidad_id' => $ubicacion->state_fiscal])->one()->nombre : null;
                        $formato['desc'] = "$ubicacion->calle_fiscal, $colonia $ubicacion->cp_fiscal, $ciudad, $estado";
    
                        array_push($arr_response, $formato);
                    }
                }
            }
        }
        return $arr_response;
    }

    /* public function getUbicacionUnicaHistorico($provider_id, $tipoUbicacion, $isRequired = false, $tipo = 'bys'){
        $response = ["ubicacion" => $isRequired ? new Ubicacion() : null, "fotografia" => $isRequired ? new FotografiaNegocio() : null ];
        $last_send_value = Historico::find()->select([new Expression('fecha_validacion::date')])->where(['and', ['provider_id' => $provider_id]])->orderBy(['fecha_validacion' => SORT_DESC])->one()['fecha_validacion'];
        $model = Historico::findBySql("SELECT * FROM provider.historico WHERE modelo = 'Ubicacion' AND provider_id = $provider_id AND tipo = '$tipo' AND data::json->>'tipo' = '$tipoUbicacion' AND fecha_validacion::date = '$last_send_value'")->orderBy(['fecha_validacion' => SORT_DESC])->one();
        if( is_null($model) || empty($model)){
            try{
                $ubicacion_json = Yii::$app->db->createCommand("
                    SELECT arr.item_object FROM provider.historico, json_array_elements(data) WITH ordinality arr(item_object)
                    WHERE modelo = 'Ubicacion' AND provider_id = :provider_id AND tipo = :tipo AND arr.item_object::jsonb->>'tipo' = :tipo_ubicacion
                    AND fecha_validacion::date = :date_val ORDER BY fecha_validacion DESC",
                    ['provider_id' => $provider_id, 'tipo' => $tipo, 'tipo_ubicacion' => $tipoUbicacion,'date_val' => $last_send_value])->queryOne();
                if( !empty($ubicacion_json['item_object']) ){
                    $response["ubicacion"] = self::setAtributesModel("app\models\\Ubicacion", json_decode($ubicacion_json['item_object']));
                    $fotografia_json = Yii::$app->db->createCommand("
                        SELECT arr.item_object FROM provider.historico, json_array_elements(data) WITH ordinality arr(item_object)
                        WHERE modelo = 'FotografiaNegocio' AND provider_id = :provider_id AND tipo = :tipo AND arr.item_object::jsonb->>'ubicacion_id' = :ubicacion_id
                        AND fecha_validacion::date = :date_val ORDER BY fecha_validacion DESC",
                        ['provider_id' => $provider_id, 'tipo' => $tipo, 'ubicacion_id' => $response["ubicacion"]->ubicacion_id, 'date_val' => $last_send_value])->queryOne();
                    if( !empty($fotografia_json['item_object']) ){ $response["fotografia"] = self::setAtributesModel("app\models\\FotografiaNegocio", json_decode($fotografia_json['item_object'])); }
                }
            }catch(yii\db\Exception $error){  /*var_dump($error->errorInfo[0]); SI ENTRA AQUI ES PORQUE NO EXISTE ESE TIPO DE UBICACION  }
        }else{
            $response["ubicacion"] = self::setAtributesModel("app\models\\Ubicacion", $model);
            $histFotografia = Historico::findBySql("SELECT * FROM provider.historico WHERE modelo = 'FotografiaNegocio' AND provider_id = $provider_id AND tipo = '$tipo' AND data::json->>'ubicacion_id' = :ubicacion_id", ['ubicacion_id' => $response["ubicacion"]->ubicacion_id])->orderBy(['fecha_validacion' => SORT_DESC])->one();
            if( !empty($histFotografia->data) ){ $response["fotografia"] = self::setAtributesModel("app\models\\FotografiaNegocio", $histFotografia); }
        }

        return $response;
    } */

    /* public function getUbicacionesHistorico($provider_id, $tipo = 'bys'){
        $direcciones = [];
        $last_send_value = Historico::find()->select([new Expression('fecha_validacion::date')])->where(['and', ['provider_id' => $provider_id]])->orderBy(['fecha_validacion' => SORT_DESC])->one()['fecha_validacion'];
        $model = Historico::findBySql("SELECT * FROM provider.historico WHERE modelo = 'Ubicacion' AND provider_id = $provider_id AND tipo = '$tipo' 
                AND fecha_validacion::date = '$last_send_value' AND data::json->>'type_address_prov' is null AND json_typeof(data) = 'object' ")
                ->orderBy(['fecha_validacion' => SORT_DESC])->all(); //Busca datos legacy primeramente en caso de migracion
        if( count($model) > 0 ){
            foreach($model as $historico){
                $response = ["ubicacion" => new Ubicacion(), "fotografia" => new FotografiaNegocio()];
                $response["ubicacion"] = self::setAtributesModel("app\models\\Ubicacion", $historico);
                $histFotografia = Historico::findBySql("SELECT * FROM provider.historico WHERE modelo = 'FotografiaNegocio' AND provider_id = $provider_id AND tipo = '$tipo' 
                        AND data::json->>'ubicacion_id' = :ubicacion_id AND json_typeof(data) = 'object'", ['ubicacion_id' => $response["ubicacion"]->ubicacion_id])
                        ->orderBy(['fecha_validacion' => SORT_DESC])->one(); //Busca datos legacy primeramente en caso de migracion
                if( !empty($histFotografia->data) ){ $response["fotografia"] = self::setAtributesModel("app\models\\FotografiaNegocio", $histFotografia); }
                array_push($direcciones, $response);
            }
        }else{ //De no ser objeto, lo trata como json array
            $ubicaciones_json = Yii::$app->db->createCommand("
                SELECT arr.item_object FROM provider.historico, json_array_elements(data) WITH ordinality arr(item_object)
                WHERE modelo = 'Ubicacion' AND provider_id = :provider_id AND tipo = :tipo AND arr.item_object::jsonb->>'type_address_prov' is null
                AND fecha_validacion::date = :date_val AND json_typeof(data) = 'array' ORDER BY fecha_validacion  DESC",
                ['provider_id' => $provider_id, 'tipo' => $tipo, 'date_val' => $last_send_value])->queryAll();
            foreach($ubicaciones_json as $registro){
                if( !empty($registro['item_object']) ){
                    $response = ["ubicacion" => new Ubicacion(), "fotografia" => new FotografiaNegocio()];
                    $response["ubicacion"] = self::setAtributesModel("app\models\\Ubicacion", json_decode($registro['item_object']));
                    $fotografia_json = Yii::$app->db->createCommand("
                        SELECT arr.item_object FROM provider.historico, json_array_elements(data) WITH ordinality arr(item_object)
                        WHERE modelo = 'FotografiaNegocio' AND provider_id = :provider_id AND tipo = :tipo AND arr.item_object::jsonb->>'ubicacion_id' = :ubicacion_id
                        AND fecha_validacion::date = :date_val AND json_typeof(data) = 'array' ORDER BY fecha_validacion DESC",
                        ['provider_id' => $provider_id, 'tipo' => $tipo, 'ubicacion_id' => $response["ubicacion"]->ubicacion_id, 'date_val' => $last_send_value])->queryOne();
                    if( !empty($fotografia_json['item_object']) ){ $response["fotografia"] = self::setAtributesModel("app\models\\FotografiaNegocio", json_decode($fotografia_json['item_object'])); }
                    array_push($direcciones, $response);
                }
            }
        }

        return $direcciones;
    } */

    /* public function getArrayHistoricoCordUbicaciones($provider_id, $tipo = 'bys'){
        $arr_response = [];
        $last_send_value = Historico::find()->select([new Expression('fecha_validacion::date')])->where(['and', ['provider_id' => $provider_id]])->orderBy(['fecha_validacion' => SORT_DESC])->one()['fecha_validacion'];
        $model = Historico::findBySql("SELECT * FROM provider.historico WHERE modelo = 'Ubicacion' AND provider_id = $provider_id 
                AND tipo = '$tipo' AND fecha_validacion::date = '$last_send_value' AND json_typeof(data) = 'object'")->orderBy(['fecha_validacion' => SORT_DESC])->all();
        if( count($model) > 0 ){
            foreach($model as $historico){
                $formato = ['coords' => null, 'titulo' => null];
                $ubicacion = self::setAtributesModel("app\models\\Ubicacion", $historico);
                $location = json_encode($ubicacion->geo_ubicacion);
                $formato['coords'] = $location;
                $formato['titulo'] = $ubicacion->tipo;
                $colonia = !empty($ubicacion->colonia_fiscal) ? CatAsentamientos::find()->where(['asentamiento_id' => $ubicacion->colonia_fiscal])->one()->nombre : null;
                $ciudad = !empty($ubicacion->city_fiscal) ? CatMunicipios::find()->where(['municipio_id' => $ubicacion->city_fiscal])->one()->nombre : null;
                $estado = !empty($ubicacion->state_fiscal) ? CatEntidades::find()->where(['entidad_id' => $ubicacion->state_fiscal])->one()->nombre : null;
                $formato['desc'] = "$ubicacion->calle_fiscal, $colonia $ubicacion->cp_fiscal, $ciudad, $estado";

                array_push($arr_response, $formato);
            }
        }else{ //De no ser objeto, lo trata como json array
            $models = Historico::findBySql("SELECT * FROM provider.historico WHERE modelo = 'Ubicacion' AND provider_id = $provider_id 
                AND tipo = '$tipo' AND fecha_validacion::date = '$last_send_value' AND json_typeof(data) = 'array'")->orderBy(['fecha_validacion' => SORT_DESC])->one();
            if( !is_null($models) || !empty($models) ){
                foreach( (array) $models->data as $registro){
                    $formato = ['coords' => null, 'titulo' => null];
                    $ubicacion = self::setAtributesModel("app\models\\Ubicacion", $registro);
                    $location = json_encode($ubicacion->geo_ubicacion);
                    $formato['coords'] = $location;
                    $formato['titulo'] = $ubicacion->tipo;
                    $colonia = !empty($ubicacion->colonia_fiscal) ? CatAsentamientos::find()->where(['asentamiento_id' => $ubicacion->colonia_fiscal])->one()->nombre : null;
                    $ciudad = !empty($ubicacion->city_fiscal) ? CatMunicipios::find()->where(['municipio_id' => $ubicacion->city_fiscal])->one()->nombre : null;
                    $estado = !empty($ubicacion->state_fiscal) ? CatEntidades::find()->where(['entidad_id' => $ubicacion->state_fiscal])->one()->nombre : null;
                    $formato['desc'] = "$ubicacion->calle_fiscal, $colonia $ubicacion->cp_fiscal, $ciudad, $estado";
    
                    array_push($arr_response, $formato);
                }
            }
        }
        return $arr_response;
    } */

    public function findModelsProviderGiro($id){
        return Yii::$app->db->createCommand(
            "SELECT pg.producto_id , cf.concepto_familia_id as familia, cg.concepto_grupo_id as grupo, cl.concepto_linea_id as linea FROM provider_giro pg 
            INNER JOIN productos_servicios.concepto_linea cl on pg.producto_id = cl.concepto_linea_id 
            INNER JOIN productos_servicios.concepto_grupo cg on cg.concepto_grupo_id = cl.concepto_grupo_id
            INNER JOIN productos_servicios.concepto_familia cf on cf.concepto_familia_id = cg.concepto_familia_id
            WHERE provider_id = :id", [':id' => intval($id) ]
        )->queryAll();
    }

    public function actionList_colpostal($id)
    {
        $colonias = ArrayHelper::map(CatAsentamientos::find()->select(['cv_asentamiento', 'nombre'])
            ->where(['cp' => $id])
            ->orderBy(['nombre' => SORT_ASC])
            ->asArray()->orderBy(['nombre' => 'ASC'])->all(), 'cv_asentamiento', 'nombre');
        $con = '';
        $con .= '<option value=""></option>';
        if (count($colonias) > 0) {
            foreach ($colonias as $key => $value) $con .= "<option value='" . $key . "'>" . $value . "</option>";
        } else {
            $con .= "<option></option>";
        }
        echo $con;
    }

    public function actionList_city($id)
    {
        $city = ArrayHelper::map(CatMunicipios::find()->select(['municipio_id', 'nombre'])
            ->where(['entidad_id' => $id])->asArray()->orderBy(['nombre' => 'ASC'])->all(), 'municipio_id', 'nombre');


        $con = '';

        $con .= '<option value="">Selecciona...</option>';

        if ($city) {
            foreach ($city as $key => $val)
                $con .= "<option value=" . $key . ">" . $val . "</option>";
        } else {
            $con .= '<option value="">No hay resultados</option>';
        }
        echo $con;
    }

    public function actionList_localidades($id, $state)
    {
        $city = ArrayHelper::map(CatLocalidades::find()->select(['localidad_id', 'nombre'])
            ->where(['and', ['entidad_id' => $state], ['municipio_id' => $id]])->asArray()->orderBy(['nombre' => 'ASC'])->all(), 'localidad_id', 'nombre');


        $con = '';

        $con .= '<option value="">Selecciona...</option>';

        if ($city) {
            foreach ($city as $key => $val)
                $con .= "<option value=" . $key . ">" . $val . "</option>";
        } else {
            $con .= '<option value="">No hay resultados</option>';
        }
        echo $con;
    }

    public function actionList_asentamiento($id, $state)
    {
        $asentamiento = ArrayHelper::map(CatAsentamientos::find()->select(['asentamiento_id', 'nombre'])
            ->where(['and', ['entidad_id' => $state], ['municipio_id' => $id]])->asArray()->orderBy(['nombre' => 'ASC'])->all(), 'asentamiento_id', 'nombre');

        $con = '';
        $con .= '<option value="">Selecciona...</option>';
        if ($asentamiento) {
            foreach ($asentamiento as $key => $val)
                $con .= "<option value=" . $key . ">" . $val . "</option>";
        } else {
            $con .= '<option value="">No hay resultados</option>';
        }
        echo $con;
    }

    public function findModelPorcentaje($id)
    {
        $model = Porcentaje::find()->where(['register_id' => $id])->one();
        if ($model == null || empty($model)) {
            $model = new Porcentaje();
        }
        return $model;
    }

    public function findModelActaConstitutiva($id)
    {

        $model = ActaConstitutiva::find()->where(['provider_id' => $id])->one();
        if ($model == null || empty($model)) {
            $model = new ActaConstitutiva();
        }
        return $model;
    }


    public function findModelActaConstitutivaConsulta($id)
    {

        $modelHistory = Historico::find()->where(['and',['modelo' => 'ActaConstitutiva'],['provider_id' => $id]])->orderBy(['fecha_validacion' => SORT_DESC])->limit(1)->one();

        if(isset($modelHistory['data']) && !empty($modelHistory['data'])){
            $model_acta = $modelHistory['data'];
            $model = new ActaConstitutiva();
            $model->load($model_acta, '');
        }else{
            $model = new ActaConstitutiva();
        }

        return $model;
    }

    public function findModelCurp($id)
    {
        $model = Curp::find()->where(['provider_id' => $id])->one();
        if ($model == null || empty($model)) {
            $model = new Curp();
        }
        return $model;
    }

    public function findModelCurpConsulta($id)
    {

        $modelHistory = Historico::find()->where(['and',['modelo' => 'Curp'],['provider_id' => $id]])->orderBy(['fecha_validacion' => SORT_DESC])->limit(1)->one();

        if(isset($modelHistory['data']) && !empty($modelHistory['data'])){
            $model_curp = $modelHistory['data'];
            $model = new Curp();
            $model->load($model_curp, '');
        }else{
            $model = new Curp();
        }

        return $model;
    }


    public function findModelIdoficial($id)
    {
        $model = IdOficial::find()->where(['provider_id' => $id])->one();
        if ($model == null || empty($model)) {
            $model = new IdOficial();
        }
        return $model;
    }

    public function findModelIdoficialConsulta($id)
    {

        $modelHistory = Historico::find()->where(['and',['modelo' => 'IdOficial'],['provider_id' => $id]])->orderBy(['fecha_validacion' => SORT_DESC])->limit(1)->one();

        if(isset($modelHistory['data']) && !empty($modelHistory['data'])){
            $model_idOficial = $modelHistory['data'];
            $model = new IdOficial();
            $model->load($model_idOficial, '');
        }else{
            $model = new IdOficial();
        }
        return $model;
    }

    public function findModelComprobanteDomicilio($id)
    {
        $model = ComprobanteDomicilio::find()->where(['provider_id' => $id])->one();
        if ($model == null || empty($model)) {
            $model = new ComprobanteDomicilio();
        }
        return $model;
    }

    public function findModelComprobanteDomicilioConsulta($id)
    {

        $modelHistory = Historico::find()->where(['and',['modelo' => 'ComprobanteDomicilio'],['provider_id' => $id]])->orderBy(['fecha_validacion' => SORT_DESC])->limit(1)->one();

        if(isset($modelHistory['data']) && !empty($modelHistory['data'])){
            $model_curp = $modelHistory['data'];
            $model = new ComprobanteDomicilio();
            $model->load($model_curp, '');
        }else{
            $model = new ComprobanteDomicilio();
        }

        return $model;
    }


    public function findModelUbicacionProv($id = 0)
    {

        if (($model = Ubicacion::find()->where(['and', ['provider_id' => $id], ['type_address_prov' => 'DOMICILIO FISCAL']])->one()) === null) {
            $model = new Ubicacion();
        }
        return $model;
    }

    public function findModelRfcProv($id = 0)
    {

        if (($model = Rfc::find()->where(['provider_id' => $id])->one()) === null) {
            $model = new Rfc();
        }
        return $model;
    }

    public function findModelDomicilioProv($id = 0)
    {

        if (($model = ComprobanteDomicilio::find()->where(['provider_id' => $id])->one()) === null) {
            $model = new ComprobanteDomicilio();
        }
        return $model;
    }


    public function actionIndexProviderCotejar()
    {
        $params = Yii::$app->request->queryParams;
        $data = ProviderQuery::getAllData($params);

        $this->layout = 'home';
        $searchModel = new ProviderSearch();

        $activarAgenda = ProviderQuery::getAllDataActAgenda($params);;//$searchModel->search(Yii::$app->request->queryParams, 'activarAgendaFilter');
        $agendarCita = $searchModel->search(Yii::$app->request->queryParams, 'agendarCitaFilter');


        return $this->render('index-provider-cotejar', [
            'searchModel' => $searchModel,
            'dataProvider' => $data,
            'activarAgenda' => $activarAgenda,
            'agendarCita' => $agendarCita
        ]);
    }


    public function actionDetalleCotejar($user_id)
    {
        if (!Yii::$app->user->can(Usuarios::ROLE_VALIDADOR_PROVEEDORES) && !Yii::$app->user->can(Usuarios::ROLE_ADMIN_PROVIDER)) {
            $this->msgError();
        }

        $provider = \app\models\Provider::find()->where(['user_id' => $user_id])->one();
        $id = $provider->provider_id;

        $tipo_persona = $provider->tipo_persona;
        $model = \app\models\Rfc::find()->where(['provider_id' => $id])->one();
        $representante = $this->findModelRepresentanteLegal($id);

        $metodo_pago = \app\models\IntervencionBancaria::find()->where(['provider_id' => $id])->one();
        $ultima_declaracion = UltimaDeclaracion::find()->where(['provider_id' => $id])->one();
        if ($ultima_declaracion == null || empty($ultima_declaracion)) {
            $ultima_declaracion = new UltimaDeclaracion();
        }
        $acta_constitutiva = $this->findModelActaConstitutiva($id);

        $model_status = new Status();
        $model_status->scenario = Status::SCENARIO_CREATE_COTEJAR;
        $model_validado = new DatosValidados();
        $model_validado->scenario = Status::SCENARIO_CREATE_COTEJAR;

        $calendario = Calendar::find()->where(['and', ['user' => $user_id], ['status' => 'CONFIRMADA']])->orderBy(['visit_day' => SORT_DESC])->one();
        return $this->renderAjax('detalle-cotejar', [
            'id' => $id,
            'tipo_persona' => $tipo_persona,
            'model' => $model,
            'representante' => $representante,
            'model_comprobante_domicilio' => $this->findModelComprobanteDomicilio($id),
            'model_act' => $this->findModelAct($id),
            'model_registro_acta' => $this->findModelRegistroActas($id),
            'model_mpago' => $metodo_pago,
            'fotografia' => $this->findModelFotografia($id),
            'model_ubicacion' => $this->findModelUbicacion($id),
            'clientes_contratos' => $this->findModelClientesContratos($id),
            'certificacion' => $this->findModelCertificacion($id),
            'accionistas' => $this->findModelAccionistas($id),
            'ultima_declaracion' => $ultima_declaracion,
            'modelProductos' => $this->findModelProviderGiro($id),
            'model_status' => $model_status,
            'model_validado' => $model_validado,
            'modActa' => $this->findModelModificacionActa($id),
            'model_acta_constitutiva' => $acta_constitutiva,
            'model_curp' => $this->findModelCurp($id),
            'model_idoficial' => $this->findModelIdoficial($id),
            'calendar' => $calendario,
            'namePro' => $this->getNameProvider($id)

        ]);
    }


    public function actionCotejoActa($id = null, $p = null, $type = null)
    {

        if ($id && $p && $type) {


            $calendar = Calendar::findOne(intval(base64_decode($id)));
            $prov = Provider::findOne(intval(base64_decode($p)));
            if ($prov && isset($calendar->visit_day) && $calendar->visit_day == date('Y-m-d') && isset($calendar->status) && $calendar->status == 'CONFIRMADA') {

                $model_status = new Status();
                $model_status->scenario = Status::SCENARIO_CREATE_COTEJAR;
                $model_validado = new DatosValidados();
                $model_validado->scenario = Status::SCENARIO_CREATE_COTEJAR;

                $model_acta = new ActaCircunstanciada();

                return $this->render('cotejo-acta', [
                    'type' => base64_decode($type),
                    'model_status' => $model_status,
                    'model_validado' => $model_validado,
                    'prov' => $prov,
                    'calendar' => $calendar,
                    'id' => $prov->provider_id,
                    'model_acta' => $model_acta
                ]);
            }
        }

        return $this->goHome();

    }


    public function actionData_download_acta($asistio, $auto_t, $auto_n, $rfc, $prov, $f_i_c, $h_i_c, $f_f_c, $h_f_c, $rev_t, $rev, $prov_id, $val_rech, $rech)
    {


        $tipo = 'bys';
        $proveedor = Provider::findOne($prov_id);

        if (!$proveedor) {
            return $this->redirect('/');
        }

        $path_doc = 'acta_circunstanciada';
        if (!file_exists($path_doc)) {
            mkdir($path_doc, 0777, true);
        }


        $nameRazon = $proveedor->name_razon_social;

        $urlActa = 'acta_moral_' . $val_rech;
        if ($proveedor->tipo_persona == 'Persona física') {

            $nameRazon = $proveedor->pf_nombre.' '.$proveedor->pf_ap_paterno.' '.$proveedor->pf_ap_materno;

            if ($asistio == 'Si') {
                $urlActa = 'acta_fisica_' . $val_rech;
            } else {
                $urlActa = 'acta_fisica_otra_' . $val_rech;
            }

        }

        $content = $this->renderPartial('/carta/pdf/' . $urlActa, [
            'model' => $proveedor,
            'asistio' => $asistio,
            'auto_t' => $auto_t,
            'auto_n' => $auto_n,
            'rfc' => $rfc,
            'prov' => $nameRazon,
            'f_i_c' => $f_i_c,
            'h_i_c' => $h_i_c,
            'f_f_c' => $f_f_c,
            'h_f_c' => $h_f_c,
            'rev_t' => $rev_t,
            'rev' => $rev,
            'rech' => $rech
        ]);


        $date_year = date("Y");
        $date_month = date("m");
        $stylesheet = file_get_contents('css/pdf.css');
        $mpdf = new \mPDF('utf-8', 'A4', 0, '', 0, 0, 5, 15, 0, 0);
        $mpdf->SetTitle('ActaCircunstanciada');
        $mpdf->SetHTMLHeader($this->renderPartial('/carta/pdf/header_acta', ['asunto' => 'Cotejo de Documentos', 'interesado' => $nameRazon, 'rfc' => $rfc]));
        $mpdf->SetHTMLFooter($this->renderPartial('/carta/pdf/footer_acta', ['revisor' => $rev, 'asistio_si_no' => $asistio, 'asistio_t' => $auto_t, 'asistio' => $auto_n, 'proveedor' => $nameRazon]));
        $mpdf->WriteHTML($stylesheet, 1);
        /* $mpdf->defaultfooterline = 0; */
        $mpdf->WriteHTML($content);
        $filename = 'acta_circunstanciada' . '/' . $proveedor->rfc . '_acta_' . $date_year . '_' . $date_month . '_' . $tipo . time() . '.pdf';
        $mpdf->Output($filename, 'D');
        chmod($filename, 0777);

        unlink($filename);
        exit();
        return false;

    }

    public function actionDetalleValidados($user_id)
    {
        if (Yii::$app->request->isAjax) {
            if (!Yii::$app->user->can(Usuarios::ROLE_ADMIN_PROVIDER) && !Yii::$app->user->can(Usuarios::ROLE_ADMIN_CONTRATISTAS) && !Yii::$app->user->can(Usuarios::ROLE_VALIDADOR_PROVEEDORES)) {
                $this->msgError();
            }

            $provider = \app\models\Provider::find()->where(['user_id' => $user_id])->one();
            $id = $provider->provider_id;
            $tipo_persona = $provider->tipo_persona;
            $tipo_provider = $provider->tipo_provider;
            $model = \app\models\Rfc::find()->where(['provider_id' => $id])->one();
            $representante = $this->findModelRepresentanteLegal($id);

            $perfilData = $this->findModelProv($id);

            $metodo_pago = \app\models\IntervencionBancaria::find()->where(['provider_id' => $id])->one();

            $acta_constitutiva = $this->findModelActaConstitutiva($id);

            $modelEF = [];
            $modelCC = [];
            $modelDA = [];
            $modelExperiencia = [];
            $modelTecnicos = [];
            $modelMaquinaria = [];
            $ultima_declaracion = [];
            $certificacion = [];
            $modelProductos = [];
            $registro = [];
            $dataProviderCarta = [];
            $dataProviderCertificado = [];
            $dataVisitas = [];
            $fotografia = $this->findModelFotografia($id);
            $model_ubicacion = $this->findModelUbicacion($id);
            $model_ubicacionNl = Ubicacion::find()->where(['and', ['provider_id' => $id], ['status_' . $tipo_provider => 'VALIDADO'], ['type_address_prov' => 'NL']])->one();


            $models = (Object)[
                'imss' => [],
                'escritura' => [],
                'registro' => [],
                'idoficial' => []
            ];

            if (Yii::$app->user->can(Usuarios::ROLE_ADMIN_PROVIDER) || Yii::$app->user->can(Usuarios::ROLE_VALIDADOR_PROVEEDORES)) {

                $certificacion = $this->findModelCertificacion($id);
                $modelProductos = $this->findModelProviderGiro($id);
                $ultima_declaracion = UltimaDeclaracion::find()->where(['provider_id' => $id])->one();
                if ($ultima_declaracion == null || empty($ultima_declaracion)) {
                    $ultima_declaracion = new UltimaDeclaracion();
                }
            } else {
                $modelEF = $this->findModelEF($id);
                $modelCC = $this->findModelCC($id);
                $modelDA = $this->findModelDA($id);
                $registro = $this->findModelRegistroPublico($id);
                $modelExperiencia = $this->findModelExperiencia($id);
                $modelTecnicos = $this->findModelTecnicos($id);
                $modelMaquinaria = $this->findModelMaquinaria($id);

                $models = (Object)[
                    'imss' => $this->findModelRegistroImss($id),
                    'escritura' => $this->findModelEscrituraPublica($id),
                    'registro' => $this->findModelRegistroPublico($id),
                ];

                $searchModelCarta = new \app\models\HistoricoCartaProtestaSearch();
                $searchModelCarta->provider_id = $provider->provider_id;
                $dataProviderCarta = $searchModelCarta->search(Yii::$app->request->queryParams,'consultaCartas');

                $searchModelCertificado = new \app\models\HistoricoCertificadosSearch();
                $searchModelCertificado->provider_id = $provider->provider_id;

                $dataProviderCertificado = $searchModelCertificado->search(Yii::$app->request->queryParams, 'validCertificadosConsultaOp');
                $dataProviderCursos = $searchModelCertificado->search(Yii::$app->request->queryParams, 'validCursosOp');

                $searchVisitas = new VisitSearch();
                /* $searchVisitas->statusV = false;
                $dataVisitas = $searchVisitas->search(Yii::$app->request->queryParams); */
                $dataVisitas = $searchVisitas->getConsulta($id);

            }

            $modelDT = EstadoFinancieroTipo::find()->where(['provider_id' => $provider->provider_id])->one();
            return $this->renderAjax('detalle-validados', [
                'data'=> $this->getDataOP($provider->provider_id),
                'id' => $id,
                'organigrama'=>$this->findModelOrganigrama($provider->provider_id),
                'tipo_persona' => $tipo_persona,
                'model' => $model,
                'representante' => $representante,
                'model_comprobante_domicilio' => $this->findModelComprobanteDomicilio($id),
                'model_act' => $this->findModelAct($id),
                'model_registro_acta' => $this->findModelRegistroActas($id),
                'model_mpago' => $metodo_pago,
                'fotografia' => $fotografia,
                'model_ubicacion' => $model_ubicacion,
                'clientes_contratos' => $this->findModelClientesContratos($id),
                'certificacion' => $certificacion,
                'accionistas' => $this->findModelAccionistas($id),
                'ultima_declaracion' => $ultima_declaracion,
                'modelProductos' => $modelProductos,
                'modActa' => $this->findModelModificacionActa($id),
                'model_acta_constitutiva' => $acta_constitutiva,
                'model_curp' => $this->findModelCurp($id),
                'model_idoficial' => $this->findModelIdoficial($id),
                'modelEF' => $modelEF,
                'modelCC' => $modelCC,
                'modelDA' => $modelDA,
                'modelExperiencia' => $modelExperiencia,
                'modelTecnicos' => $modelTecnicos,
                'modelMaquinaria' => $modelMaquinaria,
                'perfilData' => $perfilData,
                'model_ubicacionNl' => $model_ubicacionNl,
                'namePro' => $this->getNameProvider($id),
                'registro' => $registro,
                'modelos' => $models,
                'cartas_data' => $dataProviderCarta,
                'tipo_provider' => 'rep_' . $tipo_provider,
                'historico_certificados_data' => $dataProviderCertificado,
                'historico_cursos' => $dataProviderCursos,
                'visitas' => $dataVisitas,
                'type' => isset($modelDT->type)?$modelDT->type:''
            ]);
        }
        return $this->goHome();
    }

    public function actionDetalleProveedor($prov=null,$eval=false,$isValidation = 0){

        if (Yii::$app->request->isAjax) {
            if (!Yii::$app->user->can(Usuarios::ROLE_ADMIN_PROVIDER) &&
                !Yii::$app->user->can(Usuarios::ROLE_ADMIN_CONTRATISTAS) &&
                !Yii::$app->user->can(Usuarios::ROLE_CALL_CENTER) &&
                !Yii::$app->user->can(Usuarios::ROLE_ADMIN_CONCURSOS) &&
                !Yii::$app->user->can(Usuarios::ROLE_VALIDADOR_PROVEEDORES)) {
                $this->msgError();
            }

            $provider = \app\models\Provider::findOne(intval($prov));
            $id = $provider->provider_id;
            $tipo_persona = $provider->tipo_persona;
            $tipo_provider = $provider->tipo_provider;
            $model = \app\models\Rfc::find()->where(['provider_id' => $id])->one();
            $representante = $this->findModelRepresentanteLegal($id,true);

            //$perfilData = $this->findModelProv($id);
            $perfil = Perfil::find()->where(['provider_id'=>$id])->one();

            $metodo_pago = \app\models\IntervencionBancaria::find()->where(['provider_id' => $id])->one();

            $acta_constitutiva = $this->findModelActaConstitutiva($id);

            $parametros_tipo_fiscal = $provider->tipo_provider == 'bys' ? [["type_address_prov" => "DOMICILIO FISCAL"]] : [["status_op" => STATUS::STATUS_VALIDADO], ["type_address_prov" => "DOMICILIO FISCAL"]];
            $parametros_nl = $provider->tipo_provider == 'bys' ? [['type_address_prov' => 'NL']] : [["status_op" => STATUS::STATUS_VALIDADO], ['type_address_prov' => 'NL']];

            //si se consulta desde BYS mostrar datos aunque sea OP
            $tipo = $provider->tipo_provider;
            if(in_array(Yii::$app->user->identity->role,[Usuarios::ROLE_ADMIN_PROVIDER,Usuarios::ROLE_VALIDADOR_PROVEEDORES, Usuarios::ROLE_REVISOR_PROVEEDORES]) && $provider->tipo_provider == 'op' ){
                unset($parametros_nl[0]);
                unset($parametros_tipo_fiscal[0]);
                $tipo = 'bys';
            }

            $ubicacion_fiscal = UbicacionController::findModelDireccionFotografia($id, $parametros_tipo_fiscal, true);
            $ubicacion_nl = UbicacionController::findModelDireccionFotografia($id, $parametros_nl);

            $ubicaciones = UbicacionController::findDireccionesArray($id, $tipo);

            $array_ubicaciones = UbicacionController::getArrayUbicaciones($id);

            $ultima_declaracion = UltimaDeclaracion::find()->where(['provider_id' => $id])->one();
            if ($ultima_declaracion == null || empty($ultima_declaracion)) {
                $ultima_declaracion = new UltimaDeclaracion();
            }

            $modules = Module::find()->where(['provider_id'=>$id])->asArray()->all();

            $rechazos = Status::find()->where(['and',['provider_id'=>$id],['status_bys'=>Status::STATUS_TERMINADO_PRO]])->asArray()->all();

            return $this->renderAjax('detalle-proveedor', [
                'perfil'=>$perfil,
                'rechazos'=>$rechazos,
                'modules'=>$modules,
                'provider'=>$provider,
                'eval'=> $eval ? true: false,
                'id' => $id,
                'tipo_persona' => $tipo_persona,
                'model' => $model,
                'representante' => $representante,
                'model_comprobante_domicilio' => $this->findModelComprobanteDomicilio($id),
                'model_act' => $this->findModelAct($id),
                'model_registro_acta' => $this->findModelRegistroActas($id),
                'model_mpago' => $metodo_pago,
                'ubicaciones' => $ubicaciones,
                'ubicacion_fiscal' => $ubicacion_fiscal,
                'ubicacion_nl' => $ubicacion_nl,
                "ubicaciones_arr" => $array_ubicaciones,
                'clientes_contratos' => $this->findModelClientesContratos($id,true),
                'certificacion' => $this->findModelCertificacion($id,true),
                'accionistas' => $this->findModelAccionistas($id,true),
                'ultima_declaracion' => $ultima_declaracion,
                'modelProductos' => $this->getDataProviderGiro($id),
                'modActa' => $this->findModelModificacionActa($id,true),
                'model_acta_constitutiva' => $acta_constitutiva,
                'model_curp' => $this->findModelCurp($id),
                'model_idoficial' => $this->findModelIdoficial($id),
                //'perfilData' => $perfilData, //ya se manda en $provider
                'namePro' => $provider->getNameOrRazonSocial(),//$this->getNameProvider($id),
                'tipo_provider' => 'rep_' . $tipo_provider,
                'isValidation' => $isValidation
            ]);
        }
        return $this->goHome();
    }

    public function actionExperienceAnalysis()
    {
        $m = new ProviderSearch();
        $models = (object)[
            'experiencia' => $m->search(Yii::$app->request->queryParams, 'validExperienciaAdm'),
            'search' => $m
        ];
        return $this->render('experience-analysis', [
            'modelos' => $models
        ]);


        $tipo = 'op';

        return $this->render('index-validador', [
            'modelos' => $modelos,
            'tipo' => $tipo
        ]);
    }

    public function actionViewAnalysis($id = null)
    {

        if ($id != null) {

            $nombre_empresa = Provider::find()->select('name_comercial')->where(['provider_id' => $id])->one()['name_comercial'];

            $dataTipoObra = self::calEspecialidadObra($id, 'obra');


            $dataTipoEspecialidad = self::calEspecialidadObra($id, 'Especialidad');

            $analisisObr = [];
            $analisisEsp = [];
            if (isset($dataTipoObra['analisis']) && !empty($dataTipoObra['analisis'])) {
                $analisisObr = self::sort_by_orden($dataTipoObra['analisis']);
            }

            if (isset($dataTipoEspecialidad['analisis']) && !empty($dataTipoEspecialidad['analisis'])) {
                $analisisEsp = self::sort_by_orden($dataTipoEspecialidad['analisis']);
            }


            return $this->renderAjax('view-analysis', [
                'array_final_obra' => $dataTipoObra['arreglo'],
                'numero_obras' => $dataTipoObra['numero'],
                'total_obras' => $dataTipoObra['total'],
                'sumObraP1' => $dataTipoObra['sumOEP1'],
                'sumObraP2' => $dataTipoObra['sumOEP2'],
                'nombre_empresa' => $nombre_empresa,
                'array_final_obra_especialidad' => $dataTipoEspecialidad['arreglo'],
                'numero_obras_especialidad' => $dataTipoEspecialidad['numero'],
                'total_obras_especialidad' => $dataTipoEspecialidad['total'],
                'sumObraP1_especialidad' => $dataTipoEspecialidad['sumOEP1'],
                'sumObraP2_especialidad' => $dataTipoEspecialidad['sumOEP2'],
                'analisis_obra' => $analisisObr,
                'analisis_especialidad' => $analisisEsp,
                'sumAnalisisTo' => $dataTipoObra['sumAnalisis'],
                'sumAnalisisTe' => $dataTipoEspecialidad['sumAnalisis'],

            ]);
        }
    }

    public function actionReport()
    {

        $params = Yii::$app->request->queryParams;

        $valTo = null;
        if (isset($params['to']) && !empty($params['to'])) {
            $valTo = intval(base64_decode($params['to']));
            $params['to'] = $valTo;

        }

        $valE = null;
        if (isset($params['e']) && !empty($params['e'])) {
            $valE = intval(base64_decode($params['e']));
            $params['e'] = $valE;
        }

        $valM = null;
        if (isset($params['m']) && !empty($params['m'])) {
            $valM = intval(base64_decode($params['m']));
            $params['m'] = $valM;
        }

        $valC = null;
        if (isset($params['c']) && !empty($params['c'])) {
            $valC = base64_decode($params['c']);
            $params['c'] = $valC;
        }

        $allData = ProviderQuery::getAllDataReport($params);

        $data = $allData['data'];

        $markers = $allData['markers'];
        $this->layout = 'home';


        $tipoObra = ArrayHelper::map(SubespecialidadCategory::find()->select(['category_id', 'nombre'])->all(), 'category_id', 'nombre');
        $esp = ProviderQuery::getEsp($valTo);

        $mun = ArrayHelper::map(Yii::$app->db->createCommand("select cm.municipio_id, cm.nombre from cat_municipios cm
              join provider.ubicacion u on u.city_fiscal = cm.municipio_id
              where u.type_address_prov = 'DOMICILIO FISCAL'
              group by municipio_id,nombre")->queryAll(), 'municipio_id', 'nombre');

        return $this->render('report', [
            'dataProvider' => $data,
            'tipoObra' => $tipoObra,
            'mun' => $mun,
            'valTo' => $valTo,
            'valE' => $valE,
            'valM' => $valM,
            'valC' => $valC,
            'esp' => $esp,
            'markers' => json_encode($markers)
        ]);

    }

    protected function findModelEscrituraPublica($id)
    {
        $model = EscrituraPublica::find()->where(['provider_id' => $id])->one();
        if (!$model) {
            $model = new EscrituraPublica();
        }
        return $model;
    }


    protected function findModelEFConsulta($id)
    {

        $modelHistory = Historico::find()->where(['and',['modelo' => 'EstadoFinanciero'],['provider_id' => $id]])->orderBy(['fecha_validacion' => SORT_DESC])->limit(1)->one();

        if(isset($modelHistory['data']) && !empty($modelHistory['data'])){
            $model_curp = $modelHistory['data'];
            $model = new EstadoFinanciero();
            $model->load($model_curp, '');
        }else{
            $model = new EstadoFinanciero();
        }

        return $model;
    }

    protected function findModelDAConsulta($id)
    {

        $modelHistory = Historico::find()->where(['and',['modelo' => 'DeclaracionIsr'],['provider_id' => $id]])->orderBy(['fecha_validacion' => SORT_DESC])->limit(1)->one();

        if(isset($modelHistory['data']) && !empty($modelHistory['data'])){
            $model_curp = $modelHistory['data'];
            $model = new DeclaracionIsr();
            $model->load($model_curp, '');
        }else{
            $model = new DeclaracionIsr();
        }

        return $model;
    }


    protected function findModelCCConsulta($id)
    {

        $modelHistory = Historico::find()->where(['and',['modelo' => 'CapacidadContratacion'],['provider_id' => $id]])->orderBy(['fecha_validacion' => SORT_DESC])->limit(1)->one();

        if(isset($modelHistory['data']) && !empty($modelHistory['data'])){
            $model_curp = $modelHistory['data'];
            $model = new CapacidadContratacion();
            $model->load($model_curp, '');
        }else{
            $model = new CapacidadContratacion();
        }

        return $model;
    }

    protected function findModelRegistroPublico($id)
    {
        $model = RegistroPublicoPropiedad::find()->where(['provider_id' => $id])->one();
        if (!$model) {
            $model = new RegistroPublicoPropiedad();
        }
        return $model;
    }

    protected function findModelRegistroImss($id)
    {
        $model = RegistroImss::find()->where(['provider_id' => $id])->one();
        if (!$model) {
            $model = new RegistroImss();
        }
        return $model;
    }

    public function actionVerifyrfc($rfc)
    {

        if (Yii::$app->request->isAjax) {
            $rfc=base64_decode($rfc);
            $trueFalse = 0;
            if (strlen($rfc) == 12 || strlen($rfc) == 13) {
                if ((Rfc::find()->where(['rfc' => trim($rfc)])->count(1)) == 0) {
                    $trueFalse = 1;
                }
            }

            return $trueFalse;
        }


    }



    public function actionEnabled()
    {
        $params = Yii::$app->request->queryParams;
        $tipo = self::providerType();
        $allData = self::enebledPro($params);

        return $this->render('enabled', [
            'model' => $allData,
            'tipo' => $tipo,
        ]);
    }

    public static function enebledPro($params)
    {

        $sql = '';
        $bind = [];
        if (isset($params['r']) && !empty($params['r'])) {

            $rfc = $params['r'];

            $sql .= " where lower(unaccent(rfc)) ilike '%' || lower(unaccent(:rfc)) || '%' ";

            $bind = [':rfc' => "%$rfc%"];
        }

        if (isset($params['n']) && !empty($params['n']) && isset($params['r']) && !empty($params['r'])) {

            $nom = $params['n'];

            $sql .= " and lower(unaccent(nombre)) ilike '%' || lower(unaccent(:nom)) || '%' ";

            $bind = [':rfc' => "%$rfc%", ':nom' => "%$nom%"];
        } elseif (isset($params['n']) && !empty($params['n'])) {
            $nom = $params['n'];

            $sql .= " where lower(unaccent(nombre)) ilike '%' || lower(unaccent(:nom)) || '%' ";
            $bind = [':nom' => "%$nom%"];
        }elseif (isset($params['pd']) && !empty($params['pd']) && $sql!=''){
            $sta = $params['pd'];

            $sql .= " and permanently_disabled = :st";

            $bind[':st'] =  $sta;
        }elseif (isset($params['pd']) && !empty($params['pd']) && $sql==''){

            $sta = $params['pd'];

            $sql .= " where permanently_disabled = :st";
            $bind = [':st' => $sta];
        }

        $status = Yii::$app->user->can(Usuarios::ROLE_VALIDADOR_PROVEEDORES) || Yii::$app->user->can(Usuarios::ROLE_ADMIN_PROVIDER)? 'bys' : 'op';

        $totalCount = Yii::$app->db
            ->createCommand("
                    WITH rw as(
                select
                p.provider_id,p.creation_date,p.rfc,p.permanently_disabled,
                                        CASE WHEN (p.name_razon_social is not null and p.name_razon_social != '') then p.name_razon_social else concat_ws(' ',p.pf_nombre,p.pf_ap_paterno,p.pf_ap_materno) end as nombre
                from
                provider p order by creation_date)
                select count(1) from rw $sql
            
            ",$bind)
            ->queryScalar();


        $sql = [
            'sql' => "WITH rw as(
                select
                p.provider_id,p.creation_date,p.rfc,p.permanently_disabled,
                                        CASE WHEN (p.name_razon_social is not null and p.name_razon_social != '') then p.name_razon_social else concat_ws(' ',p.pf_nombre,p.pf_ap_paterno,p.pf_ap_materno) end as nombre
                from
                provider p order by creation_date)
                select rw.provider_id,rw.creation_date::date,rw.rfc,rw.permanently_disabled, rw.nombre from rw $sql
            
            ",
            'totalCount' => $totalCount,
            //'sort' =>false, to remove the table header sorting
            'sort' => [
                'attributes' => [
                    'rfc' => [
                        'asc' => ['rfc' => SORT_ASC],
                        'desc' => ['rfc' => SORT_DESC],
                        'default' => SORT_DESC,
                        'label' => 'Rfc',
                    ],
                    'nombre' => [
                        'asc' => ['nombre' => SORT_ASC],
                        'desc' => ['nombre' => SORT_DESC],
                        'default' => SORT_DESC,
                        'label' => 'Nombre',
                    ],
                ],
            ]
        ];

        if ($bind) {
            $sql['params'] = $bind;
        }

        $dataProvider = new SqlDataProvider($sql);

        return $dataProvider;
    }


    public function actionEnabledChange($id = null,$status=null){

        if(($mod = Provider::findOne(intval($id)))!==null && in_array($status,['ACTIVO','INACTIVO'])){
            $mod->permanently_disabled = $status;
            $mod->save(false);
        }

        return $this->redirect('/provider/enabled');

    }

    public function actionNoLocalizados(){

        $searchModel = new ProviderSearch();
        $dataProvider = $searchModel->search(Yii::$app->request->queryParams, 'noLocalizados');

        return $this->render('no-localizados', [
            'searchModel' => $searchModel,
            'dataProvider' => $dataProvider,
        ]);
    }

    public function actionIncompletos(){

        $searchModel = new ProviderSearch();
        $dataProvider = $searchModel->search(Yii::$app->request->queryParams, 'incompletos');

        return $this->render('incompletos', [
            'searchModel' => $searchModel,
            'dataProvider' => $dataProvider,
        ]);
    }


    public function actionVerify(){
        NoLocalizados::deleteAll(['and',['type_register' => 'NO_LOCALIZADOS'],['status' => true]]);
        $dataProvider = ArrayHelper::getColumn(HistoricoCartaProtesta::find()->select('provider_id')->where(['provider_type' => 'bys'])->distinct()->asArray()->all(),'provider_id');
        foreach ($dataProvider as $v){
            $acta_firmada = Provider::find()->select('status_carta_bys')->where(['provider_id' => $v])->one()['status_carta_bys'];
            if(GeneralController::verificarModulosValidador('status_bys', $v, 'bys', $acta_firmada)){
                $status = Provider::find()->select('status_cotejar')->where(['provider_id' => $v])->one()['status_cotejar'];
                if($status ==''){
                    if(($mo = NoLocalizados::find()->where(['and',['type_register' => 'INCOMPLETOS'],['provider_id' => $v],['status' => true]])->count(1))==0){
                        $model = new NoLocalizados();
                        $model->provider_id = $v;
                        $model->type_register = 'NO_LOCALIZADOS';
                        $model->save();
                    }
                }
            }
        }
    }

    public function actionVerifyCert()
    {
        NoLocalizados::deleteAll(['and',['type_register' => 'INCOMPLETOS'],['status' => true]]);
        $dataProvider = ArrayHelper::getColumn(HistoricoCertificados::find()->select('provider_id')->where(['and', ['tipo' => 'CERTIFICADO'], ['provider_type' => 'bys']])->distinct()->asArray()->all(), 'provider_id');
        foreach ($dataProvider as $v) {
            $acta_firmada = Provider::find()->select('status_carta_bys')->where(['provider_id' => $v])->one()['status_carta_bys'];
            if (!GeneralController::verificarModulosValidador('status_bys', $v, 'bys', $acta_firmada)) {
                if(($mo = NoLocalizados::find()->where(['and',['type_register' => 'INCOMPLETOS'],['provider_id' => $v],['status' => true]])->count(1))==0){
                    $model = new NoLocalizados();
                    $model->provider_id = $v;
                    $model->type_register = 'INCOMPLETOS';
                    $model->save();
                }
            }
        }
    }


    public function actionVerifyPendientes()
    {
        ini_set('max_execution_time', '0');
        ModulesComplete::deleteAll();
        $dataProvider = ArrayHelper::getColumn(Yii::$app->db->createCommand("select provider_id from provider /*where permanently_disabled in( 'EN PROCESO','INACTIVO')*/ order by provider_id desc")->queryAll(), 'provider_id');

        foreach ($dataProvider as $v) {
            if(($modules = GeneralController::verificarModulosValPen('status_bys', $v, 'bys', null)) == 12){
            $model = new ModulesComplete();
            $model->provider_id = $v;
            $model->modules = intval($modules);
            $model->save();
            }
        }

        return $this->redirect('index-admin-provider');
    }

    public function actionResetpass($id=null){
        if(!$id || !Yii::$app->request->isAjax)
            return $this->redirect('home');
        $sql = " 
            select (u.creation_date) as creacion_cuenta, (p.creation_date) as creacion_proveedor ,name_razon_social, name_comercial,u.email, u.username, u.user_id, p.\"Clave_ProveedorSire\" as sire  
            from public.provider p inner join public.usuarios u on u.user_id = p.user_id where p.provider_id = ".intval($id);
        $model = Provider::findBySql($sql)->one();
        return $this->renderAjax('resetpass',['model'=>$model]);
    }


    public function actionResetpassword($id=null){
        if(!$id || !Yii::$app->user->can(Usuarios::ROLE_ADMIN_PROVIDER))
            return $this->redirect('home');
        $model = Usuarios::findOne($id);
        $model->password = '$2y$12$Ye2HLQh48RxPq8AS2J4LvO4RGSuVLnrPqhNBIxEb78JoSM14ZyX/y';//Temp@123
        $model->save(false);
        return $this->redirect('index-detalle-provider');
    }

    public function actionConsultaFuncionarios($id){

        $provider_data = Provider::findOne($id);
        $accionistas = self::findModelAccionistas($id, true);
        $representantes = self::findModelRepresentanteLegal($id, true);
        
        return $this->renderAjax('consulta-funcionarios',[
            'model' => $provider_data,
            'modelAccionistas' => $accionistas,
            'modelRepresentantes' => $representantes 
        ]);
    }

    public function actionDetalleAtributosProveedor($id, $perfil = 'bys'){
        $provider = Provider::findOne(intval($id));
        $provider_id = $provider->provider_id;

        $tipo = $perfil;
        $parametros_tipo_fiscal = $tipo == 'bys' ? [["type_address_prov" => "DOMICILIO FISCAL"]] : [["status_op" => STATUS::STATUS_VALIDADO], ["type_address_prov" => "DOMICILIO FISCAL"]];
        $parametros_nl = $tipo == 'bys' ? [['type_address_prov' => 'NL']] : [["status_op" => STATUS::STATUS_VALIDADO], ['type_address_prov' => 'NL']];

        $nombre_provider = $this->getNameProvider($provider_id);
        $curp_data = $this->findModelCurp($provider_id);
        $idOf_data = $this->findModelIdoficial($provider_id);
        $rfc_data = $this->findRfc($provider_id);
        $actividades_data = $this->findModelAct($provider_id);
        $acta_data = $this->findModelActaConstitutiva($provider_id);

        $representante_data = $this->findModelRepresentanteLegal($provider_id,true); //VALIDAR PARA OP, ya que trae todos
        $accionistas_data = $this->findModelAccionistas($provider_id,true); //VALIDAR PARA OP, ya que trae todos
        $mod_acta_data = $this->findModelModificacionActa($provider_id,true); //VALIDAR PARA OP, ya que trae todos

        $pago_data = $this->findDatosBancarios($provider_id);
        $ubicacion_nl_data = UbicacionController::findModelDireccionFotografia($provider_id, $parametros_nl);
        $ubicaciones_data = UbicacionController::findDireccionesArray($provider_id, 'bys');

        if($tipo == 'bys'){
            
            $productos_data = $this->getDataProviderGiro($provider_id);
            $ult_declaracion_data = UltimaDeclaracion::find()->where(['provider_id' => $provider_id])->one();
            $ult_declaracion_data = ($ult_declaracion_data == null || empty($ult_declaracion_data)) ? new UltimaDeclaracion() : $ult_declaracion_data;
            $clientes_data = $this->findModelClientesContratos($provider_id,true);
            $certificados_data = $this->findModelCertificacion($provider_id,true);
            $ubicacion_fiscal_data = UbicacionController::findModelDireccionFotografia($provider_id, $parametros_tipo_fiscal, true);
            return $this->renderAjax('detalle-atributos-proveedor-bys', [ 
                'nombre_provider' => $nombre_provider, 
                'provider_data' => $provider,
                'rfc_data' => $rfc_data,
                'acta_data' => $acta_data,
                'mod_acta_data' => $mod_acta_data,
                'representante_data' => $representante_data,
                'accionistas_data' => $accionistas_data,
                'actividades_data' => $actividades_data,
                'productos_data' => $productos_data,
                'ult_declaracion_data' => $ult_declaracion_data,
                'clientes_data' => $clientes_data,
                'certificados_data' => $certificados_data,
                'ubicacion_fiscal_data' => $ubicacion_fiscal_data,
                'ubicacion_nl_data' => $ubicacion_nl_data,
                'ubicaciones_data' => $ubicaciones_data,
                'pago_data' => $pago_data,
                'curp_data' => $curp_data,
                'idOf_data' => $idOf_data
            ]);
        }else if ( $tipo == "op"){
            $dom_fiscal_data = $this->findModelUbicacionProv($provider_id);
            $comprobante_data = $this->findModelComprobanteDomicilio($provider_id);
            $curriculum_data = $this->findModelCurriculum($provider_id);
            $referencias_data = $this->findReferenciasOp($provider_id);
            $imss_data = $this->findModelRegistroImss($provider_id);
            $propiedad_data = $this->findModelEscrituraPublica($provider_id);
            $registro_data = $this->findModelRegistroPublico($provider_id);
            $fotografias_data = $this->findFotografiasOp($provider_id);
            $organigrama_data = $this->findModelOrganigrama($provider_id);
            $personal_tec_data = $this->findPersonalTecnico($provider_id);
            $maquinaria_data = $this->findMaquinarias($provider_id);
            $experiencia_data = $this->findExperiencia($provider_id);
            $estado_financiero_data = $this->findModelEF($provider_id);
            $declaracion_isr_data = $this->findModelDA($provider_id);
            $capacidad_contratacion_data = $this->findModelCC($provider_id);
            return $this->renderAjax('detalle-atributos-proveedor-op', [ 
                'nombre_provider' => $nombre_provider, 
                'provider_data' => $provider,
                'dom_fiscal_data' => $dom_fiscal_data,
                'comprobante_data' => $comprobante_data,
                'curriculum_data' => $curriculum_data,
                'referencias_data' => $referencias_data,
                'curp_data' => $curp_data,
                'idOf_data' => $idOf_data,
                'rfc_data' => $rfc_data,
                'actividades_data' => $actividades_data,
                'acta_data' => $acta_data,
                'imss_data' => $imss_data,
                'propiedad_data' => $propiedad_data,
                'registro_data' => $registro_data,
                'representante_data' => $representante_data,
                'accionistas_data' => $accionistas_data,
                'mod_acta_data' => $mod_acta_data,
                'pago_data' => $pago_data,
                'ubicacion_nl_data' => $ubicacion_nl_data,
                'ubicaciones_data' => $ubicaciones_data,
                'fotografias_data' => $fotografias_data,
                'organigrama_data' => $organigrama_data,
                'personal_tec_data' => $personal_tec_data,
                'maquinaria_data' => $maquinaria_data,
                'experiencia_data' => $experiencia_data,
                'estado_financiero_data' => $estado_financiero_data,
                'declaracion_isr_data' => $declaracion_isr_data,
                'capacidad_contratacion_data' => $capacidad_contratacion_data
            ]);
        } 
        
    }

    //Funcion compatible con logicas de guardado en historico V3
    //Se obtiene el ultimo registro de validacion
    public function getHistoricoModelo($modelo, $provider_id, $rt_array = false, $tipo = 'bys'){
        $class = "app\models\\" . $modelo;
        $model = Historico::find()->where(['and',['modelo' => $modelo], ['provider_id' => $provider_id], ['tipo' => $tipo] ])->orderBy(['fecha_validacion' => SORT_DESC])->one();
        if(is_null($model) || empty($model)){ return $rt_array ? [new $class()] : new $class(); }
        if(GeneralController::is_multidimentional($model->data) && $modelo != 'Ubicacion'){
            if($rt_array){
                $arr_result = [];
                foreach( (array) $model->data as $registro ){
                    $result = self::setAtributesModel($class, $registro);
                    array_push($arr_result, $result);
                }
                $response = $arr_result;
            }else{ $response = self::setAtributesModel($class, $model->data[0]); }
        }else{ $response = $rt_array ? [self::setAtributesModel($class, $model->data)] : self::setAtributesModel($class, $model->data); }

        return $response;
    }

    public function getArrayHistorico($modelo, $provider_id, $tipo='bys'){
        $class = "app\models\\" . $modelo;
        $response = [new $class];
        $condicion = ['modelo' => $modelo, 'provider_id' => $provider_id, 'tipo' => $tipo, "json_typeof(data)" => 'array'];
        $fecha_validacion = self::get_last_validation_date_module($modelo, $provider_id, $tipo);
        if( !is_null($fecha_validacion) ){ 
            $model_v3 = Historico::find()->where(['and', $condicion , ['>=', 'fecha_validacion', $fecha_validacion ] ] )->one();
            if( is_null($model_v3) ){
                $condicion['json_typeof(data)'] = 'object';
                $model_v1 = Historico::find()->where(['and', $condicion , ['>=', 'fecha_validacion', $fecha_validacion ] ] )->all();
                if( count($model_v1) > 0 ){
                    $response = [];
                    foreach( $model_v1 as $historico ){
                        $instancia = self::setAtributesModel($class, $historico->data);
                        array_push($response, $instancia);
                    }
                }
            }else{
                $response = [];
                foreach( (array) $model_v3->data as $historico ){
                    $result = self::setAtributesModel($class, $historico);
                    array_push($response, $result);
                }
            }
        }

        return $response;
    }

    public function get_last_validation_date_module($modelo, $provider_id, $tipo){
        $date = Historico::find()->select(new Expression("to_char(fecha_validacion - INTERVAL '5 min','yyyy-mm-dd HH:MI') as fecha_validacion"))->where(['and', ['provider_id' => $provider_id, 'modelo' => $modelo, 'tipo' => $tipo]])
        ->orderBy(['fecha_validacion' => SORT_DESC])->one()['fecha_validacion'];
        
        return $date;
    }

    //Funcion compatible con logicas de guardado en historico V2
    //Se obtiene la fecha de la validacion mas reciente y se muestran los datos de esa fecha
    /* public function getHistoricoModelo($modelo, $provider_id, $rt_array = false, $tipo = 'bys'){
        $class = "app\models\\" . $modelo;
        $last_send_value = Historico::find()->select([new Expression('fecha_validacion::date')])->where(['and', ['provider_id' => $provider_id]])->orderBy(['fecha_validacion' => SORT_DESC])->one()['fecha_validacion'];
        $model = Historico::find()->where(['and',['modelo' => $modelo], ['provider_id' => $provider_id], ['tipo' => $tipo], ['(fecha_validacion)::date' => $last_send_value] ])->orderBy(['fecha_validacion' => SORT_DESC])->one();
        if(is_null($model) || empty($model)){ return $rt_array ? [new $class()] : new $class(); }
        if(GeneralController::is_multidimentional($model->data)){
            if($rt_array){
                $arr_result = [];
                foreach( (array) $model->data as $registro ){
                    $result = self::setAtributesModel($class, $registro);
                    array_push($arr_result, $result);
                }
                $response = $arr_result;
            }else{ $response = self::setAtributesModel($class, $model->data[0]); }
        }else{ $response = $rt_array ? [self::setAtributesModel($class, $model->data)] : self::setAtributesModel($class, $model->data); }

        return $response;
    } */

    //LEGACY V1
    /* public function getHistoricoModelo($modelo, $provider_id, $rt_array = false, $tipo = 'bys'){
        $class = "app\models\\" . $modelo;
        if($rt_array){
            $model = Historico::find()->where(['and',['modelo' => $modelo], ['provider_id' => $provider_id], ['tipo' => $tipo]])->orderBy(['fecha_validacion' => SORT_DESC])->all();
            $response = [];
            if( count($model) > 0 ){
                foreach($model as $historico){
                    $result = self::setAtributesModel($class, $historico);
                    array_push($response, $result);
                }
            }else{ $response =  [new $class()]; }
        }else{
            $model = Historico::find()->where(['and',['modelo' => $modelo], ['provider_id' => $provider_id], ['tipo' => $tipo]])->orderBy(['fecha_validacion' => SORT_DESC])->one();
            $response = new $class();
            if( !empty($model->data) ){ $response = self::setAtributesModel($class, $model); }
        }

        return $response;
    } */

    private function setAtributesModel($clase, $dataHistorico){
        $response = new $clase;
        foreach($dataHistorico as $atributo => $valor){
            if($response->hasAttribute($atributo)){ $response->$atributo = $valor; }
        }

        return $response;
    }

    //ESTA FUNCION SE HIZO POR EL CAMBIO DE RECORDS CON JSON OBJECTS A JSON ARRAYS, SE COMENTO PORQUE SE REGRESO AL MODELO ANTERIOR
    /* private function getHistoricoModelo($modelo, $provider_id, $rt_array = false, $tipo = 'bys'){
        $class = "app\models\\" . $modelo;
        $model = Historico::find()->where(['and',['modelo' => $modelo], ['provider_id' => $provider_id], ['tipo' => $tipo]])->orderBy(['fecha_validacion' => SORT_DESC])->one();
        if ( is_null($model) || empty($model->data) ){ return $rt_array ? [new $class()] : new $class(); }
        $arr_result = [];
        foreach( (array) $model->data as $registro ){
            if( $rt_array ){ return self::setAtributesModel($class, $registro); }
            else{
                $result = self::setAtributesModel($class, $registro);
                array_push($arr_result, $result);
            }
        }

        return $arr_result;
    }

    private function setAtributesModel($clase, $historico){
        $response = new $clase;
        foreach($historico as $atributo => $valor){
            if($response->hasAttribute($atributo)){ $response->$atributo = $valor; }
        }

        return $response;
    } */

    public function findRfc($provider_id){
        $model = Rfc::find()->where(['provider_id' => $provider_id])->one();
        return $model = $model ? $model : new Rfc(); 
    }

    public function findReferenciasOp($provider_id){
        $model = ClientesContratos::find()->where(['and',['activo' => true, 'tipo' => 'op', 'provider_id' => $provider_id]])->all();
        return $model = $model ? $model : [ new ClientesContratos() ]; 
    }

    public function findModelCurriculum($provider_id){
        $model = Curriculum::find()->where(['provider_id' => $provider_id])->one();
        return $model = $model ? $model : new Curriculum(); 
    }

    public function findDatosBancarios($provider_id){
        $model = IntervencionBancaria::find()->where(['provider_id' => $provider_id])->one();
        return $model = $model ? $model : new IntervencionBancaria(); 
    }

    public function findFotografiasOp($provider_id){
        $model = FotografiaNegocio::find()->where(['and', ['provider_id' => $provider_id, 'activo' => true ]])->all();
        return $model = $model ? $model : [new FotografiaNegocio()]; 
    }

    public function findModelOrganigrama($provider_id){
        $model = Organigrama::find()->where(['provider_id'=>$provider_id])->one();
        return $model = $model ? $model : new Organigrama(); 
    }

    public function findPersonalTecnico($provider_id){
        $model = PersonalTecnico::find()->where(['and', ['provider_id' => $provider_id, 'activo' => true ]])->all();
        return $model = $model ? $model : [new PersonalTecnico()]; 
    }

    public function findMaquinarias($provider_id){
        $model = MaquinariaEquipos::find()->where(['and', ['provider_id' => $provider_id], ['activo' => true]])->all();
        return $model = $model ? $model : [new MaquinariaEquipos()]; 
    }

    public function findExperiencia($provider_id){
        $model = Experiencia::find()->where(['and', ['provider_id' => $provider_id], ['activo' => true]])->all();
        return $model = $model ? $model : [new Experiencia()]; 
    }

    public function actionGetprogress($id){
        $all = [];
        $data = $this->getDataOP(intval($id));
        foreach ($data->arriba as $row){
            array_push($all,$row['porcentaje']);
        }
        foreach ($data->abajo as $row){
            array_push($all,$row['porcentaje']);
        }
        return round(array_sum($all)/count($all),2);
    }


    // Función para obtener un mapeo de los estatus del dashboard en el menú izquierdo 

    public static function actionGetMapStatusDashboar(){
            $provider = Provider::find()
                ->where(['provider_id' => Yii::$app->user->identity->providerid])
                ->one();
            $tipo = $provider->tipo_provider;
            if ($tipo == 'op') {
                $datos = self::getDataOP();
            } else {
                $datos = self::getDashboardDataBys();
            }

            $aux=[];
            foreach ($datos as $division){
                $aux = array_merge($aux,$division);
            }

            $mapeo = array_map(function( $modulo){
                 $status = $modulo['terminado']? 'terminado':( $modulo['rechazo']?'rechazo':($modulo['enviado']?'enviado':'pendiente')); 
                 return [$modulo['icono']=> $status];
                },$aux );

            $valor = json_encode(array_merge(...array_values($mapeo)));

        return $valor;
    }

    public static function actionGetListProveedores($texto=null){
        $proveedores = Yii::$app->db->createCommand(
            "WITH proveedores as (SELECT p.provider_id,
            CASE WHEN (p.name_razon_social is not null and p.name_razon_social != '') then p.name_razon_social 
            else concat_ws(' ',p.pf_nombre,p.pf_ap_paterno,p.pf_ap_materno) end as nombre,
            CASE WHEN (r.rfc is not null) THEN r.rfc 
                WHEN (p.rfc is not null) THEN p.rfc
                WHEN (u.rfc is not null) THEN u.rfc
                ELSE null END as rfc
            FROM provider p LEFT JOIN provider.rfc r on p.provider_id = r.provider_id
            LEFT JOIN usuarios u on p.user_id = u.user_id WHERE p.enabled = true)
            SELECT * FROM proveedores pf
            WHERE nombre ilike '%$texto%' OR rfc ilike '%$texto%' ORDER BY nombre" )->queryAll();

        echo json_encode($proveedores);exit();
    }

    /**
     * Metodo que retorna los grupos de los productos que ofrece el proveedor, con limite de 3 y prioridad a las especialidades
     * @param Integer $provider_id Id del proveedor
     * @return Array Arreglo con la descripcion del grupo de productos
     */
    public static function getGrupoProductosProveedor($provider_id){
        $productos = ProviderGiro::find()->select("grupo.descripcion, provider_giro.especialidad")
        ->innerJoin(['producto'=>'productos.producto'],'producto.producto_id = provider_giro.producto_id')
        ->innerJoin(['clase'=>'productos.clase'],'clase.clase_id = producto.clase_id')
        ->innerJoin(['grupo'=>'productos.grupo'],'grupo.grupo_id = clase.grupo_id')
        ->where(['and',['provider_giro.provider_id' => $provider_id, 'provider_giro.active' => true]])
        ->orderBy("provider_giro.especialidad DESC")
        ->distinct()->asArray()->limit(3)->all();

        return ( !is_null($productos) && !empty($productos) ) ? $productos : [];
    }

    public static function registroCertificado($provider_id, $fecha, $codigo_verificacion, $filename, $tipo, $tipo_cert = HistoricoCertificados::TIPO_CERTIFICADO, $vigencia=null){
        $certificado = new HistoricoCertificados();
        $certificado->provider_id = $provider_id;
        $certificado->codigo_verificacion = $codigo_verificacion;
        $certificado->url_certificado = $filename;
        $certificado->provider_type = $tipo;
        if( !is_null($tipo_cert) && !empty($tipo_cert) ){ $certificado->tipo = $tipo_cert; }
        if( !is_null($fecha) && !empty($fecha) ){ $certificado->created_at = $fecha; }
        $certificado->vigencia = $vigencia;
        $certificado->save();
        /* if ($certificado->save() && $certificado_previo_id) {
            $model = FirstUpdateCertificate::findOne(intval($certificado_previo_id));
            $model->historico_certificado_id = $certificado->historico_certificados_id;
            $model->save();
        } */

        return $certificado->historico_certificados_id;
    }
    public function actionBusqueda($q=null){
        /* $proveedores_query = "WITH proveedores as (
            SELECT p.provider_id, p.\"Clave_ProveedorSire\" as num_proveedor, CASE WHEN (p.name_razon_social is not null and p.name_razon_social != '') then p.name_razon_social 
            else concat_ws(' ',p.pf_nombre,p.pf_ap_paterno,p.pf_ap_materno) end as nombre,
            r.rfc, p.vigencia FROM provider p LEFT JOIN provider.rfc r on p.provider_id = r.provider_id 
            WHERE p.enabled is true AND p.permanently_disabled = 'ACTIVO')
            SELECT * FROM proveedores pf
            WHERE nombre ilike '%$q%' OR rfc ilike '%$q%' OR num_proveedor = '$q'  ORDER BY nombre"; */
        $proveedores_query = " WITH proveedores AS (
            SELECT p.provider_id, p.\"Clave_ProveedorSire\" AS num_proveedor, 
                CASE 
                    WHEN (p.name_razon_social IS NOT NULL AND p.name_razon_social != '') THEN p.name_razon_social 
                    ELSE CONCAT_WS(' ', p.pf_nombre, p.pf_ap_paterno, p.pf_ap_materno) 
                END AS nombre,
                r.rfc, MAX(hc.created_at)::date AS ultimo_certificado, p.vigencia::date AS vigencia_certificado
            FROM provider p LEFT JOIN provider.rfc r ON p.provider_id = r.provider_id 
            LEFT JOIN (
                SELECT hc.provider_id, hc.created_at FROM historico_certificados hc
                WHERE hc.tipo = 'CERTIFICADO' AND hc.provider_type = 'bys' ORDER BY hc.created_at DESC
            ) hc ON p.provider_id = hc.provider_id
            WHERE p.enabled IS TRUE AND p.permanently_disabled = 'ACTIVO' GROUP BY p.provider_id, r.rfc_id
        )
        SELECT * FROM proveedores pf
        WHERE nombre ILIKE '%$q%' OR rfc ILIKE '%$q%' OR num_proveedor = '$q' ORDER BY nombre LIMIT 20";
        $provider_result = Yii::$app->db->createCommand($proveedores_query)->queryAll();
        return json_encode($provider_result);
    }

    /* public function getVigenciaCertificado($provider_id, $sql_mode = false){
        $query = "SELECT hc.created_at, p.vigencia FROM historico_certificados hc INNER JOIN provider p ON p.provider_id = hc.provider_id
        WHERE hc.tipo = 'CERTIFICADO' AND hc.provider_type = 'bys' AND hc.provider_id = $provider_id ORDER BY hc.created_at DESC LIMIT 1";

        $result = Yii::$app->db->createCommand($query)->queryOne();
        return json_encode($result);
    }
 */



}