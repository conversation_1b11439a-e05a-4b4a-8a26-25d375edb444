<?php

namespace app\controllers;

use app\helpers\GeneralController;

use app\models\AuditorDependencia;
use app\models\ProviderSearch;
use kartik\form\ActiveForm;
use Yii;
use app\models\Auditoria;
use app\models\AuditoriaSearch;
use yii\helpers\ArrayHelper;
use yii\helpers\Url;
use yii\web\Controller;
use yii\web\NotFoundHttpException;
use yii\filters\VerbFilter;
use yii\web\Response;
use app\models\Usuarios;


/**
 * AuditoriaController implements the CRUD actions for Auditoria model.
 */
class AuditoriaController extends Controller
{
    /**
     * @inheritdoc
     */
    public function behaviors()
    {
        return [
            'verbs' => [
                'class' => VerbFilter::className(),
                'actions' => [
                    //'delete' => ['POST'],
                ],
            ],
        ];
    }

    /**
     * Lists all Auditoria models.
     * @return mixed
     */
    public function actionIndex()
    {

        if(Yii::$app->user->identity->role != Usuarios::ROLE_DIOS && !Yii::$app->user->can(Usuarios::ROLE_ADMIN_PROVIDER)){
            $this->msgError();
        }

        $searchModel = new AuditoriaSearch();
        $dataProvider = $searchModel->search(Yii::$app->request->queryParams);
        $this->layout='home';
        return $this->render('index', [
            'searchModel' => $searchModel,
            'dataProvider' => $dataProvider,
        ]);
    }

    public function msgError(){
        echo $this->render('/site/error',['name' => 'Lo sentimos...','message' => 'Acceso denegado']);
        
        exit();
    }
    /**
     * Displays a single Auditoria model.
     * @param integer $id
     * @return mixed
     * @throws NotFoundHttpException if the model cannot be found
     */
    public function actionView($id)
    {
        if (Yii::$app->user->isGuest) {
            return $this->goHome();
        }

        if(Yii::$app->user->identity->role != Usuarios::ROLE_DIOS && !Yii::$app->user->can(Usuarios::ROLE_ADMIN_PROVIDER)){
            $this->msgError();
        }
        
        $this->layout='home';
        
        return $this->render('view', [
            'model' => $this->findModel($id),
        ]);
    }

    public function actionViewAuditoria($id)
    {
        if(isset($_POST['prov']) && $_POST['prov'] != ''){
            $proveedores = explode(",",$_POST['prov']);
            foreach ($proveedores as $proveedor) {
                Yii::$app->db->createCommand()->insert('provider.auditoria_relacion', [
                    'status' => 'ACTIVO',
                    'auditoria_id' => $id,
                    'provider_id'=>$proveedor,
                    'created_by' => Yii::$app->user->getId(),
                ])->execute();
            }
        }
        $searchModel = new ProviderSearch();
        $searchModel->auditoria_id = $id;
        $dataProvider = $searchModel->search(Yii::$app->request->queryParams,'sinAsignar');
        $dataAsignados = $searchModel->search(Yii::$app->request->queryParams,'Asignados');
        $this->layout='home';
        return $this->render('view-auditoria', [
            'model' => $this->findModel($id),
            'searchModel' => $searchModel,
            'dataProvider' => $dataProvider,
            'dataAsignados' => $dataAsignados
        ]);
    }
    public function actionDeleteAsignacion($id)
    {

        $searchModel = new ProviderSearch();
        $searchModel->auditoria_id = $id;
        $dataProvider = $searchModel->search(Yii::$app->request->queryParams,'sinAsignar');
        $dataAsignados = $searchModel->search(Yii::$app->request->queryParams,'Asignados');
        $this->layout='home';


        if(isset($_POST['id_prov']) && $_POST['id_prov'] != ''){

            $proveedores = explode(",",$_POST['id_prov']);
            foreach ($proveedores as $proveedor) {
                \Yii::$app->db->createCommand("update provider.auditoria_relacion set status = 'INACTIVO' where provider_id=$proveedor and auditoria_id=:id")
                    ->bindValue(':id', $id)
                    ->execute();
            }
            return $this->render('view-auditoria', [
                'model' => $this->findModel($id),
                'searchModel' => $searchModel,
                'dataProvider' => $dataProvider,
                'dataAsignados' => $dataAsignados
            ]);

        }else{
            return $this->render('view-auditoria', [
                'model' => $this->findModel($id),
                'searchModel' => $searchModel,
                'dataProvider' => $dataProvider,
                'dataAsignados' => $dataAsignados
            ]);
        }
    }
    /**
     * Creates a new Auditoria model.
     * If creation is successful, the browser will be redirected to the 'view' page.
     * @return mixed
     */
    public function actionCreate()
    {
        $model = new Auditoria();
        $this->layout='home';
        $searchModel = new ProviderSearch();
        $dataProvider = $searchModel->search(Yii::$app->request->queryParams,'consultaFilter');
        if(Yii::$app->request->isAjax && $model->load(Yii::$app->request->post())){
            Yii::$app->response->format = Response::FORMAT_JSON;
            return ActiveForm::validate($model);
        }else if ($model->load(Yii::$app->request->post())) {
            $t = Yii::$app->db->beginTransaction();
            $contraseña = $model->password;
            if($model->save()){
                $url_auditoria = Url::home('https')."provider/consulta?id=".base64_encode($model->auditoria_id)."&token=".base64_encode($model->email);
                $model_new = Auditoria::findOne($model->auditoria_id);
                $model_new->url_auditoria = $url_auditoria;
                if($model_new->save()){
                    $t->commit();
                    $params = ['correo' => $model->email, 'pass' => $contraseña, 'fecha_inicio' => $model->fecha_inicio, 'fecha_fin' => $model->fecha_fin, 'link' => $url_auditoria];

                    GeneralController::sendEmail('/provider/correos/auditoria',null,$model->email,'Acceso a la plataforma Proveedores',$params);
                    return $this->redirect(['view', 'id' => $model->auditoria_id]);
                }
                $t->rollBack();
            }
        }

        $dependencia = ArrayHelper::map(AuditorDependencia::find()->select(['nombre','nombre'])->all(),'nombre','nombre');

        return $this->render('create', [
            'model' => $model,
            'searchModel' => $searchModel,
            'dataProvider' => $dataProvider,
            'dependencia' => $dependencia
        ]);
    }

    /**
     * Updates an existing Auditoria model.
     * If update is successful, the browser will be redirected to the 'view' page.
     * @param integer $id
     * @return mixed
     * @throws NotFoundHttpException if the model cannot be found
     */
    public function actionUpdate($id)
    {
        if(Yii::$app->user->identity->role != Usuarios::ROLE_DIOS && !Yii::$app->user->can(Usuarios::ROLE_ADMIN_PROVIDER)){
            $this->msgError();
        }

        $this->layout='home';
        $model = $this->findModel($id);
        $searchModel = new ProviderSearch();
        $dataProvider = $searchModel->search(Yii::$app->request->queryParams,'consultaFilter');
        if(Yii::$app->request->isAjax && $model->load(Yii::$app->request->post())){
            Yii::$app->response->format = Response::FORMAT_JSON;
            return ActiveForm::validate($model);
        }else if ($model->load(Yii::$app->request->post()) && $model->save()) {
            return $this->redirect(['view', 'id' => $model->auditoria_id]);
        }

        $dependencia = ArrayHelper::map(AuditorDependencia::find()->select(['nombre','nombre'])->all(),'nombre','nombre');

        return $this->render('update', [
            'model' => $model,
            'searchModel' => $searchModel,
            'dataProvider' => $dataProvider,
            'dependencia' => $dependencia
        ]);
    }

    /**
     * Deletes an existing Auditoria model.
     * If deletion is successful, the browser will be redirected to the 'index' page.
     * @param integer $id
     * @return mixed
     * @throws NotFoundHttpException if the model cannot be found
     */
    public function actionDelete($id)
    {
        $model = $this->findModel($id);
        $model->activo = false;
        $model->save();
        return $this->redirect(['index']);
    }

    /**
     * Finds the Auditoria model based on its primary key value.
     * If the model is not found, a 404 HTTP exception will be thrown.
     * @param integer $id
     * @return Auditoria the loaded model
     * @throws NotFoundHttpException if the model cannot be found
     */
    protected function findModel($id)
    {
        if (($model = Auditoria::findOne($id)) !== null) {
            return $model;
        }

        throw new NotFoundHttpException('The requested page does not exist.');
    }
}
