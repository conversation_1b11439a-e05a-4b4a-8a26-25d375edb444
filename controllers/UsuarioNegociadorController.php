<?php

namespace app\controllers;

use app\models\GrupoProducto;
use app\models\Usuarios;
use Yii;
use app\models\UsuarioNegociador;
use app\models\UsuarioNegociadorSearch;
use yii\helpers\ArrayHelper;
use yii\web\Controller;
use yii\web\NotFoundHttpException;
use yii\filters\VerbFilter;

/**
 * UsuarioNegociadorController implements the CRUD actions for UsuarioNegociador model.
 */
class UsuarioNegociadorController extends Controller
{
    /**
     * @inheritdoc
     */
    public function behaviors()
    {
        return [
            'verbs' => [
                'class' => VerbFilter::className(),
                'actions' => [
                    'delete' => ['POST'],
                ],
            ],
        ];
    }

    /**
     * Lists all UsuarioNegociador models.
     * @return mixed
     */
    public function actionIndex()
    {
        $this->layout = 'home';
        $searchModel = new UsuarioNegociadorSearch();
        $dataProvider = $searchModel->search(Yii::$app->request->queryParams);

        return $this->render('index', [
            'searchModel' => $searchModel,
            'dataProvider' => $dataProvider,
        ]);
    }

    /**
     * Displays a single UsuarioNegociador model.
     * @param string $id
     * @return mixed
     */
    public function actionView($id)
    {
        $this->layout = 'home';
        return $this->render('view', [
            'model' => $this->findModel($id),
        ]);
    }

    /**
     * Creates a new UsuarioNegociador model.
     * If creation is successful, the browser will be redirected to the 'view' page.
     * @return mixed
     */
    public function actionCreate()
    {
        $model = new UsuarioNegociador();
        $this->layout = 'home';
        if ($model->load(Yii::$app->request->post())){
            $factual = date("Y-m-d H:i:s");
            $model->created_by = Yii::$app->user->id;
            $model->created_date = $factual;
            if($model->save()) {
                return $this->redirect(['view', 'id' => $model->user_negociador_id]);
            }
        }
        $negociador = UsuarioNegociador::find()->getNegociadorCombo();
        $grupoprod = GrupoProducto::find()->getGrupoProdCombo();
            return $this->render('create', [
                'model' => $model,
                'negociador' => $negociador,
                'grupoprod' => $grupoprod
            ]);
    }

    /**
     * Updates an existing UsuarioNegociador model.
     * If update is successful, the browser will be redirected to the 'view' page.
     * @param string $id
     * @return mixed
     */
    public function actionUpdate($id)
    {
        $model = $this->findModel($id);
        $this->layout = 'home';
        if ($model->load(Yii::$app->request->post())) {
            $factual = date("Y-m-d H:i:s");
            $model->updated_by = Yii::$app->user->id;
            $model->updated_date = $factual;
            if($model->save()){
                return $this->redirect(['view', 'id' => $model->user_negociador_id]);
            }
        }
        $negociador = UsuarioNegociador::find()->getNegociadorCombo();
        $grupoprod = GrupoProducto::find()->getGrupoProdCombo();
        return $this->render('update', [
                'model' => $model,
                'negociador' => $negociador,
                'grupoprod' => $grupoprod
            ]);
    }

    /**
     * Deletes an existing UsuarioNegociador model.
     * If deletion is successful, the browser will be redirected to the 'index' page.
     * @param string $id
     * @return mixed
     */
    public function actionDelete($id)
    {
        $this->findModel($id)->delete();

        return $this->redirect(['index']);
    }

    /**
     * Finds the UsuarioNegociador model based on its primary key value.
     * If the model is not found, a 404 HTTP exception will be thrown.
     * @param string $id
     * @return UsuarioNegociador the loaded model
     * @throws NotFoundHttpException if the model cannot be found
     */
    protected function findModel($id,$grupoproductosArr = false)
    {
        if (($model = UsuarioNegociador::findOne($id)) !== null) {
            if($grupoproductosArr){
                $model->grupoproductosArr = ArrayHelper::getColumn($model->grupoproductos, 'grupo_producto_id');
            }
            return $model;
        } else {
            throw new NotFoundHttpException('The requested page does not exist.');
        }
    }
}
