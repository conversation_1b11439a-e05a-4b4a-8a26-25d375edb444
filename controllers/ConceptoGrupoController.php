<?php

namespace app\controllers;

use app\helpers\GeneralController;
use app\models\ConceptoFamilia;
use app\models\ConceptoGrupo;
use app\models\ConceptoGrupoInegi;
use app\models\ConceptoGrupoSearch;
use yii\helpers\ArrayHelper;
use yii\web\Controller;
use yii\web\NotFoundHttpException;
use yii\filters\VerbFilter;

/**
 * ConceptoGrupoController implements the CRUD actions for ConceptoGrupo model.
 */
class ConceptoGrupoController extends GeneralController
{
    /**
     * @inheritDoc
     */
    public function behaviors()
    {
        return array_merge(
            parent::behaviors(),
            [
                'verbs' => [
                    'class' => VerbFilter::className(),
                    'actions' => [
                        'delete' => ['POST'],
                    ],
                ],
            ]
        );
    }

    /**
     * Lists all ConceptoGrupo models.
     * @return mixed
     */
    public function actionIndex()
    {
        $searchModel = new ConceptoGrupoSearch();
        $dataProvider = $searchModel->search($this->request->queryParams);

        return $this->render('index', [
            'searchModel' => $searchModel,
            'dataProvider' => $dataProvider,
        ]);
    }

    /**
     * Displays a single ConceptoGrupo model.
     * @param int $concepto_grupo_id Concepto Grupo ID
     * @return mixed
     * @throws NotFoundHttpException if the model cannot be found
     */
    public function actionView($id)
    {
        return $this->render('view', [
            'model' => $this->findModel($id),
        ]);
    }

    /**
     * Creates a new ConceptoGrupo model.
     * If creation is successful, the browser will be redirected to the 'view' page.
     * @return mixed
     */
    public function actionCreate()
    {
        $model = new ConceptoGrupo();
        $modelInegi = new ConceptoGrupoInegi();

        if ($this->request->isPost) {
            $t = \Yii::$app->db->beginTransaction();
            if ($model->load($this->request->post()) && $modelInegi->load($this->request->post())) {
                if($model->save()){
                    $modelInegi->concepto_grupo_id = $model->concepto_grupo_id;
                    if($modelInegi->save()){
                        $t->commit();
                        return $this->redirect(['view', 'id' => $model->concepto_grupo_id]);
                    }else{$t->rollBack();}
                }else{$t->rollBack();}
            }
        } else {
            $model->loadDefaultValues();
        }

        $familia = ArrayHelper::map(ConceptoFamilia::find()->select(['familia','concepto'])->asArray()->all(),'familia','concepto');

        return $this->render('create', [
            'model' => $model,
            'modelInegi' => $modelInegi,
            'familia' => $familia
        ]);
    }

    /**
     * Updates an existing ConceptoGrupo model.
     * If update is successful, the browser will be redirected to the 'view' page.
     * @param int $concepto_grupo_id Concepto Grupo ID
     * @return mixed
     * @throws NotFoundHttpException if the model cannot be found
     */
    public function actionUpdate($id)
    {
        $model = $this->findModel($id);
        $modelInegi = $this->findModelInegi($id);

        if ($this->request->isPost && $model->load($this->request->post()) && $modelInegi->load($this->request->post())) {
            $model->save();
            $modelInegi->save();
            return $this->redirect(['view', 'id' => $model->concepto_grupo_id]);
        }

        $familia = ArrayHelper::map(ConceptoFamilia::find()->select(['familia','concepto'])->asArray()->all(),'familia','concepto');

        return $this->render('update', [
            'model' => $model,
            'modelInegi' => $modelInegi,
            'familia' => $familia
        ]);
    }

    /**
     * Deletes an existing ConceptoGrupo model.
     * If deletion is successful, the browser will be redirected to the 'index' page.
     * @param int $concepto_grupo_id Concepto Grupo ID
     * @return mixed
     * @throws NotFoundHttpException if the model cannot be found
     */
    public function actionDelete($id)
    {
        $this->findModel($id)->delete();

        return $this->redirect(['index']);
    }

    /**
     * Finds the ConceptoGrupo model based on its primary key value.
     * If the model is not found, a 404 HTTP exception will be thrown.
     * @param int $concepto_grupo_id Concepto Grupo ID
     * @return ConceptoGrupo the loaded model
     * @throws NotFoundHttpException if the model cannot be found
     */
    protected function findModel($id)
    {
        if (($model = ConceptoGrupo::findOne($id)) !== null) {
            return $model;
        }
    }

    protected function findModelInegi($id)
    {
        if (($model = ConceptoGrupoInegi::findOne(['concepto_grupo_id' => $id])) !== null) {
            return $model;
        }else{
            return new ConceptoGrupoInegi();
        }
    }
}
