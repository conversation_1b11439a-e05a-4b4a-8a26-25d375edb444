<?php

namespace app\controllers;

use Yii;
use app\models\ListEmail;
use app\models\ListEmailSearch;
use yii\web\Controller;
use yii\web\NotFoundHttpException;
use yii\filters\VerbFilter;

/**
 * ListEmailController implements the CRUD actions for ListEmail model.
 */
class ListEmailController extends Controller
{
    /**
     * @inheritdoc
     */
    public function behaviors()
    {
        return [
            'verbs' => [
                'class' => VerbFilter::className(),
                'actions' => [
                    'delete' => ['POST'],
                ],
            ],
        ];
    }

    /**
     * Lists all ListEmail models.
     * @return mixed
     */
    public function actionIndex()
    {
        $searchModel = new ListEmailSearch();
        $dataProvider = $searchModel->search(Yii::$app->request->queryParams);

        return $this->render('index', [
            'searchModel' => $searchModel,
            'dataProvider' => $dataProvider,
        ]);
    }

    /**
     * Displays a single ListEmail model.
     * @param string $id
     * @return mixed
     */
    public function actionView()
    {
        return $this->render('view');
    }

    /**
     * Creates a new ListEmail model.
     * If creation is successful, the browser will be redirected to the 'view' page.
     * @return mixed
     */
    public function actionCreate()
    {
        $model = new ListEmail();
        $this->layout = 'mainMail';
        /*if ($model->load(Yii::$app->request->post()) && $model->save()) {
            if (Yii::$app->request->isAjax) {
                return $this->renderAjax('view');
            }else {
                return $this->redirect(['view']);
            }
        } else {
            return $this->render('create', [
                'model' => $model,
            ]);
        }*/
       if (Yii::$app->request->post()['email'] && Yii::$app->request->post()['email']!=''){
            $mail = Yii::$app->request->post()['email'];
            $validate_mail = ListEmail::find()->select('email')->where(['email' => $mail])->one();
            $model->email = $mail;
            if(!$validate_mail['email'] && $model->save()){
                Yii::$app->getSession()->setFlash('success', 'Su correo a sido guardado exitosamente !');
                return $this->goHome();
            }else {
                    Yii::$app->getSession()->setFlash('success', 'Su correo ya se encuentra registrado !');
                    return $this->goHome();
                }
            }else {
            Yii::$app->getSession()->setFlash('error', 'Es necesario ingresar su correo !');
            return $this->goHome();
        }
    }
    /**
     * obtenemos los munciipios por estado.
     *
     * @return mixed
     */
    public function actionSaveEmail($email)
    {
        $model = new ListEmail();
        if ($email!=''){
            $mail =$email;
            $validate_mail = ListEmail::find()->select('email')->where(['email' => $mail])->one();
            $model->email = $mail;
            if(!$validate_mail['email'] && $model->save()){
                return '333*Su correo a sido guardado exitosamente !';
            }else {
                return '336*Su correo ya se encuentra registrado !';
                }
            }else {
            return '336*Es necesario ingresar su correo !';
        }

    }
    /**
     * Updates an existing ListEmail model.
     * If update is successful, the browser will be redirected to the 'view' page.
     * @param string $id
     * @return mixed
     */
    public function actionUpdate($id)
    {
        $model = $this->findModel($id);

        if ($model->load(Yii::$app->request->post()) && $model->save()) {
            return $this->redirect(['view', 'id' => $model->email_id]);
        } else {
            return $this->render('update', [
                'model' => $model,
            ]);
        }
    }

    /**
     * Deletes an existing ListEmail model.
     * If deletion is successful, the browser will be redirected to the 'index' page.
     * @param string $id
     * @return mixed
     */
    public function actionDelete($id)
    {
        $this->findModel($id)->delete();

        return $this->redirect(['index']);
    }

    /**
     * Finds the ListEmail model based on its primary key value.
     * If the model is not found, a 404 HTTP exception will be thrown.
     * @param string $id
     * @return ListEmail the loaded model
     * @throws NotFoundHttpException if the model cannot be found
     */
    protected function findModel($id)
    {
        if (($model = ListEmail::findOne($id)) !== null) {
            return $model;
        } else {
            throw new NotFoundHttpException('The requested page does not exist.');
        }
    }
}
