<?php

namespace app\controllers;

use Yii;
use yii\web\Controller;
use yii\web\NotFoundHttpException;
use yii\filters\VerbFilter;
use app\helpers\GeneralController;
use app\controllers\ProviderController;


/**
 * AyudaController implements the CRUD actions for Ayuda model.
 */
class AyudaController extends GeneralController
{
    /**
     * @inheritdoc
     */
    public function behaviors()
    {
        return [
            'verbs' => [
                'class' => VerbFilter::className(),
                'actions' => [
                    'delete' => ['POST'],
                ],
            ],
        ];
    }

    /**
     * Lists all Ayuda models.
     * @return mixed
     */
    public function actionIndex()
    {
        
        return $this->redirect("checklist");

        $this->layout = 'ayuda';

        return $this->render('index', [
        ]);
    }

    public function actionFind($find=null){
        $folder = opendir('./../views/ayuda');
        $data = [];
        if ($folder && $find) {
            while (($file = readdir($folder)) !== FALSE) {
                if(!in_array($file,['.','..','bys-guia-rapida.php','op-guia-rapida.php','index.php'])){ //vistas sin revisar
                    $text = strip_tags($this->renderPartial($file));
                    preg_match_all("/$find/", $text, $matches,PREG_OFFSET_CAPTURE);
                    $match = array_map(function($row){return $row[1];},$matches[0]);
                    if($match){
                        $url = 'ayuda/'.str_replace('.php','',$file);
                        foreach ($match as $key=>$m){
                            $str = substr($text,($m),250);
                            //$str = str_replace($find,"<b>$find</b>",$str);
                            $str = str_replace("  ","",$str);
                            $str = str_replace(["\n","\r"],'',$str);
                            array_push($data,['url'=>$url,'text'=>htmlspecialchars($str),'count'=>$key,'find'=>$find]);
                        }
                    }
                }
            }
        }
        closedir($folder);
        return json_encode($data);
    }

    /**
     * Lists all Ayuda models.
     * @return mixed
     */
    public function actionPreguntasFrecuentes()
    {
        
        $this->layout = 'ayuda';

        return $this->render('preguntas-frecuentes', [
        ]);
    }

    /**
     * Lists all Ayuda models.
     * @return mixed
     */
    public function actionSecop()
    {
        $this->layout = 'ayuda';

        return $this->render('secop', [
        ]);
    }

    /**
     * Lists all Ayuda models.
     * @return mixed
     */
    public function actionChecklist()
    {
        
        $this->layout = 'ayuda';

        return $this->render('checklist', [
        ]);
    } 

    /**
     * Lists all Ayuda models.
     * @return mixed
     */
    public function actionCrearNuevoUsuario()
    {
        $this->layout = 'ayuda';

        return $this->render('crear-nuevo-usuario', [
        ]);
    }


    /**
     * Lists all Ayuda models.
     * @return mixed
     */
    public function actionBysGuiaRapida()
    {
        $this->layout = 'ayuda';

        $provider =  (object)[
            'tipo_provider' => 'bys',
        ];

        $datos = ProviderController::getDashboardDataBys();

        // $datos = array('porcentaje' => 0);

        return $this->render('bys-guia-rapida', [
            'datos' => $datos,
            'provider' => $provider,
        ]);
    }

    /**
     * Lists all Ayuda models.
     * @return mixed
     */
    public function actionOpGuiaRapida()
    {

        $this->layout = 'ayuda';

        return $this->render('op-guia-rapida', [
        ]);
    }

    /**
     * Lists all Ayuda models.
     * @return mixed
     */
    public function actionBysModulos()
    {

        $this->layout = 'ayuda';

        return $this->render('bys-modulos', [
        ]);
    }

    /**
     * Lists all Ayuda models.
     * @return mixed
     */
    public function actionOpModulos()
    {

        $this->layout = 'ayuda';

        return $this->render('op-modulos', [
        ]);
    }

        /**
     * Lists all Ayuda models.
     * @return mixed
     */
    public function actionSoporte()
    {

        $this->layout = 'ayuda';

        return $this->render('soporte', [
        ]);
    }

}
