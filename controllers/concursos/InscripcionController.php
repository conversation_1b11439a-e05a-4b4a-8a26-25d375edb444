<?php


namespace app\controllers\concursos;


use app\helpers\GeneralController;
use app\models\Carrito;
use app\models\CatEntidades;
use app\models\CatMunicipios;
use app\models\Detalle;
use app\models\EscrituraPublica;
use app\models\Firmar;
use app\models\Provider;
use app\models\ProviderConcurso;
use app\models\RegistroConcursos;
use app\models\UsuariosAcuerdos;
use mPDF;
use Yii;
use yii\helpers\ArrayHelper;

class InscripcionController extends GeneralController
{


    public function actionInscripcion($id){
        if(!$id)
            return $this->goHome();

        $user_id = Yii::$app->user->id;
        $provider = Provider::find()->where(['user_id' =>$user_id])->one();


        if(Yii::$app->request->isPost){

            $modelCheck = new UsuariosAcuerdos();
            $modelCheck->user_id  = $user_id;
            $modelCheck->action = 'concursos/inscripcion/inscripcion';
            $modelCheck->tipo = UsuariosAcuerdos::TYPE_CONFIRMACION_INSCRIPCION_CONCURSO;
            if(!$modelCheck->save())
                var_dump($modelCheck->errors);

            return $this->redirect('firmar?id='.$id);
        }

        $concurso = RegistroConcursos::findOne(intval($id));
        $modelData = $this->getDataConcurso($user_id);


        return $this->render('inscripcion',[
            'modelData'=>$modelData,
            'concurso'=>$concurso
        ]);
    }

    public function generarDocumentoConcurso($concurso, $provider, $user_id, $firma = null){
        $name_file_save = null;
        define('_MPDF_TTFONTPATH', './fonts/'); //cambia la ruta de las fuentes utilizadas en la creación del pdf (la configuración default puede solicitar alguans fuentes necesarias)
    
        /* $provider = Provider::find()->where(['user_id' => $user_id])->one();
        $concurso = RegistroConcursos::findOne($concurso_id); */
        $modelData = $this->getDataConcurso($user_id);

        $mpdf = new mPDF('utf-8', 'A4', 0, '', 5, 5, 15,0);
        $mpdf->SetTitle('Inscripción a concurso');
        $stylesheet = file_get_contents('css/pdf.css');

        GeneralController::add_custom_fonts_to_mpdf($mpdf);
        $mpdf->SetHTMLHeader($this->renderPartial('/concursos/solicitud/header-solicitud'));
        $mpdf->SetHTMLFooter($this->renderPartial('/concursos/solicitud/footer-solicitud'));

        $datos_doc = [ 'provider' => $provider, 'concurso' => $concurso, 'modelData' => $modelData ];
        $datos_doc = is_null($firma) ? $datos_doc : array_merge($datos_doc, ['firma' => $firma]);
        $content = $this->renderPartial('/concursos/inscripcion/carta_inscripcion', $datos_doc);

        $mpdf->WriteHTML($stylesheet, 1);
        $mpdf->WriteHTML(mb_convert_encoding($content, 'UTF-8', 'UTF-8'));

        $name_file_save = (is_null($firma) ? Firmar::pre_path : Firmar::path) . md5(microtime(true) * 10000) . '.pdf';

        if(!is_dir(Firmar::pre_path)){ mkdir(Firmar::pre_path,0775,true); }
        if(!is_dir(Firmar::path)){ mkdir(Firmar::path,0775,true); }

        $mpdf->Output($name_file_save, 'F');

        return $name_file_save;
    }

    public function actionFirmar($id){
        if (!$id) { return $this->goHome(); }
        $user_id = Yii::$app->user->id;
        $provider = Provider::findOne(['user_id' => $user_id]);
        $concurso = RegistroConcursos::findOne(intval($id));
        $url_doc = self::generarDocumentoConcurso($concurso, $provider, $user_id);

        $data = file_get_contents($url_doc);
        $base64 = base64_encode($data);
        return $this->render('form_firmar', 
        [
            'concurso' => $concurso,
            'documento' => $url_doc,
            'doc_b64' => $base64
        ] );
    }

    public function actionFirmarDocumento(){

        $datos = $this->request->post();
        $concurso_id = $datos['concurso_id'];
        $firma = $datos['cadena'];

        $user_id = Yii::$app->user->id;
        $provider = Provider::findOne(['user_id' => $user_id]);
        $concurso = RegistroConcursos::findOne($concurso_id);
        $doc_url_path = self::generarDocumentoConcurso($concurso, $provider, $user_id, $firma);

        return json_encode(['doc_firmado' => $doc_url_path]);
    }

    public function actionPreDetalle($id,$doc){
        if (!$id) { return $this->goHome(); }

        $concurso = RegistroConcursos::findOne($id);
        $carrito = Carrito::find()->where(['and',['activo'=>true],['created_by'=>Yii::$app->user->id]])->one();
        $provider = Provider::find()->where(['user_id' =>Yii::$app->user->id])->one();
        if(!$carrito){
            $carrito = new Carrito();
            $carrito->created_by = Yii::$app->user->id;
            $carrito->provider_id = $provider->provider_id;
            $carrito->save();
        }
        $reg = new ProviderConcurso();
        $reg->concurso_id = intval($id);
        $reg->provider_id = $provider->provider_id;
        $reg->doc = $doc;
        $reg->save();

        $detalle = new Detalle();
        $detalle->concurso_id = intval($id);
        $detalle->created_by = Yii::$app->user->id;
        $detalle->monto = intval($_ENV['MONTO_CONCURSO']);
        $detalle->cantidad = 1;
        $detalle->concepto = $concurso->numero_proceso;
        $detalle->carrito_id = $carrito->id;
        $detalle->save();

        return $this->redirect('/concursos/carrito/detalle');
    }

    public function actionFirma($id){
        if (!$id)
            return $this->goHome();

        
        

        if(Yii::$app->request->isPost){
            
        }



        /* $modelData = $this->getDataConcurso(); */

        

        /* $path = 'concursos/inscripcion/'.$concurso->concurso_id."/";
        if(!is_dir($path)){
            mkdir($path,775,true);
        }
        $nameFile = $provider->rfc.'.pdf'; */
        
        /* $concurso = RegistroConcursos::findOne(intval($id));
        return $this->render('firma',[
            'concurso'=>$concurso,
            'file'=>'/concursos/inscripcion/'.$concurso->concurso_id."/".$nameFile
        ]); */
    }

    public static function getDataConcurso($user_id){

        $sql = "
            select p.provider_id, p.name_razon_social, curp.curp, acta.num_acta as acta, d.calle_fiscal as dom_calle, d.num_ext_fiscal as dom_numero_ext, 
            d.telefono as dom_telefono, d.cp_fiscal as dom_cp, d.correo as dom_correo, ent.nombre as dom_estado, 
            mun.nombre as dom_municipio, asen.nombre as dom_colonia,
            concat_ws(' ',rl.nombre,rl.ap_paterno, rl.ap_materno) as rep_nombre, esc.notario as esc_notario, 
            esc.fecha as esc_fecha, esc.url_escritura_publica as esc_pub,
            p.rfc as rfc , c.url_certificado as certif , u.tipo_persona as tipo, r.url_rfc as constancia, 
            d.url_comprobante_domicilio as comp_dom,
            o.url_idoficial as identif, rl.documento_identificacion as rep_id, rl.documento_acta as docto_poder_x ,
            rl.tipo_poder as tipo_poder_desc, case when rl.tipo_poder='ACTA' then 1  when rl.tipo_poder='PODER' then 2  when rl.tipo_poder='ACTUALIZACIÓN' then 3  else 0 end as tipo_poder,
             case when rl.tipo_poder='PODER' then rl.documento_acta when rl.tipo_poder='ACTUALIZACIÓN' then mod.documento_acta  else acta.documento_acta end as docto_poder
            from provider p 
            left join historico_certificados c on c.provider_id = p.provider_id and c.tipo = 'CERTIFICADO' and c.provider_type = 'bys'
            left join usuarios u on u.user_id = p.user_id
            left join provider.rfc r on r.provider_id = p.provider_id
            left join provider.ubicacion d on d.provider_id = p.provider_id and d.activo is true and d.tipo = 'DOMICILIO FISCAL'
            left join provider.id_oficial o on o.provider_id = p.provider_id
            left join provider.representante_legal rl on rl.provider_id = p.provider_id and rl.activo is true and rl.rep_bys is true
            left join cat_municipios mun on mun.municipio_id = d.city_fiscal
            left join cat_entidades ent on ent.entidad_id = d.state_fiscal
            left join cat_asentamientos asen on asen.asentamiento_id = d.colonia_fiscal
            left join provider.curp curp on curp.provider_id = p.provider_id
            left join provider.escritura_publica esc on esc.provider_id = p.provider_id
            left join provider.modificacion_acta mod on mod.provider_id = p.provider_id
            left join provider.acta_constitutiva acta on acta.provider_id = p.provider_id
            where p.user_id = $user_id order by c.created_at desc";

        return  Provider::findBySql($sql)->asArray()->one();

    }


    public function actionDocument($rfc=null,$signature=null){

    }





}
