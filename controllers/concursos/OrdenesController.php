<?php


namespace app\controllers\concursos;


use app\helpers\GeneralController;
use app\models\Detalle;
use app\models\Pago;
use yii\data\ActiveDataProvider;

class OrdenesController extends GeneralController
{
    public function actionDetalle($url=null){
        /*$query = Carrito::find()->select('*')
            ->leftJoin(['det'=>'concursos.detalle'],'det.carrito_id = concursos.carrito.id')
            ->leftJoin(['pag'=>'concursos.pago'], 'pag.id = det.pago_id')
            ->where(['and',['concursos.carrito.activo'=>false],['det.activo'=>true]]);*/
        $query = Pago::find()
            ->where(['and',['activo'=>true],['created_by'=>\Yii::$app->user->id]]);
        $url = ($url)? json_decode(Pago::findOne(intval($url))->data_pago)->response->datos->url_recibo :null;

        $data = [
            'data' => new ActiveDataProvider([
                'query'=> $query
            ]),
            'url'=>$url
        ];
        return $this->render('detalle',$data);
    }

    public function actionView($id=null){
        if(!$id)
            return $this->redirect('detalle');

        $query = Detalle::find()
            ->where(['and',['pago_id'=>intval($id)],['activo'=>true]]);

        $data = new ActiveDataProvider([
            'query'=>$query,
            'sort'=>false
        ]);

        return $this->renderAjax('view',['data'=>$data]);

    }

    public function actionDelete($id){
        $model = Pago::findOne($id);
        $model->activo = false;
        $model->last_modification_by = \Yii::$app->user->id;
        $model->last_modification_at = date('Y-m-d H:i:s');
        $model->save();
        $this->redirect('detalle');
    }


}