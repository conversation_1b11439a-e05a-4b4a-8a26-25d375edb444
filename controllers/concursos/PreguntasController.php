<?php

namespace app\controllers\concursos;

use app\helpers\GeneralController;
use app\models\Firmar;
use app\models\Pregunta;
use app\models\Provider;
use app\models\RegistroConcursos;
use app\models\SolicitudPreguntaSearch;
use app\models\SolicitudPregunta;
use yii\web\Response;
use Yii;
use mPDF;
use yii\helpers\ArrayHelper;
use yii\widgets\ActiveForm;

class PreguntasController extends GeneralController{

    public function actionIndex(){
        $solicitudesSearch = new SolicitudPreguntaSearch();
        $dataProvider = $solicitudesSearch->search(Yii::$app->request->queryParams, 'activos');

        return $this->render('index', [
            'solicitudes' => $dataProvider,
            'searchModel' => $solicitudesSearch
        ]);

    }

    public function actionUpdate($concurso_id=null, $pregunta_id = null){
        $isRequest = Yii::$app->request->isAjax;
        $postRequest = Yii::$app->request->post();
        $user_id = Yii::$app->user->getId();
        $provider = Provider::find()->where(['user_id'=>$user_id])->one();

        $pregunta = self::findModel($pregunta_id);
        $pregunta->provider_id = $provider->provider_id;

        if ( $isRequest && $pregunta->load($postRequest) ) {
            $errores = [];
            Yii::$app->response->format = Response::FORMAT_JSON;
            if(isset($postRequest['RegistroConcursos']['concurso_id']) && empty($postRequest['RegistroConcursos']['concurso_id']) ){
                $errores['registroconcursos-concurso_id'] = ['Debe seleccionar un concurso'];
            }
            return array_merge($errores, ActiveForm::validate($pregunta));
        } else if ( $pregunta->load($postRequest) ){
            $s_concurso_id = is_null($pregunta->solicitud_id) ? $postRequest['RegistroConcursos']['concurso_id'] : $concurso_id;
            $solicitud = SolicitudPregunta::getSolicitudActiva($provider->provider_id, $s_concurso_id);
            $pregunta->solicitud_id = $solicitud->solicitud_id;

            $pregunta->save();
            return $this->redirect(['/concursos/preguntas/index']);
        }

        $condicion_concurso = ['and', ['activo' => true]];

        !is_null($concurso_id) && array_push($condicion_concurso, ['concurso_id' => $concurso_id]);

        $concursos_consulta = RegistroConcursos::find()->select(['concurso_id', 'numero_proceso'])->where($condicion_concurso)->asArray()->all();

        $concursos = ArrayHelper::map($concursos_consulta, 'concurso_id', 'numero_proceso');

        return $this->renderAjax('form_pregunta', [
            'model' => $pregunta,
            'concurso_id' => $concurso_id,
            'concursos' => $concursos,
        ]);

    }

    public function actionRevisar($pregunta_id){
        $isRequest = Yii::$app->request->isAjax;
        $postRequest = Yii::$app->request->post();
        $user_id = Yii::$app->user->getId();

        $pregunta = self::findModel($pregunta_id);
        $solicitud = SolicitudPregunta::findOne($pregunta->solicitud_id);
        $nombre_concurso = RegistroConcursos::findOne($solicitud->concurso_id)->numero_proceso;
        if ( $isRequest && $pregunta->load($postRequest) ){
            $errores = [];
            Yii::$app->response->format = Response::FORMAT_JSON;
            if(empty($pregunta->tipo_revisor)){
                $errores['pregunta-tipo_revisor'] = ["Debe asignar la pregunta o responderla"];
            }
            if($pregunta->tipo_revisor == Pregunta::TIPO_REVISION && empty($pregunta->respuesta)){
                $errores['pregunta-respuesta'] = ["La respuesta no puede estar vacia"];
            }
            return array_merge($errores, ActiveForm::validate($pregunta));
        } else if ( $pregunta->load($postRequest) ) {
            $role_revisor = Yii::$app->user->identity->role;

            if (!empty($pregunta->url_doc_detalle) && GeneralController::str_contains($pregunta->url_doc_detalle, "archivos_tmp") ) {
                if(!is_dir(Pregunta::PATH)){ mkdir(Pregunta::PATH,0775,true); }
                $new_doc_path = str_replace('archivos_tmp/', Pregunta::PATH, $pregunta->url_doc_detalle);
                $this->copyFile($pregunta->url_doc_detalle, $new_doc_path);
                $pregunta->url_doc_detalle = $new_doc_path;
            }

            if( isset(Pregunta::ROLES_ASIGNACION[$role_revisor]) && 
            Pregunta::ROLES_ASIGNACION[$role_revisor] == $pregunta->tipo_revisor){
                $pregunta->id_revisor = $user_id;
            }
            $pregunta->last_modification_by = $user_id;
            $pregunta->last_modification_at = date('Y-m-d H:i:s'); //Ver porque no tiene zona horaria

            $pregunta->save();
            return $this->redirect(['/concursos/solicitud/index-evaluacion']);

        }

        return $this->renderAjax('form_revision', [
            'model' => $pregunta,
            'nombre_concurso' => $nombre_concurso,
        ]);
    }

    public function actionValidar($pregunta_id, $val=null){
        $user_id = Yii::$app->user->getId();
        $pregunta = self::findModel($pregunta_id);
        $solicitud = SolicitudPregunta::findOne($pregunta->solicitud_id);
        $nombre_concurso = RegistroConcursos::findOne($solicitud->concurso_id)->numero_proceso;
        
        if(is_null($val)){
            return $this->renderAjax('form_validacion', [
                'model' => $pregunta,
                'nombre_concurso' => $nombre_concurso,
            ]);
        }else{
            if($val == 'si'){
                $pregunta->id_revisor = $user_id;
                $pregunta->estatus = Pregunta::STATUS_RESUELTO;
            }else if($val == 'no'){
                $pregunta->estatus = Pregunta::STATUS_REVISION;
            }

            $pregunta->last_modification_by = $user_id;
            $pregunta->last_modification_at = date('Y-m-d H:i:s'); //Ver porque no tiene zona horaria
            $pregunta->save();
            
            if($val == 'si'){
                $p_revision = Pregunta::find()->where(['and', ['activo' => true], ['or', ['estatus' => Pregunta::STATUS_REVISION], ['estatus' => Pregunta::STATUS_VALIDACION]], 
                ['not in', 'pregunta_id', [$pregunta_id]], ['solicitud_id' => $pregunta->solicitud_id]])->all();
                if(count($p_revision) == 0){ //No existe el status validacion en solicitud, asi que conforme lleguen las preguntas a validacion se mostraran
                    $concurso = RegistroConcursos::findOne(['concurso_id' => $solicitud->concurso_id]);
                    $preguntas = Pregunta::find()->where(['and', ['activo' => true], ['solicitud_id' => $pregunta->solicitud_id]])->asArray()->all();
                    $provider = Provider::findOne($solicitud->provider_id);
                    $solicitud->estatus = SolicitudPregunta::STATUS_RESUELTO; //cambiamos el status a firmado
                    $doc_url_path = $this->generarDocumentoPreguntas($concurso, $preguntas, $solicitud, $provider);
                    $solicitud->url_doc = $doc_url_path; //Documento ya firmado
                    $solicitud->last_modification_by = $user_id;
                    $solicitud->last_modification_at = date('Y-m-d H:i:s');
                    $solicitud->save();
                }
            }
            return $this->redirect(['/concursos/solicitud/index-evaluacion']);
        }

    }

    public function actionEnviarValidacion($pregunta_id){
        $user_id = Yii::$app->user->getId();
        $pregunta = Pregunta::findOne($pregunta_id);
        $pregunta->estatus = Pregunta::STATUS_VALIDACION;
        $pregunta->last_modification_by = $user_id;
        $pregunta->last_modification_at = date('Y-m-d H:i:s'); //Ver porque no tiene zona horaria
        $pregunta->save();
        return $this->redirect(['/concursos/solicitud/index-evaluacion']);
    }

    public static function findModel($id){
        $model = null;
        if(!is_null($id)){
            $model = Pregunta::find()->where(['and',['pregunta_id' => $id], ['activo' => true]])->one();
        }

        if(is_null($model)){
            $model = new Pregunta();
            $model->created_by = Yii::$app->user->identity->user_id;
            $model->estatus = Pregunta::STATUS_EDICION;
        }
        
        $model = is_null($model) ? new Pregunta() : $model;

        return $model;
    }


}