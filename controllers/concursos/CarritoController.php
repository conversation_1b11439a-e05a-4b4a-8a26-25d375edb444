<?php


namespace app\controllers\concursos;


use app\helpers\GeneralController;
use app\models\Carrito;
use app\models\Detalle;
use app\models\ErrorInesperado;
use app\models\Pago;
use app\models\Provider;
use app\models\ProviderConcurso;
use app\models\RegistroConcursos;
use app\models\UsuariosAcuerdos;
use yii\data\ActiveDataProvider;
use yii\filters\VerbFilter;

class CarritoController extends GeneralController
{

    public function behaviors()
    {
        return [
            'verbs' => [
                'class' => VerbFilter::className(),
                'actions' => [
                    'metodo' => ['POST'],
                ],
            ],
        ];
    }

    public function actionDetalle(){
        $carrito = Carrito::find()
            ->where(['and',['activo'=>true],['created_by'=>\Yii::$app->user->id]])
            ->one();

        $query =  Detalle::find()
            ->where(['and',['activo'=>true],['carrito_id'=>$carrito ? $carrito->id : 0],['created_by'=>\Yii::$app->user->id]]);
        $data = [
            'data' => $data = new ActiveDataProvider([
                'query'=> $query
            ])
        ];
        //var_dump($query->createCommand()->getRawSql()).exit();
        return $this->render('detalle',$data);
    }


    public function actionMetodo(){

        $carrito = Carrito::find()
            ->where(['and',['activo'=>true],['created_by'=>\Yii::$app->user->id]])->one();
        $detalles = Detalle::find()
            ->where(['and',['activo'=>true],['carrito_id'=>$carrito->id]])
            ->asArray()->all();

        $provider = Provider::find()->where(['user_id'=>\Yii::$app->user->id])->one();
        $api = $_ENV['API_PAGO']."/pay";
        $entidad_id = $_ENV['ENTIDAD_ID'];
        $id_tipo_servicio = intval($_ENV['ID_TIPO_SERVICIO']);
        $auth = $_ENV['API_AUTH_PAGO'];
        $headers = [
            'Authorization:'.$auth,
            'Content-Type:application/json'
        ];

        $monto = array_sum(array_column($detalles,'monto'));
        $uuid = $carrito->id.'C'.time();
        $nombreRz = $provider->nameOrRazonSocial;
        $detallesCart = [];
        forEach($detalles as $key => $value){
            array_push($detallesCart,[
                'concepto'=>trim($value['concepto']),
                'partida'=>$_ENV['PARTIDA_CONCURSO'], //ver que show con la partida
                'importe_concepto' => intval($value['monto'])
            ]);
        }
        $json = [
            'token' => 'DD0FDED2FE302392164520BF7090E1B3BEB7',
            'url_retorno'=> 'url',
            'importe_transaccion'=> $monto,
            'id_transaccion'=>$uuid,
            'entidad'=> $entidad_id,
            'tramite'=>[[
                'id_seguimiento'=> $carrito->id,
                'id_tipo_servicio'=> $id_tipo_servicio,
                'id_tramite'=> $carrito->id.'_',
                'auxiliar_1'=>'',
                'auxiliar_2'=>'',
                'auxiliar_3'=>'',
                'importe_tramite'=> $monto,
                'datos_solicitante'=>[
                    'nombre'=>'',
                    'apellido_paterno'=>'',
                    'apellido_materno'=>'',
                    'curp'=>'',
                    'rfc'=>'',
                    'email'=> '',
                    'calle'=> '',
                    'colonia'=> '',
                    'numexterior'=> '',
                    'numinterior'=> '',
                    'municipio'=> '',
                    'codigopostal'=> '',
                    'razon_social'=> $nombreRz
                ],
                'datos_factura'=>[
                    'razon_social'=> $nombreRz,
                    'nombre'=> '',
                    'apellido_paterno'=> '',
                    'apellido_materno'=> '',
                    'rfc'=> '',
                    'curp'=> '',
                    'email'=> '',
                    'calle'=> '',
                    'colonia'=> '',
                    'numexterior'=> '',
                    'numinterior'=> '',
                    'municipio'=> '',
                    'codigopostal'=> ''
                ],
                'detalle'=>$detallesCart]
            ]
        ];

        $response = $this->curlPostForm($api,json_encode($json),$headers);

        if(!$response || isset($response['error'])){
            $error = new ErrorInesperado();
            $error->tipo_error = 'CARRITO_'.$carrito->id;
            $error->data = (!$response)?'No hay conexion':$response;
            $error->user_id = \Yii::$app->user->id;
            $error->save();
            \Yii::$app->session->setFlash('error','Ha ocurrido un incidente al enviar a carrito.');
            return $this->redirect('detalle');
        }else{

            $pago = new Pago();
            $pago->data = json_encode($response);

            $pago->folio = $response['response']['folio'];
            $pago->monto = $monto;
            $pago->tipo = 0;
            $pago->created_by = \Yii::$app->user->id;
            $pago->entidad = intval($entidad_id);
            if(!$pago->save())
                return json_encode($pago->errors).exit();


            \Yii::$app->db->createCommand()
                ->update('concursos.detalle',['pago_id'=>$pago->id],['id'=>array_values(array_column($detalles,'id'))])
                ->execute();

            $carrito->activo = false;
            if(!$carrito->save())
                return json_encode($carrito->errors).exit();

            return $this->redirect('metodo-pago?id='.$pago->id);

        }

    }


    public function actionMetodoPago($id=null){
        if(!$id)
            return $this->redirect('detalle');

        $pago = Pago::findOne(intval($id));
        //var_dump($id).exit();
        return $this->render('metodo-pago',['data'=>$pago,'id'=>$id]);
    }


    public function actionPago($id=null,$cuenta_id=null,$folio=null){
        if(!$cuenta_id || !$folio || !$id)
            return $this->redirect('detalle');

        $pago = Pago::findOne(intval($id));
        $pago->cuenta_id = intval($cuenta_id);
        $pago->save();

        $auth = $_ENV['API_AUTH_PAGO'];
        $headers = [
            'Authorization:'.$auth,
            'Content-Type:application/json'
        ];
        $api = $_ENV['API_PAGO']."/databank";

        $json = [
            'folio' => intval($folio),
            'cuenta_id'=> intval($cuenta_id),
        ];

        $response = $this->curlPostForm($api,json_encode($json),$headers);

        if(isset($response->data)){
            $error = new ErrorInesperado();
            $error->tipo_error = 'PAGO_'.$pago->id;
            $error->data = $response;
            $error->user_id = \Yii::$app->user->id;
            $error->save();
            \Yii::$app->session->setFlash('warning','Ha ocurrido un incidente al enviar a carrito');
            return $this->redirect('metodo-pago?id='.$id);
        }else{
            $pago->data_pago = json_encode($response);
            $pago->status = 'Pendiente';
            $pago->save();

            return $this->redirect('/concursos/ordenes/detalle?url='.$pago->id);
        }
    }


    private function curlPostForm($url,$data,$headers){
        $curl = curl_init();
        curl_setopt($curl, CURLOPT_URL, $url);
        curl_setopt($curl, CURLOPT_POST, 1);
        curl_setopt($curl, CURLOPT_POSTFIELDS, $data);
        curl_setopt($curl, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
        $curlData = curl_exec($curl);
        curl_close($curl);
        return  json_decode($curlData, true);
    }


    public function actionDelete($id){
        $model = Detalle::findOne($id);
        $model->activo = false;
        $model->last_modification_by = \Yii::$app->user->id;
        $model->last_modification_at = date('Y-m-d H:i:s');
        $model->save();
        $mProviderConcurso = ProviderConcurso::find()
            ->where(['and',['activo'=>true],['concurso_id'=>$model->concurso_id],['provider_id'=>\Yii::$app->user->identity->provider]])->one();
        $mProviderConcurso->activo = false;
        $mProviderConcurso->last_updated_at = date('Y-m-d H:i:s');
        $mProviderConcurso->save(false);
        $this->redirect('detalle');
    }

    public function actionConsultapago($folio=null){

        header('Access-Control-Allow-Origin: *');
        header('Access-Control-Allow-Methods: GET, POST');
        header("Access-Control-Allow-Headers: X-Requested-With");

        $r = [
          'NO'=>Pago::STATUS_PENDIENTE,
          'SI'=>Pago::STATUS_PAGADO,
          'VENCIDO'=>Pago::STATUS_VENCIDO
        ];

        $folio = intval($folio);
        $pago = Pago::find()->where(['folio'=>$folio])->one();
        $resp = $this->consultaPago($pago);
        $status = isset($r[$resp->estatus]) ? $r[$resp->estatus] : Pago::STATUS_PENDIENTE;
        if($pago && $resp){
            $pago->status = $status;
            $pago->fecha = $resp->fecha_pago;
            $pago->consulta_pago = json_encode($resp);
            $pago->fecha_limite = $resp->fecha_limite;
            $pago->save();
            $provider = Provider::find()->where(['user_id' =>$pago->created_by])->one();
            $detalle = Detalle::find()->where(['pago_id'=>$pago->id])->one();
            //finaliza el registro del concurso en la tabla de provider_concurso si es pagado
            if($status==Pago::STATUS_PAGADO){
                $modelConc = ProviderConcurso::find()
                    ->where(['and',['provider_id'=>$provider->provider_id],['activo'=>true],['concurso_id'=>$detalle->concurso_id]])
                    ->one();
                $modelConc->last_updated_at =  $resp->fecha_pago;
                $modelConc->save();
            }
        }
        return $status;
    }

    private function consultaPago($pago=null){
        if(!$pago)
            return false;
        $api = $_ENV['API_CONSULTA_PAGO'];
        $params = ["folio"=>$pago->folio, "entidad"=>$pago->entidad];
        $options = ['trace'=>true];
        try {
            $client=new \SoapClient($api,$options);
            $client->__setLocation($api);
            $data=$client->ConsultaPago($params);
            $json = json_decode($data->json);
        } catch (\SoapFault  $e) {
            return ['status'=>'error','msg'=>$e->getMessage()];
            die($e->getMessage());
        }
        return $json;
    }

}