<?php

namespace app\controllers\concursos;

use app\helpers\GeneralController;
use app\models\Firmar;
use app\models\Pregunta;
use app\models\Provider;
use app\models\RegistroConcursos;
use app\models\SolicitudPregunta;
use app\models\SolicitudPreguntaSearch;
use app\models\Usuarios;
use mPDF;
use Yii;

class SolicitudController extends GeneralController{

    public function actionVerDocumento($solicitud_id=null){

        $solicitud = SolicitudPregunta::findOne($solicitud_id);
        $url_doc = self::verificarExistenciaDocumento($solicitud);
        
        return $this->renderAjax('/site/visor', [ 'pdf' => $url_doc] );
        //return $mode == 'R' ? $this->renderAjax('/site/visor', [ 'pdf' => $solicitud->url_doc] ) : $solicitud->url_doc;

    }

    public function actionFirmar($solicitud_id){
        $solicitud = SolicitudPregunta::findOne($solicitud_id);
        $url_doc = self::verificarExistenciaDocumento($solicitud);

        $data = file_get_contents($url_doc);
        $base64 = base64_encode($data);
        return $this->renderAjax('form_firmar', 
        [
            'solicitud_id' => $solicitud_id,
            'doc_b64' => $base64
        ] );
    }
    
    public function actionFirmarDocumento(){

        $datos = $this->request->post();
        $solicitud_id = $datos['solicitud_id'];
        $firma = $datos['cadena'];

        $solicitud = SolicitudPregunta::findOne($solicitud_id);
        $concurso = RegistroConcursos::findOne(['concurso_id' => $solicitud->concurso_id]);
        $preguntas = Pregunta::find()->where(['and', ['activo' => true], ['solicitud_id' => $solicitud_id]])->asArray()->all();
        $provider = Provider::findOne($solicitud->provider_id);
        $doc_url_path = self::generarDocumentoPreguntas($concurso, $preguntas, $solicitud, $provider, $firma);
        $solicitud->url_doc = $doc_url_path; //Documento ya firmado
        $solicitud->estatus = SolicitudPregunta::STATUS_FIRMADO; //cambiamos el status a firmado
        $solicitud->last_modification_by = Yii::$app->user->identity->user_id;
        $solicitud->last_modification_at = date('Y-m-d H:i:s');
        $solicitud->save();
    }

    public function actionEnviarRevision($solicitud_id){
        $user_id = Yii::$app->user->identity->user_id;
        $solicitud = SolicitudPregunta::findOne($solicitud_id);
        $solicitud->estatus = SolicitudPregunta::STATUS_REVISION; //cambiamos el status a firmado
        $solicitud->last_modification_by = $user_id;
        $solicitud->last_modification_at = date('Y-m-d H:i:s'); //Ver porque no tiene zona horaria
        $solicitud->save();

        Pregunta::updateAll(['estatus' => Pregunta::STATUS_REVISION, 'last_modification_by' => $user_id, 
            'last_modification_at' => date('Y-m-d H:i:s') ], ['and', ['solicitud_id' => $solicitud_id], ['activo' => true]]);

        $this->redirect(['/concursos/preguntas/index']);
    }

    public function actionIndexEvaluacion(){

        $rol = Yii::$app->user->identity->role;
        $filtro = $rol == Usuarios::ROLE_PREGUNTAS_REVISOR ? 'enValidacion' : 'enRevision';
        $searchModel = new SolicitudPreguntaSearch();
        $dataProvider = $searchModel->search(Yii::$app->request->queryParams, $filtro);

        return $this->render('index-evaluacion', [
            'searchModel' => $searchModel,
            'dataProvider' => $dataProvider,
        ]);

    }

    public function verificarExistenciaDocumento($solicitud){
        if(empty($solicitud->url_doc)){
            $concurso = RegistroConcursos::findOne(['concurso_id' => $solicitud->concurso_id]);
            $preguntas = Pregunta::find()->where(['and', ['activo' => true], ['solicitud_id' => $solicitud->solicitud_id]])->asArray()->all();
            $provider = Provider::findOne($solicitud->provider_id);
            $doc_url_path = self::generarDocumentoPreguntas($concurso, $preguntas, $solicitud, $provider);
            $solicitud->url_doc = $doc_url_path;
            $solicitud->last_modification_by = Yii::$app->user->identity->user_id;
            $solicitud->last_modification_at = date('Y-m-d H:i:s');
            $solicitud->save();
        }
        return $solicitud->url_doc;
    }

    /* Pendiente documentar parte del PDF y validar si se puede usar el de GeneralController */
    public function generarDocumentoPreguntas($concurso, $preguntas, $solicitud, $provider, $firma = null){
        $name_file_save = null;
        define('_MPDF_TTFONTPATH', './fonts/'); //cambia la ruta de las fuentes utilizadas en la creación del pdf (la configuración default puede solicitar alguans fuentes necesarias)
    
        $mpdf = new mPDF('utf-8', 'A4', 0, '', 0, 0, 5, 15, 0, 0);
        $mpdf->SetTitle('Documento preguntas');
        $stylesheet = file_get_contents('css/pdf.css');

        GeneralController::add_custom_fonts_to_mpdf($mpdf);
        $mpdf->SetHTMLHeader($this->renderPartial('header-solicitud'));
        $mpdf->SetHTMLFooter($this->renderPartial('footer-solicitud'));
        
        $datos_doc = [ 'solicitud' => $solicitud, 'concurso' => $concurso, 'preguntas' => $preguntas, 'provider' => $provider ];
        $datos_doc = is_null($firma) ? $datos_doc : array_merge($datos_doc, ['firma' => $firma]);
        $content = $this->renderPartial('plantilla-solicitud', $datos_doc);

        $mpdf->WriteHTML($stylesheet, 1);
        $mpdf->WriteHTML($content);

        if(is_null($firma)){
            $name_file_save = Firmar::pre_path . md5($solicitud->solicitud_id) . '_no_valido_concursos_pregunta' . md5($solicitud->concurso_id) . '.pdf';
        }else{
            $name_file_save = Firmar::path . md5($solicitud->solicitud_id) . '_concursos_' . md5($solicitud->concurso_id) . '.pdf';
        }

        if(!is_dir(Firmar::pre_path)){ mkdir(Firmar::pre_path,0775,true); }
        if(!is_dir(Firmar::path)){ mkdir(Firmar::path,0775,true); }

        $mpdf->Output($name_file_save, 'F');

        return $name_file_save;
    }

}