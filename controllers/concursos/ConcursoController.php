<?php

namespace app\controllers\concursos;

use app\helpers\GeneralController;
use app\models\ConcursosProductos;
use app\models\ErrorInesperado;
use app\models\GrupoProducto;
use app\models\HistoricoCertificados;
use app\models\Producto;
use app\models\ProviderConcurso;
use app\models\Ubicacion;
use app\models\Usuarios;
use Yii;
use app\models\Concursos;
use app\models\Grupo;
use app\models\Productos;
use app\models\RegistroConcursos;
use app\models\RegistroConcursosSearch;
use Exception;
use yii\data\ActiveDataProvider;
use yii\data\ArrayDataProvider;
use yii\filters\AccessControl;
use yii\helpers\ArrayHelper;
use yii\filters\VerbFilter;
use yii\web\UploadedFile;
use yii\web\Response;
use kartik\form\ActiveForm;
use yii\db\Query;

/**
 * ConcursosController implements the CRUD actions for Concursos model.
 */
class ConcursoController extends GeneralController
{
    public static function puedeInscribirse($provider_id=null, $concurso_id=null){
        if(!$concurso_id)
            return false;

        $provider_id = (!$provider_id)? Yii::$app->user->identity->provider : $provider_id;
        //return true; //TODO por el momento se retorna siempre en true quitar en prod

        //tiene al menos un certificado?
        $tieneCert = HistoricoCertificados::find()
            ->where(['and',['provider_id'=>$provider_id],['tipo'=>'CERTIFICADO'],['provider_type'=>'bys']])
            ->all();
        if(!$tieneCert){return false;}

        $concurso = RegistroConcursos::findOne($concurso_id);

        //es parte de los que se pueden inscribir?
        if(!in_array($concurso->tipo,['LICITACION_NA','LICITACION_INT','LICITACION_INT_AB','SUBASTA'])
            ||   !preg_match("/DGAS-DC/i",strtoupper($concurso->numero_proceso))
        ){return false;}

        //esta activo aun el concurso?
        if(time() > strtotime($concurso->fecha_limite_inscripcion)){ return false; }

        //tiene un registro de que se inscribió con anterioridad?
        $tieneConc = ProviderConcurso::find()->where(['and',['activo'=>true],['provider_id'=>$provider_id],['concurso_id'=>$concurso_id]])->one();

        if($tieneConc){
            $tienePagoVencido = Yii::$app->db->createCommand("
            
                select * from concursos.pago p inner join concursos.detalle d on d.pago_id = p.id 
                where d.concurso_id = {$concurso_id} and p.status = 'Vencido' and p.created_by = {$provider_id}

            ")->execute();
            if(!$tienePagoVencido){return false;}
        }

        //sino entonces está activo
        return true;

    }

    /**
     * @inheritdoc
     */
    public function behaviors()
    {
        return [
            'access' => [
                'class' => AccessControl::className(),
                'only' => ['index','view','update','create'],
                'rules' => [
                    [
                        'actions' => [],
                        'allow' => true,
                        'roles' => ['@'],
                    ],
                ],
            ],
            'verbs' => [
                'class' => VerbFilter::className(),
                'actions' => [
                    //'delete' => ['POST'],
                ],
            ],
        ];
    }


    public function actionIndex()
    {
        if(!Yii::$app->user->can(Usuarios::ROLE_ADMIN_CONCURSOS))
            return $this->goHome();

        $searchModel = new RegistroConcursosSearch();
        $dataProvider = $searchModel->search(Yii::$app->request->queryParams);
        $this->layout='home';
        return $this->render('index', [
            'searchModel' => $searchModel,
            'dataProvider' => $dataProvider,
        ]);
    }

    /**
     * Funcion encargada de notificar con copia oculta a los proveedores relacionados en el grupo del catalogo
     *  de productos con el concurso indicado 
     */
    public function actionNotificarProveedores(int $concurso_id){
        $concurso = RegistroConcursos::findOne($concurso_id);
        $productos = $concurso->grupo_producto;
        if(!empty($productos)){
            $query_inst = (new Query())->select('p.email')->distinct('p.email')->from('provider p')->
                innerJoin('provider_giro pg', 'p.provider_id = pg.provider_id')->where(['in', 'pg.producto_id', $productos])->all();
            /* $sql_proveedores = "SELECT DISTINCT(p.email) FROM provider p INNER JOIN provider_giro pg ON p.provider_id = pg.provider_id
            INNER JOIN productos.producto pp on pg.producto_id = pp.producto_id
            INNER JOIN productos.clase pc on pp.clase_id = pc.clase_id WHERE pc.grupo_id = ${grupo_id}"; */
            //$raw_proveedores = Yii::$app->db->createCommand($sql_proveedores)->queryAll();
            $correos_proveedores = ArrayHelper::getColumn($query_inst,'email');
            if( count($correos_proveedores) > 0 ){
                GeneralController::sendMassivePrivateEmail('/provider/correos/invitacion_concurso_general', '<EMAIL>', $correos_proveedores, 'Un concurso para ti');
                $concurso->estatus_email = true;
                $concurso->save(false);
            }
            /* $datos_concurso = [ 'nombre_concurso' => $concurso->numero_proceso, 'tipo_concurso' => RegistroConcursos::TIPOS_CONCURSOS[$concurso->tipo],
                'grupo_producto' => $grupo->nombre, 'concurso_id' => $concurso_id]; */
        }

        return $this->redirect('index');

    }


    public function actionViewcsv($id=null){
        $model = new RegistroConcursos();
        $data = [
            'model'=>new ArrayDataProvider([
                'allModels'=>glob($model->pathCSV.intval($id)."_*")
                ])
            ];
        return $this->renderAjax('viewcsv',$data);
    }


    public function actionView($id){
        return $this->render('view', [
            'model' => $this->findRegistroConcurso($id),
        ]);


    }


    public function actionViewhome($id){
        return $this->renderAjax('viewhome', [
            'model' => $this->findRegistroConcurso($id),
        ]);
    }


    public function actionViewGob($concurso_id){
        return $this->renderAjax('view-gob', [
            'model' => Concursos::getConcursosApi(null, $concurso_id)
        ]);
    }

    public function actionViewConcursos($filter=null){

        /*
        $concursos_site = RegistroConcursos::findAll(['activo' => 'true']);
        //$concurso_gob = Concursos::getConcursosApi();
        $total_data = array_merge($concursos_site);
        usort( $total_data, function($a,$b){ 
            $date_comp_a = $a['tipo'] == 'CONCURSO' ? $a['fecha_fallo_definitivo'] : $a['fecha_limite_inscripcion'];
            $date_comp_b = $b['tipo'] == 'CONCURSO' ? $b['fecha_fallo_definitivo'] : $b['fecha_limite_inscripcion'];
            return strtotime($date_comp_a) + strtotime($date_comp_b);
        });


        $countTotal = count($total_data);
        */

        $concursos = RegistroConcursos::find()->where(['activo'=>true])->orderBy('concurso_id desc');


        //$concursos->asArray()->all()
        return $this->render('view-concursos', [
            'concursos' => $concursos
        ]);

    }

    public function actionMisConcursos(){
        if(!Yii::$app->user->can(Usuarios::ROLE_PROVIDER))
            return $this->goHome();

        $query = RegistroConcursos::find()->select("*")
            ->innerJoin('provider_concurso','registro_concursos.concurso_id = provider_concurso.concurso_id')
            ->where(['and',['provider_id'=>Yii::$app->user->identity->provider],['provider_concurso.activo'=>true]]);

        $dataProvider = new ActiveDataProvider(['query'=>$query]);

        return $this->render('mis-concursos',['dataProvider'=>$dataProvider]);
    }

    public function actionCreate()
    {
        $model = new Concursos();
        $this->layout = 'home';
        $t = $model->getDb()->beginTransaction();
        $model->imageFile = UploadedFile::getInstance($model, 'url_img');


        $model->created_id = Yii::$app->user->getId();
        if ($model->load(Yii::$app->request->post())) {
            $model->url_img = !empty($model->imageFile) ? $model->path_concursos . '/' . md5($model->imageFile->baseName) . time() . '.' . $model->imageFile->extension : null;

            if ($model->save()) {
                if (!file_exists($model->path_concursos)) {
                    mkdir($model->path_concursos, 0755, true);
                }
                !empty($model->imageFile) ? $model->imageFile->saveAs($model->url_img) : null;
                $validate = true;
                foreach ($model->producto as $val){
                    $cp = new ConcursosProductos();
                    $cp->concurso_id = $model->concurso_id;
                    $cp->producto_id = $val;
                    $validate = $validate && $cp->save();
                }

                if($validate){
                    $t->commit();
                    return $this->redirect(['view', 'id' => $model->concurso_id]);
                }
                $t->rollBack();

            } else {
                $t->rollBack();
            }
        }
        //$grupo = ArrayHelper::map(GrupoProducto::find()->select(['grupo_producto_id','nombre'])->asArray()->all(),'grupo_producto_id','nombre');
        $grupo = ArrayHelper::map(Grupo::find()->select(['grupo_id','descripcion'])->asArray()->all(),'grupo_id','descripcion');

        return $this->render('create', [
            'model' => $model,
            'grupo' => $grupo,
            'producto' => [],
            'productoSelected' => []
        ]);
    }

    public function actionUpdate($id=null)
    {

        $isRequest = Yii::$app->request->isAjax;
        $postRequest = Yii::$app->request->post();

        $model = $this->findRegistroConcurso($id);
        //$arr_grupos = ArrayHelper::map(GrupoProducto::find()->select(['grupo_producto_id','nombre'])->asArray()->all(),'grupo_producto_id','nombre');
        $arr_grupos = ArrayHelper::map(Grupo::find()->select(['grupo_id','descripcion'])->asArray()->all(),'grupo_id','descripcion');
        $arr_productos = is_null($id) ? [] : ArrayHelper::map( Productos::findAll($model->grupo_producto), 'producto_id','descripcion');
        $model->user_id = Yii::$app->user->identity->user_id;
        if ( $isRequest && $model->load($postRequest) ) {
            Yii::$app->response->format = Response::FORMAT_JSON;
            return ActiveForm::validate($model);
        }else if ( $model->load($postRequest) ) {

            if($model->tipo == 'LICITACION'){
                $model->imagen_concurso = $model->saveFinalPath($model->imagen_concurso);
                $model->archivo_convocatoria = $model->saveFinalPath($model->archivo_convocatoria);
                $model->ficha_tecnica = $model->saveFinalPath($model->ficha_tecnica);
                $model->archivo_ppte = $model->saveFinalPath($model->archivo_ppte);
                $model->archivo_ftae = $model->saveFinalPath($model->archivo_ftae);
                $model->acta_comite = $model->saveFinalPath($model->acta_comite);
                $model->archivo_fd = $model->saveFinalPath($model->archivo_fd);
            }
            if($model->tipo == 'CONCURSO'){
                $model->imagen_concurso = $model->saveFinalPath($model->imagen_concurso);
                $model->archivo_bft = $model->saveFinalPath($model->archivo_bft);
                $model->archivo_propuestas = $model->saveFinalPath($model->archivo_propuestas);
                $model->archivo_comite = $model->saveFinalPath($model->archivo_comite);
                $model->archivo_fallo = $model->saveFinalPath($model->archivo_fallo);
            }
            if($model->tipo == 'SUBASTA'){
                $model->ficha_tecnica = $model->saveFinalPath($model->ficha_tecnica);
                $model->imagen_concurso = $model->saveFinalPath($model->imagen_concurso);
                $model->archivo_aclaraciones = $model->saveFinalPath($model->archivo_aclaraciones);
                $model->archivo_ft = $model->saveFinalPath($model->archivo_ft);
                $model->acta_comite = $model->saveFinalPath($model->acta_comite);
                $model->archivo_op_subasta = $model->saveFinalPath($model->archivo_op_subasta);
                $model->archivo_fd = $model->saveFinalPath($model->archivo_fd);
            }

            $model->grupo_producto = array_map(function ($item) {return intval($item); }, $postRequest['grupo_producto'] );

            //if($model->estatus_email == RegistroConcursos::STATUS_ENVIADO){ $model->estatus_email = RegistroConcursos::STATUS_MODIFICADO; }

            if($model->save()){ return $this->redirect(['index']); }
        }

        return $this->render('update', [
            'model' => $model,
            'arr_grupos' => $arr_grupos,
            'arr_productos' => $arr_productos
        ]);

    }

    public function actionUpdateNew($id=null){
        $model = $this->findRegistroConcurso($id);
        return $this->render('update-new', [ 'model' => $model ]);
    }

    public function copyFile($file_tmp, $new_folder)
    {

        if (file_exists($file_tmp)) {
            rename($file_tmp, $new_folder);
        }

    }

    public function actionDelete($id)
    {
        $model = $this->findRegistroConcurso($id);
        $model->activo = false;
        $model->save(false);
        return $this->redirect(['index']);
    }

    public function actionGetConcursos($estatus=null, $year=null){
        $response = Yii::$app->response;
        $response->format = Response::FORMAT_JSON; 

        $date_actual = time();
        $concursos_site = RegistroConcursos::find()
            ->where(['activo' => 'true'])
            ->orderBy(['fecha_limite_inscripcion'=>SORT_DESC])
            ->all();
        $order_status = [
            1 => 'licitacion_iniciar',
            2 => 'licitacion_abierta',
            3 => 'licitacion_proceso',
            4 => 'licitacion_adjudicada'
        ];

        $response_concursos =  array_map( function($concurso) use ($date_actual){
            $arr_response = [
                "render_data" => [ 'clase' => 'color_iniciar', 'class_icon' => '', 'concurso_type' => '', 'titulo' => 'Por Iniciar', 'imagen' => 'licitacion_iniciar'], 
                "concurso_data"  => $concurso->toArray()
            ];
            if (GeneralController::str_contains($concurso['tipo'], 'LICITACION' )){
                $arr_response["render_data"]['class_icon'] = 'publica';
                $arr_response["render_data"]['concurso_type'] = $concurso['tipo'] == 'LICITACION_NA' ? "Licitación pública nacional" : 
                    ($concurso['tipo'] == 'LICITACION_INT' ? "Licitación pública internacional" : 'Licitación pública internacional abierta');
                $date_inicio = strtotime($concurso['fecha_inicio_inscripcion']);
                $date_fin = strtotime($concurso['fecha_limite_inscripcion']);
                $date_adj = strtotime($concurso['fecha_h_fallo_definitivo']);
                if($date_actual >= $date_inicio && $date_actual <= $date_fin){
                    $arr_response["render_data"]['clase'] = 'color_abierta';
                    $arr_response["render_data"]['titulo'] = 'Abierta';
                    $arr_response["render_data"]['imagen'] = 'licitacion_abierta';
                } else if($date_actual > $date_fin && $date_actual <= $date_adj){
                    $arr_response["render_data"]['clase'] = 'color_proceso';
                    $arr_response["render_data"]['titulo'] = 'En Proceso';
                    $arr_response["render_data"]['imagen'] = 'licitacion_proceso';
                }else if ($date_actual > $date_adj){
                    $arr_response["render_data"]['clase'] = 'color_adjudicada';
                    $arr_response["render_data"]['titulo'] = 'Adjudicada';
                    $arr_response["render_data"]['imagen'] = 'licitacion_adjudicada';
                }
            } else if ($concurso['tipo'] == 'CONCURSO') {
                $arr_response["render_data"]['class_icon'] = 'restringida';
                $arr_response["render_data"]['concurso_type'] = 'Concurso por invitación restringida';
                $date_inicio = strtotime($concurso->fecha_apertura_propuestas);
                $date_fin = strtotime($concurso->fecha_fallo_definitivo);
                if($date_actual >= $date_inicio && $date_actual <= $date_fin){
                    $arr_response["render_data"]['clase'] = 'color_abierta';
                    $arr_response["render_data"]['titulo'] = 'Abierta';
                    $arr_response["render_data"]['imagen'] = 'licitacion_abierta';
                } else if ($date_actual > $date_fin){
                    $arr_response["render_data"]['clase'] = 'color_adjudicada';
                    $arr_response["render_data"]['titulo'] = 'Adjudicada';
                    $arr_response["render_data"]['imagen'] = 'licitacion_adjudicada';
                }
            } else {
                $arr_response["render_data"]['class_icon'] = 'subasta';
                $arr_response["render_data"]['concurso_type'] = 'Subasta electrónica inversa';
                $date_inicio = strtotime($concurso['fecha_inicio_inscripcion']);
                $date_fin = strtotime($concurso['fecha_limite_inscripcion']);
                $date_adj = strtotime($concurso['fecha_h_fallo_definitivo']);
                if($date_actual >= $date_inicio && $date_actual <= $date_fin){
                    $arr_response["render_data"]['clase'] = 'color_abierta';
                    $arr_response["render_data"]['titulo'] = 'Abierta';
                    $arr_response["render_data"]['imagen'] = 'licitacion_abierta';
                } else if($date_actual > $date_fin && $date_actual <= $date_adj){
                    $arr_response["render_data"]['clase'] = 'color_proceso';
                    $arr_response["render_data"]['titulo'] = 'En Proceso';
                    $arr_response["render_data"]['imagen'] = 'licitacion_proceso';
                }else if ($date_actual > $date_adj){
                    $arr_response["render_data"]['clase'] = 'color_adjudicada';
                    $arr_response["render_data"]['titulo'] = 'Adjudicada';
                    $arr_response["render_data"]['imagen'] = 'licitacion_adjudicada';
                }
            }

            return $arr_response;
        }, $concursos_site);

        if($estatus){
            $response_concursos = array_filter($response_concursos, function($data) use ($estatus) { return $data["render_data"]['imagen'] == $estatus; });
        }

        //var_dump($response_concursos).exit();
        /* usort( $response_concursos, function($a,$b){ //Hace un intento de ordenamiento de concursos por fecha de fin
            $date_comp_a = $a['tipo'] == 'CONCURSO' ? $a['fecha_fallo_definitivo'] : $a['fecha_limite_inscripcion'];
            $date_comp_b = $b['tipo'] == 'CONCURSO' ? $b['fecha_fallo_definitivo'] : $b['fecha_limite_inscripcion'];
            return strtotime($date_comp_a) - strtotime($date_comp_b);
        }); */

        return array_values($response_concursos);

    }

    protected function findModel($id){
        return ($model = Concursos::findOne($id)) !== null ? $model :new Concursos();
    }

    protected function findRegistroConcurso($id){
        return ($model = RegistroConcursos::findOne($id)) !== null ? $model :new RegistroConcursos();
    }

    public function actionList_productos($id = null){
        $con ='';
        $con .='<option value=""></option>';
        if(Yii::$app->request->isAjax && $id!=null && !empty($id)){
            $act1 =  ArrayHelper::map(Producto::find()->select(['producto_id','nombre'])
                ->where(['grupo_producto_id' => $id])
                ->orderBy(['producto_id'=>SORT_ASC])
                ->asArray()->all(),'producto_id','nombre');

            if (count($act1) > 0) {
                foreach ($act1 as $key => $value) $con .="<option value='" . $key . "'>" . $value . "</option>";
            } else {
                $con .="<option>No hay Productos</option>";
            }
        }

        echo $con;
    }

    public function findModelConcursoProducto($id){

        $model = ConcursosProductos::find()->select("p.grupo_producto_id as grupo_producto")
            ->addSelect(new \yii\db\Expression("string_agg(concursos_productos.producto_id::text,',') as producto_id"))
            ->from('concursos_productos')->innerJoin('producto p','p.producto_id = concursos_productos.producto_id')
            ->groupBy("grupo_producto_id")->where(['concurso_id' => $id])->asArray()->one();

        if($model == null || empty($model)){
            $model = new ConcursosProductos();
        }
        return $model;
    }

    /*
    public function actionConcurso($tramite_id=null,$concurso_id=null,$redirect=false){

        //modelosPt1
        $usuario = Usuarios::findOne(Yii::$app->user->id);
        $proveedor = Provider::find()->where(['user_id'=>$usuario->user_id])->one();


        if(!$tramite_id || !$concurso_id)
            return $this->goHome();
        //redirecciona al tramite
        if($redirect){
            $model = ProviderConcurso::find()
                ->where(['provider_id'=>$proveedor->provider_id, 'concurso_id'=>$concurso_id, 'activo'=>true])->one();
            $model = !$model ?  new ProviderConcurso() : $model ;
            $model->provider_id = $proveedor->provider_id;
            $model->concurso_id = $concurso_id;
            $model->save(false);
            $this->redirect('/tramites/procedures/create/'.$tramite_id.'?concurso_id='.$concurso_id);//Redirige al panel
        }


        //datos base
        $clientId = 'gobnl-webapp';
        $clientSecretApi = '4d79e7298176b0f674f0ee8ca48e57669e16ec76';
        $secretKey = '4d2d4ded34dfedwd2323w';
        $basicAuth = 'Basic '.base64_encode($clientId.':'.$clientSecretApi);
        $urlGPM = 'http://pruebas.gpm.nl.gob.mx/api/';

        //modelosPt2
        //validaciones iniciales
        $curp = Curp::find()->where(['provider_id'=>$proveedor->provider_id])->one();
        $rl = RepresentanteLegal::find()->where(['activo'=>true,'rep_bys'=>true,'provider_id'=>$proveedor->provider_id])->one();
        if($proveedor->tipo_persona == 'Persona moral'){
            if(!$rl || !isset($rl->telefono) || !isset($rl->curp) || $rl->telefono == '' || $rl->curp == ''){
                Yii::$app->session->setFlash('error','Es necesario agregar un representante legal para poder continuar');
                return Yii::$app->getResponse()->redirect(['/' . $proveedor->tipo_provider . '/legales/view']);
            }
            //var_dump($proveedor->tipo_persona,json_encode($rl->telefono));
        }else{
            $dom = Ubicacion::find()->where(['activo'=>true, 'tipo'=>'DOMICILIO FISCAL','provider_id'=>$proveedor->provider_id])->one();
            if(!$dom || !isset($dom->telefono) || $dom->telefono == ''){
                Yii::$app->session->setFlash('error','Es necesario agregar datos del domicilio fiscal para poder continuar');
                return Yii::$app->getResponse()->redirect(['/' . $proveedor->tipo_provider . '/ubicacion/index']);
            }
        }



        //verifica si existe el usuario
        $dataBuscar = [
            'client_id'=>$clientId,
            'curp'=>$proveedor->tipo_persona == 'Persona moral' ? $rl->curp : $curp->curp,
            'origin'=>'PROVEEDORES',
        ];

        $headersGen = [
            'Authorization:'.$basicAuth,
            'Content-Type:application/json'
        ];

        //var_dump($urlGPM,$headersGen);
        //print_r("<br><br>");
        $dataToken =  $this->curlPostForm($urlGPM.'usuario_buscar',$dataBuscar,['Authorization:'.$basicAuth]);

        //print_r($dataBuscar);
        //print_r("<br><br>");

        //var_dump(json_encode($dataToken));
        //print_r("<br><br>");

        //si no existe, darlo de alta en la plataforma
        if(!isset($dataToken['origin'])){

            $formUser = [
                "tipo_persona"=>$proveedor->tipo_persona == 'Persona moral' ? 'moral' : 'fisica',
                "usuario"=>$usuario->username.'_'.$usuario->user_id.'_'.$proveedor->provider_id,
                "password"=>$usuario->password,
                "password_confirm"=>$usuario->password,
                "nombres"=>$proveedor->tipo_persona == 'Persona moral' ? $proveedor->name_razon_social : $proveedor->pf_nombre,
                "apellido_paterno"=>$proveedor->tipo_persona == 'Persona moral' ? '' : $proveedor->pf_ap_paterno,
                "apellido_materno"=>$proveedor->tipo_persona == 'Persona moral' ? '' : $proveedor->pf_ap_materno,
                "email"=>$usuario->email,
                "curp"=>$proveedor->tipo_persona == 'Persona moral' ? '': $curp->curp,
                "rfc"=>$proveedor->rfc,
                "telefono"=>$proveedor->tipo_persona == 'Persona moral' ? $rl->telefono : $dom->telefono,
                "origin"=>"PROVEEDORES",
                "client_id"=>"gobnl-webapp"
            ];

            $usuarioRegistrar =  $this->curlPostForm($urlGPM.'usuario_registrar',$formUser,['Authorization:'.$basicAuth]);


            //print_r($urlGPM.'usuario_registrar');
            //print_r("<br><br>");


            //print_r($formUser);
            //print_r("<br><br>");

            //print_r($usuarioRegistrar).exit();
            //print_r("<br><br>");


            if(isset($usuarioRegistrar['error'])){
                $this->setErrorConcursos($usuarioRegistrar);
                Yii::$app->session->setFlash('error','Ocurrió un incidente, favor de reportar a un administrador.');
                return Yii::$app->getResponse()->redirect(['/provider/dashboard']);
            }
        }

        //loguear al usuario
        $dataAccess = [
            'client_id'=>$clientId,
            'username'=>$usuario->username,
            'id'=>$proveedor->tipo_persona == 'Persona moral' ? $proveedor->rfc: $curp->curp,
            'api_key'=>$secretKey,
            'password'=>$usuario->password,
            'grant_type'=>'password',
            'origin'=>'PROVEEDORES',
            "rfc"=>$proveedor->rfc,
        ];

        //var_dump('<br><br>dataAccess',json_encode($dataAccess));

        $dataToken = $this->solicitaDatosPost($urlGPM.'access_token',$dataAccess,$headersGen);


        if(isset($dataToken['error']) || gettype($dataToken) == 'string'){
            $this->setErrorConcursos($dataToken);
            //var_dump('<br><br>dataToken',($dataToken)).exit();
            Yii::$app->session->setFlash('error','Ocurrió un incidente, favor de reportar a un administrador.');
            return Yii::$app->getResponse()->redirect(['/provider/dashboard']);
        }

        //pide la data del usuario
        $headersUser = [
            'Authorization:Bearer '.$dataToken['access_token'],
            'Content-Type:application/json'
        ];
        $dataUser = $this->solicitaDatosGet($urlGPM.'usuario_perfil',$headersUser);
        //var_dump('<br><br>',json_encode($dataUser));

        //setea la sesion con los datos del usuario en js
        $script = "
            localStorage.setItem('token_','".($dataToken['access_token'])."');
            console.log(localStorage.getItem('token_'));
            localStorage.setItem('token_expiration_','".(($dataToken['expires_in']+time())*1000)."');
            console.log(localStorage.getItem('token_expiration_'));
            localStorage.setItem('authentication_','".(json_encode($dataToken))."');
            console.log(localStorage.getItem('authentication_'));
            localStorage.setItem('user_','".(json_encode($dataUser['data']))."');
            console.log(localStorage.getItem('user_'));
            localStorage.setItem('token','".CriptoController::cryptoJsAesEncrypt($secretKey,$dataToken['access_token'])."');
            console.log(localStorage.getItem('token'));
            localStorage.setItem('authentication','".CriptoController::cryptoJsAesEncrypt($secretKey,json_encode($dataToken))."');
            console.log(localStorage.getItem('authentication'));
            localStorage.setItem('user','".CriptoController::cryptoJsAesEncrypt($secretKey,json_encode($dataUser['data']))."');
            console.log(localStorage.getItem('user'));
            localStorage.setItem('token_expiration','".CriptoController::cryptoJsAesEncrypt($secretKey,($dataToken['expires_in']+time())*1000)."');
            console.log(localStorage.getItem('token_expiration'));
            location.href = 'concurso?tramite_id=$tramite_id&concurso_id=$concurso_id&redirect=1';
        ";

        //var_dump('<br><br>',($script)).exit();

        return $this->render('localstorage',['script'=>$script]);
    }
*/

    private function curlPostForm($url,$data,$headers){
        $curl = curl_init();
        curl_setopt($curl, CURLOPT_URL, $url);
        curl_setopt($curl, CURLOPT_POST, 1);
        curl_setopt($curl, CURLOPT_POSTFIELDS, $data);
        curl_setopt($curl, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
        $curlData = curl_exec($curl);
        curl_close($curl);
        return  json_decode($curlData, true);
    }


    private function setErrorConcursos($array){
        $id = Yii::$app->user->getId();
        $error = new ErrorInesperado();
        $error->tipo_error = 'ErrorConcursos_'.$id;
        $error->data = gettype($array) == 'string' ? json_decode($array) : $array;
        $error->user_id = $id;
        $error->save(false);
    }



}
