<?php


namespace app\controllers\concursos;



use app\helpers\GeneralController;
use app\models\Provider;
use app\models\ProviderConcurso;
use app\models\RegistroConcursosSearch;
use app\models\Usuarios;
use Yii;
use ZipArchive;

class AdminController extends GeneralController
{

    public function actionIndex()
    {
        if(!Yii::$app->user->can(Usuarios::ROLE_ADMIN_CONCURSOS))
            return $this->goHome();

        $searchModel = new RegistroConcursosSearch();
        $dataProvider = $searchModel->search(Yii::$app->request->queryParams,true);
        $this->layout='home';
        return $this->render('registrados', [
            'searchModel' => $searchModel,
            'dataProvider' => $dataProvider,
        ]);
    }


    public function actionExport(){
        $data = Provider::findBySql('
        
            select c.numero_proceso as "Número de proceso",
            c.descripcion as "Nombre del Procedimiento", pc.created_at as "Fecha de inscripción",
            case when p.tipo_persona = \'Persona moral\' then \'Persona Moral\' else \'Persona Física\' end as "Tipo persona",
             p.rfc as "RFC",  p.name_razon_social as "Proveedor", concat_ws(\' \',nombre, ap_paterno, ap_materno) as "Representante Legal",
            p.telfono as "Teléfono de contacto"
            from provider p inner join provider_concurso pc on pc.provider_id = p.provider_id 
            inner join registro_concursos c on c.concurso_id = pc.concurso_id
            left join provider.representante_legal r on r.provider_id = p.provider_id
            order by c.concurso_id desc
         
         ')->asArray()->all();

        return $this->downloadFile('DB_Concursos'.date('Y-m-d').'.csv',$data);
    }


    public function actionFinishedone($id=null,$idConcurso){
        if(!$id)
            return $this->goHome();
        $nameZip = $this->createZipByProvider($id,$idConcurso);
        return $this->downloadZip($nameZip);

    }


    public function actionFinishedall($id=null){
        if(!$id)
            return $this->goHome();
        $model = ProviderConcurso::find()->where(['concurso_id'=>$id])->all();
        $path_zip = 'provider/zip/';
        $nameZipConcurso = 'provider/zip/'.$id.'_CONCURSO.zip';
        $path_vacio = "{$path_zip}concursos_vacio.txt";
        if(file_exists($nameZipConcurso)){
            return $this->downloadZip($nameZipConcurso);
        }

        $zipConcurso = new \ZipArchive();
        $zipConcurso->open($nameZipConcurso, \ZIPARCHIVE::CREATE);
        $count_files = 0;

        foreach ($model as $prov){
            $zipProv = $this->createZipByProvider($prov->provider_id,$id);
            $zipConcurso->addFile($zipProv,$prov->provider_id.'.zip');
            $count_files++;
        }
        if($count_files == 0){
            if(!file_exists($path_vacio)){
                $fp = fopen($path_vacio,"wb");
                fwrite($fp,'La consulta no regreso registros');
                fclose($fp);
            }
            $zipConcurso->addFile($path_vacio,'documento_vacio.txt');
        }
        $zipConcurso->close();

        return $this->downloadZip($nameZipConcurso);

    }


    public function createZipByProvider($providerId,$ConcursoId){

        $provider = Provider::findOne($providerId);
        $path_zip = 'provider/zip/';
        $path_vacio = "{$path_zip}concursos_vacio.txt";

        //identifica el nombre del archivo
        $nameZip = "{$path_zip}{$providerId}_PROVEEDOR.zip";
        !file_exists($path_zip) && mkdir($path_zip,0775,true);

        //retornar si ya existe el zip creado
        if(file_exists($nameZip)){ return $nameZip; }

        if($provider->tipo_persona == 'Persona moral'){  
            
            $data = Yii::$app->db->createCommand("
                SELECT cer.url_certificado AS \"CertificadoProveedor\",
                act.documento_acta AS \"ActaConstitutiva\",	
                (select string_agg(documento_acta,'|') from provider.modificacion_acta where provider_id = {$provider->provider_id} and activo is true group by provider_id) AS \"Modificaciones\",
                CASE WHEN rl.tipo_poder = 'ACTA' THEN act.documento_acta 
                    WHEN rl.tipo_poder = 'PODER' THEN rl.documento_acta 
                    WHEN rl.tipo_poder = 'ACTUALIZACION' THEN moda.documento_acta 
                    END AS \"AcreditacionRL\",
                rl.documento_identificacion AS \"IdentificacionOficialRepLegal\", 
                rfc.url_rfc AS \"ConstanciaSituacionFiscal\",
                ubi.url_comprobante_domicilio AS \"ComprobanteDomicilio\" ,
                pc.doc AS \"ManifiestoInteres\" 
                FROM provider pro 
                LEFT JOIN historico_certificados cer on cer.provider_id = pro.provider_id and cer.tipo = 'CERTIFICADO'
                LEFT JOIN provider.ubicacion ubi on ubi.provider_id = pro.provider_id and ubi.tipo = 'DOMICILIO FISCAL' and ubi.activo is true
                LEFT JOIN provider.representante_legal rl on rl.provider_id = pro.provider_id and rl.activo is true and rl.rep_bys is true
                LEFT JOIN provider.rfc rfc on rfc.provider_id = pro.provider_id
                LEFT JOIN provider.acta_constitutiva act on act.provider_id = pro.provider_id
                LEFT JOIN provider.modificacion_acta moda on moda.provider_id = pro.provider_id and moda.activo is true
                LEFT JOIN provider_concurso pc on pc.provider_id = pro.provider_id
                WHERE pro.provider_id = {$provider->provider_id} ORDER BY cer.created_at DESC LIMIT 1")->queryOne();//si es persona moral

        }else{ //si es persona fisica

            $data = Yii::$app->db->createCommand("
                SELECT cer.url_certificado AS \"CertificadoProveedor\",
                rfc.url_rfc AS \"ConstanciaSituacionFiscal\",
                id.url_idoficial AS \"IdentificacionOficial\", ubi.url_comprobante_domicilio AS \"ComprobanteDomicilio\"  ,
                pc.doc AS \"ManifiestoInteres\" 
                FROM provider pro 
                LEFT JOIN historico_certificados cer on cer.provider_id = pro.provider_id and cer.tipo = 'CERTIFICADO'
                LEFT JOIN provider.ubicacion ubi on ubi.provider_id = pro.provider_id and ubi.tipo = 'DOMICILIO FISCAL' and ubi.activo is true
                LEFT JOIN provider.id_oficial id on id.provider_id = pro.provider_id
                LEFT JOIN provider.rfc rfc on rfc.provider_id = pro.provider_id
                LEFT JOIN provider_concurso pc on pc.provider_id = pro.provider_id
                WHERE pro.provider_id = {$provider->provider_id} ORDER BY cer.created_at DESC LIMIT 1")->queryOne();//si es persona moral

        }

        $zip = new ZipArchive();
        $zip->open($nameZip, ZipArchive::CREATE);
        $count_files = 0;
        $aux = 1;

        foreach ($data as $key => $file){
            if(!empty($file)){
                if($key == 'Modificaciones'){
                    foreach (explode('|',$file) as $keym => $filem){
                        $zip->addFile($filem,$aux.".".($keym+1)."-".$key.'.pdf');
                    }
                }else{
                    $zip->addFile($file,$aux."-".$key.'.pdf');
                }
                $aux++;
                $count_files++;
            }
        }

        if($count_files == 0){
            if(!file_exists($path_vacio)){
                $fp = fopen($path_vacio,"wb");
                fwrite($fp,'La consulta no regreso registros');
                fclose($fp);
            }
            $zip->addFile($path_vacio,'documento_vacio.txt');
        }

        $zip->close();
        return $nameZip;
    }


    public function downloadZip($filename){
        if(file_exists($filename)) {
            header('Content-Description: File Transfer');
            header('Content-Type: application/octet-stream');
            header("Cache-Control: no-cache, must-revalidate");
            header("Expires: 0");
            header('Content-Disposition: attachment; filename="'.basename($filename).'"');
            header('Content-Length: ' . filesize($filename));
            header('Pragma: public');
            flush();
            readfile($filename);
        }
        else{
            echo "File ($filename) does not exist.";
        }
    }

}