<?php

namespace app\controllers;

use app\helpers\GeneralController;
use app\models\CatEntidades;
use app\models\CatVialidad;
use app\models\NoteVisit;
use app\models\Provider;
use app\models\ProviderQuery;
use app\models\ProviderSearch;
use app\models\Usuarios;
use app\models\VisitDetails;
use kartik\mpdf\Pdf;
use mPDF;
use Yii;
use app\models\Visit;
use app\models\VisitSearch;
use yii\data\ActiveDataProvider;
use yii\helpers\ArrayHelper;
use yii\web\Controller;
use yii\web\NotFoundHttpException;
use yii\filters\VerbFilter;

/**
 * VisitController implements the CRUD actions for Visit model.
 */
class VisitController extends GeneralController
{
    /**
     * {@inheritdoc}
     */
    public function behaviors()
    {
        return [
            'verbs' => [
                'class' => VerbFilter::className(),
                'actions' => [
                    // 'delete' => ['POST'],
                ],
            ],
        ];
    }

    /**
     * Lists all Visit models.
     * @return mixed
     */
    public function actionIndex()
    {
        $role = \Yii::$app->user->identity->role;

        $tipo = in_array($role, ['VALIDADOR PROVEEDORES', 'ADMIN PROVIDER']) ? 'bys' : 'op';

        $params = \Yii::$app->request->queryParams;
        $data = ProviderQuery::getAllDataVisit($params,$tipo);
        $searchModel = new ProviderSearch();
        return $this->render('index', [
            'dataProvider' => $data,
            'tipo' => $tipo,
            'searchModel' => $searchModel
        ]);
    }


    public function actionRetornarVisit($id = null, $prov_id = null)
    {
        if ($id != null) {
            $provider_id = intval($prov_id);
            if (($mod = Provider::findOne($provider_id)) !== null) {
                $mod->status_cotejar = null;
                if ($mod->save(false)) {
                    $visit = Visit::find()->where(['and', ['provider_id' => $provider_id], ['status' => true]])->asArray()->all();

                    if ($visit) {
                        foreach ($visit as $v) {

                            if ($v['modify']) {
                                Visit::updateAll(['status' => false], ['visit_id' => $v['visit_id']]);
                            } else {
                                VisitDetails::deleteAll(['visit_id' => $v['visit_id']]);
                                Visit::deleteAll(['visit_id' => $v['visit_id']]);
                            }
                        }
                    }
                    $this->insertMovProv($provider_id, 'RETORNAR ETAPA EDICION');
                }
            }

        }

        return $this->redirect('/visit/index');

    }


    public function actionHistoricIndex()
    {

        if (Yii::$app->user->can(Usuarios::ROLE_USUARIO_CONSULTA_BYS)) {

            $perCon = ArrayHelper::getColumn(Yii::$app->db->createCommand("select distinct p.permission from permission_view_module p
                    join permission_view_module_user pu on pu.permission_view_module_id = p.permission_view_module_id
                    where pu.user_id = :id", [':id' => Yii::$app->user->getId()])->queryAll(), 'permission');

            if (!in_array('Histórico de Visitas', $perCon)) {
                return $this->goHome();
            }
        }

        $role = \Yii::$app->user->identity->role;


        $tipo = in_array($role, ['VALIDADOR PROVEEDORES', 'ADMIN PROVIDER']) ? 'bys' : 'op';


        $searchModel = new VisitSearch();
        $searchModel->statusV = false;
        $dataProvider = $searchModel->search(Yii::$app->request->queryParams);

        return $this->render('historic-index', [
            'searchModel' => $searchModel,
            'dataProvider' => $dataProvider,
            'tipo' => $tipo
        ]);
    }

    /**
     * Displays a single Visit model.
     * @param integer $id
     * @return mixed
     * @throws NotFoundHttpException if the model cannot be found
     */
    public function actionView($id)
    {
        return $this->render('view', [
            'model' => $this->findModel($id),
        ]);
    }

    /**
     * Creates a new Visit model.
     * If creation is successful, the browser will be redirected to the 'view' page.
     * @return mixed
     */
    public function actionCreate()
    {
        $model = new Visit();

        //echo '<pre>';
        //var_dump(array_merge(Yii::$app->request->post()['otros_giros']));exit();
        if ($model->load(Yii::$app->request->post()) && $model->save()) {
            return $this->redirect(['view', 'id' => $model->visit_id]);
        }
        $state = ArrayHelper::map(CatEntidades::find()->select(['entidad_id', 'nombre'])->asArray()
            ->all(), 'entidad_id', 'nombre');

        $vialidad = ArrayHelper::map(CatVialidad::find()->select(['vialidad_id', 'descripcion'])->asArray()
            ->all(), 'vialidad_id', 'descripcion');

        return $this->render('create', [
            'model' => $model,
            'state' => $state,
            'vialidad' => $vialidad
        ]);
    }

    /**
     * Creates a new Visit model.
     * If creation is successful, the browser will be redirected to the 'view' page.
     * @return mixed
     */
    public function actionCreateVisit($id = null)
    {
        $model = $this->findModel(intval($id));

        if ($model->load(Yii::$app->request->post()) && $model->save()) {
            return $this->redirect(['index']);
        }


        return $this->renderAjax('create_visit', [
            'model' => $model
        ]);
    }


    public function actionCreateVisitOp($id=null)
    {
        $model = $this->findModel(intval($id));

        if ($model->load(Yii::$app->request->post())) {
            $model->provider_type = 'op';
            $model->save(false);
            return $this->redirect(['index']);
        }


        $prov = ArrayHelper::map(Yii::$app->db->createCommand("select distinct p.provider_id as id, CASE WHEN (p.name_razon_social is not null and p.name_razon_social != '') then p.name_razon_social else concat_ws(' ',p.pf_nombre,p.pf_ap_paterno,p.pf_ap_materno) end as proveedor 
        from provider p
        join provider.datos_validados dv using(provider_id)
        where dv.status_op = 'VALIDADO'")->queryAll(),'id','proveedor');
        return $this->renderAjax('create-visit-op', [
            'model' => $model,
            'prov' => $prov
        ]);
    }


    /**
     * Updates an existing Visit model.
     * If update is successful, the browser will be redirected to the 'view' page.
     * @param integer $id
     * @return mixed
     * @throws NotFoundHttpException if the model cannot be found
     */
    public function actionUpdate($id)
    {
        $model = $this->findModel($id);

        if ($model->load(Yii::$app->request->post()) && $model->save()) {
            return $this->redirect(['view', 'id' => $model->visit_id]);
        }

        return $this->render('update', [
            'model' => $model,
        ]);
    }

    /**
     * Deletes an existing Visit model.
     * If deletion is successful, the browser will be redirected to the 'index' page.
     * @param integer $id
     * @return mixed
     * @throws NotFoundHttpException if the model cannot be found
     */
    public function actionDelete($id)
    {
        $this->findModel($id)->delete();

        return $this->redirect(['index']);
    }

    /**
     * Finds the Visit model based on its primary key value.
     * If the model is not found, a 404 HTTP exception will be thrown.
     * @param integer $id
     * @return Visit the loaded model
     * @throws NotFoundHttpException if the model cannot be found
     */
    protected function findModel($id)
    {
        if (($model = Visit::findOne($id)) !== null) {
            return $model;
        }else{
            return new Visit();
        }
    }


    public function actionFormato($v = null, $u = null)
    {

        $data = [
            'button' => null
        ];
        if ($v && $u) {
            $data['model'] = VisitDetails::find()->where(['and', ['visit_id' => intval($v)], ['ubicacion_id' => intval($u)]])->one();
        } else {
            $data['model'] = new VisitDetails();
        }

        $html = $this->renderAjax("/visit-details/format", $data);

        $mpdf = new \mPDF('c');
        $mpdf->setFooter('Formato De Visita | ProveedoresNL [https://proveedores.nl.gob.mx] | Página {PAGENO} de {nb}');

        //var_dump($html); exit;
        $html = str_ireplace('checked', 'checked="checked"', $html); //marca los check/radio cuando estan marcados
        $mpdf->writeHtml($html);

        $mpdf->Output('PDF.pdf', "I");
    }


    public function actionLastvisit($id){

        $lastVisit = Visit::find()
            ->where(['and',['provider_id'=>intval($id)],['provider_type'=>'op'],['not',['created_by'=>null]]])
            ->orderBy('created_at desc')->one();
        if($lastVisit){
            return substr($lastVisit->created_at,0,10);
        }else{
            return '---';
        }

    }


    public function actionAddnote($id=null,$visit_id=null,$ubicacion_id = null){
        //if(!$visit_id || !$ubicacion_id)
        //    return $this->goHome();
        $model = (($model = NoteVisit::findOne($id)) !== null)?$model:new NoteVisit();
        $docOld = $model->note_file;
        if($model->load(Yii::$app->request->post())){
            if (!empty($model->note_file) && $model->note_file != $docOld) {
                $new_name = str_replace('archivos_tmp/', $model->path . '/', $model->note_file);
                $this->copyFile($model->note_file, $new_name);
                $model->note_file = $new_name;
            }
            if($id){//si es actualización
                $model->last_modification_at = date('Y-m-d H:i:s');
                $model->last_modification_by = Yii::$app->user->id;
            }else{
                $model->created_by = Yii::$app->user->id;
                $model->visit_id = $visit_id;
                $model->ubicacion_id = $ubicacion_id;
            }
            if(!$model->save())
                return json_encode($model->errors);
            return $this->redirect('/visit/index');
        }
        return $this->renderAjax('addnote',['model'=>$model]);
    }


    public function actionViewnote($visit_id=null){
        if(!$visit_id)
            return $this->goHome();
        $data = [
            'dataProvider' => new ActiveDataProvider([
                'query'=> NoteVisit::find()->select('note_visit,*,u.tipo as tipo_ubicacion')
                    ->leftJoin('provider.ubicacion u','u.ubicacion_id = note_visit.ubicacion_id')
                    ->where(['and',['note_visit.active'=>true],['note_visit.visit_id'=>$visit_id]])
            ]),
        ];
        return $this->renderAjax('viewnote',$data);
    }


    public function actionRemovenote($id=null){
        if(!$id)
            return $this->goHome();
        $model = NoteVisit::findOne($id);
        $model->last_modification_at = date('Y-m-d H:i:s');
        $model->last_modification_by = Yii::$app->user->id;
        $model->active = 0;
        $model->save();
        return $this->redirect('/visit/index');
    }
}
