<?php

namespace app\controllers;

use app\custom\Module;
use app\helpers\GeneralController;
use app\models\Asignacion;
use app\models\ProviderQuery;
use app\models\ProviderSearch;
use yii\data\ActiveDataProvider;
use yii\data\SqlDataProvider;
use yii\filters\VerbFilter;
use app\models\Provider;
use app\helpers\BysController;
use yii\web\Controller;

/**
 * IntervencionBancariaController implements the CRUD actions for IntervencionBancaria model.
 */
class ValidacionController extends GeneralController
{
    /**
     * @inheritdoc
     */
    public function behaviors()
    {
        return [
            'verbs' => [
                'class' => VerbFilter::className(),
                'actions' => [
                    'delete' => ['POST', 'GET'],
                ],
            ],
        ];
    }


    public function actionIndex()
    {

        $params = \Yii::$app->request->queryParams;
        $data = ProviderQuery::getAllDataValidation($params);

        return $this->render('index', [
            'dataProvider' => $data
        ]);

    }

    public function actionView($id=0){

        //TODO quitar fecha cuando se arregle el detalle de asignacion, y las reasignaciones se pasen a historicos

        $data = new SqlDataProvider([
            'sql' => " select a.*, concat_ws(' ',ur.nombre, ur.primer_apellido, ur.segundo_apellido) as revisor,   
                        concat_ws(' ',uv.nombre, uv.primer_apellido, uv.segundo_apellido) as validador  
                        from provider.asignacion a 
                        inner join usuarios ur on ur.user_id = a.id_validador 
                        left join usuarios uv on uv.user_id = a.last_updated_by
                        where id_proveedor = ".intval($id)." and date(a.created_at) >= '2022-08-09' 
                        order by a.created_at desc
                         ",
            'sort' =>false,
        ]);
        return $this->renderAjax('view', [
            'data' => $data
        ]);

    }

    public function modelName()
    {
        return Asignacion::className();
    }

    public function searchName()
    {
        return Asignacion::className();
    }
}
