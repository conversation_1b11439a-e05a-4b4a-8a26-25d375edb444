<?php

namespace app\controllers;

use app\helpers\GeneralController;
use Yii;
use app\models\NonWorkingDays;
use app\models\NonWorkingDaysSearch;
use yii\web\Controller;
use yii\web\NotFoundHttpException;
use yii\filters\VerbFilter;

/**
 * NonWorkingDaysController implements the CRUD actions for NonWorkingDays model.
 */
class NonWorkingDaysController extends GeneralController
{
    /**
     * {@inheritdoc}
     */
    public function behaviors()
    {
        return [
            'verbs' => [
                'class' => VerbFilter::className(),
                'actions' => [
                    'delete' => ['POST','GET'],
                ],
            ],
        ];
    }

    public function actionIndex(){
        $searchModel = new NonWorkingDaysSearch();
        $dataProvider = $searchModel->search(Yii::$app->request->queryParams);
        return $this->render('index', [
            'searchModel' => $searchModel,
            'dataProvider' => $dataProvider,
        ]);
    }


    public function actionUpdate($id=null){
        $model = $this->findModel($id);
        if ($model->load(Yii::$app->request->post())) {
            $model->user_type = 'ADMIN';
            if($model->save()){
                return $this->redirect(['index']);
            }
            return $this->redirect(['index']);
        }

        return $this->renderAjax('update', [
            'model' => $model,
        ]);
    }


    public function actionDelete($id){
        $this->findModel($id)->delete();
        return $this->redirect(['index']);
    }


    protected function findModel($id){
        return (($model = NonWorkingDays::findOne($id))!==null)?$model:new NonWorkingDays();
    }
}
