<?php

namespace app\controllers;


use app\helpers\BysController;
use app\helpers\GeneralController;
use app\models\DireccionNlSearch;
use app\models\ExperienciaSearch;
use app\models\Provider;
use app\models\ProviderSearch;
use app\models\Usuarios;
use Yii;
use yii\filters\AccessControl;
use yii\web\Controller;
use yii\filters\VerbFilter;
use app\models\LoginForm;
use app\models\ContactForm;
use yii\helpers\ArrayHelper;

class TecnicosController extends GeneralController
{
    public function behaviors()
    {
        return [
            'access' => [
                'class' => AccessControl::className(),
                'rules' => [
                    [
                        'allow' => true,
                        'roles' => ['@'],
                    ],
                ],
            ],
            'verbs' => [
                'class' => VerbFilter::className(),
                'actions' => [
                    'logout' => ['post'],
                ],
            ],
        ];
    }


    public function actionIndexValidador()
    {

        /* Carga los parametros de busqueda generales que aplican en todos los tabs */
        $simpleParams = isset(Yii::$app->request->queryParams) ? Yii::$app->request->queryParams : [];
        $providerParams = GeneralController::addPrefixKeyArray($simpleParams, 'provider');

        $m = new ProviderSearch();
        if(Yii::$app->user->can(Usuarios::ROLE_VALIDADOR_CONTRATISTAS)){
            $searchModel = new DireccionNlSearch();
            $modelos = (object)[
                'ubicacion' => $m->search([$m->formName() => $simpleParams],'validUbicacion'),
                'ubicacionNl' => $searchModel->search([ $searchModel->formName() => $providerParams ],'mainFilterUbicacionNl'),
                'referencias' => $m->search([$m->formName() => $simpleParams],'validClientes'),
                'fotografia' => $m->search([$m->formName() => $simpleParams],'validFotografia'),
                'experiencia' => $m->search([$m->formName() => $simpleParams],'validExperiencia'),
                'personal' => $m->search([$m->formName() => $simpleParams],'validPersonal'),
                'maquinaria' => $m->search([$m->formName() => $simpleParams],'validMaquinaria'),
                'organigrama' => $m->search([$m->formName() => $simpleParams],'validOrganigrama'),
                'search' => $m,

            ];
        }
        else{                
            $searchModel = new DireccionNlSearch();
            $modelos = (Object)[
                'ubicacion' => $m->search([$m->formName() => $simpleParams],'validUbicacion'),
                'ubicacionNl' => $searchModel->search([ $searchModel->formName() => $providerParams ],'mainFilterUbicacionNl'),
                'certificacion' => $m->search([$m->formName() => $simpleParams],'validCertificacion'),
                'clientes' => $m->search([$m->formName() => $simpleParams],'validClientes'),
                'fotografia' => $m->search([$m->formName() => $simpleParams],'validFotografia'),
                'search' => $m
            ];
        }


        $tipo = self::providerType();

        return $this->render('index-validador',[
            'modelos' => $modelos,
            'tipo' => $tipo,
            'searchModel' => $searchModel,
            'params' => $simpleParams
        ]);
    }

    public function actionViewAnalisis($id = null){

        if($id!=null){

            $nombre_empresa = Provider::find()->select('name_comercial')->where(['provider_id' => $id])->one()['name_comercial'];

            $dataTipoObra = self::calEspecialidadObra($id,'obra');

            $dataTipoEspecialidad = self::calEspecialidadObra($id,'Especialidad');

            $analisisObr = [];
            $analisisEsp = [];
            if(isset($dataTipoObra['analisis']) && !empty($dataTipoObra['analisis'])){
                $analisisObr =  self::sort_by_orden($dataTipoObra['analisis']);
            }

            if(isset($dataTipoEspecialidad['analisis']) && !empty($dataTipoEspecialidad['analisis'])){
                $analisisEsp = self::sort_by_orden($dataTipoEspecialidad['analisis']);
            }
            return $this->renderAjax('view-analisis',[
                'array_final_obra' => $dataTipoObra['arreglo'],
                'numero_obras' => $dataTipoObra['numero'],
                'total_obras' => $dataTipoObra['total'],
                'sumObraP1' => $dataTipoObra['sumOEP1'],
                'sumObraP2' => $dataTipoObra['sumOEP2'],
                'nombre_empresa' => $nombre_empresa,
                'array_final_obra_especialidad' => $dataTipoEspecialidad['arreglo'],
                'numero_obras_especialidad' => $dataTipoEspecialidad['numero'],
                'total_obras_especialidad' => $dataTipoEspecialidad['total'],
                'sumObraP1_especialidad' => $dataTipoEspecialidad['sumOEP1'],
                'sumObraP2_especialidad' => $dataTipoEspecialidad['sumOEP2'],
                'analisis_obra' => $analisisObr,
                'analisis_especialidad' => $analisisEsp,
                'sumAnalisisTo' => $dataTipoObra['sumAnalisis'],
                'sumAnalisisTe' => $dataTipoEspecialidad['sumAnalisis'],

            ]);
        }

    }
}
