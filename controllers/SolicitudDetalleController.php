<?php

namespace app\controllers;

use app\models\Requisicion;
use app\models\SolicitudCotizacion;
use app\models\SolicitudStatus;
use Yii;
use app\models\SolicitudDetalle;
use app\modelsSolicitudDetalleSearch;
use yii\web\Controller;
use yii\web\NotFoundHttpException;
use yii\filters\VerbFilter;

/**
 * SolicitudDetalleController implements the CRUD actions for SolicitudDetalle model.
 */
class SolicitudDetalleController extends Controller
{
    /**
     * @inheritdoc
     */
    public function behaviors()
    {
        return [
            'verbs' => [
                'class' => VerbFilter::className(),
                'actions' => [
                    'delete' => ['POST'],
                ],
            ],
        ];
    }

    /**
     * Lists all SolicitudDetalle models.
     * @return mixed
     */
    public function actionIndex()
    {
        $searchModel = new modelsSolicitudDetalleSearch();
        $dataProvider = $searchModel->search(Yii::$app->request->queryParams);

        return $this->render('index', [
            'searchModel' => $searchModel,
            'dataProvider' => $dataProvider,
        ]);
    }

    /**
     * Displays a single SolicitudDetalle model.
     * @param string $id
     * @return mixed
     */
    public function actionView($id)
    {
        return $this->render('view', [
            'model' => $this->findModel($id),
        ]);
    }

    /**
     * Creates a new SolicitudDetalle model.
     * If creation is successful, the browser will be redirected to the 'view' page.
     * @return mixed
     */
    public function actionCreate()
    {
        $model = new SolicitudDetalle();
        if ($model->load(Yii::$app->request->post()) && $model->save()) {
            return $this->redirect(['view', 'id' => $model->solicitud_detalle_id]);
        } else {
            return $this->render('update', [
                'model' => $model,
            ]);
        }
    }

    /**
     * Updates an existing SolicitudDetalle model.
     * If update is successful, the browser will be redirected to the 'view' page.
     * @param string $id
     * @return mixed
     */
    public function actionUpdate($id)
    {
        $trans = Yii::$app->db->beginTransaction();
        if (Yii::$app->request->post()) {
                $costo_unico = Yii::$app->request->post()['costo_unitario'];
                $importe = Yii::$app->request->post()['importe'];
                //$iva = Yii::$app->request->post()['iva'];
                $total = Yii::$app->request->post()['total'];
                $iva_total = Yii::$app->request->post()['iva_total'];
                $subtotal = Yii::$app->request->post()['subtotal'];
                $cotizacion_file = Yii::$app->request->post()['cotizacion_file'];
                $condi_pago = Yii::$app->request->post()['condiciones_pago_provider_id'];
                $vigencia_coti = Yii::$app->request->post()['vigencia_cotizacion_provider'];
                $observacion_provider = Yii::$app->request->post()['observacion_provider'];
                $exito = true;
                foreach($costo_unico as $key=>$data){
                    $detalle = SolicitudDetalle::findOne($key);
                    $detalle->importe = $importe[$key];
                    $detalle->costo_unitario = $data;
                    if(!empty($detalle->getDirtyAttributes())){
                        $exito = $exito && $detalle->Update();
                    }
                }
                if($exito){

                    $requisicion_id = SolicitudCotizacion::find()->select('requisicion_id')->where(['solicitud_id' => $id])->one()->requisicion_id;
                    $factual = date("Y-m-d H:i:s");
                    $solicitud = SolicitudCotizacion::findOne($id);
                    //$solicitud->iva = $iva;
                    $solicitud->total = $total;
                    $solicitud->subtotal = $subtotal;
                    $solicitud->iva_total = $iva_total;
                    $solicitud->cotizacion_file = $cotizacion_file;
                    $solicitud->condiciones_pago_provider_id = $condi_pago;
                    $solicitud->vigencia_cotizacion_provider = $vigencia_coti;
                    $solicitud->status = SolicitudCotizacion::VALIDADA;
                    $solicitud->observacion_provider = $observacion_provider;
                    $solicitud->provider_cotizacion_date = $factual;
                    $solicitud->Update();
                    $requisicion = Requisicion::findOne($requisicion_id);
                    $requisicion->status =Requisicion::COTIZADA;
                    $requisicion->Update();

                    $sol_status = SolicitudStatus::find()->select('status_solicitud_id')->where(['and',['solicitud_id'=>$id],['status' => 'PENDIENTE']])->one();
                    $soli_status = $sol_status['status_solicitud_id'];
                    if(isset($soli_status) && !empty($soli_status)){
                        $solicitud_status = SolicitudStatus::findOne($soli_status);
                        $solicitud_status->status =SolicitudStatus::STATUS_TERMINADO;
                        $solicitud_status->Update();
                    }
                    $trans->commit();
                    return $this->redirect(['../solicitud-cotizacion']);
                }
                $trans->rollBack();
        }else{
            return $this->redirect(['../solicitud-cotizacion']);
        }
    }

    /**
     * Deletes an existing SolicitudDetalle model.
     * If deletion is successful, the browser will be redirected to the 'index' page.
     * @param string $id
     * @return mixed
     */
    public function actionDelete($id)
    {
        $this->findModel($id)->delete();

        return $this->redirect(['index']);
    }

    /**
     * Finds the SolicitudDetalle model based on its primary key value.
     * If the model is not found, a 404 HTTP exception will be thrown.
     * @param string $id
     * @return SolicitudDetalle the loaded model
     * @throws NotFoundHttpException if the model cannot be found
     */
    protected function findModel($id)
    {
        if (($model = SolicitudDetalle::findOne($id)) !== null) {
            return $model;
        } else {
            throw new NotFoundHttpException('The requested page does not exist.');
        }
    }
}
