<?php

namespace app\controllers;

use app\helpers\GeneralController;
use app\models\Bancos;
use app\models\DatosValidados;
use app\models\Historico;
use app\models\Module;
use app\models\ProviderSearch;
use moonland\phpexcel\Excel;
use Yii;
use app\models\IntervencionBancaria;
use app\models\IntervencionBancariaSearch;
use yii\data\SqlDataProvider;
use yii\web\Controller;
use yii\web\NotFoundHttpException;
use yii\filters\VerbFilter;
use yii\helpers\ArrayHelper;
use yii\web\UploadedFile;
use app\models\Provider;
use app\models\Usuarios;
use app\models\Status;
use app\models\Porcentaje;
use app\helpers\BysController;
use app\models\BancosSearch;
use yii\db\Query;

use yii\web\Response;
use kartik\form\ActiveForm;



if (Yii::$app->user->can(Usuarios::ROLE_PROVIDER)) {
    Yii::$app->params['provider_id'] = Yii::$app->user->identity->getProvider();
}

/**
 * IntervencionBancariaController implements the CRUD actions for IntervencionBancaria model.
 */
class BancoController extends GeneralController
{
    /**
     * @inheritdoc
     */
    public function behaviors()
    {
        return [
            'verbs' => [
                'class' => VerbFilter::className(),
                'actions' => [
                    'delete' => ['POST', 'GET'],
                ],
            ],
        ];
    }


    public function actionIndex($fi = null, $ff = null)
    {

        if (Yii::$app->user->can(Usuarios::ROLE_METODO_PAGO)) {

            $params = Yii::$app->request->queryParams;
            $dataSiet = $this->dataSiet($fi, $ff, $params);


            return $this->render('index', [
                'data' => $dataSiet,
                'fi' => $fi,
                'ff' => $ff
            ]);

        } else {
            return $this->goHome();
        }
    }


    public function actionHistorical()
    {

        if (Yii::$app->user->can(Usuarios::ROLE_METODO_PAGO)) {
            $searchModel = new ProviderSearch();
            $dataProvider = $searchModel->search(Yii::$app->request->queryParams, 'historicoBanco');
            return $this->render('historical', [
                'searchModel' => $searchModel,
                'dataProvider' => $dataProvider
            ]);
        } else {
            return $this->goHome();
        }
    }

    public function dataSiet($fi, $ff, $params)
    {

        if ($fi == null || $ff == null) {
            $fi = '2020-01-01';
            $ff = date('Y-m-d');
        }

        $sqlSearchNp = '';
        $sqlSearchR = '';
        $sqlSearchP = '';
        $sqlSearchT = '';
        $sqlSearchF = '';
        $bind = [];
        //r,rs,tp,f
        if (isset($params['np']) && !empty($params['np'])) {

            $np = $params['np'];

            $sqlSearchNp = ' and "Clave_ProveedorSire" = :np';

            $bind[':np'] = "$np";
        }

        if (isset($params['r']) && !empty($params['r'])) {

            $r = $params['r'];

            $sqlSearchR = " and lower(unaccent(rfc)) ilike '%' || lower(unaccent(:r)) || '%' ";

            $bind[':r'] = "%$r%";
        }

        if (isset($params['rs']) && !empty($params['rs'])) {

            $r = $params['rs'];

            $sqlSearchP = " and lower(unaccent(proveedor)) ilike '%' || lower(unaccent(:rs)) || '%' ";

            $bind[':rs'] = "%$r%";
        }

        $bind = !empty($bind) ? $bind : '';

        $totalCount = Yii::$app->db->createCommand("
        with ct1 as(
                                        with ct as
                                (
                                select distinct 
                                    provider_id, created_at,
                                    row_number() over (partition by provider_id order by created_at desc) rn
                                from
                                    historico_certificados  where provider_type = 'bys' and tipo='CERTIFICADO' and created_at between '$fi' and '$ff' order by provider_id
                                )
                                select
                                    p.\"Clave_ProveedorSire\",CASE WHEN (p.name_razon_social is not null and p.name_razon_social != '') then p.name_razon_social else concat_ws(' ',p.pf_nombre,p.pf_ap_paterno,p.pf_ap_materno) end as proveedor,p.rfc,
                                    ct.created_at as fecha_firmado,b.banco, ib.cuenta_clave,p.email,concat_ws('','https://proveedores.nl.gob.mx/',ib.estado_cuenta) as pdf, ib.estado_cuenta
                                from
                                    ct
                                  join provider p using(provider_id)
                                  join provider.intervencion_bancaria ib on ib.provider_id = p.provider_id
                                  join bancos b on b.id_banco = ib.banco
                                where
                                 rn = 1 and p.\"Clave_ProveedorSire\"!='')
                                  select count(1) from ct1 where fecha_firmado between '$fi' and '$ff' $sqlSearchNp $sqlSearchR $sqlSearchP $sqlSearchT $sqlSearchF ", $bind)->queryScalar();


        $sql = [
            'sql' =>
                "
                with ct1 as(
                with ct as
                            (
                            select distinct 
                                provider_id, created_at,
                                row_number() over (partition by provider_id order by created_at desc) rn
                            from
                                historico_certificados  where provider_type = 'bys' and tipo='CERTIFICADO' and created_at between '$fi' and '$ff' order by provider_id
                            )
                            select
                                p.\"Clave_ProveedorSire\",CASE WHEN (p.name_razon_social is not null and p.name_razon_social != '') then p.name_razon_social else concat_ws(' ',p.pf_nombre,p.pf_ap_paterno,p.pf_ap_materno) end as proveedor,p.rfc,
                              ct.created_at as fecha_firmado,b.banco, ib.cuenta_clave,p.email,concat_ws('','https://proveedores.nl.gob.mx/',ib.estado_cuenta) as pdf, ib.estado_cuenta
                            from
                                ct
                              join provider p using(provider_id)
                              join provider.intervencion_bancaria ib on ib.provider_id = p.provider_id
                              join bancos b on b.id_banco = ib.banco
                            
                            where
                             rn = 1 and p.\"Clave_ProveedorSire\"!='')
                             select * from ct1 where fecha_firmado between '$fi' and '$ff'  $sqlSearchNp  $sqlSearchR $sqlSearchP $sqlSearchT $sqlSearchF order by fecha_firmado",
            'totalCount' => $totalCount
            //'sort' =>false, to remove the table header sorting
        ];

        if ($bind) {
            $sql['params'] = $bind;
        }

        $dataProvider = new SqlDataProvider($sql);

        return $dataProvider;
    }


    public function actionDownloadData($fi = null, $ff = null)
    {

        date_default_timezone_set('America/Monterrey');

        if ($fi && $ff) {
            $interval = " created_at::DATE between '$fi' and  '$ff'";
        } else {
            $fi = '2020-01-01';
            $ff = date('Y-m-d');
            $interval = " created_at::DATE between '$fi' and  '$ff'";
        }


        $data_all_final = Yii::$app->db->createCommand("
              with ct as
                            (
                            select distinct 
                                provider_id, created_at,
                                row_number() over (partition by provider_id order by created_at desc) rn
                            from
                                historico_certificados  where provider_type = 'bys' and tipo='CERTIFICADO' and $interval order by provider_id
                            )
                            select
                                p.\"Clave_ProveedorSire\",CASE WHEN (p.name_razon_social is not null and p.name_razon_social != '') then p.name_razon_social else concat_ws(' ',p.pf_nombre,p.pf_ap_paterno,p.pf_ap_materno) end as proveedor,p.rfc,
                              ct.created_at as fecha_firmado,b.banco, ib.cuenta_clave,p.email,concat_ws('','https://proveedores.nl.gob.mx/',ib.estado_cuenta) as pdf, ib.estado_cuenta
                            from
                                ct
                              join provider p using(provider_id)
                              join provider.intervencion_bancaria ib on ib.provider_id = p.provider_id
                              join bancos b on b.id_banco = ib.banco
                            
                            where
                             rn = 1 and p.\"Clave_ProveedorSire\"!='' ")->queryAll();

        if ($data_all_final) {
            $columnsData = array_keys($data_all_final[0]);

            $headersData = array_combine($columnsData, $columnsData);

            foreach ($headersData as $index => $header) {
                $headersData[$index] = str_replace("_", " ", strtoupper($header));
            }

            $now = date("Y-m-d");
            $fi_name = 'cuentas_bancarias' . "_" . $now . ".xlsx";
            Excel::widget([
                'models' => $data_all_final,
                'mode' => 'export',
                'fileName' => $fi_name,
                'columns' => $columnsData,
                'headers' => $headersData,
            ]);
            exit();
            return true;
        } else {
            return $this->redirect(['index']);
        }


    }

    public function actionView()
    {
        $id = Yii::$app->user->identity->providerid;
        /* if (($id == null || !Provider::verifyProvider($id) || Yii::$app->user->can(Usuarios::ROLE_PROVIDER)) && (!Provider::verifyEtapa2($id))) {
            return $this->render('/site/error', ['name' => 'Lo sentimos...', 'message' => 'Acceso denegado']);
        } */

        $model = $this->findModel($id);
        $tipo_provider = Provider::find()->select('tipo_provider')->where(['provider_id' => $id])->one()->tipo_provider;

        $rechazo = Status::find()->where(['and', ['modelo' => 'intervencion_bancaria'], ['status_' . $tipo_provider => Status::STATUS_PENDIENTE], ['register_id' => $id]])->asArray()->one();
        if (empty($rechazo)) {
            $rechazo = Status::find()->getStatusRechazarCotejo($id);
        }
        $modelName = $this->modelName();
        $count_files = GeneralController::countFilesModel($id, $model->id, 'estado_cuenta', $modelName);

        return $this->render('view', [
            'model' => $model,
            'rechazo' => $rechazo,
            'count_files' => $count_files,
            'modelName' => $modelName,

        ]);
    }

    public function actionViewbys()
    {
        $id = Yii::$app->user->identity->providerid;
        /* if (($id == null || !Provider::verifyProvider($id) || Yii::$app->user->can(Usuarios::ROLE_PROVIDER)) && (!Provider::verifyEtapa2($id))) {
            return $this->render('/site/error', ['name' => 'Lo sentimos...', 'message' => 'Acceso denegado']);
        } */

        $provider = Provider::find()->where(['user_id' => Yii::$app->user->getId()])->one();

        //Rechazos
        $modelStatus = Module::getstatusbymodule('bys_bancos',$provider->provider_id);
        $rechazo = $modelStatus->status_bys == Status::STATUS_RECHAZADO  || $modelStatus->status_bys == Status::STATUS_ENEDICION  ?
            Status::find()->getRechazo( 'bys_bancos', $provider->provider_id) : null;

        $model = $this->findModel($id);

        return $this->render('viewbys', [
            'model' => $model,
            'modelStatus'=>$modelStatus,
            'rechazo'=>$rechazo,
            'provider'=>$provider
        ]);
    }


    public function actionViewValidar($id)
    {
        $queryParams = Yii::$app->request->queryParams;
        $id = isset($queryParams['id']) ? intval( $queryParams['id'] ) : null;
        $opcionesArray = isset($queryParams['opciones']) ? $queryParams['opciones'] : null;
        $opcionesSerial = isset($opcionesArray) ? serialize($opcionesArray) : null;
        $opcionesBase64 = isset($opcionesSerial) ? base64_encode($opcionesSerial) : null;
        if ($id == null || !Provider::verifyProvider($id) ||
            Yii::$app->user->can(Usuarios::ROLE_PROVIDER) && (!Provider::verifyEtapa2($id))) {
            echo $this->render('/site/error', ['name' => 'Lo sentimos...', 'message' => 'Acceso denegado']);

            return false;
        }
        $porcentaje = [];
        $modelos = [];
        $intervencion_ban = $this->findModel($id);

        if (Yii::$app->user->can(Usuarios::ROLE_PROVIDER)) {
            if (Yii::$app->request->get('tipo')) {
                $tipo_provider = Yii::$app->request->get('tipo');
            } else {
                $id_provider = Yii::$app->user->identity->getProvider();
                $tipo_provider = Provider::find()->select('tipo_provider')->where(['provider_id' => $id_provider])->one()->tipo_provider;
            }
            $porcentaje = $this->findModelPorcentaje($id);
        }
        $rechazo = new Status();
        $rechazo->scenario = Status::SCENARIO_CREATE;
        $model_validado = new DatosValidados();
        $model_validado->scenario = DatosValidados::SCENARIO_CREATE;
        if (isset($intervencion_ban->provider_id) && !empty($intervencion_ban->provider_id)) {
            $modelos[$intervencion_ban->formName()] = $intervencion_ban->attributes;
        }
        $modelos = base64_encode(json_encode($modelos));

        $tipo_provider = Provider::find()->select('tipo_provider')->where(['provider_id' => $id])->one()['tipo_provider'];

        $msjRechazo = Status::find()->where(['and', ['modelo' => 'intervencion_bancaria'], ['status_' . $tipo_provider => Status::STATUS_TERMINADO_PRO], ['register_id' => $id]])->one();
        $movements = [];
        if ('status_' . $tipo_provider == 'status_bys') {
            $movements = $this->countAllMovements($id, 'intervencion_bancaria');
        }

        return $this->renderAjax('view-validar', [
            'model' => $intervencion_ban,
            'model_porcentaje' => $porcentaje,
            'model_validado' => $model_validado,
            'rechazo' => $rechazo,
            'modelos' => $modelos,
            'msjRechazo' => $msjRechazo,
            'namePro' => $this->getNameProvider($id),
            'movements' => $movements,
            'id' => $id,
            'opciones' => $opcionesBase64,
            'moduleName' => 'intervencion_bancaria',
            'providerType' => $tipo_provider

        ]);
    }

    public function actionUpdate()
    {
        if (Yii::$app->request->isAjax || Yii::$app->request->post()) {
            if (Yii::$app->user->can(Usuarios::ROLE_PROVIDER)) {
                $id = Yii::$app->params['provider_id'];
            }
            /*if ($id == null || !Provider::verifyProvider($id) || !self::verifyUpdate($id)) {
                return $this->render('/site/error', ['name' => 'Lo sentimos...', 'message' => 'Acceso denegado']);
            }*/

            $modelStatus = Module::getstatusbymodule('bys_bancos',$id);
            $rechazo = $modelStatus->status_bys == Status::STATUS_RECHAZADO  || $modelStatus->status_bys == Status::STATUS_ENEDICION  ?
                Status::find()->getRechazo( 'bys_bancos', $id) : null;
            
            $provider = Provider::findOne($id);

            $tipo_provider = $provider->tipo_provider; //Provider::find()->select('tipo_provider')->where(['provider_id' => $id])->one()['tipo_provider'];
            $status_provider = 'status_' . $tipo_provider;
            $model = $this->findModel($id);
            $modelOld = $model->attributes;
            $model_porcentaje = $this->findModelPorcentaje($id);
            $urlEstadoCuentaOld = $model->estado_cuenta;
            if ( Yii::$app->request->isAjax && $model->load(Yii::$app->request->post()) ) {
                Yii::$app->response->format = Response::FORMAT_JSON;
                $result = [];
               /*  if( empty($model->nombre_titular_cuenta) ){ $result['intervencionbancaria-nombre_titular_cuenta'] = ['El nombre del titular es un campo requerido']; } */
               if(strlen($model->cuenta_clave) != 18){ $result['intervencionbancaria-cuenta_clave'] = ['Clabe debería contener 18 dígitos']; }
                /* else{
                    $nombre_responsable = $provider->getFullName();
                } */
                return array_merge($result,ActiveForm::validate($model));
            } else if ($model->load(Yii::$app->request->post())) {
                $verify = Porcentaje::find()->select(['register_id'])->where(['and', ['register_id' => $id], ['modelo' => 'metodo_pago']])->one();
                if ($verify) {
                    $porcentajeOld = $model_porcentaje->porcentaje;

                    if (isset($_POST['porcentaje_total']) && $_POST['porcentaje_total'] != '') {
                        $model_porcentaje->porcentaje = round($_POST['porcentaje_total']);
                    } else {
                        $model_porcentaje->porcentaje = $porcentajeOld;
                    }

                    if (($requi_status = Status::find()->where(['and', ['register_id' => $id], [$status_provider => Status::STATUS_PENDIENTE], ['modelo' => 'intervencion_bancaria']])->one()) !== null) {
                        $requi_status->$status_provider = Status::STATUS_TERMINADO_PRO;
                        $requi_status->save();
                    }
                    \Yii::$app->db->createCommand("update provider.porcentaje set porcentaje = $model_porcentaje->porcentaje where modelo='metodo_pago' and register_id =:id")
                        ->bindValue(':id', $id)
                        ->execute();
                } else {
                    $porcen = new Porcentaje();
                    $porcen->modelo = 'metodo_pago';
                    $porcen->register_id = $id;
                    if (isset($_POST['porcentaje_total']) && $_POST['porcentaje_total'] != '') {
                        $porcen->porcentaje = round($_POST['porcentaje_total']);
                    } else {
                        $porcen->porcentaje = 0;
                    }
                    $porcen->save();
                }

                $this->makeDir($model->path);

                if (!empty($model->estado_cuenta) && $model->estado_cuenta != $urlEstadoCuentaOld) {
                    $new_name = str_replace('archivos_tmp/', $model->path . '/', $model->estado_cuenta);
                    $this->copyFile($model->estado_cuenta, $new_name);
                    $model->estado_cuenta = $new_name;
                }
                $model->$status_provider = Status::STATUS_ENEDICION;
                if (!$model->save(false)) {
                    return $this->goHome();
                }

                if ($status_provider == 'status_bys') {

                    $countValCert = $this->verifyProviderCert($model->provider_id, 'bys');
                    $countValRec = self::verifyValRec('intervencion_bancaria', $model->provider_id);

                    if ($countValCert > 0 || $countValRec > 0) {
                        $trueFalse = $countValCert > 0 ? true : false;
                        $this->compareModels($modelOld, $model->attributes, $model->provider_id, 'intervencion_bancaria', $model->provider_id, $trueFalse);
                    }

                    self::eliminarCita();
                }
                if($tipo_provider == 'bys'){
                    self::updateModulesBys($model->provider_id,'bys_bancos');
                    return $this->redirect('viewbys');
                }

                return $this->redirect(['view', 'id' => base64_encode($model->provider_id)]);
            }
            return $this->renderAjax('update', [
                'model' => $model,
                'tipo_pro' => $status_provider,
                'rechazo' => $rechazo,
                'provider' => $provider
            ]);
        }

        return $this->goHome();

    }


    public function actionDelete($id)
    {
        $this->findModel($id)->delete();

        return $this->redirect(['index']);
    }


    protected function findModel($id)
    {
        /*if (($model = IntervencionBancaria::findOne($id)) !== null) {
            return $model;
        }*/

        $model = IntervencionBancaria::find()->where(['provider_id' => $id])->one();
        if ($model == null || empty($model)) {
            $model = new IntervencionBancaria();
            $model->provider_id = $id;
            $model->save();
        }
        return $model;
        //throw new NotFoundHttpException('The requested page does not exist.');
    }

    public function actionBanco($id)
    {

        $banco = Bancos::find()->select(['id_banco', 'banco'])->where(['codigo' => $id])->asArray()->one();

        return json_encode($banco);
    }

    public function actionBancos(){
        $searchModel = new BancosSearch();
        $dataProvider = $searchModel->search(Yii::$app->request->queryParams);

        return $this->render('bancos', [
            'searchModel' => $searchModel,
            'dataProvider' => $dataProvider,
        ]);
    }

    public function actionFormBancos($id_banco = null){
        $isRequest = Yii::$app->request->isAjax;
        $postRequest = Yii::$app->request->post();
        $model = new Bancos();

        if( $id_banco !== null ){
            $model = Bancos::findOne(intval($id_banco));
        }

        if( $isRequest || $postRequest ){

            if ( $isRequest && $model->load($postRequest) ){
                Yii::$app->response->format = Response::FORMAT_JSON;
                return ActiveForm::validate($model);
            } else if ( $model->load($postRequest) ) {
                $model->save();
                return $this->redirect(['bancos']);
            }

        }

        return $this->renderAjax('form-bancos', ['model' => $model]);
    }

    public function actionEliminarBanco($id_banco = null){
        $banco = Bancos::findOne($id_banco);

        if($banco == null){
            return $this->redirect(['bancos']);
        }

        $banco->delete();

        return $this->redirect(['bancos']);
    }

    public function actionBancoId($id)
    {

        $banco = Bancos::find()->select(['id_banco', 'banco'])->where(['id_banco' => $id])->asArray()->one();

        return json_encode($banco);
    }

    public function findModelPorcentaje($id)
    {
        $model = Porcentaje::find()->where(['and', ['register_id' => $id], ['modelo' => 'metodo_pago']])->one();
        if ($model == null || empty($model)) {
            $model = new Porcentaje();
        }
        return $model;
    }

    public function actionTerminar()
    {


        $id = Yii::$app->user->identity->providerid;

        if ($id == null || !Provider::verifyProvider($id) || !Provider::verifyEtapa2($id) || !self::verifyTerminar($id)) {
            echo $this->render('/site/error', ['name' => 'Lo sentimos...', 'message' => 'Acceso denegado']);
            return false;
        }
        if (Yii::$app->request->get('tipo')) {
            $get_tipo = Yii::$app->request->get('tipo');
            $status = ($get_tipo == 'bys') ? 'status_bys' : 'status_op';
        } else {
            $provider = Provider::findOne($id);
            $status = ($provider->tipo_provider == 'bys') ? 'status_bys' : 'status_op';
        }

        $status_save = Status::STATUS_PORVALIDAR;

        $rfc = Yii::$app->db->createCommand();
        $rfc->update('provider.intervencion_bancaria', [$status => $status_save], 'provider_id =' . $id);
        $rfc->execute();

        $IdLastVal = $this->saveLastSendValidation($id, 'metodoPagoVal', 'intervencion_bancaria', $provider->tipo_provider);
        $this->saveLastSendValidationRelation($IdLastVal, $id, 'intervencion_bancaria', $id, $provider->tipo_provider);


        if ($provider->tipo_provider == 'bys') {
            $correos_validadores = self::getEmailValidador(3);
            self::sendEmail('/provider/correos/nuevo', null, $correos_validadores, 'Nuevo proveedor', ['modulo' => 'Método de pago', 'tipo_provider' => $provider->tipo_provider]);
        }else{
            GeneralController::sendModuloRevisionEmail($provider->email, 'Método Pago');
        }

        GeneralController::allModulesComplete($id);

        return $this->redirect('../../provider/dashboard');
    }

    //valida si puede entrar a action update
    public function verifyUpdate($provider)
    {
        if ($provider) {
            $tipo_pro = Provider::find()->select('tipo_provider')->where(['provider_id' => $provider])->one()['tipo_provider'];
            $tipo_pro = 'status_' . $tipo_pro;
            $status = IntervencionBancaria::find()->select($tipo_pro)->where(['provider_id' => $provider])->one()[$tipo_pro];
            if ($status == Status::STATUS_VALIDADO || $status == Status::STATUS_ENEDICION || $status == Status::STATUS_RECHAZADO) {
                return true;
            }
        }
        return false;
    }

    //valida si puede entrar a action terminar
    public function verifyTerminar($provider)
    {


        if ($provider) {
            $tipo_provider = 'status_' . Yii::$app->user->identity->tipo;

            $model = IntervencionBancaria::find()->where(['provider_id' => $provider])->one();
            if (($model->$tipo_provider == Status::STATUS_ENEDICION || $model->$tipo_provider == Status::STATUS_RECHAZADO || $model->$tipo_provider == Status::STATUS_VALIDADO) && $this->estaLleno($model)) {
                return true;
            }
        }
        return false;
    }

    public function modelName()
    {
        return IntervencionBancaria::className();
    }

    public function searchName()
    {
        return IntervencionBancariaSearch::className();
    }
}
