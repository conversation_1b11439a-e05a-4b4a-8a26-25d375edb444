<?php

namespace app\controllers;
use app\helpers\BysController;
use app\helpers\GeneralController;
use app\models\ActaConstitutiva;
use app\models\ActaConstitutivaSearch;
use app\models\AltaHacienda;
use app\models\AltaHaciendaSearch;
use app\models\CatActividades;
use app\models\CatEntidades;
use app\models\CatMunicipios;
use app\models\CatRamas;
use app\models\CatSectores;
use app\models\City;
use app\models\Curp;
use app\models\DatosValidados;
use app\models\EscrituraPublica;
use app\models\EscrituraPublicaSearch;
use app\models\Giro;
use app\models\IdOficial;
use app\models\ComprobanteDomicilio;
use app\models\ModelGiro;
use app\models\ModificacionActaSearch;
use app\models\PerfilSearch;
use app\models\Porcentaje;
use app\models\RegistroImss;
use app\models\RegistroPublicoPropiedad;
use app\models\RepresentanteLegal;
use app\models\Model;
use app\models\Provider;
use app\models\ProviderSearch;
use app\models\RepresentanteLegalSearch;
use app\models\State;
use app\models\Status;
use app\models\RegistroActas;
use app\models\Historico;
use Yii;
use app\models\Rfc;
use app\models\RfcSearch;
use app\models\Usuarios;
use yii\db\Query;
use yii\helpers\ArrayHelper;
use yii\web\Controller;
use yii\web\NotFoundHttpException;
use yii\filters\VerbFilter;
use yii\web\UploadedFile;
use app\models\RelacionAccionistas;
use app\models\RelacionAccionistasSearch;
if(Yii::$app->user->can(Usuarios::ROLE_PROVIDER)){
    Yii::$app->params['provider_id'] = Yii::$app->user->identity->getProvider();
}

/**
 * LegalesController implements the CRUD actions for Rfc model.
 */
class LegalesController extends GeneralController
{
    /**
     * @inheritdoc
     */
    public function behaviors()
    {
        return [
            'verbs' => [
                'class' => VerbFilter::className(),
                'actions' => [
                    'delete' => ['POST'],
                ],
            ],
        ];
    }


    public function actionIndexValidador()
    {
        $searchModel = new RfcSearch();
        $searchPerfil = new PerfilSearch();
        $searchPropiedad = new EscrituraPublicaSearch();
        $searchHacienda = new AltaHaciendaSearch();
        $searchProvider = new ProviderSearch();
        $searchModificacion = new ModificacionActaSearch();
        $searchRepresentante = new RepresentanteLegalSearch();

        /* Carga los parametros de busqueda generales que aplican en todos los tabs */
        $simpleParams = isset(Yii::$app->request->queryParams) ? Yii::$app->request->queryParams : [];
        $providerParams = GeneralController::addPrefixKeyArray($simpleParams, 'provider');

        $datosPerfil = $searchPerfil->search([$searchPerfil->formName() => $providerParams],'mainPerfil');
        $datosHacienda = $searchHacienda->search([$searchHacienda->formName() => $providerParams],'mainHacienda');
        $dataProvider = $searchModel->search([$searchModel->formName() => $providerParams],'mainFilterLegales');

        $datosModificacionActa = $searchProvider->search([$searchProvider->formName() => $simpleParams],'validModificacionActa');
        $datosRelacionAccionistas = $searchProvider->search([$searchProvider->formName() => $simpleParams],'validRelacionAccionistas');
        $datosRepresentante = $searchProvider->search([$searchProvider->formName() => $simpleParams],'validRepresentante');

        $tipo = self::providerType();

        return $this->render('index-validador', [
            'searchModel' => $searchModel,
            'searchPropiedad' => $searchPropiedad,
            'searchHacienda' => $searchHacienda,
            'dataProvider' => $dataProvider,
            'datosHacienda' => $datosHacienda,
            'datosRelacionAccionistas' => $datosRelacionAccionistas,
            'datosRepresentante' => $datosRepresentante,
            'searchProvider'=> $searchProvider,
            'tipo' => $tipo,
            'datosModificacionActa' => $datosModificacionActa,
            'searchModificacion' => $searchModificacion,
            'searchRepresentante' => $searchRepresentante,
            'datosPerfil' => $datosPerfil,
            'searchPerfil' => $searchPerfil,
            'params' => $simpleParams
        ]);
    }

    public function actionConfirm(){

        $role = Yii::$app->user->identity->role;
        $status_global = $role == 'VALIDADOR PROVEEDORES'?'status_bys':($role == 'VALIDADOR CONTRATISTAS'?'status_op':'');

        $id = Yii::$app->request->post()['provider_id'];
        $rfc = Yii::$app->db->createCommand();
        $rfc->update('provider.rfc',[$status_global => Status::STATUS_VALIDADO],'provider_id ='.$id);
        $rfc->execute();

        $status_id = Status::find()->select('status_id')
            ->where(['and',['register_id' => $id],[$status_global => Status::STATUS_PENDIENTE]])
            ->andWhere(['modelo'=>'RFC'])->one();
        if(!empty($status_id['status_id'])){
            $requi_status = Status::findOne($status_id);
            $requi_status->$status_global = Status::STATUS_TERMINADO;
            $requi_status->update();

        }

        Yii::$app->session->setFlash('success', 'Los datos se validaron correctamente');
        return Yii::$app->getResponse()->redirect(['datosLegales/rfc/index-validador']);

    }

    public function actionViewValidar($id=null){
        $id = intval($id);

        if($id == null || strlen($id)>15 || !Provider::verifyProviderExistence($id)){
            $this->msgError();
        }
        $modelos = [];
        $model = $this->findModel($id);
        $model_curp = $this->findModelCurp($id);
        $model_idoficial = $this->findModelIdoficial($id);
        $model_comprobante_domicilio = $this->findModelComprobanteDomicilio($id);
        $model_registro_imss = $this->findModelRegistroImss($id);
        //$model_representante_legal = $this->findModelRepresentanteLegal($id);
        $model_status = new Status();
        $model_validado = new DatosValidados();
        $model_act = $this->findModelAct($id);
        if(isset($model->provider_id) && !empty($model->provider_id)){
            $modelos[$model->formName()] = $model->attributes;
        }
        if(isset($model_curp->provider_id) && !empty($model_curp->provider_id)){
            $modelos[$model_curp->formName()] = $model_curp->attributes;
        }
        if(isset($model_idoficial->provider_id) && !empty($model_idoficial->provider_id)){
            $modelos[$model_idoficial->formName()] = $model_idoficial->attributes;
        }
        if(isset($model_comprobante_domicilio->provider_id) && !empty($model_comprobante_domicilio->provider_id)){
            $modelos[$model_comprobante_domicilio->formName()] = $model_comprobante_domicilio->attributes;
        }
        if(isset($model_registro_imss->provider_id) && !empty($model_registro_imss->provider_id)){
            $modelos[$model_registro_imss->formName()] = $model_registro_imss->attributes;
        }
        /*if(isset($model_representante_legal->provider_id) && !empty($model_representante_legal->provider_id)){
            $modelos[$model_representante_legal->formName()] = $model_representante_legal->attributes;
        }*/
        if(isset($model_act[0]->provider_id) && !empty($model_act[0]->provider_id)){
            for($x = 0; $x<count($model_act);$x++){
                $modelos[$model_act[$x]->formName()][] = $model_act[$x]->attributes;
            }
        }
        $model_representante_legal =$this->findModelRepresentanteLegal($id);

        $rfc = Provider::find()->select('rfc')->where(['provider_id' => $id])->one()->rfc;

        return $this->renderAjax('view-validar', [
            'model' => $model,
            'model_curp' => $model_curp,
            'model_idoficial' => $model_idoficial,
            'model_comprobante_domicilio' => $model_comprobante_domicilio,
            'model_registro_imss' => $model_registro_imss,
            'model_representante_legal' => $model_representante_legal,
            'rfc'=>$rfc,
            'model_status' => $model_status,
            'model_act' => (empty($model_act)) ? [new Giro()] : $model_act,
            'model_validado' => $model_validado,
            'modelos' => base64_encode(json_encode($modelos))
        ]);

    }

    /**
     * Finds the Rfc model based on its primary key value.
     * If the model is not found, a 404 HTTP exception will be thrown.
     * @param integer $id
     * @return Rfc the loaded model
     * @throws NotFoundHttpException if the model cannot be found
     */
    protected function findModel($id)
    {
        $model = Rfc::find()->where(['provider_id' => $id])->one();
        if($model == null || empty($model)){
            $model = new Rfc();
        }
        return $model;
    }
    public function findModelCurp($id)
    {
        $model = Curp::find()->where(['provider_id' => $id])->one();
        if($model == null || empty($model)){
            $model = new Curp();
        }
        return $model;
    }
    public function findModelHistorico($id)
    {
        $model = Historico::find()->where(['provider_id' => $id,'modelo' => 'Rfc'])->one();
        if($model == null || empty($model)){
            $model = new Historico();
        }
        return $model;
    }
    public function findModelIdoficial($id)
    {
        $model = IdOficial::find()->where(['provider_id' => $id])->one();
        if($model == null || empty($model)){
            $model = new IdOficial();
        }
        return $model;
    }
    public function findModelComprobanteDomicilio($id)
    {
        $model = ComprobanteDomicilio::find()->where(['provider_id' => $id])->one();
        if($model == null || empty($model)){
            $model = new ComprobanteDomicilio();
        }
        return $model;
    }
    public function findModelRegistroImss($id)
    {
        $model = RegistroImss::find()->where(['provider_id' => $id])->one();
        if($model == null || empty($model)){
            $model = new RegistroImss();
        }
        return $model;
    }
    public function findModelRepresentanteLegal($id)
    {

        $model = RepresentanteLegal::find()->where(['and',['provider_id' => $id],['activo' => true]])->one();
        if($model == null || empty($model)){
            $model = new RepresentanteLegal();
        }
        return $model;
    }
    public function findModelRegistroActas($id)
    {
        $model = RegistroActas::find()->where(['acta_id' => $id])->one();
        if($model == null || empty($model)){
            $model = new RegistroActas();
        }
        return $model;
    }

    public function findModelPorcentaje($id)
    {
        $model = Porcentaje::find()->where(['and', ['register_id' => $id], ['modelo' => 'rfc']])->one();
        if($model == null || empty($model)){
            $model = new Porcentaje();
        }
        return $model;
    }

    public function actionList_city($id){

        $city = ArrayHelper::map(CatMunicipios::find()->select(['municipio_id','nombre'])->where(['cv_estado' => $id])->asArray()->all(),'municipio_id','nombre');

        $con ='';
        $con .='<option value=""></option>';

        if(count($city)>0){
            foreach ($city as $key=>$value){
                $con .='<option value="'.$key.'">'.$value.'</option>';
            }
        }else{
            $con .='<option value="">No hay ciudades</option>';
        }
        echo $con;


    }
    public function findModelAct($id)
    {
        $model = Giro::find()->where(['and',['provider_id' => $id, 'active' => true]])->all();
        if($model == null || empty($model)){
            $model = [new Giro()];
        }
        return $model;
    }
    public function actionList_rama($id){
        $rama =  ArrayHelper::map(CatRamas::find()->select(['rama_id','nombre_rama'])
            ->where(['sector_id' => $id])
            ->orderBy(['nombre_rama'=>SORT_ASC])
            ->asArray()->all(),'rama_id','nombre_rama');

        $con ='';
        $con .='<option value=""></option>';
        if (count($rama) > 0) {
            foreach ($rama as $key => $value) $con .="<option value='" . $key . "'>" . $value . "</option>";
        } else {
            $con .="<option>No hay ramas</option>";
        }

        echo $con;
    }

    public function actionList_actividad($id){
        $act1 =  ArrayHelper::map(CatActividades::find()->select(['actividad_id','nombre_actividad'])
            ->where(['rama_id' => $id])
            ->orderBy(['nombre_actividad'=>SORT_ASC])
            ->asArray()->all(),'actividad_id','nombre_actividad');
        $con ='';
        $con .='<option value=""></option>';
        if (count($act1) > 0) {
            foreach ($act1 as $key => $value) $con .="<option value='" . $key . "'>" . $value . "</option>";
        } else {
            $con .="<option>No hay actividades</option>";
        }

        echo $con;
    }
    //valida si puede entrar a action update
    public  function  verifyUpdate($provider){
        if($provider){
            $status = Rfc::find()->select('status_bys')->where(['provider_id' => $provider])->one()['status_bys'];
            if($status == Status::STATUS_ENEDICION || $status == Status::STATUS_RECHAZADO || $status == Status::STATUS_RECHAZADO){
                return true;
            }
        }
        return false;
    }
    //valida si puede entrar a action terminar
    public  function  verifyTerminar($provider){
        if($provider){
            $status = Rfc::find()->select('status_bys')->where(['provider_id' => $provider])->one()['status_bys'];
            $porcentaje = Porcentaje::find()->select('porcentaje')->where(['and', ['register_id' => $provider], ['modelo' => 'rfc']])->one()['porcentaje'];
            if(($status == Status::STATUS_ENEDICION && $porcentaje=='100')||($status == Status::STATUS_RECHAZADO && $porcentaje=='100')){
                return true;
            }
        }
        return false;
    }



    protected function findModelEscrituraPublica()
    {
        $model = EscrituraPublica::find()->where(['provider_id' => Yii::$app->user->identity->providerId])->one();
        if(!$model){
            $model = new EscrituraPublica();
        }
        return $model;
    }
    public function findModelRegistroPublico()
    {
        $model = RegistroPublicoPropiedad::find()->where(['provider_id' => Yii::$app->user->identity->providerId])->one();
        if(!$model){
            $model = new RegistroPublicoPropiedad();
        }
        return $model;
    }


    public function findModelPorcentajeEP($id)
    {
        $model = Porcentaje::find()->where(['and', ['register_id' => $id], ['modelo' => 'escritura_publica']])->one();
        if($model == null || empty($model)){
            $model = new Porcentaje();
        }
        return $model;
    }



}
