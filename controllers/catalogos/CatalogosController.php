<?php 

namespace app\controllers\catalogos;

use app\helpers\GeneralController;
use app\models\RegistroCorreos;
use Yii;
use yii\helpers\ArrayHelper;

class CatalogosController extends GeneralController{

    /**
     * Devuelve el catalogo de la consulta
     * 
     * */ 
    public function actionGetCatalogo($filtro){
        $datos = [];
        $query = "";
        if($filtro == RegistroCorreos::FILTRO_TIPO_OBRA){
            $query = "SELECT DISTINCT(category_id) as id, nombre FROM provider.subespecialidad_category";
        }
        if($filtro == RegistroCorreos::FILTRO_ESPECIALIDAD){
            $query = "SELECT DISTINCT(subespecialidad_id) as id, nombre FROM provider.cat_subespecialidades";
        }
        if($filtro == RegistroCorreos::FILTRO_DIVISION){
            $query = "SELECT DISTINCT(division_id) as id, descripcion as nombre FROM productos.division";
        }
        if($filtro == RegistroCorreos::FILTRO_GRUPO){
            $query = "SELECT DISTINCT(grupo_id) as id, descripcion as nombre FROM productos.grupo";
        }
        /* if($filtro == self::FILTRO_CLASE){
            $query = "SELECT DISTINCT(clase_id) as id, descripcion as nombre FROM productos.clase";
        }
        if($filtro == self::FILTRO_PRODUCTO){
            $query = "SELECT DISTINCT(producto_id) as id, descripcion as nombre FROM productos.producto";
        } */

        $result = Yii::$app->db->createCommand($query)->queryAll();
        $datos = ArrayHelper::map($result, 'id', 'nombre');

        return json_encode($datos);
    }

}

?>