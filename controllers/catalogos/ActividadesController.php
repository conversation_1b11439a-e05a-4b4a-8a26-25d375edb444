<?php

namespace app\controllers\catalogos;

use app\helpers\GeneralController;
use app\models\CatActividades;
use app\models\CatActividadesSearch;
use yii\web\Controller;
use yii\web\NotFoundHttpException;
use yii\filters\VerbFilter;

/**
 * ActividadesController implements the CRUD actions for CatActividades model.
 */
class ActividadesController extends GeneralController
{
    /**
     * @inheritDoc
     */
    public function behaviors()
    {
        return array_merge(
            parent::behaviors(),
            [
                'verbs' => [
                    'class' => VerbFilter::class,
                    'actions' => [
                        'delete' => ['POST'],
                    ],
                ],
            ]
        );
    }

    /**
     * Lists all CatActividades models.
     * @return mixed
     */
    public function actionIndex()
    {
        $searchModel = new CatActividadesSearch();
        $dataProvider = $searchModel->search($this->request->queryParams);

        return $this->render('index', [
            'searchModel' => $searchModel,
            'dataProvider' => $dataProvider,
        ]);
    }

    public function actionView($id)
    {
        return $this->renderAjax('view', [
            'model' => $this->findModel($id),
        ]);
    }

    public function actionUpdate($id = null){
        $model = $this->findModel($id);

        if ($this->request->isPost && $model->load($this->request->post()) && $model->save()) {
            return $this->redirect(['index']);
        }

        return $this->renderAjax('update', [
            'model' => $model,
        ]);
    }


    public function actionDelete($id){
        $model = $this->findModel($id);

        return $this->redirect(['index']);
    }

    protected function findModel($id){
        return (($model = CatActividades::findOne($id)) !== null) ? $model : new CatActividades();
    }
}
