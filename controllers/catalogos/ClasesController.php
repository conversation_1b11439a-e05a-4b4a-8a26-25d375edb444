<?php 

namespace app\controllers\catalogos;

use app\helpers\GeneralController;
use app\models\Clase;
use app\models\ClaseSearch;
use app\models\Grupo;
use app\models\Productos;
use kartik\form\ActiveForm;
use Yii;
use yii\helpers\ArrayHelper;
use yii\web\Response;

class ClasesController extends GeneralController{

    /**
     * Lists all Clases models.
     * @return mixed
     */
    public function actionIndex()
    {
        $searchModel = new ClaseSearch();
        $dataProvider = $searchModel->search($this->request->queryParams);

        return $this->render('index', [
            'searchModel' => $searchModel,
            'dataProvider' => $dataProvider,
        ]);
    }

    public function actionView($id){
        return $this->renderAjax('view', [
            'model' => $this->findClaseData($id),
        ]);
    }

    public function actionUpdate($id = null){
        $model = $this->findModel($id);
        $grupos = ArrayHelper::map(Grupo::find()->all(),'grupo_id', 'descripcion');

        if (Yii::$app->request->isAjax && $model->load(Yii::$app->request->post())) {
            Yii::$app->response->format = Response::FORMAT_JSON;

            $result = [];
            if(empty($model->grupo_id)){ $result['clase-grupo_id'] = ['Debe seleccionar una grupo de clase'];  }
            if(empty($model->descripcion)){ $result['clase-descripcion'] = ['La descripcion de la clase es obligatoria'];  }
            if(empty($model->clave)){ $result['clase-clave'] = ['La clase debe tener una clave']; }
            else{
                $condicion = $model->clase_id ? ['and', ['clave' => $model->clave], ['not in', 'clase_id', [$model->clase_id]] ] : ['clave' => $model->clave];
                $consulta_clase = Clase::find()->where($condicion)->all();
                if(count($consulta_clase) == 0){ 
                    $consulta_producto = Productos::find()->where(['clave' => $model->clave])->all();
                    if(count($consulta_producto) > 0){ $result['clase-clave'] = ['Esta clave esta asociada a una producto, debe ser unica']; }
                }else{
                    $result['clase-clave'] = ['Esta clave esta asociada a una clase existente'];
                }
            }
            return array_merge($result,ActiveForm::validate($model));
        } else if ($model->load(Yii::$app->request->post())) {
            if($model->save()){ return $this->redirect(['index']); }
        }

        return $this->renderAjax('update', [
            'model' => $model,
            'grupos' => $grupos,
        ]);
    }

    public function actionDelete($id){
        $productos = Productos::findAll(['clase_id' => $id]);
        if(count($productos) > 0){
            Yii::$app->session->setFlash('error', "No es posiblie eliminar el registro porque existen productos relacionados a esta clase");
        }else{
            $this->findModel($id)->delete();
            Yii::$app->session->setFlash('success', "Clase eliminada exitosamente");
        }
        return $this->redirect(['index']);
    }
    
    protected function findModel($id){
        return (($model = Clase::findOne($id)) !== null) ? $model : new Clase();
    }

    public function findClaseData($id){
        return Clase::completeData($id);
    }

}

?>