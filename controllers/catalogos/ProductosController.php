<?php 

namespace app\controllers\catalogos;

use app\helpers\GeneralController;
use app\models\Clase;
use app\models\Productos;
use app\models\ProductosSearch;
use kartik\form\ActiveForm;
use Yii;
use yii\helpers\ArrayHelper;
use yii\web\Response;

class ProductosController extends GeneralController{

    /**
     * Lists all Productos models.
     * @return mixed
     */
    public function actionIndex()
    {
        $searchModel = new ProductosSearch();
        $dataProvider = $searchModel->search($this->request->queryParams);

        return $this->render('index', [
            'searchModel' => $searchModel,
            'dataProvider' => $dataProvider,
        ]);
    }

    public function actionView($id)
    {
        return $this->renderAjax('view', [
            'model' => $this->findProductoData($id),
        ]);
    }

    public function actionUpdate($id = null){
        $model = $this->findModel($id);
        $clases = ArrayHelper::map(Clase::find()->all(),'clase_id', 'descripcion');
        $clave_clase = Clase::findOne($model->clase_id)['clave'];

        if (Yii::$app->request->isAjax && $model->load(Yii::$app->request->post())) {
            Yii::$app->response->format = Response::FORMAT_JSON;

            $result = [];
            if(empty($model->clase_id)){ $result['productos-clase_id'] = ['Debe seleccionar una clase de producto'];  }
            if(empty($model->descripcion)){ $result['productos-descripcion'] = ['La descripcion del producto es obligatoria'];  }
            if(empty($model->clave)){ $result['productos-clave'] = ['El producto debe tener una clave']; }
            else{
                $consulta_clase = Clase::find()->where(['clave' => $model->clave])->all();
                if(count($consulta_clase) == 0){ 
                    $condicion = $model->producto_id ? ['and', ['clave' => $model->clave], ['not in', 'producto_id', [$model->producto_id]] ] : ['clave' => $model->clave];
                    $consulta_producto = Productos::find()->where($condicion)->all();
                    if(count($consulta_producto) > 0){ $result['productos-clave'] = ['Esta clave esta asociada a una producto, debe ser unica']; }
                }else{
                    $result['productos-clave'] = ['Esta clave esta asociada a una clase existente'];
                }
            }
            return array_merge($result,ActiveForm::validate($model));
        } else if ($model->load(Yii::$app->request->post())) {
            if($model->save()){ return $this->redirect(['index']); }
        }

        return $this->renderAjax('update', [
            'model' => $model,
            'clases' => $clases,
            'clave_clase' => $clave_clase
        ]);
    }

    public function actionDelete($id){
        $this->findModel($id)->delete();
        return $this->redirect(['index']);
    }

    protected function findModel($id){
        return (($model = Productos::findOne($id)) !== null) ? $model : new Productos();
    }

    protected function findProductoData($id){
        return Productos::completeData($id);
    }

    public function actionGetIdClaseByClave($clave_clase){
        $response = null;
        if($clave_clase){
           $clase =  Clase::find()->where(['clave' => $clave_clase])->one();
           if( !is_null($clase) && !empty($clase) ){ $response = $clase['clase_id']; }
        }

        return $response;
    }

    public function actionGetClaveClaseById($clase_id){
        $response = null;
        if($clase_id){
           $clave =  Clase::findOne($clase_id)['clave'];
           if( !is_null($clave) && !empty($clave) ){ $response = $clave; }
        }

        return $response;
    }

}

?>