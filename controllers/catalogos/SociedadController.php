<?php

namespace app\controllers\catalogos;

use app\helpers\GeneralController;
use app\models\SociedadMercantil;
use app\models\SociedadMercantilSearch;
use yii\filters\VerbFilter;

/**
 * SociedadController implements the CRUD actions for SociedadMercantil model.
 */
class SociedadController extends GeneralController
{

    public function behaviors(){
        return array_merge(
            parent::behaviors(),
            [
                'verbs' => [
                    'class' => VerbFilter::className(),
                    'actions' => [
                        'delete' => ['POST'],
                    ],
                ],
            ]
        );
    }


    public function actionIndex(){
        $searchModel = new SociedadMercantilSearch();
        $dataProvider = $searchModel->search($this->request->queryParams);

        return $this->render('index', [
            'searchModel' => $searchModel,
            'dataProvider' => $dataProvider,
        ]);
    }


    public function actionView($id){
        return $this->renderAjax('view', [
            'model' => $this->findModel($id),
        ]);
    }


    public function actionUpdate($id=null){
        $model = $this->findModel($id);
        if ($this->request->isPost && $model->load($this->request->post())) {
            $model->created_by = \Yii::$app->user->id;
            if ($model->save()) {
                return $this->redirect(['index']);
            }
        }
        return $this->renderAjax('update', [
            'model' => $model,
        ]);
    }


    public function actionDelete($id){
        $model = $this->findModel($id);
        $model->last_modification_at = date('Y-m-d H:i:s');
        $model->last_modification_by = \Yii::$app->user->id;
        $model->activo = false;
        if(!$model->save())
            return var_dump($model->errors);
        return $this->redirect(['index']);
    }


    protected function findModel($id){
        return (($model = SociedadMercantil::findOne($id)) !== null) ? $model : new SociedadMercantil();
    }
}
