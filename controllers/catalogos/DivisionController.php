<?php 

namespace app\controllers\catalogos;

use app\helpers\GeneralController;
use app\models\Division;
use app\models\DivisionSearch;
use app\models\Grupo;
use kartik\form\ActiveForm;
use Yii;
use yii\db\Expression;
use yii\web\Response;

class DivisionController extends GeneralController{

    /**
     * Lists all Division models.
     * @return mixed
     */
    public function actionIndex()
    {
        $searchModel = new DivisionSearch();
        $dataProvider = $searchModel->search($this->request->queryParams);

        return $this->render('index', [
            'searchModel' => $searchModel,
            'dataProvider' => $dataProvider,
        ]);
    }

    public function actionUpdate($id = null){
        $model = $this->findDivision($id);

        if (Yii::$app->request->isAjax && $model->load(Yii::$app->request->post())) {
            Yii::$app->response->format = Response::FORMAT_JSON;

            $result = [];
            if(empty($model->descripcion)){ $result['division-descripcion'] = ['La descripcion de la familia es obligatoria'];  }
            else{
                $condicion = $model->division_id ? ['and', ['not in', 'division_id', [$model->division_id]], ['UNACCENT(UPPER(descripcion))' => new Expression("UNACCENT(UPPER(:desc))", [":desc" => $model->descripcion])] ] : ['UNACCENT(UPPER(descripcion))' => new Expression("UNACCENT(UPPER(:desc))", [":desc" => $model->descripcion])];
                $consulta_division = Division::find()->where($condicion)->all();
                if(count($consulta_division) > 0){ $result['division-descripcion'] = ['Esta familia ya existe']; }
            }
            return array_merge($result,ActiveForm::validate($model));
        } else if ($model->load(Yii::$app->request->post())) {
            if($model->save()){ return $this->redirect(['index']); }
        }

        return $this->renderAjax('update', [ 'model' => $model ]);
    }

    public function actionDelete($id){
        $grupos = Grupo::findAll(['division_id' => $id]);
        if(count($grupos) > 0){
            Yii::$app->session->setFlash('error', "No es posiblie eliminar el registro porque existen grupos relacionados a esta Familia.");
        }else{
            $this->findDivision($id)->delete();
            Yii::$app->session->setFlash('success', "Familia eliminada exitosamente");
        }
        return $this->redirect(['index']);
    }

    public function findDivision($id){
        return (($model = Division::findOne($id)) !== null) ? $model : new Division();
    }

}

?>