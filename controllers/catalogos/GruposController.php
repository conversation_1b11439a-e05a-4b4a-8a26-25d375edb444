<?php 

namespace app\controllers\catalogos;

use app\helpers\GeneralController;
use app\models\Clase;
use app\models\Division;
use app\models\Grupo;
use app\models\GrupoSearch;
use kartik\form\ActiveForm;
use Yii;
use yii\db\Expression;
use yii\helpers\ArrayHelper;
use yii\web\Response;

class GruposController extends GeneralController{

    /**
     * Lists all Grupo models.
     * @return mixed
     */
    public function actionIndex()
    {
        $searchModel = new GrupoSearch();
        $dataProvider = $searchModel->search($this->request->queryParams);

        return $this->render('index', [
            'searchModel' => $searchModel,
            'dataProvider' => $dataProvider,
        ]);
    }

    public function actionView($id){
        return $this->renderAjax('view', [
            'model' => $this->findGrupoData($id),
        ]);
    }

    public function actionUpdate($id = null){
        $model = $this->findGrupoData($id);
        $divisiones = ArrayHelper::map(Division::find()->all(),'division_id', 'descripcion');

        if (Yii::$app->request->isAjax && $model->load(Yii::$app->request->post())) {
            Yii::$app->response->format = Response::FORMAT_JSON;

            $result = [];
            if(empty($model->division_id)){ $result['grupo-division_id'] = ['Debe seleccionar una division de grupo'];  }
            if(empty($model->descripcion)){ $result['grupo-descripcion'] = ['La descripcion de la clase es obligatoria'];  }
            else{
                $condicion = $model->grupo_id ? ['and', ['not in', 'grupo_id', [$model->grupo_id]], ['UNACCENT(UPPER(descripcion))' => new Expression("UNACCENT(UPPER(:desc))", [":desc" => $model->descripcion])] ] : ['UNACCENT(UPPER(descripcion))' => new Expression("UNACCENT(UPPER(:desc))", [":desc" => $model->descripcion])];
                $consulta_grupo = Grupo::find()->where($condicion)->all();
                if(count($consulta_grupo) > 0){ $result['grupo-descripcion'] = ['Este grupo ya existe']; }
            }
            return array_merge($result,ActiveForm::validate($model));
        } else if ($model->load(Yii::$app->request->post())) {
            if($model->save()){ return $this->redirect(['index']); }
        }

        return $this->renderAjax('update', [
            'model' => $model,
            'divisiones' => $divisiones,
        ]);
    }

    public function actionDelete($id){
        $clases = Clase::findAll(['grupo_id' => $id]);
        if(count($clases) > 0){
            Yii::$app->session->setFlash('error', "No es posiblie eliminar el registro porque existen clases relacionados a este grupo");
        }else{
            $this->findGrupoData($id)->delete();
            Yii::$app->session->setFlash('success', "Grupo eliminado exitosamente");
        }
        return $this->redirect(['index']);
    }

    public function findGrupoData($id){
        return (($model = Grupo::findOne($id)) !== null) ? $model : new Grupo();
    }

}

?>