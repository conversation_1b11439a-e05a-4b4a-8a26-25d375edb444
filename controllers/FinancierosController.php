<?php

namespace app\controllers;

use app\helpers\BysController;
use app\helpers\GeneralController;
use app\models\BalanceEstado;
use app\models\BalanceEstadoSearch;
use app\models\CapacidadContratacionSearch;
use app\models\DeclaracionIva;
use app\models\DeclaracionIvaSearch;
use app\models\EstadoFinanciero;
use app\models\EstadoFinancieroSearch;
use app\models\IntervencionBancariaSearch;
use app\models\Porcentaje;
use app\models\Provider;
use app\models\Status;
use app\models\UltimaDeclaracion;
use app\models\UltimaDeclaracionSearch;
use app\models\Usuarios;
use Yii;
use app\models\DeclaracionIsr;
use app\models\DeclaracionIsrSearch;
use yii\filters\VerbFilter;


class FinancierosController extends GeneralController
{


    public function behaviors()
    {
        return [
            'verbs' => [
                'class' => VerbFilter::className(),
                'actions' => [
                    'delete' => ['POST'],
                ],
            ],
        ];
    }


    public function actionIndexValidador()
    {
        $searchUltimaDeclaracion = [];
        $datosUltimaDeclaracion = [];
        $searchBalanceEstado = [];
        $searchDeclaracionIva = [];
        $searchModel = [];
        $searchEstadoFinanciero = [];
        $searchCapacidadContratacion = [];
        //$datosBalanceEstado = [];
        //$datosDeclaracionIva = [];
        //$dataProvider = [];
        $datosEstadoFinanciero = [];
        //$datosCapacidadContratacion = [];

        /* Carga los parametros de busqueda generales que aplican en todos los tabs */
        $simpleParams = isset(Yii::$app->request->queryParams) ? Yii::$app->request->queryParams : [];
        $providerParams = GeneralController::addPrefixKeyArray($simpleParams, 'provider');

        $datosFinancierosParams = array_merge($simpleParams, $providerParams);

        $searchMetodoPago = new IntervencionBancariaSearch();
        $metodoPago = $searchMetodoPago->search( [$searchMetodoPago->formName() => $providerParams ], null, 'metodoPagoVal');

        if(Yii::$app->user->can(Usuarios::ROLE_VALIDADOR_PROVEEDORES) || Yii::$app->user->can(Usuarios::ROLE_ADMIN_PROVIDER) ){
            $searchUltimaDeclaracion = new UltimaDeclaracionSearch();
            $datosUltimaDeclaracion = $searchUltimaDeclaracion->search( [$searchUltimaDeclaracion->formName() => $providerParams ],'mainFilterUltimaDeclaracion');
        }else if(Yii::$app->user->can(Usuarios::ROLE_VALIDADOR_CONTRATISTAS)){
            $searchBalanceEstado = new BalanceEstadoSearch();
            $searchDeclaracionIva = new DeclaracionIvaSearch();
            $searchModel = new DeclaracionIsrSearch();
            $searchEstadoFinanciero = new EstadoFinancieroSearch();
            $searchCapacidadContratacion = new CapacidadContratacionSearch();

            //$datosBalanceEstado = $searchBalanceEstado->search(Yii::$app->request->queryParams,'mainFilterBalanceEstado');
            //$datosDeclaracionIva = $searchDeclaracionIva->search(Yii::$app->request->queryParams,'mainFilterDeclaracionIva');
            //$dataProvider = $searchModel->search(Yii::$app->request->queryParams,'mainFilterDeclaracionIsr');
            $datosEstadoFinanciero = $searchEstadoFinanciero->search([$searchEstadoFinanciero->formName() => $datosFinancierosParams ],'mainFilterEstadoFinanciero');
           // $datosCapacidadContratacion = $searchCapacidadContratacion->search(Yii::$app->request->queryParams, 'mainFilterCapcidadContratacion');

        }else{
            $this->msgError();
        }

        $tipo = self::providerType();

        return $this->render('index-validador', [
            'searchModel' => $searchModel,
            'searchBalanceEstado' => $searchBalanceEstado,
            'searchDeclaracionIva' => $searchDeclaracionIva,
            'searchEstadoFinanciero' => $searchEstadoFinanciero,
            'searchCapacidadContratacion' => $searchCapacidadContratacion,
            //'dataProvider' => $dataProvider,
            //'datosBalanceEstado' => $datosBalanceEstado,
            //'datosDeclaracionIva' => $datosDeclaracionIva,
            'datosEstadoFinanciero' => $datosEstadoFinanciero,
            //'datosCapacidadContratacion' => $datosCapacidadContratacion,
            'searchUltimaDeclaracion' => $searchUltimaDeclaracion,
            'datosUltimaDeclaracion' => $datosUltimaDeclaracion,
            'searchMetodoPago' => $searchMetodoPago,
            'metodoPago' => $metodoPago,
            'params' => $simpleParams,
            'tipo' => $tipo
        ]);
    }

    public function actionConfirm(){
        $id = Yii::$app->request->post()['provider_id'];

        $declaracion_isr = Yii::$app->db->createCommand();
        $declaracion_isr->update('provider.declaracion_isr',['status_op' => Status::STATUS_VALIDADO],'provider_id ='.$id);
        $declaracion_isr->execute();

        $status_id = Status::find()->select('status_id')
            ->where(['and',['register_id' => $id],['status_op' => Status::STATUS_PENDIENTE]])
            ->andWhere(['modelo'=>'DeclaracionIsr'])->one();
        if(!empty($status_id['status_id'])){
            $requi_status = Status::findOne($status_id);
            $requi_status->status_op = Status::STATUS_TERMINADO;
            $requi_status->update();
            \Yii::$app->db->createCommand("update provider.declaracion_isr set status_op = :val where status_op = :edi and provider_id =:id")
                ->bindValue(':id', $id)
                ->bindValue(':val', Status::STATUS_VALIDADO)
                ->bindValue(':edi', Status::STATUS_ENEDICION)
                ->execute();
        }

        Yii::$app->session->setFlash('success', 'Los datos se validaron correctamente');
        return Yii::$app->getResponse()->redirect(['datosFinancieros/declaracion-isr/index-validador']);

    }


    protected function findModel($id)
    {
        $model = DeclaracionIsr::find()->where(['provider_id' => $id])->one();
        if($model == null || empty($model)){
            $model = new DeclaracionIsr();
        }
        return $model;
    }


    public function findModelPorcentaje($id)
    {
        $model = Porcentaje::find()->where(['and', ['register_id' => $id], ['modelo' => 'declaracion_isr']])->one();
        if($model == null || empty($model)){
            $model = new Porcentaje();
        }
        return $model;
    }


    public function findModelUltimaDeclaracion($id)
    {
        $model = UltimaDeclaracion::find()->where(['provider_id' => $id])->one();
        if($model == null || empty($model)){
            $model = new UltimaDeclaracion();
        }
        return $model;
    }


    public function findModelBalanceEstado($id)
    {
        $model = BalanceEstado::find()->where(['provider_id' => $id])->one();
        if($model == null || empty($model)){
            $model = new BalanceEstado();
        }
        return $model;
    }


    public function findModelDeclaracionIva($id)
    {
        $model = DeclaracionIva::find()->where(['provider_id' => $id])->one();
        if($model == null || empty($model)){
            $model = new DeclaracionIva();
        }
        return $model;
    }


    public function findModelEstadoFinanciero($id)
    {
        $model = EstadoFinanciero::find()->where(['provider_id' => $id])->one();
        if($model == null || empty($model)){
            $model = new EstadoFinanciero();
        }
        return $model;
    }


}
