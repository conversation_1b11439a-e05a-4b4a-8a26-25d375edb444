<?php

namespace app\controllers;

use app\helpers\GeneralController;
use app\models\NonWorkingDays;
use app\models\NotAssist;
use app\models\Provider;
use app\models\Usuarios;
use kartik\form\ActiveForm;
use Yii;
use app\models\Calendar;
use app\models\CalendarSearch;
use yii\helpers\ArrayHelper;
use yii\web\Controller;
use yii\web\NotFoundHttpException;
use yii\filters\VerbFilter;
use yii\web\Response;

/**
 * CalendarController implements the CRUD actions for Calendar model.
 */
class CalendarController extends GeneralController
{
    /**
     * {@inheritdoc}
     */
    public function behaviors()
    {
        return [
            'verbs' => [
                'class' => VerbFilter::className(),
            ],
        ];
    }

    /**
     * Lists all Calendar models.
     * @return mixed
     */
    public function actionIndex()
    {

        return $this->goHome();
        //        $searchModel = new CalendarSearch();
//        $dataProvider = $searchModel->search(Yii::$app->request->queryParams);
//
//        return $this->render('index', [
//            'searchModel' => $searchModel,
//            'dataProvider' => $dataProvider,
//        ]);
    }


    public function actionDocumentacionCotejo(){
        return $this->render('documentacion-cotejo');
    }

    public function actionDocumentacionCotejoPre(){
        return $this->render('documentacion-cotejo-pre');
    }
    /**
     * Displays a single Calendar model.
     * @param integer $id
     * @return mixed
     * @throws NotFoundHttpException if the model cannot be found
     */
    public function actionView($id)
    {
        return $this->render('view', [
            'model' => $this->findModel($id),
        ]);
    }

    /**
     * Creates a new Calendar model.
     * If creation is successful, the browser will be redirected to the 'view' page.
     * @return mixed
     */
    public function actionCreate($date = null)
    {
        $model = $this->findModel();
        if ($date != null) {
            if (Yii::$app->request->isAjax && $model->load(Yii::$app->request->post())) {
                Yii::$app->response->format = Response::FORMAT_JSON;
                return ActiveForm::validate($model);
            } else if ($model->load(Yii::$app->request->post())) {
                $user_id = Yii::$app->user->getId();
                $countCalendar = Calendar::find()->where(['and', ['status' => 'CONFIRMADA'], ['visit_day' => $date], ['visit_time' => $model->visit_time]])->count();
                $countValCot = $this->getAllValidadores();
                if (($verify = Calendar::find()->where(['and', ['user' => $user_id], ['status' => 'CONFIRMADA'], ['>=', 'visit_day', $date]])->one()) == null) {
                    if ($countCalendar < $countValCot) {
                        $model->user = $user_id;
                        $model->visit_day = $date;
                        $model->status = 'CONFIRMADA';
                        if ($model->save()) {
                            if (($pro = Provider::find()->where(['user_id' => $user_id])->one()) !== null) {
                                $pro->status_cotejar = 'PENDIENTE';
                                $pro->save(false);
                                self::sendEmail('/provider/correos/cita_confirmada', null, $pro->email, 'Confirmación de cita para cotejo', ['dia' => $model->visit_day, 'hora' => $model->visit_time], null);
                            }
                            Yii::$app->session->setFlash('success', "Cita agendada exitosamente.");
                            return $this->redirect(['documentacion-cotejo']);
                        } else {
                            Yii::$app->session->setFlash('error', "Error al agendar la cita.");
                        }
                    } else {
                        Yii::$app->session->setFlash('error', "Ya hay una cita agendada el mismo día y en el mismo horario");
                    }

                } else {
                    Yii::$app->session->setFlash('error', "Ya cuenta con una cita agendada el día " . $verify['visit_day'] . ' a las ' . $verify['visit_time']);
                }
                return $this->redirect(['calendar']);
            }
            return $this->renderAjax('create', [
                'model' => $model,
                'date' => $date
            ]);
        }
        return $this->redirect(['calendar']);
    }

    /**
     * Updates an existing Calendar model.
     * If update is successful, the browser will be redirected to the 'view' page.
     * @param integer $id
     * @return mixed
     * @throws NotFoundHttpException if the model cannot be found
     */
    public function actionUpdate($id)
    {
        $model = $this->findModel($id);

        if ($model->load(Yii::$app->request->post()) && $model->save()) {
            return $this->redirect(['view', 'id' => $model->calendar_id]);
        }

        return $this->render('update', [
            'model' => $model,
        ]);
    }

    /**
     * Updates an existing Calendar model.
     * If update is successful, the browser will be redirected to the 'view' page.
     * @param integer $id
     * @return mixed
     * @throws NotFoundHttpException if the model cannot be found
     */
    public function actionChange($id = null)
    {
        if (Yii::$app->request->isAjax) {
            $id = intval(base64_decode($id));
            $model = $this->findModelChange($id);

            if ($model->load(Yii::$app->request->post()) && $model->save()) {
                return $this->redirect(['agendar']);
            }

            $nameProv = Yii::$app->db->createCommand("select  CASE WHEN (p.name_razon_social is not null and p.name_razon_social != '') then p.name_razon_social else concat_ws(' ',p.pf_nombre,p.pf_ap_paterno,p.pf_ap_materno) end as nombre from
            provider p where user_id = :id", [':id' => intval($model->user)])->queryOne()['nombre'];

            return $this->renderAjax('_change', [
                'model' => $model,
                'nameProv' => $nameProv
            ]);
        }

    }


    public function actionCreation()
    {
        $model = new Calendar();

        if ($model->load(Yii::$app->request->post())) {
            $model->status = 'CONFIRMADA';
            if ($model->save()) {
                Calendar::updateAll(['status' => 'CANCELADA'],['and',['user' => $model->user],['not in','calendar_id',[$model->calendar_id]],['status' => 'CONFIRMADA']]);
                Provider::updateAll(['status_cotejar' => 'PENDIENTE'], ['user_id' => $model->user]);
            }
            return $this->redirect(['agendar']);
        }

        return $this->renderAjax('_creation', [
            'model' => $model,
            'data' => ArrayHelper::map(Yii::$app->db->createCommand("select p.user_id,CASE WHEN (p.name_razon_social is not null and p.name_razon_social != '') then p.name_razon_social else concat_ws(' ',p.pf_nombre,p.pf_ap_paterno,p.pf_ap_materno) end as nombre
                                        from provider p where  status_cotejar !='VALIDADO' and rfc is not null")->queryAll(), 'user_id', 'nombre')
        ]);

    }

    /**
     * Deletes an existing Calendar model.
     * If deletion is successful, the browser will be redirected to the 'index' page.
     * @param integer $id
     * @return mixed
     * @throws NotFoundHttpException if the model cannot be found
     */
    public function actionDelete($id)
    {

        $mod = $this->findModelChange(intval($id));
        $mod->status = 'CANCELADA';
        if($mod->save(false)){
            Provider::updateAll(['status_cotejar' => 'PENDIENTE AGENDAR CITA'], ['user_id' => $mod->user]);
        }

        return $this->redirect(['agendar']);
    }

    /**
     * Finds the Calendar model based on its primary key value.
     * If the model is not found, a 404 HTTP exception will be thrown.
     * @param integer $id
     * @return Calendar the loaded model
     * @throws NotFoundHttpException if the model cannot be found
     */
    protected function findModel()
    {
        if (($model = Calendar::find()->where(['and', ['user' => Yii::$app->user->getId()], ['status' => 'CONFIRMADA']])->one()) === null) {
            $model = new Calendar();
        }
        return $model;
    }

    /**
     * Finds the Calendar model based on its primary key value.
     * If the model is not found, a 404 HTTP exception will be thrown.
     * @param integer $id
     * @return Calendar the loaded model
     * @throws NotFoundHttpException if the model cannot be found
     */
    protected function findModelChange($id = null)
    {
        if (($model = Calendar::findOne($id)) === null) {
            $model = new Calendar();
        }
        return $model;
    }


    public function actionCalendar()
    {
        if (Yii::$app->user->can(Usuarios::ROLE_PROVIDER)) {

            $pro = Yii::$app->user->identity->providerid;
            $arr = ['PENDIENTE', 'RECHAZADO', 'PENDIENTE AGENDAR CITA'];
            if (!in_array(Provider::find()->select('status_cotejar')->where(['provider_id' => $pro])->one()['status_cotejar'], $arr)) {
                return $this->goHome();
            }

        }
        return $this->render('calendar', [
            'events' => $this->EventsCalendar(),
            'amonestacion' => $this->notAssist()
        ]);
    }

    public function actionHoliday()
    {

        if (Yii::$app->request->isAjax) {

            $year = date('Y');
            $day = date('Y-m-d');
            $days = ArrayHelper::getColumn(Yii::$app->db->createCommand("select non_working_day from non_working_days
                                                where (holiday is true or (holiday is false and full_day is true)) and user_type = 'ADMIN' and 
                                                trim(substring(non_working_day::text,1,4)) = '$year' and non_working_day>= '$day'")->queryAll(), 'non_working_day');

            return json_encode(['status' => true, 'msg' => 'Success', 'data' => $days]);
        }

        return $this->redirect('calendar');

    }

    public function actionVerifyDateHolidayAdmin($date = null)
    {

        if ($date != null && Yii::$app->request->isAjax) {

            if (($model = NonWorkingDays::find()->where(['and', [['or', ['holiday' => true], ['and', ['holiday' => false], ['full_day' => true]]]], ['user_type' => 'ADMIN']])->one()) !== null) {
                return json_encode(['status' => false, 'msg' => 'Día feriado']);
            }
            return json_encode(['status' => true, 'msg' => 'Success']);
        }

        return $this->redirect('calendar');

    }

    public function actionVerifyDateHolidayUser($date = null)
    {

        if ($date != null) {
            $model = NonWorkingDays::find()->where(['and', ['holiday' => false], ['full_day' => true]])->count('1');
            return json_encode(['status' => true, 'msg' => $model]);
        }
        return $this->redirect('calendar');
    }

    public function EventsCalendar()
    {

        $date = date('Y-m-d');

        $personas = $this->getAllValidadores();

        $MiCita = Calendar::find()->select([new \yii\db\Expression('concat_ws(\'/\',"visit_day","visit_time") as micita')])
            ->where(['and', ['user' => Yii::$app->user->getId()], ['status' => 'CONFIRMADA']])->one()['micita'];

        $data = Yii::$app->db->createCommand("WITH a as(SELECT visit_day, count(1) as total from calendar
                                                where visit_day>=:d and status = 'CONFIRMADA'
                                                group by visit_day, visit_time order by visit_day,visit_time)
                                                select visit_day, sum(total) as total from a group by visit_day,total;", [':d' => $date])->queryAll();


        $allData = [];

        $explode_fecha = '';

        if ($MiCita !== null) {
            $explode_fecha = explode('/', $MiCita)[0];
        }


        foreach ($data as $val) {
            $arr = ['title' => '', 'start' => '', 'end' => '', 'rendering' => 'background', 'allDay' => true, 'className' => ''];
            $horasLaborales = $this->totalHours($val['visit_day'], $val['total'], $personas);

            $bgcolor = $this->getBgColor($horasLaborales['porcentaje']);

            if ($val['visit_day'] == $explode_fecha) {
                $arr['title'] = $MiCita;
            }

            $arr['start'] = $val['visit_day'];
            $arr['end'] = $val['visit_day'];
            $arr['className'] = $bgcolor;

            if ($horasLaborales['porcentaje'] >= 100) {
                $arr['className'] = 'completo';

            } else if ($horasLaborales['porcentaje'] <= 20) {
                $arr['className'] = 'libre';

            }

            array_push($allData, $arr);
        }

        return json_encode($allData);
    }


    public function totalHours($fecha = null, $horasDesc = null, $validadores = null)
    {


        $dia_semana = $this->diaSemana($fecha);
        $rangos_dias = Yii::$app->db->createCommand("select start_time,end_time,start_time_2,end_time_2 from working_hours
                       where day = :d", [':d' => $dia_semana])->queryOne();

        $total1 = ($this->hoursDiff($rangos_dias['start_time'], $rangos_dias['end_time'])) + 1;

        $total2 = ($this->hoursDiff($rangos_dias['start_time_2'], $rangos_dias['end_time_2'])) + 1;


        $totalCitasDia = ($total1 + $total2) * $validadores;


        $data = Yii::$app->db->createCommand("select full_day,start_time, end_time from non_working_days
                                                  where non_working_day = :d and holiday is false and (full_day is false or full_day is true)"
            , [':d' => $fecha])->queryAll();


        foreach ($data as $val) {

            if ($val['full_day']) {
                $horasDesc += ($total1 + $total2);
            } else {

                $hT = 7 - $this->hoursDiff($val['start_time'], $val['end_time']);

                $horasDesc += $hT;
            }
        }

        $porcentaje = 100;


        if ($horasDesc > 0) {
            $porcentaje = ($horasDesc * 100) / $totalCitasDia;
        }


        return ['porcentaje' => $porcentaje, 'total_horas' => $totalCitasDia, 'horasDes' => $horasDesc];
    }

    public function hoursDiff($hora_ini, $hora_fin)
    {

        $fecha = date("Y-m-d");

        $ts_fin = strtotime($fecha . " " . $hora_fin);
        $ts_ini = strtotime($fecha . " " . $hora_ini);


        $diff = ($ts_fin - $ts_ini) / 3600;

        return $diff;
    }


    function diaSemana($fecha)
    {

        $dias = ['Domingo', 'Lunes', 'Martes', 'Miercoles', 'Jueves', 'Viernes', 'Sabado'];
        $dia_actual = $dias[date('N', strtotime($fecha))];

        return $dia_actual;
    }


    public function getBgColor($porcentaje)
    {
        $bgcolor = 'mi_cita';
        if ($porcentaje <= 20) {
            $bgcolor = 'libre';
        } elseif ($porcentaje > 20 && $porcentaje < 99) {
            $bgcolor = 'medio';
        } elseif ($porcentaje >= 100) {
            $bgcolor = 'completo';
        }
        return $bgcolor;
    }

    public function actionAllHoursByDay($date = null, $hour = null)
    {


        $personas = $this->getAllValidadores();

        $horariosInhabiles = Yii::$app->db->createCommand("select full_day,start_time, end_time from non_working_days
                                                  where non_working_day = :d and holiday is false and (full_day is false or full_day is true)", [':d' => $date])->queryAll();


        $horarios = [];
        $horariosRepetidos = [];
        if (!empty($horariosInhabiles)) {


            foreach ($horariosInhabiles as $val) {

                if ($val['full_day']) {
                    $horarios = array_merge($horarios, $this->horasLaboralesDia($date));
                } else {
                    $horarios = array_merge($horarios, $this->generarHoras($val['start_time'], $val['end_time']));

                }
            }

            $horariosRepetidos = $this->contarValoresArray($horarios);


        }


        $data = Yii::$app->db->createCommand("SELECT visit_time, count(1) as total from calendar
                                                  where visit_day = :d and status = 'CONFIRMADA'
                                                  group by visit_time order by visit_time", [':d' => $date])->queryAll();


        $horasLaborales = $this->horasLaboralesDia($date);

        $horasLaboralesPersonas = [];

        foreach ($horasLaborales as $hor) {

            //if (in_array($hor,$horarios))
            $horasLaboralesPersonas = array_merge($horasLaboralesPersonas, [$hor => $personas]);

        }


        if ($hour != null && isset($horasLaboralesPersonas[$hour])) {
            $horasLaboralesPersonas[$hour] = $horasLaboralesPersonas[$hour] + 1;
        }

        foreach ($data as $d) {
            $total = $d['total'];
            if (isset($horasLaboralesPersonas[$d['visit_time']])) {
                $citasDia = $horasLaboralesPersonas[$d['visit_time']];
                $horasLaboralesPersonas[$d['visit_time']] = $citasDia - $total;
            }

        }

        foreach ($horariosRepetidos as $hr => $hr2) {

            $total = $personas - $hr2;

            $horasLaboralesPersonas[$hr] = $total > 0 ? $total : 0;

        }


        $con = '';
        $con .= '<option value=""></option>';
        $contador = 0;
        foreach ($horasLaboralesPersonas as $hlp => $hlpi) {
            if ($hlpi > 0) {
                $contador++;
                $con .= "<option value='" . $hlp . "'>" . $hlp . "</option>";
            }
        }

        if ($contador == 0) {
            $con .= '<option value="">No hay horarios disponibles</option>';
        }

        echo $con;

    }

    function contarValoresArray($array)
    {
        $contar = array();

        foreach ($array as $value) {
            if (isset($contar[$value])) {
                $contar[$value] += 1;
            } else {
                $contar[$value] = 1;
            }
        }
        return $contar;
    }


    public function horasLaboralesDia($fecha)
    {

        $dia_semana = $this->diaSemana($fecha);

        $rangos_dias = Yii::$app->db->createCommand("select start_time,end_time,start_time_2,end_time_2 from working_hours
                       where day = :d", [':d' => $dia_semana])->queryOne();

        $primerRango = $this->generarHoras($rangos_dias['start_time'], $rangos_dias['end_time']);
        $segundoRango = $this->generarHoras($rangos_dias['start_time_2'], $rangos_dias['end_time_2']);

        $arrFinal = array_merge($primerRango, $segundoRango);

        return $arrFinal;

    }


    public function generarHoras($inicio, $fin)
    {

        $inicio = intval(explode(':', $inicio)[0]);

        $fin = intval(explode(':', $fin)[0]);

        $horas = [];

        for ($x = $inicio; $x <= $fin; $x++) {
            $h = "midnight +" . $x . " hour";
            $hora = date('H:i:s', strtotime($h));
            array_push($horas, $hora);
        }
        return $horas;
    }


    public function getAllValidadores()
    {

        /*$data = Yii::$app->db->createCommand("select count(1) as total from usuarios u
                join usuarios_responsabilidades ur using(user_id)
                join cat_responsabilidades cr on cr.responsabilidad_id = ur.responsabilidad_id
                where cr.descripcion = 'COTEJAR'  and u.status = 'ACTIVO'")->queryOne()['total'];*/

        $dias = ['2021-12-20','2021-12-21','2021-12-22','2021-12-27','2021-12-28','2021-12-29','2022-01-03','2022-01-04'];

        if(in_array( date('Y-m-d'),$dias)){
            return 1;
        }

        return 2;

    }


    public function actionVerifyVisit()
    {
        $user_id = Yii::$app->user->getId();

        $data = [];
        $status = false;
        if (($model = Calendar::find()->where(['and', ['user' => $user_id], ['status' => 'CONFIRMADA']])->asArray()->one()) !== null) {
            $data = $model;
            $status = true;
        }

        return json_encode(['status' => $status, 'data' => $data]);
    }

    public function actionCancel()
    {

        $model = $this->findModel();
        $status = 'error';
        $msg = 'Error inesperado vualva a intentarlo';
        $date = date('Y-m-d');
        if (isset($model->visit_time) && !empty($model->visit_time)) {
            $nuevafecha = strtotime('-2 day', strtotime($model->visit_day));
            $nuevafecha = date('Y-m-d', $nuevafecha);
            if ($nuevafecha >= $date) {
                $model->status = 'CANCELADA';
                if ($model->save(false)) {
                    if (($pro = Provider::find()->where(['user_id' => $model->user])->one()) !== null) {
                        $pro->status_cotejar = 'PENDIENTE AGENDAR CITA';
                        $pro->save(false);
                        self::sendEmail('/provider/correos/cita_cancelada', null, $pro->email, 'Cita cancelada', ['dia' => $model->visit_day, 'hora' => $model->visit_time], null);
                    }

                    $status = 'success';
                    $msg = 'Cita cancelada exitosamente.';
                } else {
                    $status = 'error';
                    $msg = 'Error al intentar cancelar la cita, intente de nuevo o contacte al administrador.';
                }
            } else {
                $status = 'error';
                $msg = 'Se vencío el limite para poder cancelar la cita.';
            }
        }
        Yii::$app->session->setFlash($status, $msg);
        return $this->redirect(['calendar']);
    }


    public function notAssist()
    {

        $dateNow = date('Y-m-d');


        $assist = false;
        $nuevafecha = null;

        if (($model = Calendar::find()->where(['user' => Yii::$app->user->getId()])->orderBy(['visit_day' => SORT_DESC])->one()) !== null) {
            if ($model->status_visit == 'NO ASISTIO') {

                $notAssist = NotAssist::find()->select('number_not_assist')->where(['user_id' => Yii::$app->user->getId()])->one()['number_not_assist'];
                $days = null;

                if ($notAssist == null || $notAssist == 0 || $notAssist == 1) {
                    $days = 3;
                } else if ($notAssist !== null && $notAssist == 2) {
                    $days = 5;
                } else if ($notAssist > 2) {
                    $days = 10;
                }


                if ($days !== null) {

                    $date = $model->visit_day;
                    $nuevafecha = $this->diasAmonestacionHabiles($date, $days);
                    $dateHoliday = $this->diasInhabiles($date, $nuevafecha);
                    if ($nuevafecha >= $dateNow) {
                        if ($dateHoliday > 0) {
                            $nuevafecha = $this->diasAmonestacionHabiles($nuevafecha, $dateHoliday);
                        }
                        $assist = true;
                    }
                }

            }

        }

        return ['status' => $assist, 'fechaAmonestacion' => $nuevafecha];

    }


    function diasAmonestacionHabiles($fecha, $nDias)
    {
        $fechaCita = strtotime($fecha);
        $dia = date('N', $fechaCita);
        $totaldias = $dia + $nDias;
        $fSemana = intval($totaldias / 5) * 2;
        $sabado = $totaldias % 5;
        $sabado == 6 ? $fSemana++ : '';
        $sabado == 0 ? $fSemana = $fSemana - 2 : '';
        $total = (($nDias + $fSemana) * 86400) + $fechaCita;
        $fechafinal = date('Y-m-d', $total);

        return $fechafinal;
    }


    function diasInhabiles($fechaInicio, $fechaFin)
    {


        $count = NonWorkingDays::find()->where(['between', 'non_working_day', $fechaInicio, $fechaFin])->count('1');

        return $count;

    }

    public function actionAgendar()
    {
        $searchModel = new CalendarSearch();
        $dataProvider = $searchModel->search(Yii::$app->request->queryParams);

        return $this->render('agendar', [
            'searchModel' => $searchModel,
            'dataProvider' => $dataProvider
        ]);

    }
}