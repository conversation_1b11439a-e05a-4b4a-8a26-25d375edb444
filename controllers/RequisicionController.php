<?php

namespace app\controllers;

use app\helpers\GeneralController;
use app\models\CondicionesPago;
use app\models\GrupoProducto;
use app\models\LugarEntrega;
use app\models\NegociadorGrupoProducto;
use app\models\Partida;
use app\models\Producto;
use app\models\Provider;
use app\models\ProviderQuery;
use app\models\ProviderSearch;
use app\models\RequisicionProducto;
use app\models\RequisicionProductoSearch;
use app\models\RequisicionStatus;
use app\models\SolicitudCotizacion;
use app\models\SolicitudCotizacionSearch;
use app\models\SolicitudDetalle;
use app\models\UnidadMedida;
use app\models\UsuarioNegociadorSearch;
use app\models\Usuarios;
use app\models\UsuariosSearch;
use Yii;
use app\models\Model;
use app\models\Requisicion;
use app\models\RequisicionSearch;
use yii\db\Query;
use yii\helpers\Json;
use yii\web\Controller;
use yii\web\NotFoundHttpException;
use yii\filters\VerbFilter;
use app\models\Dependencia;
use yii\helpers\ArrayHelper;
use yii\web\Session;

/**
 * RequisicionController implements the CRUD actions for Requisicion model.
 */
class RequisicionController extends GeneralController
{
    /**
     * @inheritdoc
     */
    public function behaviors()
    {
        return [
            'verbs' => [
                'class' => VerbFilter::class,
                'actions' => [
                    'delete' => ['POST'],
                ],
            ],
        ];
    }
    /**
     * Lists all Requisicion models.
     * @return mixed
     */
    public function actionIndex()
    {
        $searchModel = new RequisicionSearch();
        $dataProvider = $searchModel->search(Yii::$app->request->queryParams);
        $dataProviderRechazo = $searchModel->search(Yii::$app->request->queryParams,'rechazoFilter');
        $etiquetas = [
            'admin' => 'Requisiciones capturadas',
            'captura' => 'Requisiciones capturadas',
            'validardep' => 'Pendientes validar dep',
            'validarsec' => 'Pendientes validar sec',
            'validarctl' => 'Validar control op'
        ];
        $id = Yii::$app->user->can(Usuarios::ROLE_CAPTURISTA)? 'captura' :(
            Yii::$app->user->can(Usuarios::ROLE_VALIDADOR_DEPENDENCIA) ? 'validardep' :(
                Yii::$app->user->can(Usuarios::ROLE_VALIDADOR_SECRETARIA) ? 'validarsec' :(
                    Yii::$app->user->can(Usuarios::ROLE_CONTROL_OPERACIONES) ? 'validarctl':
                        'admin')));
        $etirec = [
            'rechazado' => 'Requisiciones rechazadas'
        ];
        $idrec = Yii::$app->user->can(Usuarios::ROLE_CAPTURISTA) ? 'rechazado' :'';
        return $this->render('index', [
            'searchModel' => $searchModel,
            'dataProvider' => $dataProvider,
            'dataProviderRechazo' => $dataProviderRechazo,
            'etirec' => $etirec,
            'etiquetas' => $etiquetas,
            'id' => $id,
            'idrec' => $idrec
        ]);
    }

    public function actionConsolidadas(){

        return $this->redirect(['../requisicion/negociador']);

    }

    public function actionIndexNegociador()
    {
        $searchModel = new RequisicionSearch();
        $dataProvider = $searchModel->search(Yii::$app->request->queryParams,'negociadorFilter');
        $porcotizacion = $searchModel->search(Yii::$app->request->queryParams,'cotizacionProviderFilter');
        $pro_ganador = $searchModel->search(Yii::$app->request->queryParams,'determinaProveedorGanadorFilter');
        $rechazados =  $searchModel->search(Yii::$app->request->queryParams,'fallosRechazadosFilter');
        $orden_compra =  $searchModel->search(Yii::$app->request->queryParams,'ordenCompraFilter');
        $data = LugarEntrega::find()->getConComboLugarEntrega();
        $etiquetas = [
            'admin' => 'Por negociar',
            'negociador' => 'Por negociar',
        ];
        $et_cotizadas = [
            'cotizadas' => 'Seguimiento cotizaciones'
        ];
        $id = Yii::$app->user->can(Usuarios::ROLE_NEGOCIADOR) || Yii::$app->user->can(Usuarios::ROLE_DIRECTOR)? 'negociador' :
                        'admin';
        $id_cotizadas = Yii::$app->user->can(Usuarios::ROLE_NEGOCIADOR) ? 'cotizadas':'';

        if (Yii::$app->request->post('hasEditable'))
        {
            $id = Yii::$app->request->post('editableKey');
            $model  = Requisicion::findOne($id) ;
            $posted = current($_POST['Requisicion']);
            if ($posted) {
                if(isset($posted['vigencia_requisicion'])){
                    $model->vigencia_requisicion = $posted['vigencia_requisicion'];
                }else if(isset($posted['lugar_entrega'])){
                    $model->lugar_entrega = $posted['lugar_entrega'];
                }
                $model->Update();
            }

            return '{"output":"","message":""}';
        }
        return $this->render('index-negociador', [
            'searchModel' => $searchModel,
            'dataProvider' => $dataProvider,
            'porcotizacion' => $porcotizacion,
            'etiquetas' => $etiquetas,
            'id' => $id,
            'et_cotizadas' => $et_cotizadas,
            'id_cotizadas' => $id_cotizadas,
            'pro_ganador' => $pro_ganador,
            'rechazados' => $rechazados,
            'orden_compra' => $orden_compra,
            'data' => $data
        ]);
    }

    public function actionRequisicionesPorNegociador(){

        $searchModel = new UsuariosSearch();
        $dataProvider = $searchModel->search(Yii::$app->request->queryParams);
        if (Yii::$app->request->post('hasEditable'))
        {
            $id = Yii::$app->request->post('editableKey');
            $model  = Requisicion::findOne($id);
            $posted = current($_POST['Requisicion']);
            if ($posted) {
                $model->user_negociador_id = $posted['fullName'];
                $model->Update();
            }
            return '{"output":"","message":""}';
        }
        return $this->render('requisicionesPorNegociador', [
            'searchModel' => $searchModel,
            'dataProvider' => $dataProvider,
        ]);

    }

    public function actionConfirm($id,$tipo_adjudicacion)
    {
        $cont = count(ArrayHelper::getColumn(SolicitudCotizacion::find()->select('solicitud_id')->where(['and',['requisicion_id'=> $id],['status' =>'VALIDADA']])->all(),'solicitud_id'));
        if(($tipo_adjudicacion==Requisicion::ADJ_TRES_COTIZACIONES && $cont >= 3) || ($tipo_adjudicacion ==Requisicion::ADJ_DIRECTA && $cont != 0)){
            $requisicion = Requisicion::findOne($id);
            $requisicion->status = Requisicion::SELECCIONAR_GANADORES;
            $requisicion->Update();
            Yii::$app->session->setFlash('success', 'La requisición ha sido cerrada exitosamente.');
            return Yii::$app->getResponse()->redirect(['../requisicion/index-negociador']);
        }else{
            if($tipo_adjudicacion==Requisicion::ADJ_TRES_COTIZACIONES){
                Yii::$app->session->setFlash('error', 'Se necesita cotizar mínimo 3 proveedores.');
                return Yii::$app->getResponse()->redirect(['../requisicion/index-negociador']);
            }else if($tipo_adjudicacion==Requisicion::ADJ_DIRECTA){
                Yii::$app->session->setFlash('error', 'Aún no te ha cotizado ningun proveedor.');
                return Yii::$app->getResponse()->redirect(['../requisicion/index-negociador']);
            }
        }
        return $this->redirect(['../requisicion/index-negociador']);
    }

    public function actionIndexCoordinador()
    {
        $searchModel = new RequisicionSearch();
        $dataProvider = $searchModel->search(Yii::$app->request->queryParams,'coordinadorValidarFilter');

        return $this->render('index-coordinador', [
            'searchModel' => $searchModel,
            'dataProvider' => $dataProvider
        ]);

    }

    public function actionIndexDirector()
    {
        $searchModel = new RequisicionSearch();
        $dataProvider = $searchModel->search(Yii::$app->request->queryParams,'directorValidarFilter');

        return $this->render('index-director', [
            'searchModel' => $searchModel,
            'dataProvider' => $dataProvider
        ]);

    }

    public function actionSolicitarCotizacion($id)
    {
        $searchModel = new RequisicionProductoSearch();
        $searchModel->requisicion_id = $id;
        $dataProvider = $searchModel->search(Yii::$app->request->queryParams);
        $model = $this->findModel($id);
        $products = Producto::find()->getProductoRequi($id);
        $provider = Requisicion::find()->getProviderCombo();
        $cond_pago = CondicionesPago::find()->getConCombo();
        $data =  ArrayHelper::map(UnidadMedida::find()->select('abreviacion')->asArray()->all(),'abreviacion','abreviacion');
        if (Yii::$app->request->post('hasEditable'))
        {
            $id = Yii::$app->request->post('editableKey');
            $model  = RequisicionProducto::findOne($id) ;
            $posted = current($_POST['RequisicionProducto']);
            if ($posted) {
                $model->um = $posted['um'];
                $model->save(false);
            }
            return '{"output":"","message":""}';
        }
        return $this->render('solicitar-cotizacion', [
            'products' => $products,
            'model' => $model,
            'provider' => $provider,
            'cond_pago' => $cond_pago,
            'searchModel' => $searchModel,
            'dataProvider' => $dataProvider,
            'data' => $data
        ]);
    }

    public function actionDetallesOrdenCompra($requi_id){

        if(!Yii::$app->request->post()){
            return "Error";
        }

        Yii::$app->session['observaciones'] = isset(Yii::$app->request->post()['observaciones'])?Yii::$app->request->post()['observaciones']:'';
        Yii::$app->session['seleccionados'] = isset(Yii::$app->request->post()['seleccionados'])?Yii::$app->request->post()['seleccionados']:'';
        Yii::$app->session['total'] = isset(Yii::$app->request->post()['total'])?Yii::$app->request->post()['total']:'';
        Yii::$app->session['subtotal'] = isset(Yii::$app->request->post()['subtotal'])?Yii::$app->request->post()['subtotal']:'';
        Yii::$app->session['iva_total'] = isset(Yii::$app->request->post()['iva_total'])?Yii::$app->request->post()['iva_total']:'';
        $data_provider = [];
            foreach (Yii::$app->session['seleccionados'] as $key => $data) {
                $des = SolicitudDetalle::findBySql("select sd.requisicion_producto_id,rp.descripcion_corta,p.name_comercial, sd.solicitud_id,pr.nombre,
                                                sc.plazo_pagar,sc.anticipo,sc.retenciones,sc.observaciones
                                                from solicitud_detalle sd
                                                join requisicion_producto rp using(requisicion_producto_id)
                                                join producto pr on pr.producto_id = rp.producto_id
                                                join solicitud_cotizacion sc on sd.solicitud_id = sc.solicitud_id
                                                join provider p on sc.provider_id = p.provider_id
                                                where sd.solicitud_detalle_id in($data)")
                    ->asArray()->all();
                array_push($data_provider,$des);
        }
        $cond_pago = ArrayHelper::map(CondicionesPago::find()->select(['condiciones_pago_id','descripcion'])
            ->asArray()->all(),'condiciones_pago_id','descripcion');


        return $this->render('detalles-orden-compra', [
                'data_provider' => $data_provider,
                'cond_pago' => $cond_pago
        ]);

    }

    public function actionFallo($requi_id){

        $trans = Yii::$app->db->beginTransaction();
        if(Yii::$app->request->post()) {
            $prod_desc = Yii::$app->request->post()['requiProd'];
            $data_orden_compra = Yii::$app->request->post()['datos'];
            \Yii::$app->db->createCommand("update solicitud_detalle set ganador = false where solicitud_id in (
                            select solicitud_id from solicitud_cotizacion where requisicion_id = :id)")
                            ->bindValue(':id', $requi_id)
                            ->execute();
            $observaciones = Yii::$app->session['observaciones'];
            $seleccionados = Yii::$app->session['seleccionados'];
            $total = Yii::$app->session['total'];
            $subtotal = Yii::$app->session['subtotal'];
            $iva_total = Yii::$app->session['iva_total'];
            $exito = true;
            $valid = true;
            $val_des = true;
            $data_solicitud = true;
            foreach ($seleccionados as $key => $data) {
                $detalle = SolicitudDetalle::findOne($data);
                $detalle->ganador = true;
                if (!empty($detalle->getDirtyAttributes())) {
                    $exito = $exito && $detalle->Update();
                }
            }
            if ($exito) {
                $requisicion = Requisicion::findOne($requi_id);
                $requisicion->status = $requisicion->tipo_requisicion=='Material'?Requisicion::VALIDAR_DIRECTOR_MATERIAL:Requisicion::VALIDAR_DIRECTOR_SERVICIO;
                $requisicion->observacion_fallo = $observaciones;
                $requisicion->subtotal = $subtotal;
                $requisicion->iva_total = $iva_total;
                $requisicion->total = $total;
                $valid = $requisicion->Update();

                $req_status = RequisicionStatus::find()->select('status_requisicion_id')->where(['and', ['requisicion_id' => $requi_id], ['status' => 'PENDIENTE']])->one();
                $requi_status = $req_status['status_requisicion_id'];
                if (isset($requi_status) && !empty($requi_status)) {
                    $requi_status = RequisicionStatus::findOne($requi_status);
                    $requi_status->status = RequisicionStatus::STATUS_TERMINADO;
                    $requi_status->Update();
                }
            }if($valid){
                foreach ($prod_desc as $key => $data) {
                    $detalle = RequisicionProducto::findOne($key);
                    $detalle->descripcion_corta = $data;
                    if (!empty($detalle->getDirtyAttributes())) {
                        $val_des = $val_des && $detalle->Update();
                    }
                }
            }if($val_des){
                foreach ($data_orden_compra as $key => $data) {
                    $detalle_sol = SolicitudCotizacion::findOne($key);
                    $detalle_sol->plazo_pagar = $data['plazo_pagar'];
                    $detalle_sol->anticipo = $data['anticipo'];
                    $detalle_sol->retenciones = $data['retenciones'];
                    $detalle_sol->observaciones = $data['observaciones'];
                    if (!empty($detalle_sol->getDirtyAttributes())) {
                        $data_solicitud = $data_solicitud && $detalle_sol->Update();
                    }
                }
                $trans->commit();
                return $this->redirect(['../requisicion/index-negociador']);
            }


            $trans->rollBack();
        }
        else{
            return $this->redirect(['../requisicion/index-negociador']);
        }

    }

    /**d
     * Displays a single Requisicion model.
     * @param string $id
     * @return mixed
     */
    public function actionView($id)
    {
        $model = $this->findModel($id);
        $model_status = new RequisicionStatus();
        $reqProducto = $this->findModelDetalleProd($id);
        return $this->renderAjax('view', [
            'model' => $model,
            'model_status' => $model_status,
            'reqProducto' => $reqProducto
        ]);
    }

    /**
     * Creates a new Requisicion model.
     * If creation is successful, the browser will be redirected to the 'view' page.
     * @return mixed
     */
    public function actionCreate()
    {
        $model = new Requisicion();
        $reqProducto = [new RequisicionProducto];

        if ($model->load(Yii::$app->request->post())) {

            $query = new Query();
            if($model->grupoProducto->nombre == 'VEHICULOS'){
                $query->select(['ud.user_id'])
                    ->from('user_dependencia ud')
                    ->join('join','usuarios u','u.user_id = ud.user_id')
                    ->where(['and',['ud.dependencia_id' => $model->dependencia_id],['u.role' => 'NEGOCIADOR']]);
                $command = $query->createCommand();
                $negociador_id = $command->queryOne()['user_id'];
            }else{
                $query->select(['ng.user_negociador_id'])
                    ->from('negociador_grupo_producto ng')
                    ->join('join','usuarios u','u.user_id = ng.user_negociador_id')
                    ->where(['and',['ng.grupo_producto_id' => $model->grupo_producto_id],['u.role' => 'NEGOCIADOR']]);
                $command = $query->createCommand();
                $negociador_id = $command->queryOne()['user_negociador_id'];
            }


            $tipo_requi = $model->total_estimado<(Requisicion::CUOTA*2000)?Requisicion::ADJ_DIRECTA:(
                $model->total_estimado<(Requisicion::CUOTA*14400) && $model->total_estimado>(Requisicion::CUOTA*2000)?Requisicion::ADJ_TRES_COTIZACIONES:(
                $model->total_estimado<(Requisicion::CUOTA*24000) && $model->total_estimado>(Requisicion::CUOTA*14400)?Requisicion::ADJ_INV_RESTRINGIDA:Requisicion::ADJ_LICITACION_PUB));

            $model->user_negociador_id = $negociador_id;
            $model->created_by = Yii::$app->user->id;
            $model->tipo_adjudicacion = $tipo_requi;
            $model->status = 'PENDIENTE VALIDAR DEPENDENCIA';
            $model->lugar_entrega = 'Entrega en Almacén';
            $factual = date("Y-m-d H:i:s");
            $model->created_date = $factual;
            $reqProducto = Model::createMultiple(RequisicionProducto::class);
            Model::loadMultiple($reqProducto, Yii::$app->request->post());
            // validacion de los modelos
            $validar = $model->validate();
            $validar = Model::validateMultiple($reqProducto) && $validar;

            if ($validar) {
                $transaction = \Yii::$app->db->beginTransaction();
                try {
                    if ($flag = $model->save(false)) {
                        foreach ($reqProducto as $reqProd) {
                            $reqProd->requisicion_id = $model->requisicion_id;
                            if (!($flag = $reqProd->save(false))) {
                                $transaction->rollBack();
                                break;
                            }
                        }
                    }
                    if ($flag) {
                        $transaction->commit();
                        return $this->redirect(['index']);
                    }
                } catch (\Exception $e) {
                    $transaction->rollBack();
                }
            }
        }
        $dependencias = ArrayHelper::map(Yii::$app->user->getIdentity()->dependencias, 'dependencia_id', 'nombre');

        $partida = Partida::find()->getPartidaCombo();
        return $this->render('create', [
            'model' => $model,
            'dependencias' => $dependencias,
            'partida' => $partida,
            'nombre' => Yii::$app->user->identity->nombre.' '.Yii::$app->user->identity->primer_apellido.' '.Yii::$app->user->identity->segundo_apellido,
            'telefono' => Yii::$app->user->identity->tel_enlace,
            'correo' => Yii::$app->user->identity->email,
            'reqProducto' => (empty($reqProducto)) ? [new RequisicionProducto] : $reqProducto
        ]);

    }

    /**
     * Updates an existing Requisicion model.
     * If update is successful, the browser will be redirected to the 'view' page.
     * @param string $id
     * @return mixed
     */
    public function actionUpdate($id)
    {
        $model = $this->findModel($id);
        $reqProducto = $this->findModelReqProd($id);

        if ($model->load(Yii::$app->request->post())) {
            $query = new Query();
            $query->select(['ng.user_negociador_id'])
                    ->from('negociador_grupo_producto ng')
                    ->join('join','usuarios u','u.user_id = ng.user_negociador_id')
                    ->where(['and',['ng.grupo_producto_id' => $model->grupo_producto_id],['u.role' => 'NEGOCIADOR']]);
            $command = $query->createCommand();
            $negociador_id = $command->queryOne()['user_negociador_id'];

            $factual = date("Y-m-d H:i:s");
            $model->user_negociador_id = $negociador_id;
            $model->updated_by = Yii::$app->user->id;
            $model->status = 'PENDIENTE VALIDAR DEPENDENCIA';
            $model->updated_date = $factual;
            $oldIDs = ArrayHelper::map($reqProducto, 'requisicion_producto_id', 'requisicion_producto_id');
            $reqProducto = Model::createMultiple(RequisicionProducto::class, $reqProducto);
            Model::loadMultiple($reqProducto, Yii::$app->request->post());
            $deletedIDs = array_diff($oldIDs, array_filter(ArrayHelper::map($reqProducto, 'requisicion_producto_id', 'requisicion_producto_id')));

            $valid = $model->validate();
            $valid = Model::validateMultiple($reqProducto) && $valid;

            if ($valid) {
                $transaction = \Yii::$app->db->beginTransaction();
                try {
                    if ($flag = $model->save(false)) {
                        if (!empty($deletedIDs)) {
                            RequisicionProducto::deleteAll(['requisicion_producto_id' => $deletedIDs]);
                        }
                        foreach ($reqProducto as $modelProductos) {
                            $modelProductos->requisicion_id = $model->requisicion_id;
                            if (!($flag = $modelProductos->save(false))) {
                                $transaction->rollBack();
                                break;
                            }
                        }
                    }
                    if ($flag) {
                        $status_dep = Requisicion::VALIDAR_DEP;
                        $status = RequisicionStatus::STATUS_TERMINADO;
                        $requisicion = Requisicion::findOne($id);
                        $requisicion->status = $status_dep;
                        $requisicion->update();
                        $status_id = RequisicionStatus::find()->select('status_requisicion_id')
                            ->where('requisicion_id='.$id.' and status=\'PENDIENTE\'')->one()->status_requisicion_id;
                        $requi_status = RequisicionStatus::findOne($status_id);
                        $requi_status->status = $status;
                        $requi_status->update();
                        $transaction->commit();
                        return $this->redirect(['index']);
                    }
                } catch (\Exception $e) {
                    $transaction->rollBack();
                }
            }
        }
        $dependencias = ArrayHelper::map(Yii::$app->user->getIdentity()->dependencias, 'dependencia_id', 'nombre');
        $grupo_producto = GrupoProducto::find()->getGrupoProdComboTipo($id);
        $partida = Partida::find()->getPartidaCombo();
        $producto = Producto::find()->getProductoCombo($id);
        $rechazo = RequisicionStatus::find()->getStatus($id);
        return $this->render('update', [
            'model' => $model,
            'dependencias' => $dependencias,
            'grupo_producto' => $grupo_producto,
            'producto' => $producto,
            'partida' => $partida,
            'rechazo' => $rechazo,
            'reqProducto' => (empty($reqProducto)) ? [new RequisicionProducto] : $reqProducto
        ]);
    }

    /**
     * Deletes an existing Requisicion model.
     * If deletion is successful, the browser will be redirected to the 'index' page.
     * @param string $id
     * @return mixed
     */
    public function actionDelete($id)
    {
        $this->findModel($id)->delete();

        return $this->redirect(['index']);
    }


    public function actionValidar_dep($requi_id)
    {
        $factual = date("Y-m-d H:i:s");
        $id = Yii::$app->user->id;
        $requisicion = Requisicion::findOne($requi_id);
        $requisicion->valid_dep_date = $factual;
        $requisicion->status = Requisicion::VALIDAR_SEC;
        $requisicion->validador_dependencia_id = $id;
        if ($requisicion->update()) {
            return $this->redirect(['index']);
        }
    }

    public function actionValidar_sec($requi_id)
    {
        $factual = date("Y-m-d H:i:s");
        $id = Yii::$app->user->id;
        $requisicion = Requisicion::findOne($requi_id);
        $requisicion->valid_sec_date = $factual;
        $requisicion->status = Requisicion::VALIDAR_CTL_OPERACIONES;
        $requisicion->validador_secretaria_id = $id;
        if ($requisicion->update()) {
            return $this->redirect(['index']);
        }
    }

    public function actionValidar_ctl_op($requi_id)
    {
        $factual = date("Y-m-d H:i:s");
        $id = Yii::$app->user->id;
        $requisicion = Requisicion::findOne($requi_id);
        $requisicion->validador_ctr_op_date = $factual;
        $requisicion->status = Requisicion::PENDIENTE_NEG;
        $requisicion->validador_ctr_op_id = $id;
        if ($requisicion->update()) {
            return $this->redirect(['index']);
        }
    }

    public function actionDownload($provider_id,$requisicion_id)
    {
        //Si el directorio existe
        $file = $provider_id.'.pdf';
        $path="ordenesCompra/".$requisicion_id."/";
        $fullPath = $path.$file;


        $size = filesize($fullPath);
        header("Content-Type: application/force-download");
        header("Content-Disposition: attachment; filename=$file");
        header("Content-Transfer-Encoding: binary");
        header("Content-Length: " . $size);
        // Descargar archivo
        readfile($fullPath);
    }

    /**
     * Finds the Requisicion model based on its primary key value.
     * If the model is not found, a 404 HTTP exception will be thrown.
     * @param string $id
     * @return Requisicion the loaded model
     * @throws NotFoundHttpException if the model cannot be found
     */
    protected function findModel($id, $dependenciasArr = false)
    {
        if (($model = Requisicion::findOne($id)) !== null) {
            if ($dependenciasArr) {
                $model->dependenciasArr = ArrayHelper::getColumn($model->dependencias, 'dependencia_id');
            }
            return $model;
        } else {
            throw new NotFoundHttpException('The requested page does not exist.');
        }
    }

    public function findModelReqProd($id)
    {
        $model = RequisicionProducto::find()->where(['requisicion_id' => $id])->all();
        return $model;
    }

    public function findModelDetalleProd($id)
    {
        $model = RequisicionProducto::findBySql('select p.nombre as producto,rp.cantidad,rp.precio_referencia as precio, rp.descripcion_corta,rp.um as unidad
                                        from requisicion_producto rp
                                        join producto p on p.producto_id = rp.producto_id
                                        join unidad_medida u on u.unidad_medida_id = p.unidad_medida_id
                                        where rp.requisicion_id =' . $id)->asArray()->all();
        return $model;
    }

    public function actionList_grupo_prod($tipo)
    {
        $grupo_pro =  ArrayHelper::map(GrupoProducto::find()->select(['grupo_producto_id','nombre'])
                ->where(['tipo' => $tipo])
                ->orderBy(['nombre'=>SORT_ASC])
                ->asArray()->all(),'grupo_producto_id','nombre');

        echo "<option></option>";
        if (count($grupo_pro) > 0) {
            foreach ($grupo_pro as $key => $value) echo "<option value='" . $key . "'>" . $value . "</option>";
        } else {
            echo "<option>No hay productos</option>";
        }
    }

    public function actionList_prod($id)
    {
        $productos = ArrayHelper::map(Producto::find()->select(['producto_id', 'concat_ws(\' \',clave,nombre) as nombre_pro'])
                ->where(['grupo_producto_id' => $id])
                ->asArray()->all(), 'producto_id', 'nombre_pro');

        echo "<option></option>";
        if (count($productos) > 0) {
            foreach ($productos as $key => $value) echo "<option value='" . $key . "'>" . $value . "</option>";
        } else {
            echo "<option>No hay productos</option>";
        }
    }
}
