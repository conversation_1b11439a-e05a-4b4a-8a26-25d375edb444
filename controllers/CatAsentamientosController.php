<?php

namespace app\controllers;

use app\helpers\GeneralController;
use app\models\CatEntidades;
use app\models\CatMunicipios;
use app\models\CatTipoAsentamientos;
use Yii;
use app\models\CatAsentamientos;
use app\models\CatAsentamientosSearch;
use yii\helpers\ArrayHelper;
use yii\web\Controller;
use yii\web\NotFoundHttpException;
use yii\filters\VerbFilter;

/**
 * CatAsentamientosController implements the CRUD actions for CatAsentamientos model.
 */
class CatAsentamientosController extends GeneralController
{
    /**
     * {@inheritdoc}
     */
    public function behaviors()
    {
        return [
            'verbs' => [
                'class' => VerbFilter::className(),
                'actions' => [
                    'delete' => ['POST'],
                ],
            ],
        ];
    }

    /**
     * Lists all CatAsentamientos models.
     * @return mixed
     */
    public function actionIndex()
    {
        $searchModel = new CatAsentamientosSearch();
        $dataProvider = $searchModel->search(Yii::$app->request->queryParams);

        return $this->render('index', [
            'searchModel' => $searchModel,
            'dataProvider' => $dataProvider,
        ]);
    }

    /**
     * Displays a single CatAsentamientos model.
     * @param integer $id
     * @return mixed
     * @throws NotFoundHttpException if the model cannot be found
     */
    public function actionView($id)
    {
        return $this->renderAjax('view', [
            'model' => $this->findModel($id),
        ]);
    }

    /**
     * Creates a new CatAsentamientos model.
     * If creation is successful, the browser will be redirected to the 'view' page.
     * @return mixed
     */
    public function actionCreate()
    {
        $model = new CatAsentamientos();

        if ($model->load(Yii::$app->request->post())) {
            $model->cv_asentamiento = '00000';
            if($model->save()){
                return $this->redirect(['view', 'id' => $model->asentamiento_id]);
            }
        }

        $tipoA = ArrayHelper::map(CatTipoAsentamientos::find()->select(['tipo_asentamiento_id','nombre'])->all(),'tipo_asentamiento_id','nombre');
        $estados = ArrayHelper::map(CatEntidades::find()->select(['entidad_id','nombre'])->all(),'entidad_id','nombre');
        $munic = [];
        return $this->render('create', [
            'model' => $model,
            'tipoA' => $tipoA,
            'estados' => $estados,
            'munic' => $munic
        ]);
    }

    /**
     * Updates an existing CatAsentamientos model.
     * If update is successful, the browser will be redirected to the 'view' page.
     * @param integer $id
     * @return mixed
     * @throws NotFoundHttpException if the model cannot be found
     */
    public function actionUpdate($id)
    {
        $model = $this->findModel($id);

        if ($model->load(Yii::$app->request->post())) {
            if(!$model->cv_asentamiento){
                $model->cv_asentamiento = '00000';
            }
            if($model->save()){
                return $this->redirect(['index', 'id' => $model->asentamiento_id]);
            }
        }

        $munic = [];

        $tipoA = ArrayHelper::map(CatTipoAsentamientos::find()->select(['tipo_asentamiento_id','nombre'])->all(),'tipo_asentamiento_id','nombre');
        $estados = ArrayHelper::map(CatEntidades::find()->select(['entidad_id','nombre'])->all(),'entidad_id','nombre');

        if($model->entidad_id){
            $munic = ArrayHelper::map(CatMunicipios::find()->select(['municipio_id','nombre'])->where(['entidad_id' => $model->entidad_id])->all(),'municipio_id','nombre');

        }

        return $this->renderAjax('update', [
            'model' => $model,
            'tipoA' => $tipoA,
            'estados' => $estados,
            'munic' => $munic
        ]);
    }

    /**
     * Deletes an existing CatAsentamientos model.
     * If deletion is successful, the browser will be redirected to the 'index' page.
     * @param integer $id
     * @return mixed
     * @throws NotFoundHttpException if the model cannot be found
     */
    public function actionDelete($id)
    {
        $this->findModel($id)->delete();

        return $this->redirect(['index']);
    }

    /**
     * Finds the CatAsentamientos model based on its primary key value.
     * If the model is not found, a 404 HTTP exception will be thrown.
     * @param integer $id
     * @return CatAsentamientos the loaded model
     * @throws NotFoundHttpException if the model cannot be found
     */
    protected function findModel($id)
    {
        if (($model = CatAsentamientos::findOne($id)) !== null) {
            return $model;
        }

        throw new NotFoundHttpException('The requested page does not exist.');
    }
}
