<?php

namespace app\controllers;

use app\helpers\GeneralController;
use app\models\RegistroCorreos;
use app\models\RegistroCorreosSearch;
use app\models\Usuarios;
use Exception;
use Yii;

class EmailController extends GeneralController{

    public function actionRegistro(){
        $searchModel = new RegistroCorreosSearch();
        $dataProvider = $searchModel->search(Yii::$app->request->queryParams);

        return $this->render("index", [
            'searchModel' => $searchModel,
            'dataProvider' => $dataProvider,
        ]);
    }

    public function actionCreate($id=null){
        $model = $this->findRegistro($id);
        $filtro = ['TODOS' => 'Todos', 'ACTIVOS' => 'Activos', 'INACTIVOS' => 'Inactivos'];
        if (Yii::$app->user->can(Usuarios::ROLE_ADMIN_CONTRATISTAS)) {
            $filtro = array_merge($filtro, ['OBRA' => 'Tipo de Obra', 'ESPECIALIDAD' => 'Especialidad']);
        }else if(Yii::$app->user->can(Usuarios::ROLE_ADMIN_PROVIDER)){
            $filtro = array_merge($filtro, ['DIVISION' => 'Division', 'GRUPO' => 'Grupo', /* 'CLASE' => 'Clase', 'PRODUCTO' => 'Producto' */]);
        }

        return $this->render("form", [
            'model' => $model,
            'filtro_admin' => $filtro,
            'isUpdate' => is_null($id) ? 'false' : 'true'
        ]);
    }

    //Se probo con datos dummy pero falta probar en otros ambientes
    public function actionEnviar(){
        try{
            $data = Yii::$app->request->post();
            $model = new RegistroCorreos();
            $model->asunto = $data['asunto'];
            $model->contenido = $data['contenido'];
            $model->url_doc = $data['file'];
            $model->filtros = $data['filtros'];
            $model->created_by = Yii::$app->user->identity->user_id;
            $model->provider_type = Yii::$app->user->can(Usuarios::ROLE_ADMIN_PROVIDER) ? 'bys' : 'op';            

            $destinatarios = self::getProveedoresFiltro($data['filtros']);

            //$bypass = ['<EMAIL>', '<EMAIL>'];

            GeneralController::sendMassivePrivateEmail('/provider/correos/_plantilla_generico', null, $destinatarios, $model->asunto, $model->url_doc, [
                'tipo_admin' => $model->provider_type,
                'contenido' => $model->contenido,
            ]);

            $model->save(false);

            return $this->redirect(["registro"]);
        }catch( Exception $e){ return json_encode(['tipo' => 'Error', 'message' => 'Ocurrio un error al enviar los correos. Conctacte a soporte', 'error' => $e->getMessage()]); }
    }

    /**
     * Retorna los destinatarios (emails) de acuerdo a los filtros solicitados
     * @param array $filtros Son los criterios de filtrado de proveedores, con la siguiente estructura
     *    $params = [
     *      'tipo'     => (string) Tipo de filtro a aplicar.
     *      'valores' => (array) Arreglo de valores a utilizar en el filtro
     *    ]
     * @return mixed Los correos de los proveedores obtenidos por el filtro
     *  */
    public function getProveedoresFiltro($filtros){
        $tipo = $filtros['tipo'];
        $valores = isset($filtros['valores']) ? $filtros['valores'] : [];
        $tipo_proveedor = Yii::$app->user->can(Usuarios::ROLE_ADMIN_PROVIDER) ? 'bys' : 'op';
        $query = '';
        if($tipo == RegistroCorreos::FILTRO_TODOS){
            $query = "SELECT DISTINCT(email) from provider WHERE tipo_provider = '$tipo_proveedor'";
        }
        if($tipo == RegistroCorreos::FILTRO_ACTIVOS || $tipo == RegistroCorreos::FILTRO_INACTIVOS){
            $query = "SELECT DISTINCT(email) from provider WHERE tipo_provider = '$tipo_proveedor' AND enabled = ".($tipo == 'ACTIVOS' ? "true" : "false");
        }
        if($tipo == RegistroCorreos::FILTRO_TIPO_OBRA){
            $query = "SELECT DISTINCT(p.email) from provider p INNER JOIN provider.experiencia ex
            ON ex.provider_id = p.provider_id INNER JOIN provider.subespecialidad_category ob ON ex.id_obra = ob.category_id
            WHERE ob.category_id in (".implode(',', array_map(function($value){ return "{$value}";}, array_values($valores))).")";
        }
        if($tipo == RegistroCorreos::FILTRO_ESPECIALIDAD){
            $query = "SELECT DISTINCT(p.email) from provider p INNER JOIN provider.experiencia ex
            ON ex.provider_id = p.provider_id INNER JOIN provider.cat_subespecialidades es ON ex.especialidades = es.subespecialidad_id
            WHERE es.subespecialidad_id in (".implode(',', array_map(function($value){ return "{$value}";}, array_values($valores))).")";
        }
        if($tipo == RegistroCorreos::FILTRO_DIVISION){
            $query = "SELECT DISTINCT(p.email) from provider p INNER JOIN public.provider_giro pg
            ON pg.provider_id = p.provider_id INNER JOIN productos.producto pp ON pg.producto_id = pp.producto_id
            INNER JOIN productos.clase pc ON pc.clase_id = pp.clase_id INNER JOIN productos.grupo ppg ON ppg.grupo_id = pc.grupo_id
            INNER JOIN productos.division pd ON pd.division_id = ppg.division_id WHERE pd.division_id 
            in (".implode(',', array_map(function($value){ return "{$value}";}, array_values($valores))).")";
        }
        if($tipo == RegistroCorreos::FILTRO_GRUPO){
            $query = "SELECT DISTINCT(p.email) from provider p INNER JOIN public.provider_giro pg
            ON pg.provider_id = p.provider_id INNER JOIN productos.producto pp ON pg.producto_id = pp.producto_id
            INNER JOIN productos.clase pc ON pc.clase_id = pp.clase_id INNER JOIN productos.grupo ppg ON ppg.grupo_id = pc.grupo_id
            WHERE ppg.grupo_id in (".implode(',', array_map(function($value){ return "{$value}";}, array_values($valores))).")";
        }

        $result = Yii::$app->db->createCommand($query)->queryAll();

        $result = array_filter($result, function($item) {
            return isset($item["email"]) && $item["email"] !== "";
        });

        $emails = array_column($result, 'email');

        return $emails;
    }

    private function findRegistro($id){
        $registro = is_null($id) ? new RegistroCorreos() : RegistroCorreos::findOne($id);
        return is_null($registro) ? new RegistroCorreos() : $registro;
    }

}

?>