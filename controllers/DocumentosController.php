<?php

namespace app\controllers;

use app\controllers\concursos\InscripcionController;
use app\helpers\FileHelper;
use app\helpers\FunctionsHelper;
use app\helpers\GeneralController;
use app\models\ClientesContratos;
use app\models\DatosValidados;
use app\models\ErrorInesperado;
use app\models\Firmar;
use app\models\FirstUpdateCertificate;
use app\models\HistoricoCertificados;
use app\models\Provider;
use app\models\ProviderGiro;
use app\models\Ubicacion;
use app\models\UltimaDeclaracion;
use app\models\Visit;
use app\models\VisitDetails;
use Yii;
use yii\web\Controller;
use yii\helpers\ArrayHelper;

class DocumentosController extends Controller{

    /* public function actionTestDoc($provider_id){
        error_reporting(E_ERROR | E_PARSE);
        $response = self::generarCertificado($provider_id, 'bys', '2024-12-30', '2024-12-30');
        var_dump($response).exit();
    } */


    //La fecha la recibe en base64 XD
    public function actionGenerarDocumentoCertificado($provider_id, $tipo, $fecha = null, $prev_cert = null){
        
        /* if( is_null($provider_id) || empty($provider_id) ){ $this->msgError(); } */

        $proveedor = Provider::findOne($provider_id);
        $visitas = Visit::find()->where(['and', ['provider_id' => $provider_id], ['status' => true]])->all();
        $vigencia = FunctionsHelper::getNuevaVigencia();

        // Yii::$app->db->createCommand()->insert('historico_certificados', [
        //     'vigencia' => $vigencia,
        // ])->execute();
        if ( !is_null($visitas) && empty($visitas) ) {
            foreach ($visitas as $visita) {
                if ($visita->modify) { Visit::updateAll(['status' => false], ['visit_id' => $visita->visit_id]);
                } else {
                    VisitDetails::deleteAll(['visit_id' => $visita->visit_id]);
                    Visit::deleteAll(['visit_id' => $visita->visit_id]);
                }
            }
        }

        if (is_null($fecha) || empty($fecha)) {
            $validacion = DatosValidados::find()->select(['created_date'])->where(['and', ['provider_id' => $provider_id], ['status_bys' => 'VALIDADO']])->orderBy(['validado_id' => SORT_DESC])->one();
            if( !is_null($validacion) ){ $fecha = $validacion->created_date; }
        }else{
            $fecha = base64_decode($fecha);
        }

        $response_certificado = self::generarCertificado($provider_id, $tipo, $vigencia, $fecha, false, $prev_cert);

        if ($response_certificado['historico_certificado_id'] && $prev_cert) {
            $fuModel = FirstUpdateCertificate::findOne(intval($prev_cert));
            $fuModel->historico_certificado_id = $response_certificado['historico_certificado_id'];
            $fuModel->save();
        }

        $proveedor->permanently_disabled = 'ACTIVO';
        $proveedor->vigencia = $vigencia;
        $proveedor->save(false);

        $subject_correo = $tipo == 'bys' ? '¡Felicidades! ya formas parte del Padrón de Proveedores': '¡Felicidades! ya formas parte del Registro Estatal de Contratistas';
        GeneralController::sendEmail('/provider/correos/certificacion', null, $proveedor->email, $subject_correo, ['tipo_provider' => $tipo], $response_certificado['documento']);
        GeneralController::AllSendNotification($proveedor->user_id, $tipo, null, 'CERTIFICADO', null, '¡Felicidades!', 'PROVIDER');
        
        GeneralController::pushNotification($proveedor->user_id, "¡Felicidades, ya formas parte del Padrón de Proveedores!");

        return $this->redirect('/first-update-certificate/index');

    }

    /**
     * 
     * MOD 18/12/2024 -> Se modifico para satisfacer la necesidad de la actualizacion del certificado. 
     * Funcion que genera y firma el certificado del proveedor
     * @param Integer $provider_id  Id del proveedor
     * @param String $tipo Tipo de proveedor (bys, op)
     * @param String $vigencia Ejemplo = '2022-12-31' Fecha de la vigencia que se mostrara en el certificado
     * @param String $fecha_generacion Ejemplo = '2022-12-31' Fecha bypass de sobreescritura de la generacion del documento. 
     * @param Boolean $actualizacion Indica si el documento a generar es un certificado o uno de actualizacion de datos.
     * @param Integer $prev_cert Id de un certificado previo, con el se actualiza un registro en FirstUpdateCertificate
     * 
     * @return Array Arreglo con el Id del historico certificado y la ruta con nombre del archivo
     *  */
    public static function generarCertificado($provider_id, $tipo, $vigencia, $fecha_generacion = null, $actualizacion = false, $prev_cert = null){

        $response = ['historico_certificado_id' => null, 'documento' => null ];

        $capacidad_financiera = [];
        $grupo_productos = [];
        $dataExperiencia = [];
        $capacidad_contratacion = null;
        $codigo_data = '';
        $qr_data = ['img' => ''];
        $firma_data = ['firmante' => '', 'cargo' => '', 'firma' => ''];
        $barcode_data = ['img' => ''];

        $proveedor = Provider::findOne($provider_id);

        FileHelper::makeDir(Firmar::pre_path);
        FileHelper::makeDir(Firmar::path);

        /* if ($tipo == 'op') {
            $dataExperiencia = Yii::$app->db->createCommand("
                    select cs.nombre as especialidad,e.id_obra as id,SUM(e.monto) as monto from provider.experiencia e
                    join provider.subespecialidad_category cs on cs.category_id = e.id_obra
                    where e.provider_id = :id and status_op = 'VALIDADO' AND e.activo is TRUE
                    group by especialidad,id order by monto DESC", [':id' => $provider_id])->queryAll();

            $capFin = Yii::$app->db->createCommand("select capacidad_contratacion_resultado from provider.capacidad_contratacion
                      where provider_id = :id ORDER BY capacidad_contratacion_id DESC", [':id' => $provider_id])->queryOne();
            
            if( isset($capFin['capacidad_contratacion_resultado']) ){
                $capacidad_contratacion = $capFin['capacidad_contratacion_resultado'];
            }
                      
            $capacidad_financiera = array_sum(ArrayHelper::getColumn($dataExperiencia, 'monto'));
        } */

        if($tipo == 'bys'){
            $grupo_productos = ProviderController::getGrupoProductosProveedor($provider_id);
        }

        /* Modificacion 9/01/2025 : Se actualiza la funcion para seprara configuraciones de actualizacion y certificacion 
            para manejar formatos distintos si se requiere */
        if( $actualizacion ) {
            $datos_pdf = [ 
                'header' => [ 'layout' => '/carta/header', 'datos' => ['tipo_provider' => $tipo] ], 
                'footer' => [ 'layout' => '/carta/certificado_footer', 'datos' => [ 'datosFirma' => $firma_data, 'datosQr' => $qr_data, 'tipo_provider' => $tipo, 'code'=> $codigo_data, 'barCode'=> $barcode_data ] ],
                'content' => [ 'layout' => "/carta/t_certificado_{$tipo}", 'datos' => [ //Actualmente este documento usa otro formato, por lo cual solo esta probado para actualizaciones
                    'productos' => $grupo_productos,
                    'proveedor' => $proveedor,
                    'dataExperiencia' => $dataExperiencia,
                    'capacidadFinanciera' => $capacidad_financiera,
                    'capFin' => $capacidad_contratacion,
                    'vigencia' => $vigencia,
                    'fecha_generacion' => $fecha_generacion,
                    'actualizacion' => $actualizacion
                ] ],
                'watermark' => [ 'image' => 'imgs/cartas/logo_sello.png', 'transparency' => 0.8 ],
                'path' => Firmar::pre_path,
                'prefix' => "{$proveedor->rfc}_certificado_proveedor",
                'identificador' => $provider_id,
            ];
        }else{
            $datos_pdf = [ 
                'header' => [ 'layout' => '/carta/marco_naranja', 'datos' => ['tipo_provider' => $tipo] ], 
                'footer' => [ 'layout' => '/carta/certificado_footer_2025', 'datos' => [ 'datosFirma' => $firma_data, 'datosQr' => $qr_data, 'tipo_provider' => $tipo, 'code'=> $codigo_data, 'barCode'=> $barcode_data ] ],
                'content' => [ 'layout' => "/carta/t_certificado_2025_bys", 'datos' => [ //Actualmente este documento usa otro formato, por lo cual solo esta probado para actualizaciones
                    'productos' => $grupo_productos,
                    'proveedor' => $proveedor,
                    'dataExperiencia' => $dataExperiencia,
                    'capacidadFinanciera' => $capacidad_financiera,
                    'capFin' => $capacidad_contratacion,
                    'vigencia' => $vigencia,
                    'fecha_generacion' => $fecha_generacion,
                    'actualizacion' => $actualizacion
                ] ],
                'watermark' => [ 'image' => 'imgs/cartas/logo_circular_marca_agua_2025.png', 'transparency' => 0.8 ],
                'path' => Firmar::pre_path,
                'prefix' => "{$proveedor->rfc}_certificado_proveedor",
                'identificador' => $provider_id,
            ];
        }

        $base_filename = FileHelper::generatePdf($actualizacion ? 'Actualizacion' : 'Certificado', $datos_pdf);
        $final_filename = str_replace(Firmar::pre_path, Firmar::path, $base_filename);

        $datos_firma = FirmaController::generarFirmaWS($provider_id, $tipo, $base_filename);

        $codigo_data = $datos_firma['datosCodigo'];
        $firma_data = $datos_firma['datosFirma'];
        $qr_data = $datos_firma['datosQr'];
        $barcode_data = $datos_firma['datosBarCode'];

        $datos_pdf['footer']['datos'] = [ 'datosFirma' => $firma_data, 'datosQr' => $qr_data, 'tipo_provider' => $tipo, 'code'=> $codigo_data, 'barCode'=> $barcode_data ];
        $datos_pdf['path'] = Firmar::path;

        $final_filename = FileHelper::generatePdf($actualizacion ? 'Actualizacion' : 'Certificado', $datos_pdf, $final_filename);
        
        chmod($final_filename, 0777);

        if (file_exists($final_filename)) {
            $response['historico_certificado_id'] = ProviderController::registroCertificado($provider_id, $fecha_generacion, $codigo_data, $final_filename, $tipo, $actualizacion ? HistoricoCertificados::TIPO_ACTUALIZACION : HistoricoCertificados::TIPO_CERTIFICADO, $vigencia);
            $proveedor->permanently_disabled = 'ACTIVO';
            $proveedor->save(false);

            //$subject_correo = $tipo == 'bys' ? '¡Felicidades! ya formas parte del Padrón de Proveedores': '¡Felicidades! ya formas parte del Registro Estatal de Contratistas';
            //GeneralController::sendEmail('/provider/correos/certificacion', null, $proveedor->email, $subject_correo, ['tipo_provider' => $tipo], $final_filename);
            //GeneralController::AllSendNotification($proveedor->user_id, $tipo, null, 'CERTIFICADO', null, '¡Felicidades!', 'PROVIDER');

        } else {
            $error = new ErrorInesperado();
            $error->tipo_error = 'FIRMAR CERTIFICADO';
            $error->data = $firma_data;
            $error->user_id = Yii::$app->user->getId();
            $error->save(false);
        }

        $response['documento'] = $final_filename;

        return $response;

    }

    /**
     * Funcion que genera y firma el certificado del curso del proveedor
     * @param Integer $provider_id  Id del proveedor
     * @param String $tipo Tipo de proveedor (bys, op)
     * @param String $fecha Ejemplo = '2022-12-31'
     * 
     * @return Array Arreglo con el Id del historico certificado y la ruta con nombre del archivo
     *  */
    public static function generarConstanciaCurso($provider_id, $tipo, $fecha){
        
        $response = ['historico_certificado_id' => null, 'documento' => null ];

        $codigo_data = '';
        $qr_data = ['img' => ''];
        $firma_data = ['firmante' => '', 'cargo' => '', 'firma' => ''];
        $barcode_data = ['img' => ''];

        $proveedor = Provider::findOne($provider_id);

        $data_firmante = Provider::findBySql(" SELECT concat_ws(' ', rl.nombre, rl.ap_paterno, rl.ap_materno) AS firmante, pc.end_date_video AS fecha
            FROM provider_course pc INNER JOIN provider.representante_legal rl ON pc.representante_id = rl.representante_legal_id
            WHERE pc.provider_id = {$provider_id} ORDER BY pc.end_date_video DESC LIMIT 1")->asArray()->one();

        if( !is_null($data_firmante) && !empty($data_firmante) ){
            $proveedor->firmante = ($proveedor->tipo_persona == 'Persona moral') ? $data_firmante['firmante'] : '';
            $fecha = (!is_null($fecha) && !empty($fecha)) ? $fecha : $data_firmante['fecha'];
        }

        $datos_pdf = [ 
            'header' => [ 'layout' => '/carta/header', 'datos' => ['tipo_provider' => $tipo] ], 
            'footer' => [ 'layout' => '/carta/certificado_footer', 'datos' => [ 'datosFirma' => $firma_data, 'datosQr' => $qr_data, 'tipo_provider' => $tipo, 'code'=> $codigo_data, 'barCode'=> $barcode_data ] ],
            'content' => [ 'layout' => '/carta/certificadocurso', 'datos' => [
                'proveedor' => $proveedor,
                'date' => $fecha
            ] ],
            'watermark' => 'imgs/cartas/logo_sello.png',
            'path' => Firmar::pre_path,
            'prefix' => "{$proveedor->rfc}_certificado_proveedor_curso",
            'identificador' => $provider_id,
        ];

        $base_filename = FileHelper::generatePdf('Certificado curso', $datos_pdf);
        $final_filename = str_replace(Firmar::pre_path, Firmar::path, $base_filename);

        $datos_firma = Firmar::generarFirmaWS($provider_id, $tipo, $base_filename);

        $codigo_data = $datos_firma['datosCodigo'];
        $firma_data = $datos_firma['datosFirma'];
        $qr_data = $datos_firma['datosQr'];
        $barcode_data = $datos_firma['datosBarCode'];

        $datos_pdf['footer']['datos'] = [ 'datosFirma' => $firma_data, 'datosQr' => $qr_data, 'tipo_provider' => $tipo, 'code'=> $codigo_data, 'barCode'=> $barcode_data ];
        $datos_pdf['path'] = Firmar::path;

        $final_filename = FileHelper::generatePdf('Certificado curso', $datos_pdf, $final_filename);
        
        chmod($final_filename, 0777);

        if (file_exists($final_filename)) {
            $response['historico_certificado_id'] = HistoricoCertificados::actualizarCertificado($provider_id, $fecha, $codigo_data, $final_filename, $tipo, null, HistoricoCertificados::TIPO_CURSO);

            $subject_correo = "Acreditación de “Curso de prevención y concientización sobre faltas administrativas y hechos de corrupción”";
            GeneralController::sendEmail('/provider/correos/constancia', null, $proveedor->email, $subject_correo, ['tipo_provider' => $tipo], $final_filename);
        } else {
            $error = new ErrorInesperado();
            $error->tipo_error = 'FIRMAR CERTIFICADO CURSO';
            $error->data = $firma_data;
            $error->user_id = Yii::$app->user->getId();
            $error->save();
        }

        $response['documento'] = $final_filename;

        return $response;
    }

    /**
     * Funcion que genera y firma el certificado del curso para un proveedor excepcionado
     * @param Excepcionado $excepcionado  Instancia del modelo
     * @param String $tipo Tipo de proveedor (bys, op)
     * 
     * @return String Ruta y nombre del documento generado
     *  */
    public static function generarContanciaCursoExcepcionado($excepcionado, $tipo){
        $codigo_data = '';
        $qr_data = ['img' => ''];
        $firma_data = ['firmante' => '', 'cargo' => '', 'firma' => ''];
        $barcode_data = ['img' => ''];

        $proveedor = new Provider();
        $proveedor->rfc = $excepcionado->rfc_empresa;
        $proveedor->name_razon_social = $excepcionado->razon_social;
        $proveedor->pf_nombre = $excepcionado->razon_social;
        $proveedor->pf_ap_paterno = "";
        $proveedor->pf_ap_materno = "";
        $proveedor->tipo_persona = ($excepcionado->tipo_persona == 'PF')? Provider::PERSONA_FISICA : Provider::PERSONA_MORAL;
        $proveedor->firmante = $excepcionado->name_signatory;

        $datos_pdf = [ 
            'header' => [ 'layout' => '/carta/header', 'datos' => ['tipo_provider' => $tipo] ], 
            'footer' => [ 'layout' => '/carta/certificado_footer', 'datos' => [ 'datosFirma' => $firma_data, 'datosQr' => $qr_data, 'tipo_provider' => $tipo, 'code'=> $codigo_data, 'barCode'=> $barcode_data ] ],
            'content' => [ 'layout' => '/carta/certificadocurso', 'datos' => [
                'proveedor' => $proveedor,
                'date' => $excepcionado->time_end
            ] ],
            'watermark' => 'imgs/cartas/logo_sello.png',
            'path' => Firmar::pre_path,
            'prefix' => "{$proveedor->rfc}_certificado_proveedor_curso",
            'identificador' => $excepcionado->id,
        ];

        $base_filename = FileHelper::generatePdf('Certificado curso', $datos_pdf);
        $final_filename = str_replace(Firmar::pre_path, Firmar::path, $base_filename);

        $datos_firma = Firmar::generarFirmaWS($excepcionado->id, $tipo, $base_filename);

        $codigo_data = $datos_firma['datosCodigo'];
        $firma_data = $datos_firma['datosFirma'];
        $qr_data = $datos_firma['datosQr'];
        $barcode_data = $datos_firma['datosBarCode'];

        $datos_pdf['footer']['datos'] = [ 'datosFirma' => $firma_data, 'datosQr' => $qr_data, 'tipo_provider' => $tipo, 'code'=> $codigo_data, 'barCode'=> $barcode_data ];
        $datos_pdf['path'] = Firmar::path;

        $final_filename = FileHelper::generatePdf('Certificado curso', $datos_pdf, $final_filename);
        
        chmod($final_filename, 0777);

        return $final_filename;
    }

    /** 
     * Funcion que genera y/o firma la carta protesta del proveedor
     * @param Integer $provider_id  Id del proveedor
     * @param String $tipo Tipo de proveedor (bys, op)
     * @param ProviderCourse $curso Instancia que tiene la informacion del curso
     * @param RepresentanteLegal $representante Instancia de la clase con la informacion del representante legal
     * @param String $firma Cadena de texto que se usara como firma electronica
     * @param String $firma_corta Cadena de texto que se usara como firma electronica de la empresa
     * 
     * @return String Ruta y nombre del documento generado
    */
    public static function generarCartaProtesta($provider_id, $tipo, $curso, $representante, $firma = null, $firma_corta = null){
        $monto_mercantil = 0;
        $estado = $colonia = $municipio = "";
        $proveedor = Provider::findOne($provider_id);
        $dom_fiscal = Ubicacion::getDomicilioFiscal($provider_id);            
        $carta_template = $tipo == 'bys' ? '/carta/pdf/cartaprotesta' : ('/carta/pdf/cartaprotesta' . $tipo . ($proveedor->isPFisica() ? 'f' : 'm'));

        FileHelper::makeDir(Firmar::pre_path);
        FileHelper::makeDir(Firmar::path);

        $estado = $dom_fiscal->getNameEstado();
        $colonia = $dom_fiscal->getNameColonia();
        $municipio = $dom_fiscal->getNameCiudad();

        if($tipo == 'bys'){
            $ultima_declaracion = UltimaDeclaracion::find()->select('ingresos_mercantiles')->where(['provider_id' => $provider_id])->one();
            $monto_mercantil = !is_null($ultima_declaracion) ? $ultima_declaracion['ingresos_mercantiles'] : $monto_mercantil;
        }

        $datos_pdf = [ 
            'header' => [ 'layout' => '/carta/pdf/header_cartaprotesta', 'datos' => ['tipo_provider' => $tipo] ], 
            'footer' => [ 'layout' => '/carta/pdf/footer_cartaprotesta', 'datos' => ['tipo_provider' => $tipo] ],
            'content' => [ 'layout' => $carta_template, 'datos' => [
                'experiencia' => ClientesContratos::obtenerClientesContratos($provider_id, $tipo),
                'model' => $proveedor,
                'course' => $curso,
                'tipo_persona' => $proveedor->isPFisica() ? 'f' : 'm',
                'model_representante' => $representante,
                'estado_nombre' => $estado,
                'colonia_nombre' => $colonia,
                'municipio_nombre' => $municipio,
                'firma' => !is_null($firma) ? $firma : null,//'9687750c2b490f96bb5addbab7e821296fa9cc5096f4c520a91351c93fc033df01c1e2675b3d363d7e9d0e93d11e91fcccd762fd4d5925a29b67daa724433a685a8c5fbd93ad5fc8c3ea705201ba2556fc41ae46f2c08783e852e7e27a0bd6c3ae64873d708ae84e56b6ade3290bdfc3555bcfe76494792adb24efb54eb198566157c44f46480ee21e5d575f25a464e1450b8185f9d5e67f709cdf2fa304c9ee080c6c99cbba76370311357bca232e67e39949bdf44dcc97675c65697d8345f8eab49285f424daf06f7cc78e13a3f894189565f386ab7d9d465d7a92300b5c8e83c4ade0d31cd401285193937906ad1f85dec7ca3ccdd438f23b93b90690a6fa'
                'firmaEmp' => !is_null($firma_corta) ? $firma_corta : null,
                'ubicacion' => $dom_fiscal,
                'montoMercantiles' => $monto_mercantil
            ] ],
            'watermark' => 'imgs/cartas/logo_sello.png',
            'path' => !is_null($firma) ? Firmar::path : Firmar::pre_path,
            'prefix' => "{$proveedor->rfc}_proveedor_carta_protesta",
            'identificador' => $provider_id,
        ];

        $base_filename = FileHelper::generatePdf('Carta Protesta', $datos_pdf);

        return $base_filename;

    }

    /**
     * Funcion que genera y/o firma el documento de preguntas para los concursos
     * @param RegistroConcursos $concurso Instancia del concurso
     * @param Pregunta[] $preguntas Arreglo de instancias de preguntas relacionadas al concurso
     * @param SolicitudPregunta $solicitud Instancia de la solicitud que contiene las preguntas
     * @param Provider $provider Instancia del modelo Provider
     * @param String $firma Cadena de texto que regresa el servicio de firmado electronico
     * 
     * @return String Ruta y nombre del documento generado
     *  */
    public static function generarDocumentoPreguntas($concurso, $preguntas, $solicitud, $provider, $firma = null){
        $name_file_save = null;

        $datos_doc = [ 'solicitud' => $solicitud, 'concurso' => $concurso, 'preguntas' => $preguntas, 'provider' => $provider ];
        $datos_doc = is_null($firma) ? $datos_doc : array_merge($datos_doc, ['firma' => $firma]);

        $datos_pdf = [ 
            'header' => [ 'layout' => '/concursos/solicitud/header-solicitud','datos' => [] ], 
            'footer' => [ 'layout' => $_ENV['VEDA_ELECTORAL'] ? null : '/concursos/solicitud/footer-solicitud','datos' => [] ],
            'content' => [ 'layout' => '/concursos/solicitud/plantilla-solicitud', 'datos' => $datos_doc ],
            'path' => (is_null($firma) ? Firmar::pre_path : Firmar::path),
            'prefix' => "{$provider->rfc}_". ( is_null($firma) ? 'previo_concursos' : 'concursos'),
            'identificador' => $solicitud->solicitud_id,
        ];

        $name_file_save = FileHelper::generatePdf('Documento preguntas', $datos_pdf);
        
        return $name_file_save;
    }

    /**
     * Funcion que genera y/o firma el documento de inscripcion al concurso
     * @param RegistroConcursos $concurso Instancia del concurso
     * @param Provider $provider Instancia del modelo Provider
     * @param Integer $user_id Id del usuario
     * @param String $firma Cadena de texto que regresa el servicio de firmado electronico
     * 
     * @return String Ruta y nombre del documento generado
     *  */
    public static function generarDocumentoInscripcionConcurso($concurso, $provider, $user_id, $firma = null, $firma_b64 = null){
        $name_file_save = null;

        $modelData = InscripcionController::getDataConcurso($user_id);

        $datos_doc = [ 'provider' => $provider, 'concurso' => $concurso, 'modelData' => $modelData ];
        $datos_doc = is_null($firma) ? $datos_doc : array_merge($datos_doc, ['firma' => $firma]);
        $datos_doc = is_null($firma_b64) ? $datos_doc : array_merge($datos_doc, ['firma_b64' => $firma_b64]);

        $datos_pdf = [ 
            'header' => [ 'layout' => '/concursos/solicitud/header-solicitud','datos' => [] ], 
            'footer' => [ 'layout' => $_ENV['VEDA_ELECTORAL'] ? null : '/concursos/solicitud/footer-solicitud','datos' => [] ],
            'content' => [ 'layout' => '/concursos/inscripcion/carta_inscripcion', 'datos' => $datos_doc ],
            'path' => (is_null($firma) ? Firmar::pre_path : Firmar::path),
            'prefix' => "{$provider->rfc}_". ( is_null($firma) ? 'previo_inscripcion' : 'inscripcion_concurso'),
            'identificador' => $concurso->concurso_id,
        ];

        $name_file_save = FileHelper::generatePdf('Documento inscripcion concurso', $datos_pdf);
        
        return $name_file_save;
    }

}

?>