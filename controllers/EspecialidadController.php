<?php

namespace app\controllers;

use app\helpers\GeneralController;
use app\models\CatSubespecialidades;
use app\models\EspecialidadSearch;
use app\models\SubespecialidadCategory;
use yii\helpers\ArrayHelper;
use yii\web\NotFoundHttpException;
use yii\filters\VerbFilter;

/**
 * EspecialidadController implements the CRUD actions for CatSubespecialidades model.
 */
class EspecialidadController extends GeneralController
{
    /**
     * @inheritDoc
     */
    public function behaviors()
    {
        return array_merge(
            parent::behaviors(),
            [
                'verbs' => [
                    'class' => VerbFilter::className(),
                    'actions' => [
                        'delete' => ['POST'],
                    ],
                ],
            ]
        );
    }

    public function actionIndex(){
        $searchModel = new EspecialidadSearch();
        $dataProvider = $searchModel->search($this->request->queryParams);

        return $this->render('index', [
            'searchModel' => $searchModel,
            'dataProvider' => $dataProvider,
        ]);
    }


    public function actionUpdate($id=null){
        $model = $this->findModel($id);
        if ($model->load($this->request->post()) && $model->save()) {
            return $this->redirect(['index']);
        }

        return $this->render('update', [
            'tipo'=>ArrayHelper::map(SubespecialidadCategory::find()->asArray()->all(),'category_id','nombre'),
            'model' => $model,
        ]);
    }

    public function actionDelete($subespecialidad_id){
        //$this->findModel($subespecialidad_id)->delete();

        return $this->redirect(['index']);
    }

    protected function findModel($id){
        return ($model = CatSubespecialidades::findOne($id)) !== null ? $model : new CatSubespecialidades();


    }
}
