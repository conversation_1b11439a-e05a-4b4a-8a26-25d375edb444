<?php

namespace app\controllers;

use app\helpers\GeneralController;
use app\models\Usuarios;
use app\models\Visit;
use kartik\form\ActiveForm;
use Yii;
use app\models\VisitDetails;
use app\models\VisitDetailsSearch;
use yii\web\Controller;
use yii\web\NotFoundHttpException;
use yii\filters\VerbFilter;
use yii\web\Response;

/**
 * VisitDetailsController implements the CRUD actions for VisitDetails model.
 */
class VisitDetailsController extends GeneralController
{
    /**
     * {@inheritdoc}
     */
    public function behaviors()
    {
        return [
            'verbs' => [
                'class' => VerbFilter::className(),
                'actions' => [
                    'delete' => ['POST'],
                ],
            ],
        ];
    }

    /**
     * Lists all VisitDetails models.
     * @return mixed
     */
    public function actionIndex()
    {
        $searchModel = new VisitDetailsSearch();
        $dataProvider = $searchModel->search(Yii::$app->request->queryParams);

        return $this->render('index', [
            'searchModel' => $searchModel,
            'dataProvider' => $dataProvider,
        ]);
    }

    /**
     * Displays a single VisitDetails model.
     * @param integer $id
     * @return mixed
     * @throws NotFoundHttpException if the model cannot be found
     */
    public function actionView($id)
    {
        return $this->render('view', [
            'model' => $this->findModel($id),
        ]);
    }

    /**
     * Creates a new VisitDetails model.
     * If creation is successful, the browser will be redirected to the 'view' page.
     * @return mixed
     */
    public function actionCreate($visit_id=null,$ub_id=null,$button =null,$type='bys')
    {
        $model = $this->findModelVisit($visit_id,$ub_id);


        $foto_actaOLD = $model->foto_acta;
        $foto_visitaOLD = $model->foto_visita;
        $foto_reciboOLD = $model->foto_recibo;
        $foto_acta_constOLD = $model->foto_acta_const;
        $foto_modOLD = $model->foto_mod;
        $foto_fachadaOLD = $model->foto_fachada;
        $foto_ofi_extOLD = $model->foto_ofi_ext;
        $foto_ofi_intOLD = $model->foto_ofi_int;

        if(Yii::$app->user->can(Usuarios::ROLE_ADMIN_CONTRATISTAS) || Yii::$app->user->can(Usuarios::ROLE_VALIDADOR_CONTRATISTAS)){
            $model->scenario = VisitDetails::SCENARIO_CREATE;
        }


        if (Yii::$app->request->isAjax && $model->load(Yii::$app->request->post())) {
            Yii::$app->response->format = Response::FORMAT_JSON;
            return ActiveForm::validate($model);
        } else if ($model->load(Yii::$app->request->post())) {

            $this->makeDir($model->path);

            if(Yii::$app->user->can(Usuarios::ROLE_ADMIN_CONTRATISTAS) || Yii::$app->user->can(Usuarios::ROLE_VALIDADOR_CONTRATISTAS)) {

                $model->des_inst_pers_cantidad_total = intval($model->des_inst_pers_cantidad_admi) + intval($model->ing) + intval($model->arq) + intval($model->abo) + intval($model->cont);

                $model->des_inst_oficina_tot = intval($model->des_inst_oficina_rec) + intval($model->des_inst_oficina_ofi) + intval($model->des_inst_oficina_bod) + intval($model->des_inst_oficina_sala) + intval($model->des_inst_oficina_sala_jun) + intval($model->des_inst_oficina_alm);

                $model->des_inst_vehiculo_trans_tot = intval($model->des_inst_vehiculo_trans_sedan) + intval($model->des_inst_vehiculo_trans_camion) + intval($model->des_inst_vehiculo_trans_pick_up);

                $model->des_inst_maquinaria_tot = intval($model->pesado) + intval($model->ligera) + intval($model->eq_menor);

                $model->des_inst_eq_computo_tot = intval($model->des_inst_eq_computo_esc) + intval($model->des_inst_eq_computo_portatil) + intval($model->impresora) + intval($model->plotter) + intval($model->secanner) + intval($model->copiadora);

            }

            if (!empty($model->foto_acta) && $model->foto_acta != $foto_actaOLD) {
                $new_nameFot = str_replace('archivos_tmp/', $model->path . '/', $model->foto_acta);
                $this->copyFile($model->foto_acta, $new_nameFot);
                $model->foto_acta = $new_nameFot;
            }

            if (!empty($model->foto_visita) && $model->foto_visita != $foto_visitaOLD) {
                $new_nameFot = str_replace('archivos_tmp/', $model->path . '/', $model->foto_visita);
                $this->copyFile($model->foto_visita, $new_nameFot);
                $model->foto_visita = $new_nameFot;
            }

            if (!empty($model->foto_recibo) && $model->foto_recibo != $foto_reciboOLD) {
                $new_nameFot = str_replace('archivos_tmp/', $model->path . '/', $model->foto_recibo);
                $this->copyFile($model->foto_recibo, $new_nameFot);
                $model->foto_recibo = $new_nameFot;
            }

            if (!empty($model->foto_acta_const) && $model->foto_acta_const != $foto_acta_constOLD) {
                $new_nameFot = str_replace('archivos_tmp/', $model->path . '/', $model->foto_acta_const);
                $this->copyFile($model->foto_acta_const, $new_nameFot);
                $model->foto_acta_const = $new_nameFot;
            }

            if (!empty($model->foto_mod) && $model->foto_mod != $foto_modOLD) {
                $new_nameFot = str_replace('archivos_tmp/', $model->path . '/', $model->foto_mod);
                $this->copyFile($model->foto_mod, $new_nameFot);
                $model->foto_mod = $new_nameFot;
            }

            if (!empty($model->foto_fachada) && $model->foto_fachada != $foto_fachadaOLD) {
                $new_nameFot = str_replace('archivos_tmp/', $model->path . '/', $model->foto_fachada);
                $this->copyFile($model->foto_fachada, $new_nameFot);
                $model->foto_fachada = $new_nameFot;
            }

            if (!empty($model->foto_ofi_ext) && $model->foto_ofi_ext != $foto_ofi_extOLD) {
                $new_nameFot = str_replace('archivos_tmp/', $model->path . '/', $model->foto_ofi_ext);
                $this->copyFile($model->foto_ofi_ext, $new_nameFot);
                $model->foto_ofi_ext = $new_nameFot;
            }

            if (!empty($model->foto_ofi_int) && $model->foto_ofi_int != $foto_ofi_intOLD) {
                $new_nameFot = str_replace('archivos_tmp/', $model->path . '/', $model->foto_ofi_int);
                $this->copyFile($model->foto_ofi_int, $new_nameFot);
                $model->foto_ofi_int = $new_nameFot;
            }


            $userLoginId = Yii::$app->user->getId();

            $model->visit_id = $visit_id;
            $model->ubicacion_id = $ub_id;
            $model->otros_giros = json_encode($model->otros_giros);
            $model->permiso_especifique = json_encode($model->permiso_especifique);
            $model->certificado_especifique = json_encode($model->certificado_especifique);
            $model->observaciones = json_encode($model->observaciones);
            $model->status = true;
            $model->created_by = $userLoginId;
            if($model->save()){
                Visit::updateAll(['modify' => true,'created_by' => $userLoginId],['visit_id' => $visit_id]);
                return $this->redirect(['/visit/index']);
            }
        }

        $dataProv = Yii::$app->db->createCommand("select distinct p.provider_id,CASE WHEN (p.name_razon_social is not null and p.name_razon_social != '') then p.name_razon_social else concat_ws(' ',p.pf_nombre,p.pf_ap_paterno,p.pf_ap_materno) end as proveedor,
                        r.rfc from provider p
                        left join provider.rfc r on r.provider_id = p.provider_id
                        join visit v on v.provider_id = p.provider_id
                        where v.visit_id = :id",[':id' => $visit_id])->queryOne();

        return $this->renderAjax('create', [
            'model' => $model,
            'button' => $button,
            'dataProv' => $dataProv,
            'type' => $type
        ]);
    }

    /**
     * Updates an existing VisitDetails model.
     * If update is successful, the browser will be redirected to the 'view' page.
     * @param integer $id
     * @return mixed
     * @throws NotFoundHttpException if the model cannot be found
     */
    public function actionUpdate($id)
    {
        $model = $this->findModel($id);

        if ($model->load(Yii::$app->request->post()) && $model->save()) {
            return $this->redirect(['view', 'id' => $model->visit_details_id]);
        }

        return $this->render('update', [
            'model' => $model,
            'button' => null
        ]);
    }

    /**
     * Deletes an existing VisitDetails model.
     * If deletion is successful, the browser will be redirected to the 'index' page.
     * @param integer $id
     * @return mixed
     * @throws NotFoundHttpException if the model cannot be found
     */
    public function actionDelete($id)
    {
        $this->findModel($id)->delete();

        return $this->redirect(['index']);
    }

    /**
     * Finds the VisitDetails model based on its primary key value.
     * If the model is not found, a 404 HTTP exception will be thrown.
     * @param integer $id
     * @return VisitDetails the loaded model
     * @throws NotFoundHttpException if the model cannot be found
     */
    protected function findModel($id)
    {
        if (($model = VisitDetails::findOne($id)) !== null) {
            return $model;
        }

        throw new NotFoundHttpException('The requested page does not exist.');
    }


    /**
     * Finds the VisitDetails model based on its primary key value.
     * If the model is not found, a 404 HTTP exception will be thrown.
     * @param integer $id
     * @return VisitDetails the loaded model
     * @throws NotFoundHttpException if the model cannot be found
     */
    protected function findModelVisit($vId,$uId)
    {
        if (($model = VisitDetails::find()->where(['and',['visit_id' => intval($vId)],['ubicacion_id' => intval($uId)]])->one()) !== null) {
            return $model;
        }else{
            return new VisitDetails();
        }
    }

    public function actionFinished($visit_id=null,$ub_id=null){

        VisitDetails::updateAll(['status' => false],['visit_id' => intval($visit_id),'ubicacion_id' => intval($ub_id)]);

        return $this->redirect('/visit/index');

    }
}
