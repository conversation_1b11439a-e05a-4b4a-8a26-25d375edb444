<?php

namespace app\controllers;

use app\helpers\GeneralController;
use app\models\UsuariosAcuerdos;
use Yii;

/**
 * Se creo con el fin de ejecutar procesos de actualizacion de datos o generacion de reportes
 * para ubicarlo de una manera mas rapida
 */
class ProcesosController extends GeneralController{

    /**
     * Crea el registro en la tabla de historicos de los modulos actuales para
     * los proveedores que no tienen registro entre 19 de julio al 19 de septiembre del 2022. 
     */
    public function actionGenerarHistorico(){

        //Query que regresa los proveedores que tuvieron validaciones despues del 19 de Julio y no tienen registros en el historico
        //Tambien obtiene los certificados de los proveedores disponibles
        $query_validados = "SELECT provider_id, hc_id FROM (SELECT p.provider_id, count(h.*) as registros, count(dv.*) as validaciones, 
            (SELECT hc.historico_certificados_id FROM historico_certificados hc WHERE hc.provider_id = p.provider_id AND tipo = 'CERTIFICADO' ORDER BY created_at DESC LIMIT 1) as hc_id  
        FROM provider p LEFT JOIN provider.historico h on p.provider_id = h.provider_id
        LEFT JOIN provider.datos_validados dv on dv.provider_id = p.provider_id WHERE dv.created_date::date >= '2022-07-19' AND dv.status_bys = 'VALIDADO'
        GROUP BY p.provider_id ORDER BY registros ) as temp WHERE temp.registros = 0 AND temp.validaciones > 0";

        $resultado_validados = Yii::$app->db->createCommand($query_validados)->queryAll();

        $modulos = ["bys_perfil" => '', "bys_legales"=> '', "bys_bys"=> '', "bys_economica"=> '', "bys_experiencia"=> '',
        "bys_domicilio"=> '', "bys_bancos"=> ''];

        foreach($resultado_validados as $proveedor){
            ValidadorController::guardarHistorico($proveedor['provider_id'], $modulos, $proveedor['hc_id'] );
        }

        echo "Datos actualizados";

    }

    /**
     * Crea el registro en la tabla de historicos de los modulos actuales para el proveedor señalado
     */
    public function actionGenerarHistoricoProveedor($provider_id = null){
        
        $modulos = ["bys_perfil" => '', "bys_legales"=> '', "bys_bys"=> '', "bys_economica"=> '', "bys_experiencia"=> '',"bys_domicilio"=> '', "bys_bancos"=> ''];

        if(!is_null($provider_id)){

            $historico_certificado_query = "SELECT historico_certificados_id FROM historico_certificados WHERE provider_id = $provider_id AND tipo = 'CERTIFICADO' ORDER BY created_at DESC LIMIT 1";

            $resultado = Yii::$app->db->createCommand($historico_certificado_query)->queryOne();

            ValidadorController::guardarHistorico($provider_id, $modulos, $resultado['historico_certificados_id'] );

            echo "Historico actualizados";

        }else{
            echo "No se proporciono id del proveedor";
        }

    }

    //Ejecutar el 15 de marzo
    public function actionActivarAvisoRegistro(){

        //Usuarios validados 
        $sql_validados = "SELECT p.user_id FROM provider p WHERE p.provider_id in
            (select DISTINCT provider_id from provider.datos_validados where status_bys='VALIDADO') 
        and enabled is true ORDER BY creation_date";
        $proveedores_validados = Yii::$app->db->createCommand($sql_validados)->queryAll();


        //Genera el aviso para que el usuario validado lo vea al entrar de nuevo
        foreach($proveedores_validados as $proveedor){
            UsuariosAcuerdos::generarAcuerdo($proveedor['user_id'], 'provider/dashboard', UsuariosAcuerdos::TYPE_AVISO_ACTUALIZACION_REGISTRO);
        }
        
        echo "Proceso terminado";

    }

    //Ejecutar el 1ro de Abril
    public function actionSetModulosRevision(){
        //Usuarios validados 
        $sql_validados = "SELECT p.provider_id FROM provider p WHERE p.provider_id in
            (select DISTINCT provider_id from provider.datos_validados where status_bys='VALIDADO') 
        and enabled is true ORDER BY creation_date";
        $proveedores_validados = Yii::$app->db->createCommand($sql_validados)->queryAll();

        //Cambia los modulos a estatus REVISION si estan validados o rechazado
        foreach($proveedores_validados as $proveedor){
            GeneralController::setEdicionModulos($proveedor['provider_id']);
        }
        
        echo "Proceso terminado";
        
    }

    /* public function actionTestBcc(){
        $emails = [ '<EMAIL>' => 'email1', '<EMAIL>' => 'email2', '<EMAIL>' => 'Marco',
        '<EMAIL>' => 'Marco R.', '<EMAIL>' => 'Marco R. G.' ];
        GeneralController::sendMassivePrivateEmail('/provider/correos/activar_agenda', null, $emails, 'Agendar cita');
    } */

}

?>