<?php

namespace app\controllers;

use app\models\Requisicion;
use yii\web\Controller;


class FichaController extends Controller
{
    public function actionFicha($requisicion_id = 2)
    {
        $requisicion = Requisicion::findOne($requisicion_id);

        $html = $this->render("ficha-tecnica", ['requisicion' => $requisicion]);

        $mpdf = new \mPDF();

        $mpdf->SetWatermarkText("SECOPNL", 0.1);
        $mpdf->showWatermarkText = true;
        $mpdf->SetTitle('Orden de compra');
        $mpdf->SetAuthor('SECOPNL');
        $mpdf->SetCreator('SECOPNL');
        $mpdf->showImageErrors = true;
        //var_dump($html); exit;
        $mpdf->writeHtml($html);
        $mpdf->Output($requisicion->dependencia->nombre . date(" Y-m-d H:i:s") . '.pdf', "I");
    }

}