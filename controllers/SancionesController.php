<?php

namespace app\controllers;

use app\helpers\GeneralController;
use app\models\Provider;
use app\models\Usuarios;
use Yii;
use app\models\Sanciones;
use app\models\SancionesSearch;
use kartik\form\ActiveForm;
use yii\web\Response;
use yii\filters\VerbFilter;

/**
 * SancionesController implements the CRUD actions for sanciones model.
 */
class SancionesController extends GeneralController
{
    /**
     * @inheritdoc
     */
    public function behaviors()
    {
        return [
            'verbs' => [
                'class' => VerbFilter::className(),
                'actions' => [
                ],
            ],
        ];
    }


    public $layout = 'main';


    public function actionIndex()
    {
        $searchModel = new SancionesSearch();
        $dataProvider = $searchModel->search(Yii::$app->request->queryParams);


        return $this->render('index', [
            'searchModel' => $searchModel,
            'dataProvider' => $dataProvider,
        ]);
    }


    public function actionView($id)
    {
        return $this->render('view', [
            'model' => $this->findModel($id),
        ]);
    }


    public function actionUpdate($id= null) {
        $isRequest = Yii::$app->request->isAjax;
        $postRequest = Yii::$app->request->post();

        $model = $this->findModel($id);
        $documento_old = $model->documento_sancion;

        if($isRequest && $model->load($postRequest)){
            $errores = [];
            Yii::$app->response->format = Response::FORMAT_JSON;
            if( $model->f_fin != null && $model->f_fin != '' && $model->f_notificacion != null && $model->f_notificacion != '' ){
                if( $model->f_fin < $model->f_notificacion){ $errores['sanciones-f_fin'] = ['La fecha de finalización de sanción no puede ser menor a la fecha de notificacion']; }
            }

            if( $model->f_resolucion != null && $model->f_resolucion != '' && $model->f_notificacion != null && $model->f_notificacion != '' ){
                if( $model->f_notificacion < $model->f_resolucion){ $errores['sanciones-f_notificacion'] = ['La fecha de notificacion no puede ser menor a la fecha de resolucion']; }
            }
            return array_merge($errores, ActiveForm::validate($model));
        }else if ($model->load($postRequest)){


            if (!empty($model->documento_sancion) && $model->documento_sancion != $documento_old) {
                $new_name = str_replace('archivos_tmp/', $model->path . '/', $model->documento_sancion);
                $this->copyFile($model->documento_sancion, $new_name);
                $model->documento_sancion = $new_name;
            }

            $modelUsuarios = Usuarios::findOne($model->provider->user_id);
            $modelUsuarios->status = 'SANCIONADO';
            $modelUsuarios->save(false);

            $modelProvider = Provider::findOne($model->proveedor_id);
            $modelProvider->permanently_disabled = 'SANCIONADO';
            $modelProvider->save(false);

            $model->save();
            return $this->redirect(['index']);
        }

        return $this->render('update', [
            'model' => $model,
        ]);
    }


    public function actionDelete($id)
    {

        $modelDelete = $this->findModel($id);

        $modelUsuarios = Usuarios::findOne($modelDelete->provider->user_id);
        $modelUsuarios->status = 'ACTIVO';
        $modelUsuarios->save(false);

        $modelProvider = Provider::findOne($modelDelete->proveedor_id);
        $modelProvider->permanently_disabled = 'EN PROCESO';
        $modelProvider->save(false);

        $modelDelete->activo = false;
        $modelDelete->save(false);

        return $this->redirect('index');
    }

    public function actionReactivar($id){
        $modelDelete = $this->findModel($id);

        $modelUsuarios = Usuarios::findOne($modelDelete->provider->user_id);
        $modelUsuarios->status = 'ACTIVO';
        $modelUsuarios->save(false);

        $modelProvider = Provider::findOne($modelDelete->proveedor_id);
        $modelProvider->permanently_disabled = 'EN PROCESO';
        $modelProvider->save(false);

        return $this->redirect('index');
    }


    protected function findModel($id)
    {
        return (($model = Sanciones::findOne($id)) !== null)?$model:new Sanciones();
    }
}
