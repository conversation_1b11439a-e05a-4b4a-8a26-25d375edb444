<?php

namespace app\controllers;

use app\helpers\GeneralController;
use app\models\Division;
use app\models\ProveeRegistroEvento;
use app\models\Provider;
use app\models\RegistroEvento;
use app\models\RegistroEventoSearch;
use app\models\RepresentanteLegal;
use app\models\Usuarios;
use SoapClient;
use Yii;
use yii\data\ActiveDataProvider;
use yii\data\SqlDataProvider;
use yii\helpers\ArrayHelper;
use yii\helpers\Url;
use yii\web\Controller;

class EventoController extends Controller
{
    public static $interes = ['1' => 'Registro al Padrón de proveedores', '2' => 'Talleres de capacitación', '3' => 'Conferencia magistral', '4' => 'Asistencia a Licitación en vivo'];

    public static function getIteres()
    {
        return self::$interes;
    }

    public function actionIndex()
    {
        return true;
    }

    public function actionRegistro()
    {
        $this->layout = 'nomain';
        return $this->render('gracias');
        header('Access-Control-Allow-Origin: *');
        header('Access-Control-Allow-Methods: GET, POST');
        header("Access-Control-Allow-Headers: X-Requested-With");

        $provider = (!Yii::$app->user->isGuest) ?
            Provider::find()->where(['user_id' => Yii::$app->user->id])->one() : null;
        $model = new RegistroEvento();
        if ($model->load(Yii::$app->request->post())) {
            $model->created_by = !Yii::$app->user->isGuest ? Yii::$app->user->id : 0;
            $model->giros = implode('|', $model->giros);
            $model->interes_evento = implode('|', $model->interes_evento);
            $model->talleres = $model->eventos ? implode('|', $model->eventos) : null;
            $model->eventos = '';
            $model->es_proveedor = ($model->provider_id) ? true : false;
            if (!$model->save()) {
                return json_encode(['status' => false, "msg" => $model->errors]);
            }

            $ch = curl_init();
            $url = 'https://chart.googleapis.com/chart?cht=qr&choe=UTF-8&chs=300x300&chl=' . Url::home(true) . 'evento/asistencia?id=' . base64_encode($model->id);
            curl_setopt($ch, CURLOPT_URL, $url);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_HEADER, 0);
            $qr = base64_encode(curl_exec($ch));
            curl_close($ch);

            GeneralController::sendEmail('/provider/correos/provee', '<EMAIL>', $model->correo_contacto, 'Bienvenid@ PROVEE NL 2023', ['qr' => $url]);

            return json_encode(['status' => true, 'qr' => $qr]);
        } else {
            return $this->render('registro', ['model' => $model, 'provider' => $provider]);
        }
    }

    public function actionRegistro2024()
    {
        $this->layout = 'via';
        return $this->render('registro2024');
    }

    public function actionAsistencia($id = null)
    {
        if (!$id)
            return false;
        $model = RegistroEvento::findOne(base64_decode($id));
        if (!$model)
            return false;
        $registro = false;
        if (!Yii::$app->user->isGuest) {
            $model->fecha_asistio = date('Y-m-d H:i:s');
            $model->registro = Yii::$app->user->id;
            $model->save();
            $registro = true;
        }

        return $this->render('asistencia', ['model' => $model, 'registro' => $registro]);
    }


    public function actionInit()
    {
        header('Access-Control-Allow-Origin: *');
        header('Access-Control-Allow-Methods: GET, POST');
        header("Access-Control-Allow-Headers: X-Requested-With");

        $data['giros'] = ArrayHelper::map(
            Division::find()
                ->select('division_id, descripcion')
                ->asArray()->all(),
            'division_id', 'descripcion'
        );
        $data['eventos'] = Division::findBySql("select evento_id, concat(to_char(fecha_inicio,'HH24:MI'),' Hrs - ', titulo) as titulo from evento where disponible is true order by fecha_inicio ")->asArray()->all();
        $data['antiguedad'] = ['Menos de 1 año' => 'Menos de 1 año', '1 - 3 años' => '1 - 3 años', '3 - 5 años' => '3 - 5 años', 'Mayor a 5 años' => 'Mayor a 5 años'];
        $data['interes'] = self::getIteres();
        $data['estratificacion'] = ['Micro' => 'Micro', 'Pequeña' => 'Pequeña', 'Mediana' => 'Mediana', 'Grande' => 'Grande'];
        return json_encode($data);
    }

    public function actionDataFuncionario($rfc = null)
    {
        header('Access-Control-Allow-Origin: *');
        header('Access-Control-Allow-Methods: GET, POST');
        header("Access-Control-Allow-Headers: X-Requested-With");

        // Validar los parámetros recibidos
        if (!$rfc) {
            return json_encode([
                'status' => false,
                'message' => 'Faltan parámetros en la solicitud'
            ]);
        }

        // Validar las credenciales de la consulta
        if (!$_ENV['AD_SOAP_URL'] && !$_ENV['AD_SOAP_USERNAME'] && !$_ENV['AD_SOAP_PASSWORD']) {
            return json_encode([
                'status' => false,
                'message' => 'Credenciales incorrectas'
            ]);
        }

        $client = new SoapClient($_ENV['AD_SOAP_URL']);
        $response = $client->__soapCall("ConsultaCURP", array([
            'Credencial' => $_ENV['AD_SOAP_USERNAME'],
            'Password' => $_ENV['AD_SOAP_PASSWORD'],
            'CURP' => $rfc
        ]));

        // El soap client no puede decodificar toda la respuesta
        $object = simplexml_load_string($response->ConsultaCURPResult->any);
        $empleado = $object->NewDataSet->Table;
        
        if (isset($empleado->IdEmpleado)) {
            return json_encode([
                'status' => true,
                'data' => $empleado
            ]);
        }

        return json_encode([
            'status' => false,
            'message' => 'Usuario no encontrado'
        ]);
    }


    public function actionDataprovider($rfc = null)
    {
        header('Access-Control-Allow-Origin: *');
        header('Access-Control-Allow-Methods: GET, POST');
        header("Access-Control-Allow-Headers: X-Requested-With");

        $provider = Provider::find()->where(['rfc' => strval($rfc)])->one();
        if (!$rfc || !$provider)
            return json_encode(['status' => false]);

        $repLeg = RepresentanteLegal::find()->where(['and', ['activo' => true], ['rep_bys' => true], ['provider_id' => $provider->provider_id]])->one();
        $rep = ($repLeg) ? $repLeg->nombre . " " . $repLeg->ap_paterno . " " . $repLeg->ap_materno : '';
        $giros = Division::findBySql("
            select distinct d.division_id 
            from provider_giro pg inner join productos.producto p on pg.producto_id=p.producto_id 
            inner join productos.clase c on c.clase_id = p.clase_id
            inner join productos.grupo g on g.grupo_id = c.grupo_id
            inner join productos.division d on d.division_id = g.division_id
            where provider_id = $provider->provider_id
         ")->asArray()->all();

        return json_encode([
            "status" => true,
            "razon_social" => $provider->NameOrRazonSocial,
            "asiste" => $provider->tipo_persona == 'Persona moral' ? $rep : $provider->NameOrRazonSocial,
            "sire" => $provider->Clave_ProveedorSire,
            "estratificacion" => $provider->estratificacion,
            "giros" => array_column(array_values($giros), 'division_id'),
            "telefono" => $provider->telfono,
            "correo" => $provider->email,
            "id" => $provider->provider_id
        ]);
    }


    public function actionLista($d = false)
    {
        if (!Yii::$app->user->can(Usuarios::ROLE_ADMIN_PROVEE))
            return $this->goHome();
        $searchModel = new RegistroEventoSearch();
        $dataProvider = $searchModel->search($this->request->queryParams);
        if ($d) {
            $intereses = EventoController::getIteres();
            $talleres = Yii::$app->db->createCommand(" select evento_id,concat(to_char(fecha_inicio,'HH24:MI'),' Hrs - ', titulo) as titulo from evento ")->queryAll();
            $data = Yii::$app->db->createCommand(" 
 
                select nombre_razon_social,persona_asiste,persona_puesto,es_proveedor,numero_sire,rfc,antiguedad,interes_evento,talleres,estratificacion,
                telefono_contacto,correo_contacto,created_at as fecha_registro,fecha_asistio as fecha_asistencia,registro from registro_evento_2023 
                
                
             ")->queryAll();


            foreach ($data as $keyy => $value) {

                $int = explode('|',str_replace("\"","",$value['interes_evento']));
                $ret = '';
                foreach ($intereses as $key=>$val){
                    if(in_array($key,$int))
                        $ret .= $val.', ';
                }
                $data[$keyy]['interes_evento'] =  substr($ret,0,-2);

                $int = explode('|',str_replace("\"","",$value['talleres']));
                $ret = '';
                foreach ($talleres as $key=>$val){
                    if(in_array($val['evento_id'],$int))
                        $ret .= $val['titulo'].', ';
                }
                $data[$keyy]['talleres'] =  substr($ret,0,-2);


            }
            GeneralController::downloadExcel($data,'Provee2023');
        }
        $this->layout = 'home';
        return $this->render('lista', [
            'searchModel' => $searchModel,
            'dataProvider' => $dataProvider,
        ]);

    }


    public function actionCancela($id = null, $motivo = null)
    {
        if (!$id || $motivo)
            return json_encode(['status' => false, 'msg' => 'All parameters are necessary']);
        $model = RegistroEvento::findOne(base64_decode($id));
        if (!$model)
            return json_encode(['status' => false, 'msg' => 'Id not exist']);
        $model->activo = false;
        $model->motivo_cancela = $motivo;
        $model->save();
        return json_encode(['status' => true]);
    }


    public function actionTalleres(){
        $data['data'] = new SqlDataProvider([
            'sql'=>" Select * from evento order by fecha_inicio "
        ]);
        $data['registro'] = Yii::$app->db->createCommand("select * from registro_evento_2023 where activo is true")->queryAll();
        $this->layout = 'home';
        return $this->render('talleres',$data);
    }


    public function actionListaprovee($d=false){
        if (!Yii::$app->user->can(Usuarios::ROLE_ADMIN_PROVEE))
            return $this->goHome();

        $query = ProveeRegistroEvento::find();

        if($d){
            GeneralController::downloadExcel($query->asArray()->all(),'AsistenciaProvee'.date('Y'));
        }

        $this->layout = 'home';
        return $this->render('listaprovee',[
            'dataProvider'=>new ActiveDataProvider([
                'query'=> $query
            ])
        ]);
    }

    public function actionView($id=null){
        return $this->renderAjax('view',['model'=>ProveeRegistroEvento::findOne($id)]);
    }

}
