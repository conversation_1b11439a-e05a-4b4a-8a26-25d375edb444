<?php

namespace app\components\fiel;

use yii\base\Component;
use Yii;

/**
 * Componente para manejar operaciones con FIEL (e.firma)
 */
class FielManager extends Component
{
    // Propiedades configurables
    public $satValidationUrl;
    public $signatureServiceUrl;
    public $tokenServiceUrl;
    public $qrServiceUrl;
    public $barcodeServiceUrl;
    public $enableCaching = true;
    public $cacheExpiration = 3600;
    public $allowOfflineValidation = false;
    public $signedDocumentsPath;
    public $preSignedDocumentsPath;
    public $tempPath;
    public $debug = false;
    /**
     * Valida un certificado FIEL
     */
    public function validateCertificate($certFile, $keyFile, $password)
    {
        try {
            // Leer contenido de los archivos
            $certContent = file_get_contents($certFile);
            $keyContent = file_get_contents($keyFile);

            if (!$certContent || !$keyContent) {
                throw new \Exception('No se pudieron leer los archivos de certificado o llave');
            }

            // Debug: Log información de los archivos
            Yii::info([
                'cert_size' => strlen($certContent),
                'key_size' => strlen($keyContent),
                'cert_starts_with' => substr($certContent, 0, 50),
                'key_starts_with' => substr($keyContent, 0, 50),
                'cert_is_binary' => !mb_check_encoding($certContent, 'UTF-8'),
                'key_is_binary' => !mb_check_encoding($keyContent, 'UTF-8')
            ], 'fiel.debug');

            // Intentar diferentes métodos de conversión
            $certPem = $this->convertToPem($certContent, 'CERTIFICATE');
            $keyPem = $this->convertToPemAdvanced($keyContent, $password);

            // Validar certificado
            $certResource = openssl_x509_read($certPem);
            if (!$certResource) {
                throw new \Exception('El certificado no es válido');
            }

            // Extraer información del certificado primero
            $certInfo = openssl_x509_parse($certResource);
            if (!$certInfo) {
                throw new \Exception('No se pudo extraer información del certificado');
            }

            // Extraer RFC del certificado
            $rfc = $this->extractRFC($certInfo);

            // Intentar validar la llave privada con diferentes métodos
            $keyResource = $this->validatePrivateKey($keyPem, $password);
            if (!$keyResource) {
                // Si falla, intentar sin conversión PEM
                $keyResource = $this->validatePrivateKey($keyContent, $password);
                if (!$keyResource) {
                    throw new \Exception('No se pudo abrir la llave privada con esa contraseña. Verifique que la contraseña sea correcta y que los archivos correspondan a la misma FIEL.');
                }
            }

            return [
                'success' => true,
                'rfc' => $rfc,
                'subject' => $certInfo['subject'],
                'issuer' => $certInfo['issuer'],
                'valid_from' => date('Y-m-d H:i:s', $certInfo['validFrom_time_t']),
                'valid_to' => date('Y-m-d H:i:s', $certInfo['validTo_time_t'])
            ];

        } catch (\Exception $e) {
            Yii::error("Error validando FIEL: " . $e->getMessage(), __METHOD__);
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Valida llave privada con diferentes métodos
     */
    protected function validatePrivateKey($keyContent, $password)
    {
        // Método 1: Intentar directamente
        $keyResource = openssl_pkey_get_private($keyContent, $password);
        if ($keyResource) {
            return $keyResource;
        }

        // Método 2: Intentar con diferentes formatos de contraseña
        $passwordVariations = [
            $password,
            trim($password),
            strtoupper($password),
            strtolower($password)
        ];

        foreach ($passwordVariations as $pwd) {
            $keyResource = openssl_pkey_get_private($keyContent, $pwd);
            if ($keyResource) {
                Yii::info("Contraseña funcionó con variación: " . $pwd, 'fiel.debug');
                return $keyResource;
            }
        }

        return false;
    }
    
    /**
     * Convierte contenido a formato PEM
     */
    protected function convertToPem($content, $type)
    {
        // Si ya está en formato PEM, devolverlo tal como está
        if (strpos($content, '-----BEGIN') !== false) {
            return $content;
        }

        // Convertir de DER (binario) a PEM (base64)
        $base64Content = base64_encode($content);
        $pemContent = "-----BEGIN {$type}-----\n";
        $pemContent .= chunk_split($base64Content, 64, "\n");
        $pemContent .= "-----END {$type}-----\n";

        return $pemContent;
    }

    /**
     * Conversión avanzada para llaves privadas
     */
    protected function convertToPemAdvanced($keyContent, $password)
    {
        // Si ya está en formato PEM, devolverlo tal como está
        if (strpos($keyContent, '-----BEGIN') !== false) {
            return $keyContent;
        }

        // Intentar diferentes tipos de llave privada
        $keyTypes = [
            'PRIVATE KEY',
            'RSA PRIVATE KEY',
            'ENCRYPTED PRIVATE KEY'
        ];

        foreach ($keyTypes as $type) {
            $base64Content = base64_encode($keyContent);
            $pemContent = "-----BEGIN {$type}-----\n";
            $pemContent .= chunk_split($base64Content, 64, "\n");
            $pemContent .= "-----END {$type}-----\n";

            // Probar si esta conversión funciona
            $testKey = openssl_pkey_get_private($pemContent, $password);
            if ($testKey) {
                Yii::info("Conversión exitosa con tipo: " . $type, 'fiel.debug');
                return $pemContent;
            }
        }

        // Si ninguna conversión funciona, devolver la original
        return $this->convertToPem($keyContent, 'PRIVATE KEY');
    }
    
    /**
     * Extrae el RFC del certificado
     */
    protected function extractRFC($certInfo)
    {
        // Buscar RFC en diferentes campos del certificado
        $possibleFields = ['serialNumber', 'CN', 'OU', 'emailAddress'];
        
        foreach ($possibleFields as $field) {
            if (isset($certInfo['subject'][$field])) {
                $value = $certInfo['subject'][$field];
                // Buscar patrón de RFC (3-4 letras, 6 números, 3 caracteres alfanuméricos)
                if (preg_match('/([A-Z&Ñ]{3,4}[0-9]{6}[A-Z0-9]{3})/', $value, $matches)) {
                    return $matches[1];
                }
            }
        }
        
        // Si no se encuentra en campos estándar, buscar en todo el subject string
        $subjectString = '';
        foreach ($certInfo['subject'] as $key => $value) {
            $subjectString .= "$key=$value ";
        }
        
        if (preg_match('/([A-Z&Ñ]{3,4}[0-9]{6}[A-Z0-9]{3})/', $subjectString, $matches)) {
            return $matches[1];
        }
        
        return '';
    }
    
    /**
     * Busca un usuario por RFC en la base de datos
     */
    public function findUserByRFC($rfc)
    {
        // Buscar en tabla usuarios
        $usuario = \app\models\Usuarios::find()->where(['rfc' => $rfc])->one();
        if ($usuario) {
            return $usuario;
        }

        // Buscar en tabla provider
        $provider = \app\models\Provider::find()->where(['rfc' => $rfc])->one();
        if ($provider) {
            return \app\models\Usuarios::findOne($provider->user_id);
        }

        // Buscar en tabla rfc
        $rfcModel = \app\models\Rfc::find()->where(['rfc' => $rfc])->one();
        if ($rfcModel) {
            $provider = \app\models\Provider::findOne($rfcModel->provider_id);
            if ($provider) {
                return \app\models\Usuarios::findOne($provider->user_id);
            }
        }

        return null;
    }
}
