<?php

namespace app\components\fiel;

use yii\base\Component;
use Yii;

/**
 * Componente para manejar operaciones con FIEL (e.firma)
 */
class FielManager extends Component
{
    // Propiedades configurables
    public $satValidationUrl;
    public $signatureServiceUrl;
    public $tokenServiceUrl;
    public $qrServiceUrl;
    public $barcodeServiceUrl;
    public $enableCaching = true;
    public $cacheExpiration = 3600;
    public $allowOfflineValidation = false;
    public $signedDocumentsPath;
    public $preSignedDocumentsPath;
    public $tempPath;
    public $debug = false;
    /**
     * Valida un certificado FIEL
     */
    public function validateCertificate($certFile, $keyFile, $password)
    {
        try {
            // Leer contenido de los archivos
            $certContent = file_get_contents($certFile);
            $keyContent = file_get_contents($keyFile);
            
            if (!$certContent || !$keyContent) {
                throw new \Exception('No se pudieron leer los archivos de certificado o llave');
            }
            
            // Convertir certificado a formato PEM si es necesario
            $certPem = $this->convertToPem($certContent, 'CERTIFICATE');
            $keyPem = $this->convertToPem($keyContent, 'PRIVATE KEY');
            
            // Validar certificado
            $certResource = openssl_x509_read($certPem);
            if (!$certResource) {
                throw new \Exception('El certificado no es válido');
            }
            
            // Validar llave privada
            $keyResource = openssl_pkey_get_private($keyPem, $password);
            if (!$keyResource) {
                throw new \Exception('No se pudo abrir la llave privada con esa contraseña');
            }
            
            // Extraer información del certificado
            $certInfo = openssl_x509_parse($certResource);
            if (!$certInfo) {
                throw new \Exception('No se pudo extraer información del certificado');
            }
            
            // Extraer RFC del certificado
            $rfc = $this->extractRFC($certInfo);
            
            return [
                'success' => true,
                'rfc' => $rfc,
                'subject' => $certInfo['subject'],
                'issuer' => $certInfo['issuer'],
                'valid_from' => date('Y-m-d H:i:s', $certInfo['validFrom_time_t']),
                'valid_to' => date('Y-m-d H:i:s', $certInfo['validTo_time_t'])
            ];
            
        } catch (\Exception $e) {
            Yii::error("Error validando FIEL: " . $e->getMessage(), __METHOD__);
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * Convierte contenido a formato PEM
     */
    protected function convertToPem($content, $type)
    {
        // Si ya está en formato PEM, devolverlo tal como está
        if (strpos($content, '-----BEGIN') !== false) {
            return $content;
        }
        
        // Convertir de DER (binario) a PEM (base64)
        $base64Content = base64_encode($content);
        $pemContent = "-----BEGIN {$type}-----\n";
        $pemContent .= chunk_split($base64Content, 64, "\n");
        $pemContent .= "-----END {$type}-----\n";
        
        return $pemContent;
    }
    
    /**
     * Extrae el RFC del certificado
     */
    protected function extractRFC($certInfo)
    {
        // Buscar RFC en diferentes campos del certificado
        $possibleFields = ['serialNumber', 'CN', 'OU', 'emailAddress'];
        
        foreach ($possibleFields as $field) {
            if (isset($certInfo['subject'][$field])) {
                $value = $certInfo['subject'][$field];
                // Buscar patrón de RFC (3-4 letras, 6 números, 3 caracteres alfanuméricos)
                if (preg_match('/([A-Z&Ñ]{3,4}[0-9]{6}[A-Z0-9]{3})/', $value, $matches)) {
                    return $matches[1];
                }
            }
        }
        
        // Si no se encuentra en campos estándar, buscar en todo el subject string
        $subjectString = '';
        foreach ($certInfo['subject'] as $key => $value) {
            $subjectString .= "$key=$value ";
        }
        
        if (preg_match('/([A-Z&Ñ]{3,4}[0-9]{6}[A-Z0-9]{3})/', $subjectString, $matches)) {
            return $matches[1];
        }
        
        return '';
    }
    
    /**
     * Busca un usuario por RFC en la base de datos
     */
    public function findUserByRFC($rfc)
    {
        // Buscar en tabla usuarios
        $usuario = \app\models\Usuarios::find()->where(['rfc' => $rfc])->one();
        if ($usuario) {
            return $usuario;
        }

        // Buscar en tabla provider
        $provider = \app\models\Provider::find()->where(['rfc' => $rfc])->one();
        if ($provider) {
            return \app\models\Usuarios::findOne($provider->user_id);
        }

        // Buscar en tabla rfc
        $rfcModel = \app\models\Rfc::find()->where(['rfc' => $rfc])->one();
        if ($rfcModel) {
            $provider = \app\models\Provider::findOne($rfcModel->provider_id);
            if ($provider) {
                return \app\models\Usuarios::findOne($provider->user_id);
            }
        }

        return null;
    }
}
