<?php
/*
 prod

$dsn = 'pgsql:host=localhost;dbname=secopcourse;port=5432';
$username = 'cyap';
$password = 'FDA285d89';
 * */

/*
 test
$dsn = 'pgsql:host=localhost;dbname=secopcourse;port=5432';
$username = 'postgres';
$password = 'postgres';
 * */

$db_engine = $_ENV['DB_ENGINE'];
$dbname = $_ENV['DB_COURSE'];
$host = $_ENV['DB_HOST'];
$port = $_ENV['DB_PORT'];
$username = $_ENV['DB_USER'];
$password = $_ENV['DB_PASS'];

$dsn = "{$db_engine}:host={$host};dbname={$dbname};port={$port}";

ini_set('max_execution_time','0');

return [
    'class' => 'yii\db\Connection',
    'dsn' => $dsn,
    'username' => $username,
    'password' => $password,
    'charset' => 'utf8',
];