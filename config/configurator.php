<?php 


class Configurator{

	/**
	* Initiate environment
	*/
	public static function init(){
        self::setEnvironment();
		require(__DIR__ . '/../vendor/autoload.php');
        require(__DIR__ . '/../vendor/yiisoft/yii2/Yii.php');   
	}

	/**
	* Load environment type from dot env file, if exists
	* If not found default framework settings will apply (ENV=prod, DEBUG=false)
	*/
	protected static function setEnvironment(){
		// check for dot env file
		if(is_file(__DIR__ . '/../.env')){

			$conf = parse_ini_file(__DIR__ . '/../.env', true);
			if(false === $conf){
				throw new \Exception("Failed parsing [.env] file.");
			}

            foreach($conf as $key => $value){
				$_ENV[$key] = ($value == 'false' || $value == 'true') ? filter_var($value, FILTER_VALIDATE_BOOLEAN) : $value;
            } 

			if(!empty($conf['ENVIRONMENT'])){
				switch(strtolower($conf['ENVIRONMENT'])){
					case 'prod':
						defined('YII_ENV') or define('YII_ENV', 'prod');
						defined('YII_DEBUG') or define('YII_DEBUG', false);
						break;
					case 'test':
						defined('YII_ENV') or define('YII_ENV', 'test');
						break;
					case 'dev':
						defined('YII_ENV') or define('YII_ENV', 'dev');
						break;
				}
			}

			if(isset($conf['DEBUG'])){
				defined('YII_DEBUG') || define('YII_DEBUG', filter_var($conf['DEBUG'], FILTER_VALIDATE_BOOLEAN) );
			}
		}
	}
}



?>