<?php
//pruebas
/*
$dsn = 'pgsql:host=localhost;dbname=secop_pruebas;port=5432';
$username = 'postgres';
$password = 'Gri<PERSON>lvar<PERSON>';
*/
$dsn = 'pgsql:host=***********;dbname=siagnl;port=5432;';
$username = 'evalera';
$password = 'ev240217';

if (isset($_ENV['SECOP_CONNECTION_STRING'])) {
    $dsn = $_ENV['SECOP_CONNECTION_STRING'];
}
if (isset($_ENV['SECOP_DATABASE_PASSWORD'])) {
    $password = $_ENV['SECOP_DATABASE_PASSWORD'];
}
if (isset($_ENV['SECOP_DATABASE_USER'])) {
    $username = $_ENV['SECOP_DATABASE_USER'];
}

//$dsn = 'pgsql:host=************;dbname=siagnl;port=5432;';
//$username = 'secop';
//$password = 'secop123';

return [
    'class' => 'yii\db\Connection',
    'dsn' => $dsn,
    'username' => $username,
    'password' => $password,
    'charset' => 'utf8',
];
