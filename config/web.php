<?php

$params = require(__DIR__ . '/params.php');
$config = [
    'id' => 'basic',
    'timeZone'=>'America/Monterrey',
    'name'=>'Proveedores Nuevo León',
    'basePath' => dirname(__DIR__),
    'bootstrap' => ['log'],
    'language' => 'es',
    'components' => [
        /*'session' => [
            'class' => 'yii\web\DbSession',
            'cookieParams' => [ 'domain' => $params['secop_subdomain'] ]
        ],*/

        'assetManager' => [
            'bundles' => [
                'yii\bootstrap\BootstrapAsset' => [
                    'depends' => [
                        'yii\jui\JuiAsset'
                    ],
                ],
            ], 
        ],
        'i18n' => [
            'translations' => [
                /*'kvgrid' => [
                    'class' => 'yii\i18n\PhpMessageSource',
                    'basePath' => '@vendor/kartik-v/yii2-grid/messages',
                ],*/ //Se comenta para que puedan traducirse los labels de apoyo del gridview de kartik
                'user*' => [
                    'class' => 'yii\i18n\PhpMessageSource',
                    'sourceLanguage' => 'en-US',
                    'basePath' => '@app/messages'
                ],
            ],
        ],
        'jwt' => [
            'class' => 'sizeg\jwt\Jwt',
            'key' => 'secret',
        ],
        'authManager' => [
            'class' => 'yii\rbac\DbManager',
        ],
        'request' => [
            // !!! insert a secret key in the following (if it is empty) - this is required by cookie validation
            'cookieValidationKey' => 'ZjENgAlUvZaztyCucZJ6BW3ht5MCcCYN',
            'enableCsrfValidation' => false,
        ],
        'cache' => [
            'class' => 'yii\caching\FileCache',
        ],
        'user' => [
            'identityClass' => 'app\models\Usuarios',
            'idParam'=>'userProv',
            'enableAutoLogin' => true,
            'authTimeout'=>3600*2,//2 horas se sesion
            'identityCookie' => [
                'name' => '_loginUser',
            ]
        ],
        'course' => [
            'class'=>'yii\web\User',
            'identityClass' => 'app\models\Course',
            'idParam'=>'userCourse',
            'enableAutoLogin' => true,
            'loginUrl' => ['course/login'],
            'identityCookie' => [
                'name' => '_loginCourse',
            ]
        ],
        'errorHandler' => [
            'errorAction' => 'site/error',
        ],
        'mailer' => [
            'class' => 'yii\swiftmailer\Mailer',
            // send all mails to a file by default. You have to set
            // 'useFileTransport' to false and configure a transport
            // for the mailer to send real emails.
            'useFileTransport' => false,
            'transport' => [
                'class' => 'Swift_SmtpTransport',
                'host' => $_ENV['SMTP_HOST'],
                'username' => $_ENV['SMTP_USER'],
                'password' => $_ENV['SMTP_PASSWORD'],
                'port' => $_ENV['SMTP_PORT'],
                'encryption' => $_ENV['SMTP_ENCRYPTION'],
                /* 'streamOptions' => [
                    'ssl' =>
                        [
                            'verify_peer' => false,
                            'verify_peer_name' => false,
                        ],
                ]*/
            ],
        ],
        'awssdk' => [
            'class' => 'fedemotta/awssdk/AwsSdk',
            'credentials' => [ //you can use a different method to grant access
                'key' => '********************',
                'secret' => 'tNUE9IZABowMJ66bEY+r2FXRO3tdvZQxTmxBse3e',
            ],
            'region' => 'us-west-2', //i.e.: 'us-east-1'
            'version' => 'latest', //i.e.: 'latest'
        ],
        'log' => [
            'traceLevel' => YII_DEBUG ? 3 : 0,
            'targets' => [
                [
                    'class' => 'yii\log\FileTarget',
                    'levels' => ['error', 'warning'],
                ],
            ],
        ],
        'db' => require(__DIR__ . '/db.php'),
        'db2' => require(__DIR__ . '/db2.php'),
        'dbcourse' => require(__DIR__ . '/dbcourse.php'),
       'urlManager' => [
        'enablePrettyUrl' => true,
        'showScriptName' => false,
        'rules' => [
            '' => 'site/index'
        ],
    ],
    ],
    'modules' => [
            'user' => [
            'class' => 'app\custom\Module',
        ],
    ],
    'params' => $params,
    'container' => [
        'definitions' => [
            'yii\widgets\LinkPager' => 'app\helpers\CustomLinkPager',
            'kartik\grid\GridView' => 'app\helpers\ExportGridView'
        ]
    ]
];

if (YII_ENV_DEV) { 
    // configuration adjustments for 'dev' environment
    $config['bootstrap'][] = 'debug';
    $config['modules']['debug'] = [
        'class' => 'yii\debug\Module',
    ];

    $config['bootstrap'][] = 'gii';
    $config['modules']['gii'] = [
        'class' => 'yii\gii\Module',
    ];

     $config['modules']['gridview'] = [
         'class' => '\kartik\grid\Module'
     ];

    $config['modules']['datecontrol'] = [
            'class' => '\kartik\datecontrol\Module'
    ];
}

return $config;
