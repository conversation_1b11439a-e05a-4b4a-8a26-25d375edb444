<?php

return [
    'notifAvailable'=>false,
    'adminEmail' => '<EMAIL>',
    'IVA' => isset($_ENV['IVA'])?$_ENV['IVA']:0.16,
    'urlToken' => 'https://insumos.nl.gob.mx:3031/getToken?user=PRUEBA&pass=prueba',
    'urlQr' => 'https://insumos.nl.gob.mx:3031/generateQr',
    'urlFirma' => 'https://insumos.nl.gob.mx:3031/Signature',
    'urlBarCode' => 'https://insumos.nl.gob.mx:3031/generateBarCode',
    'socketToken' => 'VfjYjwjVwO94eTDI6X9FFxLH3CEmMVt1',
    'sign' =>'NmMzODA4MmI2YjMxMzdjYWYyMzg3MTE0ZThmZjg1ZDlhNWIyMmVhNzFiNTZiYWNiNzUzNWIwNjQ0NTlmNWI3YThhZjZkOTBhYmI2NDM1NWZkYjAwYTY5OGQ1OTRiMmUyMjg1Mzc3YmMyODA4NzNiNjgxMjZiODU5Mzg1NDAxMzM1ZDMyOGM3NjQ0NDVlZmUzMGUxZmMzNmUwM2ZkMGY4NzRmYjBjYjE1MTU4ZTNmOGJiYmJhNmVjYTdkNTM3ZmQzOTUwZjdhOWM3YjkyNGQ2YmQzNzhkOTc1NDVlMGZiZTA4MDZjOTJjMWI0ZTk3N2JkM2Q0MzUwMmRiNDk0OTNlOA==',
    'secop_subdomain' => isset($_SERVER['SECOP_SUBDOMAIN']) ? $_SERVER['SECOP_SUBDOMAIN'] : '.proveedores-test.com',
    'secop_url_old' => isset($_SERVER['SECOP_URL_OLD']) ?  $_SERVER['SECOP_URL_OLD'] : 'http://proveedores-test.com',
    'secop_url_beta' => isset($_SERVER['SECOP_URL_BETA']) ?  $_SERVER['SECOP_URL_BETA'] : 'http://beta.proveedores-test.com',
    'key_encode' => 'fldsmdfr',
    'url_funcionarios' => 'https://app.st.nl.gob.mx/MiPortalQA/APIRest/api/Values',
    'url_post_funcionarios' => 'https://app.st.nl.gob.mx/MiPortalQA/APIRest/api/Consulta',
    'url_insumos_jwt' => 'http://insumos.test.nl.gob.mx/api/auth',
    'url_insumos_consulta' => 'http://insumos.test.nl.gob.mx:3031/searchldap',
    'url_insumos_login_ad' => 'http://insumos.test.nl.gob.mx:3031/loginldap',
    'insumos_jwt_credenciales' => ['username' => 'fun1', 'password' => 'prueba123'],
    //'sireApiPublic' => 'MSHSNYHIXS',
    //'sireApiSecret' => '236F3E48621F1354F8EE8E91310F232788B19ECB',
    //'sireBaseUrl' => 'http://************:1522/apirest/catalogos/',
    'flag_concursos' => true,
    'entidad_id'=>22,
    'id_tipo_servicio'=>804,
    'api_pago' => 'http://**************/payments-api/v1',
    'api_consulta_pago' => 'http://*************/wsconsulta/ws_motorpago_qa.php?wsdl',
    'api_auth_pago' => 'Bearer yf3puRWCxfgV9kTTg9xK8mmo74QAhatjtvN2662RUrfC9WVaH7RGD7yUFJQyNF22JJvdhibXKv7kc298wLKtEYd39H9mfijq6XLk'
];
