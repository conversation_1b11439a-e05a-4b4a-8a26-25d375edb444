<?php
// Script para verificar si existe el RFC en la base de datos

// Configurar <PERSON><PERSON>
defined('YII_DEBUG') or define('YII_DEBUG', true);
defined('YII_ENV') or define('YII_ENV', 'dev');

require(__DIR__ . '/vendor/autoload.php');
require(__DIR__ . '/vendor/yiisoft/yii2/Yii.php');

$config = require(__DIR__ . '/config/web.php');
$app = new yii\web\Application($config);

use app\models\Usuarios;
use app\models\Provider;
use app\models\Rfc;

$rfc = 'OASH970823LB6';
echo "Buscando RFC: $rfc\n\n";

// Buscar en tabla usuarios
$usuario = Usuarios::find()->where(['rfc' => $rfc])->one();
if ($usuario) {
    echo "✅ Usuario encontrado en tabla usuarios:\n";
    echo "   - ID: " . $usuario->user_id . "\n";
    echo "   - Username: " . $usuario->username . "\n";
    echo "   - Email: " . $usuario->email . "\n";
    echo "   - Status: " . $usuario->status . "\n\n";
} else {
    echo "❌ No se encontró usuario en tabla usuarios\n\n";
}

// Buscar en tabla provider
$provider = Provider::find()->where(['rfc' => $rfc])->one();
if ($provider) {
    echo "✅ Provider encontrado en tabla provider:\n";
    echo "   - Provider ID: " . $provider->provider_id . "\n";
    echo "   - User ID: " . $provider->user_id . "\n";
    echo "   - Nombre/Razón Social: " . $provider->getFullName() . "\n\n";
} else {
    echo "❌ No se encontró provider en tabla provider\n\n";
}

// Buscar en tabla rfc
$rfcModel = Rfc::find()->where(['rfc' => $rfc])->one();
if ($rfcModel) {
    echo "✅ RFC encontrado en tabla rfc:\n";
    echo "   - Provider ID: " . $rfcModel->provider_id . "\n";
    
    $provider = Provider::findOne($rfcModel->provider_id);
    if ($provider) {
        echo "   - Provider asociado: " . $provider->getFullName() . "\n";
        echo "   - User ID: " . $provider->user_id . "\n";
    }
    echo "\n";
} else {
    echo "❌ No se encontró en tabla rfc\n\n";
}

// Buscar usuarios similares
echo "Buscando usuarios con RFC similar...\n";
$similares = Usuarios::find()->where(['like', 'rfc', 'OASH%', false])->limit(5)->all();
foreach ($similares as $similar) {
    echo "   - RFC: " . $similar->rfc . " | Username: " . $similar->username . "\n";
}

echo "\nBusqueda completada.\n";
