<?php

namespace app\helpers;

use yii\widgets\DetailView;
use yii\helpers\ArrayHelper;
use yii\helpers\Html;

class AtributosDetailView extends DetailView{

    protected function renderAttribute($attribute, $index)
    {
        if(!$attribute['value']){
            $local_template = '<tr><th{captionOptions}>{label}</th></tr>';
            if (is_string($local_template)) {
                $captionOptions = Html::renderTagAttributes(ArrayHelper::getValue($attribute, 'captionOptions', []));
                return strtr($local_template, [
                    '{label}' => $attribute['label'],
                    '{value}' => $this->formatter->format($attribute['value'], $attribute['format']),
                    '{captionOptions}' => $captionOptions,
                ]);
            }
    
            return call_user_func($local_template, $attribute, $index, $this);
        }
    }

    public function run()
    {
        $rows = [];
        $i = 0;
        if($this->isComplete($this->attributes)){
            /* echo "<div><h5 class='text-center'>Modulo completo</h5></div>"; */
        }else{
            foreach ($this->attributes as $attribute) {
                $rows[] = $this->renderAttribute($attribute, $i++);
            }
    
            $options = $this->options;
            $tag = ArrayHelper::remove($options, 'tag', 'table');
            echo Html::tag($tag, implode("\n", $rows), $options);
        }
        
    }

    public function isComplete($model){
        $complete = true;
        foreach($model as $atributo){
            if(empty($atributo['value']) || $atributo['value'] == null){
                $complete = false;
            }
        }

        return $complete;
    }

}

?>