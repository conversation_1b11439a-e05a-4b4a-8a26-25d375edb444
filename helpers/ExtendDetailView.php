<?php

namespace app\helpers;

use yii\widgets\DetailView;
use yii\helpers\ArrayHelper;
use yii\helpers\Html;

class ExtendDetailView extends DetailView{

    public $id_model;
    public $modulo;
    public $modelo;
    public $show_checkbox;
    public $rechazo;

    protected function renderAttribute($attribute, $index)
    {
        $atributo = isset($attribute['attribute']) ? $attribute['attribute'] : '';
        $disabled = $this->excludeAttr($atributo) ? 'disabled' : '';
        $nombre_modelo = is_array($this->model)? $this->modelo : $this->model->formName();
        $local_template = ($this->show_checkbox) ? '<tr '.$this->rechazoAttr($this->rechazo, $nombre_modelo, $this->id_model, $atributo).'><th{captionOptions}>{label}</th><td{contentOptions}>{value}</td><td class="checkbox-rechazo"><input class="att_rechazo" type="checkbox" name="{modulo}" modelo="{modelo}" id_model={id_model} value="{attribute}" '.$disabled.' ></td></tr>' : '<tr><th{captionOptions}>{label}</th><td{contentOptions}>{value}</td></tr> ';
        if (is_string($local_template)) {
            $captionOptions = Html::renderTagAttributes(ArrayHelper::getValue($attribute, 'captionOptions', []));
            $contentOptions = Html::renderTagAttributes(ArrayHelper::getValue($attribute, 'contentOptions', []));
            return strtr($local_template, [
                '{label}' => $attribute['label'],
                '{modulo}' => $this->modulo,
                '{modelo}' => $nombre_modelo,
                '{id_model}' => $this->id_model,
                '{attribute}' => $atributo,
                '{value}' => $this->formatter->format($attribute['value'], $attribute['format']),
                '{captionOptions}' => $captionOptions,
                '{contentOptions}' => $contentOptions,
            ]);
        }

        return call_user_func($local_template, $attribute, $index, $this);
    }

    private function excludeAttr($atributo){
        $excludeNames = ['rfc','name_razon_social','email_registro'];
        return in_array($atributo, $excludeNames);
    }

    private function rechazoAttr($rechazo, $modelo, $id_modelo, $atributo){
        return (!empty($rechazo) && GeneralController::hasAtrRechazo($rechazo['atributos'], $modelo, $id_modelo, $atributo)) ? 'class = "danger"' : '';
    }

}

?>