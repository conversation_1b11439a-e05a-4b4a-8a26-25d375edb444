<?php

namespace app\helpers;

use app\models\Modulos;
use app\models\Provider;
use app\models\Usuarios;

use Yii;

/* Clase encargada de encapsular funciones de utilidad en la plataforma, algo asi como GeneralController pero mas bonito */
class FunctionsHelper{

    const DOCUMENTOS_INFORMATIVOS = [
        'AVISO_PRIVACIDAD' => 'https://www.nl.gob.mx/es/aviso-de-privacidad-integral-para-el-registro-en-el-padron-de-proveedores',
        'REQUISITOS_REGISTRO' => '/docs/avisos/Requisitos_para_el_Registro_en_el_Padron_de_Proveedores_NL_2025.pdf'
    ];

    /**
     *  Funcion encargada de devolder los valores a mostrar correspondientes del usuario autenticado (nombre y siglas) 
     *  return array
     *  */
    public static function getDisplayNames(){
        $names = [ 'd_nombre' => '', 'd_siglas' => ''];
        if (!Yii::$app->user->isGuest){
            if ( isset(Yii::$app->user->identity->tipo) && self::isRole(Usuarios::ROLE_PROVIDER)) {
                $proveedor = Provider::findOne(['user_id' => Yii::$app->user->getId()]);
                if( self::isNullOrEmpty($proveedor) ){ return $names; }
                
                if( $proveedor->isPFisica() ){
                    $names['d_nombre'] = $proveedor->pf_nombre . ' ' . $proveedor->pf_ap_paterno;
                    $names['d_siglas'] = substr($proveedor->pf_nombre,0,1).substr($proveedor->pf_ap_paterno,0,1);
                } else {
                    $names['d_nombre'] = $proveedor->getFullName();
                    $names['d_siglas'] = substr($names['d_nombre'],0,2);
                }

            } else {
                $names['d_nombre'] = !self::isNullOrEmpty(Yii::$app->user->identity->nombre) ? Yii::$app->user->identity->nombre : Yii::$app->user->identity->username;
                $names['d_siglas'] = substr($names['d_nombre'],0,2);
            }
        }
        return $names;
    }

    /** Funcion que se encarga de validar si el usuario tiene cierto rol por el esquema de base de datos o el de asignaciones */
    public static function isRole($role){
        return Yii::$app->user->identity->role == $role || Yii::$app->user->can($role);
    }

    public static function isValidTokenFiel($rfc, $fiel_data){
        $data = self::getDataFromFIEL($fiel_data);
        return trim($rfc) == $data['undefined2'];
    }

    public static function getDataFromFIEL($fiel_data){
        $split = explode('/',$fiel_data);
        $data = [];
        $und = 0;
        $non = 0;
        for ($i = 0; $i < count($split); $i++) {
            $aux = explode('=',$split[$i]);
            if(count($aux) == 1)
                $data['und'.($non++)] = $aux[0];
            else
                $data[$aux[0] == 'undefined' ? $aux[0].($und++) : $aux[0]] = $aux[1];
        }
        return $data;
        
    }

    public static function isNullOrEmpty($value){
        return !isset($value) || empty($value);
    }

    public static function getItemsMenu(){
        $rol = Yii::$app->user->identity->role;
        return Modulos::MENU[$rol];
    }

    /**
     * Funcion encargada de retornar la nueva vigencia de acuerdo si es actualizacion o certificacion.
     * @param bool $is_actualizacion Indica si es actualizacion o no, de ser NO se regresa el valor de la certificacion.
     */
    public static function getNuevaVigencia($is_actualizacion = false){
        return $is_actualizacion ? (date("Y") + 2)."-12-31" : (date("Y") + 1)."-12-31";
    }

    /** 
     * Function encargada de devolver la ruta del documento informativo solicitado, esto porque son documentos cambiantes cada cierto tiempo
     * @param String $tipo_documento El tipo de documento a solicitar
     * @return String
    */
    public static function getRutaDocumento($tipo_documento){
        return isset( self::DOCUMENTOS_INFORMATIVOS[$tipo_documento] ) ? self::DOCUMENTOS_INFORMATIVOS[$tipo_documento] : '';
    }

    public static function limpiarString($str) {
        // Elimina todos los caracteres no alfabéticos y convierte a minúsculas
        return preg_replace('/[^a-zA-Z]/', '', strtolower($str));
    }
    
    /**
     * Compara 2 strings eliminando para una comparacion mas limpia todos los elementos distintos al alfabeto. 
     * @param String $str1 El valor uno en la comparacion
     * @param String $str2 El valor dos en la comparacion
     * 
     * @return bool
     *  */
    function compararStrings($str1, $str2) {
        // Limpia ambas cadenas
        $str1Limpio = self::limpiarString($str1);
        $str2Limpio = self::limpiarString($str2);
    
        return $str1Limpio === $str2Limpio;
    }

}

?>