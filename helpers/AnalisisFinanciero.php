<?php

namespace app\helpers;

use app\models\CapacidadContratacion;
use app\models\ParametrosAnalisis;


class AnalisisFinanciero
{

    public function analisis($proveedor_id)
    {
        $parametros = ParametrosAnalisis::find()->one();

        $parametro1 = 0.0;
        $parametro2 = 0.0;
        $parametro3 = 0.0;

        $datos = CapacidadContratacion::find()->where("provider_id = $proveedor_id and status = 'ACEPTADO'")->orderBy((['(registro_fecha)' => SORT_DESC]))->one();

        if (empty($datos))
            return "Pendiente";

        $pasivo_total = $datos->pasivo_circulante + $datos->pasivo_largo_plazo + $datos->pasivo_diferido;

        $liquidez = $this->liquidez($datos->activo_circulante, $pasivo_total);

        switch ($liquidez) {
            case ($liquidez <= 0.50) : {
                $parametro1 = $parametros->nivel1;
                break;
            }
            case ($liquidez > 0.50 && $liquidez <= 0.75) : {
                $parametro1 = $parametros->nivel2;
                break;
            }
            case ($liquidez > 0.75 && $liquidez <= 1) : {
                $parametro1 = $parametros->nivel3;
                break;
            }
            case ($liquidez > 1 && $liquidez <= 1.25) : {
                $parametro1 = $parametros->nivel4;
                break;
            }
            case ($liquidez > 1.25) : {
                $parametro1 = $parametros->nivel5;
                break;
            }
        }

        $solvencia = $this->solvencia($datos->pasivo_circulante, $datos->activo_circulante);

        switch ($solvencia) {
            case ($solvencia <= 0.50) : {
                $parametro2 = $parametros->nivel1;
                break;
            }
            case ($solvencia > 0.50 && $solvencia <= 0.75) : {
                $parametro2 = $parametros->nivel2;
                break;
            }
            case ($solvencia > 0.75 && $solvencia <= 1) : {
                $parametro2 = $parametros->nivel3;
                break;
            }
            case ($solvencia > 1 && $solvencia <= 1.25) : {
                $parametro2 = $parametros->nivel4;
                break;
            }
            case ($solvencia > 1.25) : {
                $parametro2 = $parametros->nivel5;
                break;
            }
        }

        $apalancamiento = $this->apalancamiento($pasivo_total, $datos->capital_contable);

        switch ($apalancamiento) {
            case ($apalancamiento <= 0.50) : {
                $parametro3 = $parametros->nivel5;
                break;
            }
            case ($apalancamiento > 0.50 && $apalancamiento <= 0.75) : {
                $parametro3 = $parametros->nivel4;
                break;
            }
            case ($apalancamiento > 0.75 && $apalancamiento <= 1) : {
                $parametro3 = $parametros->nivel3;
                break;
            }
            case ($apalancamiento > 1 && $apalancamiento <= 1.25) : {
                $parametro3 = $parametros->nivel2;
                break;
            }
            case ($apalancamiento > 1.25) : {
                $parametro3 = $parametros->nivel1;
                break;
            }
        }

        $capacidad_contratacion = $this->capacidad($datos->activo_circulante, $datos->pasivo_circulante, $parametros->anticipo);
        $resultado = ($capacidad_contratacion) * ($parametro1) * ($parametro2) * ($parametro3);

        return ($resultado + $datos->ventas_totales) / 2;
    }

    private function solvencia($pasivo_circulante, $activo_circulante)
    {
        return $pasivo_circulante / $activo_circulante;
    }

    private function liquidez($activo_circulante, $pasivo_total)
    {
        return $activo_circulante / $pasivo_total;
    }

    private function apalancamiento($pasivo_total, $capital_contable)
    {
        return $pasivo_total / $capital_contable;
    }

    private function capacidad($activo_circulante, $pasivo_circulante, $anticipo)
    {
        return ($activo_circulante - $pasivo_circulante) / $anticipo;
    }

}