<?php
namespace app\helpers;

use Yii;
use yii\filters\AccessControl;
use yii\filters\VerbFilter;

    abstract class ProviderController extends GeneralController
    {

        public function beforeAction($action)
        {

            if (Yii::$app->user->can(Usuarios::ROLE_ADMIN_SECOP)) {
                $this->redirect(array('/usuarios/index'));
            }            
            return true;
        }

    }

?>