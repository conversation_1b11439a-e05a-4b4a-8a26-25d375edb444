<?php

namespace app\helpers;


use app\models\Asignacion;
use app\assets\AppAsset;
use app\controllers\ValidadorController;
use app\models\ActaConstitutiva;
use app\models\AltaHacienda;
use app\models\ComprobanteDomicilio;
use app\models\Curp;
use app\models\DatosValidados;
use app\models\FirstUpdateCertificate;
use app\models\Giro;
use app\models\HistoricoCertificados;
use app\models\IdOficial;
use app\models\IntervencionBancaria;
use app\models\ModulesComplete;
use app\models\Movements;
use app\models\Perfil;
use app\models\Provider;
use app\models\ProviderGiro;
use app\models\Status;
use app\models\Ubicacion;
use app\models\UltimaDeclaracion;
use app\models\Visit;
use app\models\VisitDetails;
use Aws\LocationService\LocationServiceClient;
use moonland\phpexcel\Excel;
use Sinergi\BrowserDetector\Browser;
use Sinergi\BrowserDetector\Os;
use yii\db\ActiveRecord;
use yii\db\Query;
use yii\db\TableSchema;
use yii\web\Controller;
use yii\helpers\ArrayHelper;
use app\models;
use app\models\Firmar;
use app\models\Historico;
use app\models\LastSendValidation;
use app\models\LastSendValidationRelation;
use app\models\Module;
use app\models\Modulos;
use app\models\SaveNotification;
use app\models\UploadFiles;
use app\models\Usuarios;
use Exception;
use Yii;
use mPDF;
use yii\web\UploadedFile;
use yii\helpers\Url;

use kartik\select2\Select2;
use kartik\grid\GridView;
use PhpOffice\PhpSpreadsheet\Calculation\DateTimeExcel\Helpers;
use yii\base\Security;
use yii\web\Response;
use ZipArchive;

abstract class GeneralController extends Controller
{
    public $statusParaEliminar = [Status::STATUS_ENEDICION, Status::STATUS_RECHAZADO, Status::STATUS_VALIDADO];
    public $statusParaTerminar = [Status::STATUS_ENEDICION, Status::STATUS_RECHAZADO, Status::STATUS_VALIDADO];


    public function beforeAction($action)
    {

        if (\Yii::$app->user->isGuest) {
            $excluidos = array(
                'usuarios/create',
                'usuarios/isvalid',
                'registration/',
                'provider/consulta',
                'provider/index-detalle-provider',
                'provider/index-detalle-auditor',
                'provider/detalle-auditor',
                'usuarios/validar-acceso',
                'ayuda/',
                'ayuda/find',
                'ayuda/preguntas-frecuentes',
                'ayuda/secop',
                'ayuda/checklist',
                'ayuda/crear-nuevo-usuario',
                'ayuda/bys-guia-rapida',
                'ayuda/bys-modulos',
                'ayuda/op-guia-rapida',
                'ayuda/op-modulos',
                'ayuda/soporte',
                'carta/externo',
                'carta/video',
                'carta/curso',
                'carta/generacartaprotesta',
                'concursos/viewhome',
                'concursos/view-gob',
                'excepcionado/excepcionado',
                'excepcionado/video',
                'visit/formato',
                'site/player',
                'concursos/concurso/view-gob',
                'concursos/carrito/consultapago',
                'concursos/concurso/get-concursos'
            );
            if (!in_array($action->controller->module->requestedRoute, $excluidos)) {
                return $this->redirect("/site/login")->send();
            }

        }
        if ($action->controller->module->requestedRoute == 'provider/index-detalle-provider') {
            $this->layout = 'main';
        } else {
            $this->layout = 'home';
        }

        if (!\Yii::$app->user->isGuest) {
            if (isset($_GET['ntf']) && !empty($_GET['ntf'])) {

                try {
                    $idNtf = intval(base64_decode($_GET['ntf']));

                    if (($notification = models\SaveNotification::findOne($idNtf)) !== null) {
                        $notification->viewed = true;
                        $notification->viewed_date = date('Y-m-d H:i:s');
                        $notification->save(false);
                        if (Yii::$app->user->can(models\Usuarios::ROLE_PROVIDER)) {
                            $provider_id = models\Usuarios::getProviderid();
                            $dataPro = models\Provider::findOne($provider_id);
                            $dataPro->tipo_provider = $notification->op_bys;
                            $dataPro->save();
                        }
                        if (($number_noti = models\NumberNotification::find()->where(['user_id' => Yii::$app->user->getId()])->one()) !== null) {
                            $number_noti->number_notification = intval($number_noti->number_notification) == 0 || intval($number_noti->number_notification) == 1 ? 0 : intval($number_noti->number_notification) - 1;
                            $number_noti->viewed = false;
                            $number_noti->save();
                        }
                    }
                } catch (\Exception $e) {

                }


            }
            $coo = null;
            if (isset($_COOKIE['_verify_login'])) {
                $coo = $_COOKIE['_verify_login'];
            }


            if (Yii::$app->params['notifAvailable']) {
                $url = Url::base('https') . ':3031';
                $js1 = "var socket = io.connect('$url', { 'forceNew': true }); socket.emit('newUser', { user: " . (Yii::$app->user->id) . ", cookie : '" . $coo . "'})";
                $this->getView()->registerJs($js1);
                $js2 = 'socket.on(\'newNotification\',(n) => {console.log(JSON.stringify(n));new PNotify({ title: \'Notificación\', text: n.mensaje, type: n.tipo, desktop: { desktop: true } });var html = "<a class=\'notificacion_link por_ver\' href = \'"+ n.url +"\'><div class=\'cont_notificacion\'><div class=\'cont_imagen_not\'><img src=\'"+n.imagen+"\'/></div><div class=\'cont_texto_not\'><span class=\'titulo_notificacion\'>"+n.mensaje2+"</span><span class=\'subtitulo_notificacion\'>"+n.subtitulo+"<span style=\'color: "+ n.color +" \'>"+n.extra+"</span></span><span class=\'tiempo_notificacion\'>Hace un momento</span><div class=\'bolita_notificiacion por_ver\'></div></div></div></a> <div class=\'raya_notificacion\'></div>";$("#contNoti").prepend(html); $("#numnoti").html( ( Number.isNaN(parseInt($("#numnoti").html()))? 0: (parseInt($("#numnoti").html()) ) )+1).attr(\'style\',\'display:initial\') })';
                $this->getView()->registerJs($js2);
            }


        }
        return parent::beforeAction($action);
    }

/*
    public function actionDeleteImg($file_key = null, $serial_id = null, $provider_id = null, $file_tmp = null)
    {
        \Yii::$app->controller->enableCsrfValidation = false;
        if ($file_key != null && $serial_id != null && $provider_id != null) {
            $explode = explode('-', $file_key);
            $tipo = $explode[0];
            $model = self::ArrModels($tipo);
            $modelo = $model['modelo'];
            $serialName = $model['tipo'];
            $key = $explode[1];

            $model_his = substr($modelo, 11, strlen($modelo));

            $arr = ArrayHelper::getColumn(models\Historico::findBySql("select data->>'$key' as files from provider.historico where modelo = '$model_his' and provider_id=$provider_id")->asArray()->all(), 'files');

            $remove_file = call_user_func("$modelo::findOne", [$serialName => $serial_id]);

            if (!in_array($remove_file->$key, $arr)) {
                if (file_exists($remove_file->$key)) {
                    try {
                        unlink($remove_file->$key);
                    } catch (Exception $e) {
                    }
                }
            }

             $remove_file->$key = null;
             if (!$remove_file->save()) {
                 return json_encode($remove_file->errors);
             }
        }

        if ($file_tmp != null && strpos($file_tmp, 'archivos_tmp/') !== false) {
            if (file_exists($file_tmp)) {
                unlink($file_tmp);
            }
        }

        return true;
    }

    */


    public static function updateModulesBys($id, $model = null)
    {
        if (!$model)
            return false;
        $modelo = Module::find()->where(['and', ['provider_id' => $id], ['model' => $model]])->one();
        if ($modelo) {
            $modelo->status_bys = Status::STATUS_ENEDICION;
            $modelo->save();
        }
        return true;
    }

    public static function setEdicionModulos($provider_id){
        /*/
        $arr_modulos = ["bys_perfil", "bys_legales", "bys_bys", "bys_economica", "bys_experiencia", "bys_domicilio", "bys_bancos"];
        /*/
        $arr_modulos = ["bys_perfil","bys_bys", "bys_economica",];
        //*/
        foreach($arr_modulos as $modulo){
            self::updateModulesBys($provider_id, $modulo);
        }
        //Provider::updateAll(['status_carta_bys' => 'PENDIENTE'], ['provider_id' => $provider_id]);
    }

    public static function ArrModels($val)
    {
        $array_models = [
            'comprobantedomicilio' => ['modelo' => 'app\models\ComprobanteDomicilio', 'tipo' => 'comprobante_domicilio_id'],
            'representantelegal' => ['modelo' => 'app\models\RepresentanteLegal', 'tipo' => 'representante_legal_id'],
            'intervencionbancaria' => ['modelo' => 'app\models\IntervencionBancaria', 'tipo' => 'id'],
            'fotografianegocio' => ['modelo' => 'app\models\FotografiaNegocio', 'tipo' => 'fotografia_negocio_id'],
            'rfc' => ['modelo' => 'app\models\Rfc', 'tipo' => 'rfc_id'],
            'curp' => ['modelo' => 'app\models\Curp', 'tipo' => 'curp_id'],
            'idoficial' => ['modelo' => 'app\models\IdOficial', 'tipo' => 'idoficial_id'],
            'ultimadeclaracion' => ['modelo' => 'app\models\UltimaDeclaracion', 'tipo' => 'ultima_declaracion_id'],
            'certificacion' => ['modelo' => 'app\models\Certificacion', 'tipo' => 'certificacion_id'],
            'registroactas' => ['modelo' => 'app\models\RegistroActas', 'tipo' => 'acta_id'],
            'actaconstitutiva' => ['modelo' => 'app\models\ActaConstitutiva', 'tipo' => 'acta_constitutiva_id'],
            'relacionaccionistas' => ['modelo' => 'app\models\RelacionAccionistas', 'tipo' => 'relacion_accionistas_id'],
            'modificacionacta' => ['modelo' => 'app\models\ModificacionActa', 'tipo' => 'modificacion_acta_id'],
            'provider' => ['modelo' => 'app\models\Provider', 'tipo' => 'provider_id'],
            'registroimss' => ['modelo' => 'app\models\RegistroImss', 'tipo' => 'registro_imss_id'],
            'escriturapublica' => ['modelo' => 'app\models\EscrituraPublica', 'tipo' => 'escritura_publica_id'],
            'experiencia' => ['modelo' => 'app\models\Experiencia', 'tipo' => 'experiencia_id'],
            'estadofinanciero' => ['modelo' => 'app\models\EstadoFinanciero', 'tipo' => 'estado_financiero_id'],
            'declaracionisr' => ['modelo' => 'app\models\DeclaracionIsr', 'tipo' => 'isr_id'],
            'personaltecnico' => ['modelo' => 'app\models\PersonalTecnico', 'tipo' => 'personal_tecnico_id'],
            'maquinariaequipos' => ['modelo' => 'app\models\MaquinariaEquipos', 'tipo' => 'maquinaria_equipos_id'],
        ];

        return isset($array_models[$val]) ? $array_models[$val] : [];
    }

/*
    public function actionDeleteF($id, $file, $model)
    {

        $array_model = [
            'balance' => models\BalanceEstado::findOne($id),
            'iva' => models\DeclaracionIva::findOne($id),
            'isr' => models\Isr::findOne($id),
            'ultima' => models\UltimaDeclaracion::findOne($id),
            'estado' => models\EstadoFinanciero::findOne($id)
        ];

        $path = \Yii::$app->basePath . '/web/';
        $model_new = $array_model[$model];
        @unlink($path . $model_new->$file);
        $model_new->$file = null;
        $model_new->save();

        return $this->redirect('update-' . $model);
    }


    public function actionEliminar($id, $file, $model, $pro)
    {

        $array_model = [
            'rfc' => models\Rfc::findOne($id),
            'curp' => models\Curp::findOne($id),
            'idoficial' => models\IdOficial::findOne($id),
            'registro_imss' => models\RegistroImss::findOne($id),
            'comprobante_domicilio' => models\ComprobanteDomicilio::findOne($id),
            'cambio_situacion_fiscal' => models\CambioSituacionFiscal::findOne($id),
            'facturas' => models\Facturas::findOne($id),
            'acta_constitutiva' => models\ActaConstitutiva::findOne($id),
            'socio' => models\Socio::findOne($id),
            'escritura_publica' => models\EscrituraPublica::findOne($id),
            'representante_legal' => models\RepresentanteLegal::findOne($id),
            'alta_hacienda' => models\AltaHacienda::findOne($id),
        ];

        $path = \Yii::$app->basePath . '/web/';
        $model_new = $array_model[$model];
        unlink($path . $model_new->$file);
        $model_new->$file = null;
        $model_new->save();

        return $this->redirect('update?id=' . $pro);
    }


    public function actionEliminar_tecnicos($id, $file, $model, $pro)
    {

        $array_model = [
            'certificacion' => models\Certificacion::findOne($id),
            'curriculum' => models\Curriculum::findOne($id),
            'fotografia' => models\FotografiaNegocio::findOne($id),
            'expriencia' => models\Experiencia::findOne($id),
            'personal' => models\PersonalTecnico::findOne($id),
            'organigrama' => models\Organigrama::findOne($id),
            'maquinaria' => models\MaquinariaEquipos::findOne($id),
        ];
        $path = \Yii::$app->basePath . '/web/';
        $model_new = $array_model[$model];
        unlink($path . $model_new->$file);
        $model_new->$file = null;
        $model_new->save();

        return $this->redirect('update?user_id=' . $pro . '&id=' . $id);
    }
*/

    public function actionArchivo($url_archivo)
    {
        return $this->redirect(\Yii::$app->homeUrl . $url_archivo);
    }


    public function actionValidar_datos()
    {
        $modelo = \Yii::$app->request->post()['modelo'];
        $modelos = isset(\Yii::$app->request->post()['modelos']) ? \Yii::$app->request->post()['modelos'] : [];
        $regitro = \Yii::$app->request->post()['register_id'];
        $id = \Yii::$app->request->post()['id'];
        $role = \Yii::$app->user->identity->role;
        $t = \Yii::$app->db->beginTransaction();
        $tipo = $role == 'VALIDADOR PROVEEDORES' ? 'bys' : ($role == 'VALIDADOR CONTRATISTAS' ? 'op' : ($role == 'VALIDADOR FOTOGRAFIA' ? 'bys' : ''));
        //$pro_id = models\Experiencia::find()->select('provider_id')->where([$regitro => $id])->one()->provider_id;
        $status = 'status_' . $tipo;
        $pro_data = call_user_func("app\models\\" . $modelo . "::findOne", [$id]);
        $pro_data->$status = 'VALIDADO';
        if ($pro_data->save(false)) {

            $provider_id = $pro_data->provider_id;
            if ($modelo == 'Experiencia') {
                $this->calcularDatos($provider_id);
            }
            $valid = true;
            if (!empty($modelos)) {
                $rfc_pro = models\Provider::find()->select('rfc')->where(['provider_id' => $provider_id])->one()->rfc;
                $models_arr = json_decode(base64_decode($modelos), true);

                foreach ($models_arr as $k => $value) {
                    $model_historico = new models\Historico();
                    if (models\DatosValidados::Verify($k)) {
                        $value['status_' . $tipo] = 'VALIDADO';
                    }
                    $model_historico->provider_id = $provider_id;
                    $model_historico->rfc = $rfc_pro;
                    $model_historico->validador_id = \Yii::$app->user->getId();
                    $model_historico->modelo = $k;
                    $model_historico->data = $value;
                    $valid = $valid && $model_historico->save();
                }
            }
            if ($valid) {
                $t->commit();
                \Yii::$app->session->setFlash('success', 'Los datos se validaron correctamente');
            } else {
                $t->rollBack();
            }
        } else {
            $t->rollBack();
            \Yii::$app->session->setFlash('error', 'Error al validar los datos');
        }

        return \Yii::$app->getResponse()->redirect(['datosTecnicos/validador/index-validador']);

    }


    public function calcularDatos($pro_id)
    {
        $role = \Yii::$app->user->identity->role;
        $tipo = $role == 'VALIDADOR PROVEEDORES' ? 'bys' : ($role == 'VALIDADOR CONTRATISTAS' ? 'op' : '');
        $query = new Query();
        $query->select("especialidades, count(*) as total, sum(monto) as monto")
            ->from("provider.experiencia")
            ->where(['and', ['provider_id' => $pro_id], ['status_' . $role => 'VALIDADO']])
            ->groupBy("especialidades");
        $command = $query->createCommand();
        $data = $command->queryAll();
        $especialidades = [];
        $total_obras_por_esp = [];
        $suma_monto_por_espe = [];
        $total_especialidades = 0;
        $suma_total_monto = 0;
        $val = true;
        foreach ($data as $key => $value) {
            $especialidades[$key] = $value['especialidades'];
            $total_especialidades += $total_obras_por_esp[$key] = $value['total'];
            $suma_total_monto += $suma_monto_por_espe[$key] = $value['monto'];
        }
        for ($x = 0; $x < count($especialidades); $x++) {
            $sum_dato1 = ($total_obras_por_esp[$x] / $total_especialidades) * 100;
            $sum_dato2 = ($suma_monto_por_espe[$x] / $suma_total_monto) * 100;
            $suma_dato3 = ($sum_dato1 + $sum_dato2) / 2;
            $espe_id = models\contratistas\AnalisisExperiencia::find()->select('analisis_experiencia_id')
                ->where(['and', ['subespecialidad_id' => $especialidades[$x]], ['provider_id' => $pro_id], ['status' => 'ACTIVO']])->one();
            if ($espe_id['analisis_experiencia_id']) {
                $analisis = models\contratistas\AnalisisExperiencia::findOne($espe_id['analisis_experiencia_id']);
                $analisis->status = 'INACTIVO';
                $analisis->save();
            }
            $model = new models\contratistas\AnalisisExperiencia();
            $model->pct_obras_por_especialidades = $sum_dato1;
            $model->pct_monto_por_especialidad = $sum_dato2;
            $model->pct_experiencia_especialidad = $suma_dato3;
            $model->subespecialidad_id = $especialidades[$x];
            $model->provider_id = $pro_id;
            $model->total_obras_por_especialidad = $total_obras_por_esp[$x];
            $model->monto_total_obras_por_especialidad = $suma_monto_por_espe[$x];
            $val = $val && $model->save();
        }
        if ($val) {
            return true;
        } else {
            return false;
        }
    }


    public function msgError()
    {
        if (!Yii::$app->request->isAjax) {
            return $this->renderAjax('/site/error', ['name' => 'Lo sentimos...', 'message' => 'Acceso denegado']);
        } else {
            return $this->renderPartial('/site/error', ['name' => 'Lo sentimos...', 'message' => 'Acceso denegado']);
        }
        exit();
    }


    public function deleteFile($pathFile = null)
    {
        if ($pathFile == null) {
            return $this->redirect('index');
        }
        if (file_exists($pathFile)) {
            try {
                //unlink($pathFile);
            } catch (Exception $e) {
                //var_dump($e).exit();
            }
        }
    }


    public function saveNewFile($model, $fieldFile, $path)
    {
        $OLDFile = $fieldFile . 'OLD';
        $URLFile = $fieldFile . 'URL';
        $this->deleteFile($model->$OLDFile);
        $model->$fieldFile = $path . '/' . md5($model->$URLFile->baseName) . strval(microtime(true) * 10000) . '.' . $model->$URLFile->extension;
        $model->$URLFile->saveAs($model->$fieldFile);
    }


    public function makeDir($dir)
    {
        if (!file_exists($dir)) {
            mkdir($dir, 0777, true);
        }
    }

    public function copyFile($file_tmp, $new_folder)
    {

        if (file_exists($file_tmp)) {
            rename($file_tmp, $new_folder);
        }

    }

    public function sendNotification($type = 'success', $message = null, $user = null, $color = null, $subtitulo = null, $msg_extra = null, $img = null, $url = null, $titulo = null)
    {

        $url = 'http://localhost:3030/sendMessage?token=' . Yii::$app->params['socketToken'] . '&tipo=' . $type . '&mensaje=' . urlencode($message) . "&user=" . $user . "&color=" . $color . "&subtitulo=" . urlencode($subtitulo) . "&extra=" . urlencode($msg_extra) . "&imagen=" . $img . "&url=" . urlencode($url) . "&mensaje2=" . urlencode($titulo);

        self::curlGet($url);
    }


    public function curlGet($url)
    {

        $curl = curl_init();
        curl_setopt($curl, CURLOPT_URL, $url);
        curl_setopt($curl, CURLOPT_POST, FALSE);
        curl_setopt($curl, CURLOPT_HEADER, false);
        curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, false);
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($curl, CURLOPT_ENCODING, "");
        $curlData = curl_exec($curl);
        curl_close($curl);

        return json_decode($curlData, true);
    }

    /**
     * Consulta en servicio st.nl.gob.mx para obtener si un usuario es o no funcionario
     * @param string $url Url del API endpoint
     * @param string $data Data que se envia en el POST
     * @return mixed Respuesta ya interpretada por el json_decode
     */
    public function curlPost($url, $data){

        $curl = curl_init();
        curl_setopt($curl, CURLOPT_URL, $url);
        curl_setopt($curl, CURLOPT_CUSTOMREQUEST, 'GET'); //Se cambio por la tonteria de app.st.nl.gob.mx
        curl_setopt($curl, CURLOPT_POSTFIELDS, $data); //Ya debio pasar por el json_encode
        curl_setopt($curl, CURLOPT_HTTPHEADER, array('Content-Type: application/json'));
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);

        $curlResponse = curl_exec($curl);
        curl_close($curl);

        return json_decode($curlResponse, true);
    }

    /**
     * Consulta en servicio st.nl.gob.mx para obtener si un usuario es o no funcionario
     * @param string $url Url del API endpoint
     * @param mixed $data array que se envia en body form-data
     * @return mixed Respuesta ya interpretada por el json_decode
     */
    public static function curlPostFormData($url, $data){

        $curl = curl_init();
        curl_setopt($curl, CURLOPT_URL, $url);
        curl_setopt($curl, CURLOPT_CUSTOMREQUEST, 'POST'); //Se cambio por la tonteria de app.st.nl.gob.mx
        curl_setopt($curl, CURLOPT_POSTFIELDS, $data); //Ya debio pasar por el json_encode
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);

        $curlResponse = curl_exec($curl);
        curl_close($curl);

        return json_decode($curlResponse, true);
    }

    /**
     * Consulta en servicio st.nl.gob.mx para obtener si un usuario es o no funcionario
     * @param string $rfc RFC a consultar
     * @return string Texto de "Si es funcionario" o "No es funcionario"
     */
    public static function restConsultaFuncionario($rfc){
        $response = "No hay respuesta";
        $url = Yii::$app->params['url_funcionarios'] .'?RFC=';
        if( !is_null($rfc) && !empty($rfc) ){ $response = self::curlGet("$url.$rfc"); }
        return $response;
    }

    /**
     * Consulta en servicio st.nl.gob.mx para obtener si los usuarios son o no funcionarios
     * @param string JSON enviado en POST al servicio con el formato { ["rfc" : "XXXX0101010XX"], ...}
     * @return mixed Mismo JSON que se envio con un campo con la respuesta "Si es funcionario" o "No es funcionario"
     */
    public static function restConsultaFuncionarios($json){
        $url = Yii::$app->params['url_post_funcionarios'];
        $response = self::curlPost($url, $json);
        return $response;
    }


    public static function sendEmail($view = null, $from = null, $email = null, $subject = null, $params = [], $attach = null)
    {

        $from = $from ? $from : '<EMAIL>';

        $arr = ['/provider/correos/nuevo'];
        if (!in_array($view, $arr)) {
            try {
                $mailer = Yii::$app->mailer->compose($view, $params)
                    ->setFrom($from)
                    ->setTo($email)
                    ->setSubject($subject);
                if ($attach != null) {
                    $mailer->attach($attach);
                }
                $mailer->send();
            } catch (\Exception $e) {
                //var_dump($e);
            }
        }
    }

    /**
     * Metodo encargado de enviar correos masivos con copia oculta a los destinatarios
     * @param String $view Vista con los datos y estructura del correo a enviar
     * @param String|mixed $from Correo o array de correos de origen
     * @param mixed $emails Array de correos destinatario
     * @param String $subject Asunto del correo (opcional)
     * @param mixed $params Array con parametros utiles para la vista del correo 
     *  */
    public static function sendMassivePrivateEmail($view = null, $from = null, $emails, $subject = null, $file = null, $params = []){
        try{
            $mailer = Yii::$app->mailer->compose($view, $params)
                ->setFrom($from)
                ->setBcc($emails)
                ->setSubject($subject);
            
            if ($file != null) {
                $mailer->attach($file);
            }

            $mailer->send();
        }catch (Exception $e){ var_dump($e); }
    }

    public static function sendModuloRevisionEmail($correo, $modulo){
        $subject = "El módulo “{$modulo}” fue enviado a revisión";
        self::sendEmail('/provider/correos/envio_modulo_revision',null, $correo, $subject, ['tipo_provider' => 'op', 'modulo' => $modulo]);
    }

    public static function notifyValidacion($email, $validados = null, $rechazados = null)
    {
        $from = '<EMAIL>';
        $view = '/provider/correos/validacion';
        $params = ['validados' => $validados, 'rechazados' => $rechazados];
        try {
            $mailer = Yii::$app->mailer->compose($view, $params)
                ->setFrom($from)
                ->setTo($email);
            $mailer->send();
        } catch (Exception $e) {
            var_dump($e);
        }
    }

    public function sendStatusModuleNotification()
    {
        $sn = new SaveNotification();
        /* $sn->module_notification_url_id =  */
    }


    public function saveSendValidation()
    {


    }


    public function modelsArray($mod)
    {

        $arr = [
            'Representante Legal' => 'Rep'

        ];

        $data = '';
        if (isset($arr[$mod])) {
            $data = $arr[$mod];
        }


        return $data;

    }


    public static function actionSendEmail2($view = '/provider/correos/confirmacion', $from = null, $email = '<EMAIL>', $subject = 'Confirmación de Subscripción ', $params = [], $attach = null)
    {
        $from = '<EMAIL>';
        try {


            $mailer = Yii::$app->mailer->compose($view, ['token' => 'fghfghfghfgh', 'id' => 268, 'tipo_provider' => 'bys'])
                ->setFrom($from)
                ->setTo($email)
                ->setSubject($subject);
            if ($attach != null) {
                $mailer->attach($attach);
            }
            $mailer->send();
        } catch (\Exception $e) {
            var_dump($e);
            exit();
        }
    }

    public static function getEmailValidador($responsabilidad = null, $tipo = null)
    {

        $tipo_pro = $tipo == 'bys' ? 'VALIDADOR PROVEEDORES' : 'VALIDADOR CONTRATISTAS';

        $query = new Query;
        $query->select('usuarios.email')
            ->from('usuarios')
            ->innerJoin('usuarios_responsabilidades',
                'usuarios_responsabilidades.user_id = usuarios.user_id')
            ->where(['and', ['usuarios.role' => $tipo_pro, 'usuarios.status' => 'ACTIVO'], ['usuarios_responsabilidades.responsabilidad_id' => $responsabilidad]]);
        $correos_validadores = $query->createCommand()->queryOne()['email'];


        return $correos_validadores;
    }


    static function providerType()
    {
        return in_array(Yii::$app->user->identity->role, ['VALIDADOR PROVEEDORES', 'ADMIN PROVIDER']) ? 'bys' : 'op';
    }


    public function terminarUno($modelo, $idRegistro, $primaryKey, $modeloStatus, $tipoProvider, $tipo = null, $IdLastVal = null)
    {
        $strModel = "app\models\\" . $modelo;
        $status = false;
        $proId = Yii::$app->user->identity->providerid;
        $model = $strModel::find()
            ->where(['and', ['in', $tipoProvider, $this->statusParaTerminar], [$primaryKey => $idRegistro, 'provider_id' => $proId], ['activo' => true]])
            ->one();
        if ($model) {
            $isLleno = $modelo == "ModificacionActa" ? $this->estaLlenoModificacionActa($model) : $this->estaLleno($model, $tipo);

            if ($isLleno) {
                $model->$tipoProvider = Status::STATUS_PORVALIDAR;
                $model->save(false);

                //$lastSendId,$providier,$modName,$registerId,$proType
                $this->saveLastSendValidationRelation($IdLastVal, $proId, $modeloStatus, $model->$primaryKey, $tipoProvider);
                Status::updateAll([$tipoProvider => Status::STATUS_TERMINADO_PRO], ['register_id' => $model->$primaryKey, 'modelo' => $modeloStatus, $tipoProvider => Status::STATUS_PENDIENTE]);

                models\ExpirationDocuments::updateAll(['status' => true], ['register_id' => $model->$primaryKey, 'module' => $modeloStatus]);

                Yii::$app->session->setFlash('success', 'Registro(s) enviado(s) a revisión.');
                $status = true;
            } else {
                Yii::$app->session->setFlash('error', "Solo los registros llenados completamente, se pueden enviar a revisar.");
            }
        } else {
            Yii::$app->session->setFlash('error', "No se encontró registro a enviar, favor de validar sus datos");
        }

        return $status;
    }


    public function terminarTodos($modelo, $primaryKey, $modeloStatus, $tipoProvider, $tipo = null, $IdLastVal = null)
    {

        $strModel = "app\models\\" . $modelo;
        $status = false;

        if ($modelo == 'ClientesContratos') {

            $tipoPr = $tipoProvider == 'status_bys' ? 'bys' : 'op';

            $where = ['and', [$tipoProvider => Status::STATUS_ENEDICION], ['provider_id' => Yii::$app->user->identity->providerid], ['activo' => true], ['tipo' => $tipoPr]];
        }
        if ($modelo == 'Ubicacion') {
            $where = ['and', [$tipoProvider => Status::STATUS_ENEDICION], ['provider_id' => Yii::$app->user->identity->providerid], ['activo' => true], ['is', 'type_address_prov', null]];
        } else {
            $where = ['and', [$tipoProvider => Status::STATUS_ENEDICION], ['provider_id' => Yii::$app->user->identity->providerid], ['activo' => true]];
        }


        $models = $strModel::find()
            ->where($where)
            ->asArray()
            ->all();
        foreach ($models as $m) {
            $status = $this->terminarUno($modelo, $m[$primaryKey], $primaryKey, $modeloStatus, $tipoProvider, $tipo, $IdLastVal);
        }
        if (count($models) == 0) {
            Yii::$app->session->setFlash('error', "No se encontraron registros para enviar a revisar, revisa tus datos por favor.");
        }

        return $status;
    }


    public function estaLleno($model, $tipo = null)
    {
        $arr = self::limpiarArray($model->getAttributes(), $tipo);
        return (count(array_filter($arr, function ($x) {
                return $x === null || $x === '';
            })) == 0) ? true : false;
    }

    /* public function getInformacionModuloBys($modulo, $opciones_modulo, $provider_id){
        $opciones = ["provider_id" => $provider_id];
        $respuesta_arr = [ 'porcentaje' => 0, 'terminado' => 0, 'rechazo' => 0, 'enviado' => 0, 'pendiente' => 0 ];
        $count_atributos = 0;
        $count_atributos_llenos = 0;
        foreach($modulo as $modelo => $modelo_prop){
            $opciones_modelo = $opciones; //Variable local para opciones de consulta
            if(isset($opciones_modulo[$modelo])){
                if(isset($opciones_modulo[$modelo]["opciones"])){
                    $opciones_modelo = array_merge($opciones_modulo[$modelo]["opciones"], [$opciones_modelo]);
                }
            }
            $obj_modelo = "app\models\\".$modelo;
            if(isset($modelo_prop["isMultiple"])){
                $respuesta = $obj_modelo::find()->where($opciones_modelo)->all();
                if(count( $respuesta ) == 0){ $count_atributos += count($modelo_prop["atributos"]); $respuesta["pendiente"] = 1; }
                else{
                    foreach($respuesta as $key => $valor){
                        $arr_atributos = $modelo_prop["atributos"];
                        if( $valor->formName() == "Certificacion" ){
                            if (isset($valor['undefined']) && $valor['undefined']) {
                                $key = array_search('vigencia', $arr_atributos);
                                unset($arr_atributos[$key]);
                            } elseif (isset($valor['undefined']) && !$valor['undefined']) {
                                $key = array_search('undefined', $arr_atributos);
                                unset($arr_atributos[$key]);
                            }
                        }
                        $count_atributos += count($arr_atributos);
                        foreach($arr_atributos as $atributo){
                            if($valor[$atributo] != null || $valor[$atributo] != '' || $valor[$atributo] === 0){
                                $count_atributos_llenos++;
                            }
                        }
                        if(isset($valor["status_bys"])){
                            $status = $valor["status_bys"];
                            if($status == Status::STATUS_VALIDADO){ $respuesta_arr["terminado"] = 1; }
                            else if($status == Status::STATUS_RECHAZADO){ $respuesta_arr["rechazo"]  = 1; }
                            else if($status == Status::STATUS_PORVALIDAR ){ $respuesta_arr["enviado"] = 1; }
                            else if($status == Status::STATUS_ENEDICION ){ $respuesta_arr["pendiente"] = 1; }
                        }
                    }
                }
            }else{
                $respuesta = $obj_modelo::find()->where($opciones_modelo)->one();
                if( $respuesta == null ){ $count_atributos += count($modelo_prop["atributos"]); $respuesta["pendiente"] = 1; }
                else{
                    $count_atributos += count($modelo_prop["atributos"]);
                    foreach($modelo_prop["atributos"] as $atributo){
                        if($respuesta[$atributo] != null || $respuesta[$atributo] != '' || $respuesta[$atributo] === 0){
                            $count_atributos_llenos++;
                        }
                    }
                    if(isset($respuesta["status_bys"])){
                        $status = $respuesta["status_bys"];
                        if($status == Status::STATUS_VALIDADO){ $respuesta_arr["terminado"] = 1; }
                        else if($status == Status::STATUS_RECHAZADO){ $respuesta_arr["rechazo"]  = 1; }
                        else if($status == Status::STATUS_PORVALIDAR ){ $respuesta_arr["enviado"] = 1; }
                        else if($status == Status::STATUS_ENEDICION ){ $respuesta_arr["pendiente"] = 1; }
                    }

                }
            }
        }
        $diferencia = $count_atributos - $count_atributos_llenos;
        if($diferencia == 0){ $respuesta_arr["porcentaje"] = 100; }
        else{ $respuesta_arr["porcentaje"] = $count_atributos > 0 ? intval(100 - (($diferencia/$count_atributos) * 100)) : 0; }

        if( $respuesta_arr["pendiente"] ){ $respuesta_arr["terminado"] = $respuesta_arr["rechazo"]  = $respuesta_arr["enviado"] = 0; }
        else if( $respuesta_arr["enviado"] ){ $respuesta_arr["terminado"] = $respuesta_arr["rechazo"]  = $respuesta_arr["pendiente"] = 0; }
        else if( $respuesta_arr["rechazo"] ){ $respuesta_arr["terminado"] = $respuesta_arr["enviado"]  = $respuesta_arr["pendiente"] = 0; }
        else if( $respuesta_arr["terminado"] ){ $respuesta_arr["pendiente"] = $respuesta_arr["rechazo"]  = $respuesta_arr["enviado"] = 0; }
        return $respuesta_arr;
    } */

    public function getInformacionModuloBys($nombre_modulo, $modulo, $opciones_modulo, $provider_id)
    {
        $opciones = ["provider_id" => $provider_id];
        $respuesta_arr = ['porcentaje' => 0, 'terminado' => 0, 'rechazo' => 0, 'enviado' => 0, 'pendiente' => 0];
        $count_atributos = 0;
        $count_atributos_llenos = 0;
        foreach ($modulo as $modelo => $modelo_prop) {
            $opciones_modelo = $opciones; //Variable local para opciones de consulta
            if (isset($opciones_modulo[$modelo]) && isset($opciones_modulo[$modelo]["opciones"])) {
                $opciones_modelo = array_merge($opciones_modulo[$modelo]["opciones"], [$opciones_modelo]);
            }
            $obj_modelo = "app\models\\" . $modelo;

            if (isset($modelo_prop["isMultiple"])) {
                $respuesta = $obj_modelo::find()->where($opciones_modelo)->all(); //Consulta del modelo dentro del modulo con las condiciones dadas
                if (count($respuesta) == 0) {
                    if (!isset($modelo_prop['isOpcional'])) {
                        if(isset($modelo_prop['min'])){
                            $count_atributos += (count($modelo_prop["atributos"]) * $modelo_prop['min']);
                        }else{ $count_atributos += count($modelo_prop["atributos"]); }
                    }
                } else {
                    if(isset($modelo_prop['min'])){
                        $total = $modelo_prop['min'] - count($respuesta);
                        $total = $total < 0 ? 0 : $total;
                        $count_atributos += (count($modelo_prop["atributos"]) * $total);
                    }
                    foreach ($respuesta as $key => $valor) {
                        $arr_atributos = $modelo_prop["atributos"]; //Atributos a validar
                        if ($valor->formName() == "Certificacion") {
                            if (isset($valor['undefined']) && $valor['undefined']) {
                                $key = array_search('vigencia', $arr_atributos);
                                unset($arr_atributos[$key]);
                            } elseif (isset($valor['undefined']) && !$valor['undefined']) {
                                $key = array_search('undefined', $arr_atributos);
                                unset($arr_atributos[$key]);
                            }
                        }

                        if($valor->formName() == "RelacionAccionistas"){
                            $arr_atributos = self::limpiezaAtributosRelacionAccionista($arr_atributos, $valor);
                        }

                        $count_atributos += count($arr_atributos);
                        foreach ($arr_atributos as $atributo) {
                            if ($valor[$atributo] != null || $valor[$atributo] != '' || $valor[$atributo] === 0) {
                                $count_atributos_llenos++;
                            }
                        }
                    }

                    if(isset($modelo_prop['funciones'])){ 
                        foreach($modelo_prop['funciones'] as $funcion){
                            $evaluacion = self::$funcion($respuesta);
                            if(!$evaluacion && $valor->formName() == "Giro") { $count_atributos_llenos = $count_atributos_llenos > 0  ? $count_atributos_llenos - 1 : $count_atributos_llenos; }
                        }
                    }
                }
            } else {
                $respuesta = $obj_modelo::find()->where($opciones_modelo)->one();
                if ($respuesta == null) {
                    if (!isset($modelo_prop['isOpcional'])) {
                        $count_atributos += count($modelo_prop["atributos"]);
                    }
                } else {
                    $arr_atributos = $modelo_prop["atributos"];
                    if( $respuesta->formName() == "UltimaDeclaracion" ){
                        if (empty($respuesta['justificante_isn'])) {
                            $key = array_search('justificante_isn', $arr_atributos);
                            unset($arr_atributos[$key]);
                        } elseif (empty($respuesta['cumplimiento_estado'])) {
                            $key = array_search('cumplimiento_estado', $arr_atributos);
                            unset($arr_atributos[$key]);
                        }
                    }
                    $count_atributos += count($arr_atributos);
                    foreach ($arr_atributos as $atributo) {
                        if ($respuesta[$atributo] != null || $respuesta[$atributo] != '' || $respuesta[$atributo] === 0) {
                            $count_atributos_llenos++;
                        }
                    }
                }
            }
        }
        $diferencia = $count_atributos - $count_atributos_llenos;
        $status_modulo = Module::find()->where(['and', ['provider_id' => $provider_id, 'model' => $nombre_modulo]])->one();
        if ($diferencia <= 0) { //Valida que no se entrege un porcentaje superior a 100
            $respuesta_arr["porcentaje"] = 100;
        } else {
            $respuesta_arr["porcentaje"] = $count_atributos > 0 ? intval(100 - (($diferencia / $count_atributos) * 100)) : 0;
            $respuesta_arr['rechazo'] = isset($status_modulo->status_bys) && $status_modulo->status_bys == Status::STATUS_RECHAZADO ? 1 : 0;
            return $respuesta_arr;
        }

        if ($status_modulo == null) {
            $respuesta_arr['pendiente'] = 0;
        } else {
            $respuesta_arr['pendiente'] = isset($status_modulo->status_bys) && $status_modulo->status_bys == Status::STATUS_ENEDICION ? 1 : 0;
            //cuando revisa revisor, aun es necesario mostrarle al proveedor que se esta validando su modulo
            $respuesta_arr['enviado'] = isset($status_modulo->status_bys) &&  in_array($status_modulo->status_bys,[Status::STATUS_PORVALIDAR,Status::STATUS_REVISADO]) ? 1 : 0;
            $respuesta_arr['rechazo'] = isset($status_modulo->status_bys) && $status_modulo->status_bys == Status::STATUS_RECHAZADO ? 1 : 0;
            $respuesta_arr['terminado'] = isset($status_modulo->status_bys) &&  $status_modulo->status_bys == Status::STATUS_VALIDADO ? 1 : 0;
        }

        return $respuesta_arr;
    }

    public function guardarModuloHistorico($modulo, $opciones_modulo, $provider_id, $hcid, $tipo, $override_f_validacion = null){
        $opciones = ["provider_id" => $provider_id];
        foreach($modulo as $modelo => $modelo_prop){
            $opciones_modelo = $opciones; //Variable local para opciones de consulta
            if (isset($opciones_modulo[$modelo]) && isset($opciones_modulo[$modelo]["opciones"])) {
                $opciones_modelo = array_merge($opciones_modulo[$modelo]["opciones"], [$opciones_modelo]);
            }
            $obj_modelo = "app\models\\" . $modelo;

            $data_modelo = isset($modelo_prop["isMultiple"]) ? $obj_modelo::find()->where($opciones_modelo)->all() : $obj_modelo::find()->where($opciones_modelo)->one();
            if($data_modelo){
                $historico = new Historico();
                $historico->provider_id = $provider_id;
                $historico->validador_id = Yii::$app->user->id;
                $historico->data = $data_modelo;
                $historico->historico_certificado_id = $hcid;
                $historico->tipo = $tipo;
                $historico->modelo = $modelo;
                if( !is_null($override_f_validacion) && !empty($override_f_validacion) ){ $historico->fecha_validacion = $override_f_validacion; }
                $historico->save(false);
            }
        }
    }

    public function debugInformacionModuloBys($nombre_modulo, $modulo, $opciones_modulo, $provider_id)
    {
        $opciones = ["provider_id" => $provider_id];
        $response = [];
        foreach ($modulo as $modelo => $modelo_prop) {
            $opciones_modelo = $opciones; //Variable local para opciones de consulta
            if (isset($opciones_modulo[$modelo]) && isset($opciones_modulo[$modelo]["opciones"])) {
                $opciones_modelo = array_merge($opciones_modulo[$modelo]["opciones"], [$opciones_modelo]);
            }
            $obj_modelo = "app\models\\" . $modelo;
            if (isset($modelo_prop["isMultiple"])) {
                $respuesta = $obj_modelo::find()->select($modelo_prop["atributos"])->where($opciones_modelo)->all();
                if (count($respuesta) == 0) {
                    if (!isset($modelo_prop['isOpcional'])) {
                        $response[$modelo] = $modelo_prop["atributos"];
                    }
                } else {
                    foreach ($respuesta as $key => $valor) {
                        $filtrado = $this->filterAttributes($valor);
                        $valores_nulos = array_filter($filtrado, function ($valor) {
                            return $valor === null || $valor === '';
                        }); //Regresa los elementos nulos
                        if (count($valores_nulos) > 0) {
                            $response[$modelo] = $valores_nulos;
                        }
                    }
                }
            } else {
                $respuesta = $obj_modelo::find()->select($modelo_prop["atributos"])->where($opciones_modelo)->one();
                if ($respuesta == null) {
                    if (!isset($modelo_prop['isOpcional'])) {
                        $response[$modelo] = $modelo_prop["atributos"];
                    }
                } else {
                    $filtrado = $this->filterAttributes($respuesta);
                    $valores_nulos = array_filter($filtrado, function ($valor) {
                        return $valor === null || $valor === '';
                    }); //Regresa los elementos nulos
                    if (count($valores_nulos) > 0) {
                        $response[$modelo] = $valores_nulos;
                    }
                }
            }
        }

        $nombre_modulo['atributos'] = $response;
        return $nombre_modulo;
    }

    /* public function isModuloCompleto($provider_id, $modulo, $opciones_modulos){
        $isComplete = true;
        $opciones = ["provider_id" => $provider_id];
        foreach($modulo as $modelo => $modelo_prop){
            $opciones_modelo = $opciones; //Variable local para opciones de consulta
            if(isset($opciones_modulos[$modelo])){
                if(isset($opciones_modulos[$modelo]["opciones"])){
                    $opciones_modelo = array_merge($opciones_modulos[$modelo]["opciones"], [$opciones_modelo]);
                }
            }
            $obj_modelo = "app\models\\".$modelo;
            if(isset($modelo_prop["isMultiple"])){
                $respuesta = $obj_modelo::find()->select($modelo_prop["atributos"])->where($opciones_modelo)->all();
                if( count( $respuesta ) == 0 ){ return false; }
                foreach($respuesta as $key => $valor){ if(!$this->isModeloCompleto($valor)){ return false; }  }
            }else{
                $respuesta = $obj_modelo::find()->select($modelo_prop["atributos"])->where($opciones_modelo)->one();
                if(!$this->isModeloCompleto($respuesta)){ return false; }
            }
        }
        return $isComplete;
    } */

    public function isModuloCompleto($provider_id, $tipo_persona, $nombre_modulo, $opciones_modulo)
    {
        $isComplete = true;
        $opciones = ["provider_id" => $provider_id];
        $modulo_atributos = self::getModuloAtributos($nombre_modulo, $tipo_persona);
        if ($modulo_atributos == null) {
            return $isComplete;
        }
        foreach ($modulo_atributos as $modelo => $modelo_prop) {
            $opciones_modelo = $opciones; //Variable local para opciones de consulta
            if (isset($opciones_modulo[$modelo])) {
                $opciones_modelo = array_merge($opciones_modulo[$modelo]["opciones"], [$opciones_modelo]);
            }
            $obj_modelo = "app\models\\" . $modelo;
            if (isset($modelo_prop["isMultiple"])) {
                $respuesta = $obj_modelo::find()->select($modelo_prop["atributos"])->where($opciones_modelo)->all();
                if (count($respuesta) == 0) {
                    if (!isset($modelo_prop['isOpcional'])) {
                        return false;
                    }
                } else {
                    if(isset($modelo_prop['min'])){
                        if( count($respuesta) < $modelo_prop['min'] ){ return false; }
                    }
                    foreach ($respuesta as $key => $valor) {
                        if (!self::isModeloCompleto($valor)) {
                            return false;
                        }
                    }
                    if(isset($modelo_prop['funciones'])){ 
                        foreach($modelo_prop['funciones'] as $funcion){
                            $evaluacion = self::$funcion($respuesta);
                            if(!$evaluacion) { return false;}
                        }
                    }
                }
            } else {
                $respuesta = $obj_modelo::find()->select($modelo_prop["atributos"])->where($opciones_modelo)->one();
                if ($respuesta == null) {
                    if (!isset($modelo_prop['isOpcional'])) {
                        return false;
                    }
                } else {
                    if (!self::isModeloCompleto($respuesta)) {
                        return false;
                    }
                }
            }
        }
        return $isComplete;
    }

    public function isModeloCompleto($resultado)
    {
        if ($resultado == null) {
            return false;
        }
        $valores_modelo = self::filterAttributes($resultado);//Regresa los valores requeridos por el modelo
        $valores_nulos = array_filter($valores_modelo, function ($valor) {
            return $valor === null || $valor === '';
        }); //Regresa los elementos nulos
        if (count($valores_nulos) != 0) {
            return false;
        } //Si se detecta un valor nulo, el modulo esta incompleto
        return true;
    }

    public function filterAttributes($resultado) {
        $atributos_filtro = $resultado->fields();
        if ($resultado->formName() == "RelacionAccionistas") {
            $valores = $resultado->getAttributes($atributos_filtro); //Consulta temporal para validar el contenido del rfc
            $atributos_filtro = self::limpiezaAtributosRelacionAccionista($atributos_filtro, $valores );
        }
        $atributos = $resultado->getAttributes($atributos_filtro);
        if ($resultado->formName() == "Certificacion") {
            if (isset($atributos['undefined']) && $atributos['undefined']) {
                unset($atributos['vigencia']);
            } elseif (isset($atributos['undefined']) && !$atributos['undefined']) {
                unset($atributos['undefined']);
            }
        }
        if( $resultado->formName() == "UltimaDeclaracion" ){
            if (empty($atributos['cumplimiento_estado'])) { unset($atributos['cumplimiento_estado']);  }
            elseif (empty($atributos['justificante_isn'])) { unset($atributos['justificante_isn']); }
        }

        return $atributos;
    }

    public function estaLlenoUltimaDeclaracion($model)
    {
        $excepciones = ['comprobantes_de_pago', 'ultima_declaracion_pago_prov', 'balance_general', 'estado_resultado', 'balance_general_anterior', 'estado_resultado_anterior'];
        $arr = self::limpiarArray($model->getAttributes(), null);
        foreach ($arr as $k => $v) {
            if (in_array($k, $excepciones)) {
                unset($arr[$k]);
            }
        }
        return (count(array_filter($arr, function ($x) {
                return $x === null || $x === '';
            })) == 0) ? true : false;
    }

    public function estaLlenoModificacionActa($model)
    {
        $excepciones = ['descripcion_cambios', 'nombre_documento'];
        $arr = $model->getAttributes();
        foreach ($arr as $k => $v) {
            if (in_array($k, $excepciones)) {
                unset($arr[$k]);
            }
        }
        return (count(array_filter($arr, function ($x) {
                return $x === null || $x === '';
            })) == 0) ? true : false;
    }

    public function estaLlenoActaConstitutiva($model)
    {
        $excepciones = ['folio_mercantil', 'ciudad_id', 'estado_id'];
        $arr = $model->getAttributes();
        foreach ($arr as $k => $v) {
            if (in_array($k, $excepciones)) {
                unset($arr[$k]);
            }
        }
        return (count(array_filter($arr, function ($x) {
                return $x === null || $x === '';
            })) == 0) ? true : false;
    }


    static function estaLlenoProvider($model)
    {

        $arr = self::limpiarArray($model, null);
        return $statusLleno = (count(array_filter($arr, function ($x) {
                return $x === null || $x === '';
            })) == 0) ? true : false;
    }


    public function estaLlenoActa($model)
    {
        $arr = self::limpiarArray($model->getAttributes(), null, 'Acta');
        return (count(array_filter($arr, function ($x) {
                return $x === null || $x === '';
            })) == 0) ? true : false;
    }

    public function porcentajeTabla($model, $tipoProvider, $provider_id = null)
    {
        $prov_id = $provider_id == null ? Yii::$app->user->identity->providerid : $provider_id;
        $modelo = "app\models\\" . $model;

        if ($model == 'Ubicacion') {
            $where = ['and', ['activo' => TRUE], ['provider_id' => $prov_id], ['is', 'type_address_prov', null]];
        } else {
            $where = ['and', ['activo' => TRUE], ['provider_id' => $prov_id]];
        }

        return $modelo::find()->select("100-((sum(case when ( $tipoProvider = '" . Status::STATUS_RECHAZADO . "' or $tipoProvider = '" . Status::STATUS_ENEDICION . "') then 1 else 0 end) )*100)/count(1) as porcentaje")
            ->where($where)->asArray()->one()['porcentaje'];
    }

    public function porcentajeTablaCarta($model, $tipoProvider, $provider_id = null)
    {

        $prov_id = $provider_id == null ? Yii::$app->user->identity->providerid : $provider_id;

        $modelo = "app\models\\" . $model;


        if ($model == 'Ubicacion') {
            $where = ['and', ['activo' => TRUE], ['provider_id' => $prov_id], ['is', 'type_address_prov', null]];
        } else {
            $where = ['and', ['activo' => TRUE], ['provider_id' => $prov_id]];
        }

        return $modelo::find()->select("100-((sum(case when ( $tipoProvider = '" . Status::STATUS_RECHAZADO . "' or $tipoProvider = '" . Status::STATUS_ENEDICION . "' or $tipoProvider = '" . Status::STATUS_PORVALIDAR . "') then 1 else 0 end) )*100)/count(1) as porcentaje")
            ->where($where)
            ->asArray()->one()['porcentaje'];
    }

    public function porcentajeTablaCartaValPen($model, $tipoProvider, $provider_id = null)
    {

        $prov_id = $provider_id == null ? Yii::$app->user->identity->providerid : $provider_id;

        $modelo = "app\models\\" . $model;


        if ($model == 'Ubicacion') {
            $where = ['and', ['activo' => TRUE], ['provider_id' => $prov_id], ['is', 'type_address_prov', null]];
        } else {
            $where = ['and', ['activo' => TRUE], ['provider_id' => $prov_id]];
        }

        $data = $modelo::find()->select("100-((sum(case when ( $tipoProvider = '" . Status::STATUS_RECHAZADO . "' or $tipoProvider = '" . Status::STATUS_ENEDICION . "') then 1 else 0 end) )*100)/count(1) as porcentaje")
            ->where($where)
            ->asArray()->one()['porcentaje'];

        if ($data == 100) {
            $isPending = $modelo::find()->select("count(1) as total")
                ->where(['and', ['provider_id' => $prov_id], ['activo' => TRUE], [$tipoProvider => Status::STATUS_PORVALIDAR]])
                ->asArray()->one()['total'];
            //var_dump($model." --> ".$isPending);
            if ($isPending != 0) {
                return -1; //retorna -1 cuando tiene toodo lleno pero en pendiente por validar
            }
        }
        return $data;
    }

    public function porcentajeTablaClientes($model, $tipoProvider, $povider_id = null)
    {
        $povider_id = $povider_id == null ? Yii::$app->user->identity->providerid : $povider_id;
        $tipo = $tipoProvider == 'status_bys' ? 'bys' : 'op';
        $modelo = "app\models\\" . $model;
        return $modelo::find()->select("100-((sum(case when ( $tipoProvider = '" . Status::STATUS_ENEDICION . "' or $tipoProvider = '" . Status::STATUS_RECHAZADO . "')
        then 1 else 0 end) )*100)/count(1) as porcentaje")
            ->where(['and', ['activo' => TRUE], ['provider_id' => $povider_id], ['tipo' => $tipo]])->asArray()->one()['porcentaje'];
    }


    public function porcentajeTablaClientesCarta($model, $tipoProvider, $povider_id = null)
    {

        $povider_id = $povider_id == null ? Yii::$app->user->identity->providerid : $povider_id;

        $tipo = $tipoProvider == 'status_bys' ? 'bys' : 'op';
        $modelo = "app\models\\" . $model;
        return $modelo::find()->select("100-((sum(case when ( $tipoProvider = '" . Status::STATUS_ENEDICION . "' or $tipoProvider = '" . Status::STATUS_RECHAZADO . "' or $tipoProvider = '" . Status::STATUS_PORVALIDAR . "')
        then 1 else 0 end) )*100)/count(1) as porcentaje")
            ->where(['and', ['activo' => TRUE], ['provider_id' => $povider_id], ['tipo' => $tipo]])->asArray()->one()['porcentaje'];
    }


    public function porcentajeTablaClientesCartaPendienteValidado($model, $tipoProvider, $povider_id = null)
    {

        $povider_id = $povider_id == null ? Yii::$app->user->identity->providerid : $povider_id;

        $tipo = $tipoProvider == 'status_bys' ? 'bys' : 'op';
        $modelo = "app\models\\" . $model;
        return $modelo::find()->select("100-((sum(case when ( $tipoProvider = '" . Status::STATUS_ENEDICION . "' or $tipoProvider = '" . Status::STATUS_RECHAZADO . "')
        then 1 else 0 end) )*100)/count(1) as porcentaje")
            ->where(['and', ['activo' => TRUE], ['provider_id' => $povider_id], ['tipo' => $tipo]])->asArray()->one()['porcentaje'];
    }

    public function porcentajeModelo($model, $tipo = null)
    {
        $arr = self::limpiarArray($model->getAttributes(), $tipo);
        return intval((count(array_filter($arr, function ($x) {
                    return ($x != null || $x != '' || $x === 0);
                })) * 100) / (count($arr)));
    }


    public function limpiarArray($arr, $tipo = null, $mod = null)
    {
        $exceptions = ['archivo_validado', 'num_int_fiscal', 'descripcion', 'geo_ubicacion', 'expirado', 'rfc', 'factura', 'url_comprobante_domicilio', 'departamento',
            'created_date', 'provider_id', 'status', 'status_op', 'status_bys', 'resultado_consulta_isn',
            'nl_interior_fiscal', 'consulta_isn', 'type_address_prov', 'no_cuenta_tipo_comprobante', 'url_factura_c_c', 'parent_rep_id', 'mancomunado','url_video','url_video_2','url_video_3','url_escrito','type_video',
            'pasivo_diferido','pasivo_total','liquidez','apalancamiento','capacidad_contratacion','capacidad_contratacion_resultado','solvencia','is_bim','software_bim','fecha_bim','contratos_bim','uso_bim', 'created_at', 'last_updated_at',
            'last_updated_by','created_by'
        ];
        if($tipo=='ACCIONISTA'){
            $exceptions = array_merge($exceptions,['correo','telefono','gender']);
            if(isset($arr['rfc']) && strlen($arr['rfc']) == 12){
                $exceptions = array_merge($exceptions,['nombre','ap_paterno','ap_materno', 'curp']);
            }else if(isset($arr['rfc']) && strlen($arr['rfc']) == 13){
                $exceptions = array_merge($exceptions,['razon_social']);
            }
        }
        if($tipo=='UBICACION'){
            $exceptions = array_merge($exceptions,['correo','departamento']);
        }

        $exceptions_ids = ['estado_id', 'vencimiento_identificacion', 'documento_identificacion', 'url_id_oficial', 'tipo_identificacion'];

        if($tipo=='ESCRITURA'){
            $exceptions = array_merge($exceptions,['fecha', 'num_escritura', 'notario', 'federatario', 'estado_id']);
            $exceptions_ids = ['vencimiento_identificacion', 'documento_identificacion', 'url_id_oficial', 'tipo_identificacion'];
        }
        


        if ((isset($arr['tipo_poder']) && ($arr['tipo_poder'] == 'ACTA' || $arr['tipo_poder'] == 'ACTUALIZACIÓN')) && (isset($arr['documento_acta']) && $arr['documento_acta'] == '')) {
            array_push($exceptions, 'documento_acta');
        }

        if (isset($arr['justificante_isn']) && $arr['justificante_isn'] == '' && isset($arr['provider_id']) && !empty($arr['provider_id'])) {
            $isn = models\UltimaDeclaracion::find()->select('resultado_consulta_isn')->where(['provider_id' => $arr['provider_id']])->one()['resultado_consulta_isn'];
            if ($isn == 1 || $isn == '1') {
                array_push($exceptions, 'justificante_isn');
            }
        }

        if (isset($arr['tipo_fotografia']) && isset($arr['ubicacion_id'])) {
            array_push($exceptions_ids, 'ubicacion_id');
        }

        if (isset($arr['type_address_prov']) && $arr['type_address_prov'] == 'NL') {
            array_push($exceptions, 'telefono');
            array_push($exceptions, 'correo');
        }

        if (isset($arr['type_address_prov']) && $arr['type_address_prov'] == 'DOMICILIO FISCAL') {
            array_push($exceptions, 'activo');
            array_push($exceptions, 'porcentaje');
        }


        if (isset($arr['undefined']) && $arr['undefined']) {
            array_push($exceptions, 'vigencia');
        } elseif (isset($arr['undefined']) && !$arr['undefined']) {
            array_push($exceptions, 'undefined');
        }

        if ($tipo == 'bys') {
            array_push($exceptions, 'rep_op');
            array_push($exceptions, 'monto');
        } else if ($tipo == 'op') {
            array_push($exceptions, 'rep_bys');
        }

        if ($mod != null) {
            array_push($exceptions, 'ciudad_id');
        }


        foreach ($arr as $k => $v) {
            if (!in_array($k, $exceptions_ids) && (in_array($k, $exceptions) || strpos($k, '_id') !== false || $k === 'id')) {
                unset($arr[$k]);
            }
        }

        return $arr;

    }

    public function actionUploadFile($register_id = null, $column_name = null, $tituloModel = null, $deleteRegister = null, $show = null)
    {

        $modelName = $this->modelName();
        $m_upload = new models\UploadFiles();
        $multiple = UploadedFile::getInstances($m_upload, 'file_url');

        $id = Yii::$app->params['provider_id'];
        if (isset($_POST['hasEditable'])) {
            $data = $_POST;
            $file_id = '';
            $descripcion = '';
            foreach ($data as $key => $val) {
                if (substr($key, 0, 8) == 'file_id_') {
                    $file_id = intval(substr($key, 8, strlen($key)));
                    $descripcion = $val;
                }
            }
            $model = models\UploadFiles::findOne($file_id);
            $model->description = $descripcion;
            $model->Save();
        }

        if (!empty($multiple)) {
            $path = self::getArrayPath();
            $verifyFiles = true;
            if (count($_FILES) == 0) {
                $verifyFiles = false;
            }
            if ($verifyFiles) {
                foreach ($multiple as $key => $val) {
                    $model = new models\UploadFiles();
                    $model->provider_id = $id;
                    $model->register_id = $register_id;
                    $model->column_name = $column_name;
                    $model->model_name = $modelName;
                    $model->file_url = $path . md5($val->baseName) . strval(microtime(true) * 10000) . '.' . $val->extension;
                    $model->document_name = $val->baseName;
                    if ($model->save(false)) {
                        $save = $val->saveAs($model->file_url);
                    }
                }
            }
        }
        if ($deleteRegister != null) {
            $deleteRegister = intval($deleteRegister);
            models\UploadFiles::find()->deleteFileMultiple($deleteRegister);
        }

        $data = models\UploadFiles::find()->getAllFiles($register_id, $id, $modelName, $column_name);

        return $this->renderAjax('/upload-file/upload-file', [
            'm_upload' => $m_upload,
            'modelName' => $modelName,
            'column_name' => $column_name,
            'register_id' => $register_id,
            'tituloModel' => $tituloModel,
            'msg' => null,
            'data' => $data,
            'show' => $show
        ]);
    }

    public function actionUploadVideo()
    {
        $ruta = 'archivos_tmp/';
        $nombre_final = '';
        $this->makeDir($ruta);
        if (!empty($_FILES)) {
            $model = new models\UploadFiles();
            $action = UploadedFile::getInstance($model, 'file_url');
            $name = md5($action->baseName) . strval(microtime(true) * 10000);
            $nombre_file = $name . '.' . $action->extension;
            $url_file = $ruta . $nombre_file;
            $nombre_final = $url_file;
            $action->saveAs($url_file);
        }

        return $nombre_final;

    }

    public function actionUpload()
    {

        $ruta = 'archivos_tmp/';
        $name_save = '';
        $this->makeDir($ruta);
        $array_files = [];
        if (!empty($_FILES)) {
            $model = new models\UploadFiles();

            $multiple = UploadedFile::getInstances($model, 'file_url');
            foreach ($multiple as $key => $val) {
                $name = md5($val->baseName) . strval(microtime(true) * 10000);
                $nombre_file = $name . '.' . $val->extension;
                $url_file = $ruta . $nombre_file;
                array_push($array_files, $url_file);
                $val->saveAs($url_file);
            }
            $name_save = $this->unirPdf($array_files);

        }
        return 'archivos_tmp/' . $name_save;
    }

    public function unirPdf($pdfs)
    {

        //$provider_id = Yii::$app->user->identity->getProvider();
        $ruta = 'archivos_tmp/';
        $fpdi = new \FPDI();
        $imgs = ['JPG', 'PNG', 'PEG', 'BMP', 'JPEG'];

        foreach ($pdfs as $doc) {
            if (in_array(strtoupper(substr($doc, -3)), $imgs)) {
                $imagen = getimagesize($doc);
                $width = intval(isset($imagen[0]) ? $imagen[0] : 216);
                $height = intval(isset($imagen[1]) ? $imagen[1] : 280);
                $o = $height > $width ? 'P' : 'L';
                $newSize = $this->RedimensionarImagen($width, $height, $o == 'L' ? 270 : 185, $o == 'L' ? 185 : 270);
                $fpdi->AddPage($o);
                @$fpdi->Image($doc, 10, 10, $newSize['w'], $newSize['h']);
            } else {


                $filepdf = fopen($doc, "r");
                if ($filepdf) {
                    $line_first = fgets($filepdf);
                    fclose($filepdf);
                }

                preg_match_all('!\d+!', $line_first, $matches);

                $pdfversion = implode('.', $matches[0]);
                //$docnew = $doc;

                // if($pdfversion >= 1.4){

                $docnew = str_replace('.pdf', '', $doc) . strval(microtime(true) * 10000) . '.pdf';

                if ( isset($_ENV['IS_LINUX']) && $_ENV['IS_LINUX']) {
                    shell_exec('gs -sDEVICE=pdfwrite -dCompatibilityLevel=1.4 -dNOPAUSE -dQUIET -dBATCH -sOutputFile="' . $docnew . '" "' . $doc . '"');
                } else {
                    shell_exec('"C:\Program Files\gs\gs9.56.1\bin\gswin64c.exe" -sDEVICE=pdfwrite -dCompatibilityLevel=1.4 -dNOPAUSE -dQUIET -dBATCH -sOutputFile="' . $docnew . '" "' . $doc . '"  2>&1');
                }
                //}


                $pages = $fpdi->setSourceFile($docnew);
                for ($i = 1; $i <= $pages; $i++) {
                    $template = $fpdi->importPage($i);
                    $size = $fpdi->getTemplateSize($template);
                    $width = isset($size['width']) ? $size['width'] : (isset($size['w']) ? ($size['w']) : 216);
                    $height = isset($size['height']) ? $size['height'] : (isset($size['h']) ? ($size['h']) : 280);
                    $fpdi->AddPage($height > $width ? 'P' : 'L', [$width, $height]);
                    $fpdi->useTemplate($template);
                }

                if (file_exists($docnew)) {
                    unlink($docnew);
                }
            }
            if (file_exists($doc)) {
                unlink($doc);
            }
        }

        $name = md5(microtime(true) * 10000) . strval(microtime(true) * 10000) . '.pdf';

        $fpdi->Output($ruta . $name, 'F');

        return $name;

    }


    function RedimensionarImagen($AnchoOriginal, $AltoOriginal, $AnchoMaximo, $AltoMaximo)
    {

        $proporcionW = $AnchoMaximo / $AnchoOriginal;
        $proporcionH = $AltoMaximo / $AltoOriginal;
        $proporcion = min($proporcionW, $proporcionH);
        $NuevoAncho = $AnchoOriginal * $proporcion;
        $NuevoAlto = $AltoOriginal * $proporcion;

        return ['w' => $NuevoAncho, 'h' => $NuevoAlto];
    }

    public function actionUploadFileDrag()
    {

        echo "hola";

    }

    public function getArrayPath()
    {
        $modelName = $this->modelName();

        $explode = explode('\\', $modelName);
        $model = end($explode);
        $type = Yii::$app->user->identity->tipo;

        if ($type == 'bys') {
            $path = [
                'IntervencionBancaria' => 'estado_cuenta/',
            ];
        } else {
            $path = [
                'IntervencionBancaria' => 'estado_cuenta/',
            ];
        }

        return $path[$model];

    }

    static function changeStatus($id = null, $modelo = null, $tipo_provider = null)
    {

        if (($requi_status = Status::find()->where(['and', ['register_id' => $id], [$tipo_provider => Status::STATUS_PENDIENTE], ['modelo' => $modelo]])->one()) !== null) {
            $requi_status->$tipo_provider = Status::STATUS_TERMINADO_PRO;
            $requi_status->save();
        }
        return true;
    }

    static function countFilesModel($provider_id = null, $register_id = null, $column_name = null, $model_name = null)
    {
        try {
            return models\UploadFiles::find()
                ->where(['and', ['provider_id' => $provider_id], ['register_id' => $register_id], ['column_name' => $column_name], ['model_name' => $model_name], ['active' => true]])
                ->count('1');
        } catch (\Exception $e) {

        }
    }

    static function oneValueFilesModel($provider_id = null, $register_id = null, $column_name = null, $model_name = null)
    {
        try {
            return models\UploadFiles::find()->select('file_url')
                ->where(['and', ['provider_id' => $provider_id], ['register_id' => $register_id], ['column_name' => $column_name], ['model_name' => $model_name], ['active' => true]])
                ->one()['file_url'];
        } catch (\Exception $e) {

        }
    }

    static function showButtonDrag($modelValueUrl = null, $modelValueId = null, $modelValueProvider = null, $modelNameColumn = null, $title = null, $titleLabel = null, $tooltip = null, $editable = true)
    {



       return Yii::$app->controller->renderPartial('/layouts/input_file', [
            'modelValueUrl' => $modelValueUrl,
            'modelValueId' => $modelValueId,
            'modelValueProvider' => $modelValueProvider,
            'modelNameColumn' => $modelNameColumn,
            'title' => $title,
            'titleLabel'=> $titleLabel,
            'tooltip' => $tooltip,
            'editable' => $editable,

        ]);


        // $url_cd = '';
        // $disabled_cd = ' disabled';
        // $value_cd = '';
        // if (!empty($modelValueUrl)) {
        //     $url_cd = '/site/visor?pdf=' . $modelValueUrl;
        //     $disabled_cd = ' showModalButtonImage';
        // }
        // if (!empty($modelValueId)) {
        //     $value_cd = $modelValueId . '-' . $modelValueProvider;
        // }

        // $titleLabel = $titleLabel == null ? $title : $titleLabel;

        // return '<div class="form-group">
        //             <label class="control-label">' . $titleLabel . $tooltip . '</label>
        //             <div class="input-group divs_buttons">
        //                 <button type="button" class="buttons_files_upload btn_view ' .(!$editable?'noeditable':''). $disabled_cd . '" value = "' . $url_cd . '" id="' . $modelNameColumn . '_view" title="' . $title . '"><i class="material-icons">check_box</i> Vista previa</button>
        //                 ' . ( $editable ? '<button type="button" class="buttons_files_upload btn-danger btn_delete" id="' . $modelNameColumn . '_delete" value="' . $value_cd . '"><i class="material-icons">delete</i></button>' : '')
        //                 . ( $editable ? '<button type="button" class="image-popup-open buttons_files_upload btn-primary btn_upload" id="' . $modelNameColumn . '_upload" title="' . $title . '"><i class="material-icons">folder</i></button>' : '') .
        //             '</div>
        //         </div>';

    }

    static function showVideoInput($valor_path = null, $model_attribute, $title = null)
    {

        $disabled = $valor_path != null && $valor_path != '' ? 'showVideoModal' : 'disabled';
        $url_path = $valor_path != null && $valor_path != '' ? $valor_path : '';


        return '<div class="form-group">
                    <div class="input-group divs_buttons">
                        <button type="button" class="buttons_files_upload btn_view ' . $disabled . '" value = "' . $url_path . '" id="' . $model_attribute . '_view" title="' . $title . '"><i class="material-icons">check_box</i>Vista previa</button>
                        <button type="button" class="buttons_files_upload btn-danger btn_delete" id="' . $model_attribute . '_delete" value="' . $url_path . '"><i class="material-icons">delete</i></button>
                        <button type="button" class="choose-video buttons_files_upload btn-primary btn_upload" id="' . $model_attribute . '_upload" title="' . $title . '"><i class="material-icons">folder</i></button>
                    </div>
                </div>';
    }

    static function pintaRechazo($motivo = null, $fecha = null, $user = null, $modulo=null)
    {
        $forma_fecha = date_format(date_create($fecha), 'd/m/Y H:i');
        $fecha = explode(' ', $forma_fecha);
        $usuario = '';
        if (isset($user)) {
            $usuario = '<span><label>Usuario: </label>' . $user . '</span>';
        };

        if($modulo)
            self::updateModulesBys(Yii::$app->user->identity->provider,$modulo);

        return '<div id="tarjeta" class="fondo-tarjeta">
                    <div class="titulo-tarjeta">
                        <label>Fecha de rechazo:</label>
                        <span>' . \yii\helpers\Html::encode($fecha[0]) . '</span>
                        <label>Hora:</label>
                        <span>' . \yii\helpers\Html::encode($fecha[1]) . ' HRS.</span>
                    </div>
                    <div class="division-tarjeta">
                        ' . GeneralController::GetSVG('flecha_rechazo') . '
                    </div>
                    <div class="contenido-tarjeta">
                        ' . $usuario . '
                        <span><label>Motivo: </label>' . $motivo . '</span>
                    </div>
                </div>';

    }


    static function getDataConnection($cookie_value = null, $save_browser = null, $session = null)
    {

        $user_id = Yii::$app->user->getId();

        try {
            $address_ip = $_SERVER['REMOTE_ADDR'];//$_SERVER['REMOTE_ADDR']    '***************'/;
            if ((models\ConnectionHistory::find()->where(['and', ['ip' => $address_ip], ['cookie' => $cookie_value], ['permission' => true], ['user_id' => $user_id]])->count('1')) === 0) {
                $address_ip = $address_ip == '127.0.0.1' ? '***************' : $address_ip;
                $json_str = file_get_contents('http://ip-api.com/json/' . $address_ip);
                $json = json_decode($json_str);
                $browser = new Browser();
                $os = new Os();
                $connection = new models\ConnectionHistory();
                $connection->user_id = $user_id;
                $connection->ip = $address_ip;
                if (isset($json->lon)) {
                    $lat = $json->lat;
                    $lng = $json->lon;
                    $connection->lat = (string)$lat;
                    $connection->lng = (string)$lng;
                    $connection->country = $json->country;
                    $connection->city = $json->city;
                    $connection->regionName = $json->regionName;
                    //$url_api = "https://image.maps.api.here.com/mia/1.6/mapview?app_id=rTo9xNILMp1EvSsia48d&app_code=O18jesDjNvLBd8WzXps7Kw&c=" . $lat . "," . $lng . "&h=300&w=400&z=8&f=0";
                    //$data_img = base64_encode(file_get_contents($url_api));
                    //$connection->img = $data_img;
                }
                $connection->cookie = $cookie_value;
                $connection->browser = $browser->getName();
                $connection->os = $os->getName();
                $connection->session = $session;
                $connection->permission = true;
                $connection->last_connection = date('Y-m-d H:i:s');
                if ($save_browser != null) {
                    $connection->browser_save = $save_browser == '1' ? true : false;
                }
                $connection->save();
            } else {

                if (isset($_COOKIE['_verify_login'])) {
                    models\ConnectionHistory::updateAll(['session' => $session, 'last_connection' => date('Y-m-d H:i:s')], ['and', ['cookie' => $_COOKIE['_verify_login']], ['user_id' => $user_id]]);
                }

            }

        } catch (\Exception $e) {

        }


    }


    static function getClientesContratos($provider_id = null, $tipo = null)
    {

        $data = [];
        if ($provider_id != null && $tipo != null && in_array($tipo, ['bys', 'op'])) {
            $status_bys_op = 'status_' . $tipo;

            $data = Yii::$app->db->createCommand("select nombre_razon_social,persona_dirigirse,telefono, sum(monto) as monto_total,year,month, count(1) as total
                                                  from provider.clientes_contratos
                                                  where provider_id = :id and  $status_bys_op in('VALIDADO')
                                                  group by
                                                  nombre_razon_social,persona_dirigirse,telefono,year,month order by year DESC,monto_total DESC limit 5", [':id' => $provider_id])->queryAll();
        }

        return $data;
    }


    static function getUltimosMeses()
    {
        $anterior = [];
        $numero_mes = ['01' => 'ENE', '02' => 'FEB', '03' => 'MAR', '04' => 'ABR', '05' => 'MAY', '06' => 'JUN',
            '07' => 'JUL', '08' => 'AGO', '09' => 'SEP', '10' => 'OCT', '11' => 'NOV', '12' => 'DIC'];
        for ($i = 0; $i <= 12; $i++) {
            $mes = date("m-Y", mktime(0, 0, 0, date("m") - $i, date("d"), date("Y")));
            $mes_explode = explode('-', $mes);

            array_push($anterior, $numero_mes[$mes_explode[0]] . '-' . $mes_explode[1]);
        }

        return $anterior;
    }


    public function actionDividirPdf()
    {

        $ruta = 'archivos_tmp/';
        $this->makeDir($ruta);
        $array_files = [];
        $model = new models\EstadoFinanciero();

        if ($model->load(Yii::$app->request->post())) {
            $model->dividirPdf = UploadedFile::getInstance($model, 'archivo');
            $archivo_name = $ruta . md5($model->dividirPdf->baseName) . time() . '.' . $model->dividirPdf->extension;
            if ($archivo_name) {
                if (!file_exists($ruta)) {
                    mkdir($ruta, 0755, true);
                }
                $model->dividirPdf->saveAs($archivo_name);
            }
            $this->dPdf($archivo_name);
            return $this->goHome();
        } else {

            return $this->render('dividir-pdf', [

                'model' => $model
            ]);

        }
    }

    public function dPdf($doc)
    {

        $ruta = 'archivos_tmp/';
        $fpdi = new \FPDI();
        $fileNameZip = 'archivos_zip.zip';
        $zip = new \ZipArchive;
        $zip->open($fileNameZip, \ZipArchive::CREATE);
        $pages = $fpdi->setSourceFile($doc);
        for ($i = 1; $i <= $pages; $i++) {
            $fpdi2 = new \FPDI();
            $pages = $fpdi2->setSourceFile($doc);
            $template = $fpdi2->importPage($i);
            $size = $fpdi2->getTemplateSize($template);
            $width = isset($size['width']) ? $size['width'] : (isset($size['w']) ? ($size['w']) : 216);
            $height = isset($size['height']) ? $size['height'] : (isset($size['h']) ? ($size['h']) : 280);
            $fpdi2->AddPage($height > $width ? 'P' : 'L', [$width, $height]);
            $fpdi2->useTemplate($template);
            $name = $ruta . $i . '.pdf';
            $fpdi2->Output($name, 'F');

            $zip->addFile($name, $name);


        }
        $zip->close();

        unlink($doc);
        header("Content-type: application/zip");
        header("Content-Disposition: attachment; filename=$fileNameZip");
        header("Content-length: " . filesize($fileNameZip));
        header("Pragma: no-cache");
        header("Expires: 0");
        readfile("$fileNameZip");

    }

    public function actionAgenda($id = null, $tipo = null, $id_usuario)
    {
        $proveedor = models\Provider::findOne($id);
        $proveedor->status_cotejar = 'PENDIENTE AGENDAR CITA';
        if (!$proveedor->save()) {
            //var_dump($proveedor->errors);exit();
        }

        self::sendEmail('/provider/correos/cotejo', null, $proveedor['email'], 'Agenda tu cita para cotejo de documentos', [], null);
        /* self::AllSendNotification($id_usuario, $tipo, null, 'CITA', null, 'Agenda tu cita', 'PROVIDER'); */

        return $this->redirect('/provider/index-provider-cotejar');
    }

    public function actionAgendaVisit($id = null, $visit_id = null)
    {

        Visit::updateAll(['status' => false, 'created_by' => Yii::$app->user->getId()], ['and', ['provider_id' => intval($id)], ['status' => true]]);
        $moP = Provider::findOne($id);
        $emailProv = $moP->email;
        $provider_id = $moP->provider_id;
        $tipo_provider_by_validador = 'bys';
        $id_usuario = $moP->user_id;
        $statusCot = 'PENDIENTE AGENDAR CITA';
        if ((HistoricoCertificados::find()->where(['and', ['provider_id' => $provider_id], ['tipo' => 'CERTIFICADO'], ['provider_type' => 'bys']])->count('1')) > 0) {
            $verifyCotejar = Movements::find()->where(['and', ['provider_id' => $provider_id], ['in', 'model', ['rfc', 'rfc_acta_constitutiva',
                'rfc_curp', 'modificacion_acta', 'relacion_accionistas',
                'representante_legal']], ['first_update_certificate_id' => null], ['certificate' => true]])->count('1');
            if ($verifyCotejar == 0) {
                $statusCot = 'VALIDADO';
                if ((Movements::find()->where(['and', ['provider_id' => $provider_id], ['!=', 'model', 'fotografia_negocio'], ['first_update_certificate_id' => null], ['certificate' => true]])->count('1')) > 0) {
                    $hcId = $this->documentocertificado($provider_id, $tipo_provider_by_validador);
                    $hcDateOld = Yii::$app->db->createCommand("select * from historico_certificados
                                        where provider_id  = $provider_id and tipo = 'CERTIFICADO' and provider_type = 'bys' and historico_certificados_id not in($hcId)
                                            order by historico_certificados_id DESC limit 1")->queryOne();
                    $first_update_certificate = new FirstUpdateCertificate();
                    $first_update_certificate->provider_id = $provider_id;
                    $first_update_certificate->update_certificate = true;
                    $first_update_certificate->historico_certificado_id = $hcId;
                    $first_update_certificate->date_old_certificate = $hcDateOld['created_at'];
                    $first_update_certificate->save();
                    Movements::updateAll(['first_update_certificate_id' => $first_update_certificate->first_update_certificate_id], ['and', ['provider_id' => $provider_id], ['first_update_certificate_id' => null], ['certificate' => true]]);
                    self::AllSendNotification($id_usuario, $tipo_provider_by_validador, null, 'CERTIFICADO', null, '¡Felicidades!', 'PROVIDER');
                }
            }
        }

        if ($statusCot == 'PENDIENTE AGENDAR CITA') {
            self::sendEmail('/provider/correos/activar_agenda', null, $emailProv, 'Agendar cita', [], null);
            /* self::AllSendNotification($id_usuario, $tipo_provider_by_validador, null, 'CITA', null, 'Agenda tu cita', 'PROVIDER'); */
        }
        Provider::updateAll(['status_cotejar' => $statusCot], ['provider_id' => $provider_id]);

        return $this->redirect('/visit/');
    }

            //función que agrega fuentes al array de $fontdata dentro del componente de mpdf
        //Nota: es necesario agregar la fuente R = Regular (o fuente base para que aplique los cambios correctamente)
      public function add_custom_fonts_to_mpdf($mpdf) {

            $fontdata = [
                'poppins' => [
                    'R' => 'Poppins-Regular.ttf',
                    'B' => 'Poppins-Bold.ttf',
                ],
                'axiforma' => [
                    'R' => 'Axiforma-Regular.ttf',
                    'B' => 'Axiforma-Heavy.ttf',
                ],
                'branding' => [
                    'R' => 'Branding-Medium.ttf',
                    'B' => 'Branding-Bold.ttf',
                ]
            ];

            foreach ($fontdata as $f => $fs) {

                // agrega al arreglo $fontdata
                $mpdf->fontdata[$f] = $fs;

                foreach (['R', 'B', 'I', 'BI'] as $style) {
                    if (isset($fs[$style]) && $fs[$style]) {
                        $mpdf->available_unifonts[] = $f . trim($style, 'R');
                    }
                }

            }

            $mpdf->default_available_fonts = $mpdf->available_unifonts;
        }

    public function documentocertificadocurso($id = null, $tipo = null, $date = null, $rfc=null){

        $proveedor = models\Provider::findOne($id);
        if (!$proveedor) {
            return $this->redirect('/');
        }

        $datosFirma = ['firmante' => '', 'cargo' => '', 'firma' => ''];
        $datosQr = ['img' => ''];

        $path_doc = 'prov_certificado';
        if (!file_exists($path_doc)) {
            mkdir($path_doc, 0777, true);
        }


        $data = Provider::findBySql(" 
            select concat_ws(' ',r.nombre,r.ap_paterno,r.ap_materno) as firmante, p.end_date_video as fecha
            from provider_course p inner join provider.representante_legal r 
            on p.representante_id = r.representante_legal_id where p.provider_id = $id 
            order by p.end_date_video desc limit 1
        ")->asArray()->one();

        if($data){
            $proveedor->firmante = ($proveedor->tipo_persona == 'Persona moral')? $data['firmante']:'';
            $date = (!$date) ? $data['fecha']:$date;
        }


        $content = $this->renderPartial('/carta/certificadocurso', [
            'proveedor' => $proveedor,
            'datosFirma' => $datosFirma,
            'datosQr' => $datosQr,
            'date' => $date
        ]);

        define('_MPDF_TTFONTPATH', './fonts/'); //cambia la ruta de las fuentes utilizadas en la creación del pdf (la configuración default puede solicitar alguans fuentes necesarias)
        $stylesheet = file_get_contents('css/pdf.css');

        $mpdf = new \mPDF();
        $mpdf->SetTitle('Certificado');
        $mpdf->SetHTMLHeader($this->renderPartial('/carta/header', ['tipo_provider' => $tipo]));
        $mpdf->SetHTMLFooter($this->renderPartial('/carta/certificado_footer', ['datosFirma' => $datosFirma, 'datosQr' => $datosQr, 'tipo_provider' => 'bys']));
        $mpdf->defaultfooterline = 0;
        $mpdf->WriteHTML($content);

        $filename = $path_doc . '/' . md5($proveedor->provider_id) . '_certificado_proveedor_curso' . time() . '.pdf';
        $mpdf->Output($filename, 'F');


        chmod($filename, 0777);

        $date_year = date("Y");
        $date_month = date("m");
        $carpetaRfc = 'documentos_firmados' . '/' . $proveedor->rfc;
        if(!file_exists($carpetaRfc)){
            mkdir($carpetaRfc,0777,true);
        }
        $filenameFinal = 'documentos_firmados' . '/' . $proveedor->rfc . '/' . $proveedor->rfc . '_certificado_curso_' . $date_year . '_' . $date_month . '_' . $tipo . time() . '.pdf';

        $codigoV = $this->codigoVerificacion($proveedor->provider_id);
        $token = $this->solicitaDatosGet(\Yii::$app->params['urlToken'])['token'];
        $datosQr = $this->solicitaDatosPost(\Yii::$app->params['urlQr'], ['encode' => $codigoV, 'base64' => 'true', 'validador' => true]);

        $protocol = self::isProduction()?'https':'http';
        $datosBarCode = $this->solicitaBarCode(\Yii::$app->params['urlBarCode'], $codigoV);
        $datosFirma = $this->solicitaDatosPost(\Yii::$app->params['urlFirma'], ['code' => $codigoV, 'token' => $token, 'type' => 'BYS', 'path' => \yii\helpers\Url::home($protocol) . $filename, 'signed_document' => \yii\helpers\Url::home('https') . $filenameFinal]);

        // Firma prueba para generar certificado en local
        //
        // $json_prueba =
        //     '{
        //         "status": "ok",
        //         "firma": "ZDYyMmE0YjMwNTMyMDQwODMxZDdmMDA4Mjk2YzNkMGY5NWFiZTBiOTAyNTRlY2NhNzY0ZjJlYTZiZTRhYmJhZjg4MzQyZWJjYmM0Y2VhZTRiNmZmODgwZDcyYTY3YTVkODRlNGU2ODAzN2I3N2VjMzc3NWYyM2MwZWRmM2NkYzQ0NTliOGMwMmU5OWRmMTVmZjg1MzJhN2Q5YWMxODkyZjFjNjdjOWEwYjFkNzkyMTIzMjNhYTY5MzYyMzEzOGZi",
        //         "firmaLarga": "ZTEyM2VhNTA2NTlkYTVmMGU3YjM1NGQwMzViZTY4NjUyMGE2MzBlNjZmZDM0YTYxMTcwNjgxMDIyZjFlNDIwODRlYmY0NTc3ZDUzOWY5NjJjODRjNGViYjRhYzY5YTZhMTQwZDJlZDY4ZWRiMTU0NDliYzcwYTI2YzA4OTM0NzMzZDMxZTFkY2RjZjMwMzBiOWQzNmJlMWFjNGQwODdiYTkxY2M0NjU3M2JkNDBmYTQ4ZjI3OWVkMDQ0ZDhiMjk4Y2Y5Y2Y3N2ZmYzQwMjM5Yjg1M2Y0OTQxY2EzMDlhZjliZWVkODUyYTlhMGQxYjhmZmIzMWYxYWFkYjgzZTU3NmJhYjhlMjU2OTRmOTQzNzc5MjY5ODUzZThhYjhhNzIxMDc3YTVmMWU2MzBlODQ3ZmE4YzNlODg0MDYyMzU3OWQ3Nzc1MGU3ZDk3Yjc5NzcxNGYwOWQ2OGYzZTJhZTc4ZDhhYTQyYjc0YzUwZTM2Njk1MTViZTMwNWEwNGFkNmFkYWVjMzE0OGY5ZTMxNTMxNjg5OGNmOThlNGUyZjgzZTEyYjM2NzMyZDMxYTBkZGEyN2EwZjI1NzQzZjFmYTdjYzE3NzU2ZmVlNDZjNjdiZjFiNDE4N2ZhNWM1OThkMjg4MmM2NmU5ZGIzNDkwMjczZDYyNzg5YjAwZDY1YzM0MDc=",
        //         "firmante": "Ing. Juanito Perez Rodriguez",
        //         "cargo": "Encargado General de Area"
        //     }';
        // $datosFirma = json_decode($json_prueba,true);

        unlink($filename);

        $content = $this->renderPartial('/carta/certificadocurso', [
            'proveedor' => $proveedor,
            'datosFirma' => $datosFirma,
            'datosQr' => $datosQr,
            'date' => $date
        ]);

        $mpdf = new mPDF('utf-8', 'A4', 0, '', 0, 0, 5, 15, 0, 0);

        $this->add_custom_fonts_to_mpdf($mpdf); //

        $mpdf->SetTitle('Certificado');
        $mpdf->SetHTMLHeader($this->renderPartial('/carta/header', ['tipo_provider' => $tipo]));
        $mpdf->SetHTMLFooter($this->renderPartial('/carta/certificado_footer', ['datosFirma' => $datosFirma, 'datosQr' => $datosQr, 'tipo_provider' => 'bys', 'barCode' => $datosBarCode, 'code' => $codigoV]));
        /* if(!$_ENV['VEDA_ELECTORAL']){ $mpdf->SetWatermarkImage('imgs/cartas/logo_sello.png',.8,array(100,100)); } */
        $mpdf->SetWatermarkImage('imgs/cartas/logo_watermark.svg',.1,array(100,100));
        $mpdf->showWatermarkImage = true;
        $mpdf->defaultfooterline = 0;
        $mpdf->WriteHTML($content);
        $mpdf->WriteHTML($stylesheet, 1);

        $mpdf->Output($filenameFinal, 'F');
        chmod($filenameFinal, 0777);

        $historicoCerId = null;
        if ($datosFirma['status'] != 'Error' && file_exists($filenameFinal)) {
            $hc = new models\HistoricoCertificados();
            $hc->provider_id = $proveedor->provider_id;
            $hc->codigo_verificacion = $codigoV;
            $hc->url_certificado = $filenameFinal;
            $hc->tipo = 'CURSO';
            $hc->provider_type = $tipo;
            if (!empty($date)) {
                $date = base64_decode($date);
                $hc->created_at = $date;
            }
            $hc->save();
            $historicoCerId = $hc->historico_certificados_id;
            $subject = "Acreditación de “Curso de prevención y concientización sobre faltas administrativas y hechos de corrupción”";
            GeneralController::sendEmail('/provider/correos/constancia',null,$proveedor->email, $subject,['tipo_provider' => $tipo ], $filenameFinal);
            //self::sendEmail('/provider/correos/certificacion', null, $proveedor['email'], 'Certificado de registro en el Padron de proveedores para el Gobierno del Estado de N.L', ['tipo_provider' => $tipo], $filenameFinal);
        } else {
            $error = new models\ErrorInesperado();
            $error->tipo_error = 'FIRMAR CERTIFICADO';
            $error->data = $datosFirma;
            $error->user_id = Yii::$app->user->getId();
            $error->save(false);
        }
        if($rfc)
            return $filenameFinal;
        return $historicoCerId;
    }



    public function documentocertificado($id = null, $tipo = null, $date = null)
    {

        $proveedor = models\Provider::findOne($id);

        if (!$proveedor) {
            return $this->redirect('/');
        }

        $datosFirma = ['firmante' => '', 'cargo' => '', 'firma' => ''];

        $datosQr = ['img' => ''];

        $path_doc = 'prov_certificado';
        if (!file_exists($path_doc)) {
            mkdir($path_doc, 0777, true);
        }

        $dataExperiencia = [];
        $capacidadFinanciera = 0;
        $capFin = 0;
        if ($tipo == 'op') {
            $dataExperiencia = Yii::$app->db->createCommand("
                    select cs.nombre as especialidad,e.id_obra as id,SUM(e.monto) as monto from provider.experiencia e
                    join provider.subespecialidad_category cs on cs.category_id = e.id_obra
                    where e.provider_id = :id and status_op = 'VALIDADO' AND e.activo is TRUE
                    group by especialidad,id order by monto DESC", [':id' => $id])->queryAll();


            $capFin = Yii::$app->db->createCommand("select capacidad_contratacion_resultado from provider.capacidad_contratacion
                      where provider_id = :id ORDER BY capacidad_contratacion_id DESC", [':id' => $id])->queryOne()['capacidad_contratacion_resultado'];
            $capacidadFinanciera = array_sum(ArrayHelper::getColumn($dataExperiencia, 'monto'));
        }

        $productos = ProviderGiro::find()->select("grupo.descripcion, provider_giro.especialidad")
            ->innerJoin(['producto'=>'productos.producto'],'producto.producto_id = provider_giro.producto_id')
            ->innerJoin(['clase'=>'productos.clase'],'clase.clase_id = producto.clase_id')
            ->innerJoin(['grupo'=>'productos.grupo'],'grupo.grupo_id = clase.grupo_id')
            ->where(['and',['provider_giro.provider_id' => $id, 'provider_giro.active' => true]])
            ->orderBy("provider_giro.especialidad DESC")
            ->distinct()
            ->asArray()->limit(3)->all();



        $content = $this->renderPartial('/carta/certificado' . $tipo, [
            'productos' => $productos,
            'proveedor' => $proveedor,
            'dataExperiencia' => $dataExperiencia,
            'capacidadFinanciera' => $capacidadFinanciera,
            'capFin' => $capFin,
            'datosFirma' => $datosFirma,
            'datosQr' => $datosQr,
            'date' => $date
        ]);

        define('_MPDF_TTFONTPATH', './fonts/'); //cambia la ruta de las fuentes utilizadas en la creación del pdf (la configuración default puede solicitar alguans fuentes necesarias)
        $stylesheet = file_get_contents('css/pdf.css');

        $mpdf = new \mPDF();
        $mpdf->SetTitle('Certificado');
        $mpdf->SetHTMLHeader($this->renderPartial('/carta/header', ['tipo_provider' => $tipo]));
        $mpdf->SetHTMLFooter($this->renderPartial('/carta/certificado_footer', ['datosFirma' => $datosFirma, 'datosQr' => $datosQr, 'tipo_provider' => $tipo]));
        $mpdf->defaultfooterline = 0;
        $mpdf->WriteHTML($content);

        $filename = $path_doc . '/' . md5($proveedor->provider_id) . '_certificado_proveedor' . time() . '.pdf';
        $mpdf->Output($filename, 'F');


        chmod($filename, 0777);

        $date_year = date("Y");
        $date_month = date("m");
        $filenameFinal = 'documentos_firmados' . '/' . $proveedor->rfc . '/' . $proveedor->rfc . '_certificado_' . $date_year . '_' . $date_month . '_' . $tipo . time() . '.pdf';

        $pathRfc = 'documentos_firmados' . '/' . $proveedor->rfc;
        if(!file_exists($pathRfc)){
            mkdir($pathRfc,0777,true);
        }


        $codigoV = $this->codigoVerificacion($proveedor->provider_id);
        $token = $this->solicitaDatosGet(\Yii::$app->params['urlToken'])['token'];
        $datosQr = $this->solicitaDatosPost(\Yii::$app->params['urlQr'], ['encode' => $codigoV, 'base64' => 'true', 'validador' => true]);

        $protocol = self::isProduction()?'https':'http';
        $datosBarCode = $this->solicitaBarCode(\Yii::$app->params['urlBarCode'], $codigoV);
        $datosFirma = $this->solicitaDatosPost(\Yii::$app->params['urlFirma'], ['code' => $codigoV, 'token' => $token, 'type' => strtoupper($tipo), 'path' => \yii\helpers\Url::home($protocol) . $filename, 'signed_document' => \yii\helpers\Url::home($protocol) . $filenameFinal]);

        // Firma prueba para generar certificado en local
        //
        // $json_prueba =
        //     '{
        //         "status": "ok",
        //         "firma": "ZDYyMmE0YjMwNTMyMDQwODMxZDdmMDA4Mjk2YzNkMGY5NWFiZTBiOTAyNTRlY2NhNzY0ZjJlYTZiZTRhYmJhZjg4MzQyZWJjYmM0Y2VhZTRiNmZmODgwZDcyYTY3YTVkODRlNGU2ODAzN2I3N2VjMzc3NWYyM2MwZWRmM2NkYzQ0NTliOGMwMmU5OWRmMTVmZjg1MzJhN2Q5YWMxODkyZjFjNjdjOWEwYjFkNzkyMTIzMjNhYTY5MzYyMzEzOGZi",
        //         "firmaLarga": "ZTEyM2VhNTA2NTlkYTVmMGU3YjM1NGQwMzViZTY4NjUyMGE2MzBlNjZmZDM0YTYxMTcwNjgxMDIyZjFlNDIwODRlYmY0NTc3ZDUzOWY5NjJjODRjNGViYjRhYzY5YTZhMTQwZDJlZDY4ZWRiMTU0NDliYzcwYTI2YzA4OTM0NzMzZDMxZTFkY2RjZjMwMzBiOWQzNmJlMWFjNGQwODdiYTkxY2M0NjU3M2JkNDBmYTQ4ZjI3OWVkMDQ0ZDhiMjk4Y2Y5Y2Y3N2ZmYzQwMjM5Yjg1M2Y0OTQxY2EzMDlhZjliZWVkODUyYTlhMGQxYjhmZmIzMWYxYWFkYjgzZTU3NmJhYjhlMjU2OTRmOTQzNzc5MjY5ODUzZThhYjhhNzIxMDc3YTVmMWU2MzBlODQ3ZmE4YzNlODg0MDYyMzU3OWQ3Nzc1MGU3ZDk3Yjc5NzcxNGYwOWQ2OGYzZTJhZTc4ZDhhYTQyYjc0YzUwZTM2Njk1MTViZTMwNWEwNGFkNmFkYWVjMzE0OGY5ZTMxNTMxNjg5OGNmOThlNGUyZjgzZTEyYjM2NzMyZDMxYTBkZGEyN2EwZjI1NzQzZjFmYTdjYzE3NzU2ZmVlNDZjNjdiZjFiNDE4N2ZhNWM1OThkMjg4MmM2NmU5ZGIzNDkwMjczZDYyNzg5YjAwZDY1YzM0MDc=",
        //         "firmante": "Ing. Juanito Perez Rodriguez",
        //         "cargo": "Encargado General de Area"
        //     }';
        // $datosFirma = json_decode($json_prueba,true);

        @unlink($filename);

        $content = $this->renderPartial('/carta/certificado' . $tipo, [
            'productos' => $productos,
            'proveedor' => $proveedor,
            'datosFirma' => $datosFirma,
            'datosQr' => $datosQr,
            'dataExperiencia' => $dataExperiencia,
            'capacidadFinanciera' => $capacidadFinanciera,
            'capFin' => $capFin,
            'date' => $date
        ]);

        $mpdf = new mPDF('utf-8', 'A4', 0, '', 0, 0, 5, 15, 0, 0);

        $this->add_custom_fonts_to_mpdf($mpdf); //

        $mpdf->SetTitle('Certificado');
        $mpdf->SetHTMLHeader($this->renderPartial('/carta/header', ['tipo_provider' => $tipo]));
        $mpdf->SetHTMLFooter($this->renderPartial('/carta/certificado_footer', ['datosFirma' => $datosFirma, 'datosQr' => $datosQr, 'tipo_provider' => $tipo, 'barCode' => $datosBarCode, 'code' => $codigoV]));
        /* if(!$_ENV['VEDA_ELECTORAL']){ $mpdf->SetWatermarkImage('imgs/cartas/logo_sello.png',.8,array(100,100)); } */
        $mpdf->SetWatermarkImage('imgs/cartas/logo_watermark.svg',.1,array(100,100));
        $mpdf->showWatermarkImage = true;
        $mpdf->defaultfooterline = 0;
        $mpdf->WriteHTML($content);
        $mpdf->WriteHTML($stylesheet, 1);

        $mpdf->Output($filenameFinal, 'F');
        chmod($filenameFinal, 0777);

        $historicoCerId = null;
        if ($datosFirma['status'] != 'Error' && file_exists($filenameFinal)) {
            $hc = new models\HistoricoCertificados();
            $hc->provider_id = $proveedor->provider_id;
            $hc->codigo_verificacion = $codigoV;
            $hc->url_certificado = $filenameFinal;
            $hc->provider_type = $tipo;
            if (!empty($date)) {
                $date = base64_decode($date);
                $hc->created_at = $date;
            }
            $hc->save();
            $historicoCerId = $hc->historico_certificados_id;

            //GuardaHistorico
            //ValidadorController::guardaHistorico($proveedor->provider_id,$hc->historico_certificados_id,$tipo);

            $subject_correo = $tipo == 'bys' ? '¡Felicidades! ya formas parte del Padrón de Proveedores': '¡Felicidades! ya formas parte del Registro Estatal de Contratistas';
            self::sendEmail('/provider/correos/certificacion', null, $proveedor->email, $subject_correo, ['tipo_provider' => $tipo], $filenameFinal);
        } else {
            $error = new models\ErrorInesperado();
            $error->tipo_error = 'FIRMAR CERTIFICADO';
            $error->data = $datosFirma;
            $error->user_id = Yii::$app->user->getId();
            $error->save(false);
        }
        return $historicoCerId;
    }

    public function actionDocumentoCer($id = null, $tipo = null, $date = null, $cert = null)
    {

        $vigencia = null;
        $proveedor = models\Provider::findOne($id);
        
        if (!$proveedor) {
            return $this->redirect('/');
        }

        $visit = Visit::find()->where(['and', ['provider_id' => $proveedor->provider_id], ['status' => true]])->asArray()->all();

        if ($visit) {
            foreach ($visit as $v) {

                if ($v['modify']) {
                    Visit::updateAll(['status' => false], ['visit_id' => $v['visit_id']]);
                } else {
                    models\VisitDetails::deleteAll(['visit_id' => $v['visit_id']]);
                    Visit::deleteAll(['visit_id' => $v['visit_id']]);
                }

            }
        }

        if ($date) {
            $dateVal = DatosValidados::find()->select(['created_date'])->where(['and', ['provider_id' => $id], ['status_bys' => 'VALIDADO']])->orderBy(['validado_id' => SORT_DESC])->limit(1)->one()['created_date'];
            $date = base64_encode($dateVal);
        }
        $datosFirma = ['firmante' => '', 'cargo' => '', 'firma' => ''];

        $datosQr = ['img' => ''];

        $path_doc = 'prov_certificado';
        if (!file_exists($path_doc)) {
            mkdir($path_doc, 0777, true);
        }

        $dataExperiencia = [];
        $capacidadFinanciera = 0;
        $capFin = 0;
        if ($tipo == 'op') {
            $dataExperiencia = Yii::$app->db->createCommand("
                    select cs.nombre as especialidad,e.id_obra as id,SUM(e.monto) as monto from provider.experiencia e
                    join provider.subespecialidad_category cs on cs.category_id = e.id_obra
                    where e.provider_id = :id and status_op = 'VALIDADO' AND e.activo is TRUE
                    group by especialidad,id order by monto DESC", [':id' => $id])->queryAll();


            $capFin = Yii::$app->db->createCommand("select capacidad_contratacion_resultado from provider.capacidad_contratacion
                      where provider_id = :id ORDER BY capacidad_contratacion_id DESC", [':id' => $id])->queryOne()['capacidad_contratacion_resultado'];
            $capacidadFinanciera = array_sum(ArrayHelper::getColumn($dataExperiencia, 'monto'));
        }

        $productos = ProviderGiro::find()->select("grupo.descripcion, provider_giro.especialidad")
            ->innerJoin(['producto'=>'productos.producto'],'producto.producto_id = provider_giro.producto_id')
            ->innerJoin(['clase'=>'productos.clase'],'clase.clase_id = producto.clase_id')
            ->innerJoin(['grupo'=>'productos.grupo'],'grupo.grupo_id = clase.grupo_id')
            ->where(['and',['provider_giro.provider_id' => $id, 'provider_giro.active' => true]])
            ->orderBy("provider_giro.especialidad DESC")
            ->distinct()
            ->asArray()->limit(3)->all();

        $content = $this->renderPartial('/carta/certificado' . $tipo, [
            'productos' => $productos,
            'proveedor' => $proveedor,
            'dataExperiencia' => $dataExperiencia,
            'capacidadFinanciera' => $capacidadFinanciera,
            'capFin' => $capFin,
            'datosFirma' => $datosFirma,
            'datosQr' => $datosQr,
            'date' => $date
        ]);

        define('_MPDF_TTFONTPATH', './fonts/'); //cambia la ruta de las fuentes utilizadas en la creación del pdf (la configuración default puede solicitar alguans fuentes necesarias)
        $stylesheet = file_get_contents('css/pdf.css');

        $mpdf = new \mPDF();
        $mpdf->SetTitle('Certificado');
        $mpdf->SetHTMLHeader($this->renderPartial('/carta/header', ['tipo_provider' => $tipo]));
        $mpdf->SetHTMLFooter($this->renderPartial('/carta/certificado_footer', ['datosFirma' => $datosFirma, 'datosQr' => $datosQr, 'tipo_provider' => $tipo]));
        $mpdf->defaultfooterline = 0;
        $mpdf->WriteHTML($content);
        $filename = $path_doc . '/' . md5($proveedor->provider_id) . '_certificado_proveedor' . time() . '.pdf';
        $mpdf->Output($filename, 'F');

        chmod($filename, 0777);

        $pathRfc = 'documentos_firmados' . '/' . $proveedor->rfc;
        if(!file_exists($pathRfc)){
            mkdir($pathRfc,0777,true);
        }
        

        $date_year = date("Y");
        $date_month = date("m");

        $pathRfc = 'documentos_firmados' . '/' . $proveedor->rfc;
        if(!file_exists($pathRfc)){
            mkdir($pathRfc,0777,true);
        }

        $filenameFinal = 'documentos_firmados' . '/' . $proveedor->rfc . '/' . $proveedor->rfc . '_certificado_' . $date_year . '_' . $date_month . '_' . $tipo . time() . '.pdf';


        $codigoV = $this->codigoVerificacion($proveedor->provider_id);
        $token = $this->solicitaDatosGet(\Yii::$app->params['urlToken'])['token'];
        $datosQr = $this->solicitaDatosPost(\Yii::$app->params['urlQr'], ['encode' => $codigoV, 'base64' => 'true', 'validador' => true]);

        $protocol = self::isProduction()?'https':'http';
        $datosBarCode = $this->solicitaBarCode(\Yii::$app->params['urlBarCode'], $codigoV);
        $datosFirma = $this->solicitaDatosPost(\Yii::$app->params['urlFirma'], ['code' => $codigoV, 'token' => $token, 'type' => strtoupper($tipo), 'path' => \yii\helpers\Url::home($protocol) . $filename, 'signed_document' => \yii\helpers\Url::home('https') . $filenameFinal]);

        @unlink($filename);

        $content = $this->renderPartial('/carta/certificado' . $tipo, [
            'productos' => $productos,
            'proveedor' => $proveedor,
            'datosFirma' => $datosFirma,
            'datosQr' => $datosQr,
            'dataExperiencia' => $dataExperiencia,
            'capacidadFinanciera' => $capacidadFinanciera,
            'capFin' => $capFin,
            'date' => $date
        ]);

        $mpdf = new mPDF('utf-8', 'A4', 0, '', 0, 0, 5, 15, 0, 0);

        $this->add_custom_fonts_to_mpdf($mpdf); //

        $mpdf->SetTitle('Certificado');
        $mpdf->SetHTMLHeader($this->renderPartial('/carta/header', ['tipo_provider' => $tipo]));
        $mpdf->SetHTMLFooter($this->renderPartial('/carta/certificado_footer', ['datosFirma' => $datosFirma, 'datosQr' => $datosQr, 'tipo_provider' => $tipo, 'barCode' => $datosBarCode, 'code' => $codigoV]));
        /* if(!$_ENV['VEDA_ELECTORAL']){ $mpdf->SetWatermarkImage('imgs/cartas/logo_sello.png',.8,array(100,100)); } */
        $mpdf->SetWatermarkImage('imgs/cartas/logo_watermark.svg',.1,array(100,100));
        $mpdf->showWatermarkImage = true;
        $mpdf->defaultfooterline = 0;
        $mpdf->WriteHTML($content);
        $mpdf->WriteHTML($stylesheet, 1);

        
        $mpdf->Output($filenameFinal, 'F');
        chmod($filenameFinal, 0777);

        if ($datosFirma['status'] != 'Error' && file_exists($filenameFinal)) {
            $hc = new models\HistoricoCertificados();
            $hc->provider_id = $proveedor->provider_id;
            $hc->codigo_verificacion = $codigoV;
            $hc->url_certificado = $filenameFinal;
            $hc->provider_type = $tipo;
            if (!empty($date)) {
                $date = base64_decode($date);
                $hc->created_at = $date;
                $anio = substr($date, 0, 4);
                $anioSumado = (int)$anio + 1;
                $vigencia = strval($anioSumado) . "-12-31";
            }
            if ($hc->save() && $cert) {
                $model = FirstUpdateCertificate::findOne(intval($cert));
                $model->historico_certificado_id = $hc->historico_certificados_id;
                $model->save();
            }
            $proveedor->permanently_disabled = 'ACTIVO';
            $proveedor->vigencia = (is_null($vigencia) || empty($vigencia)) ? (date("Y") + 1)."-12-31" : $vigencia;
            $proveedor->save(false);

            //GuardaHistorico
            //ValidadorController::guardaHistorico($proveedor->provider_id,$hc->historico_certificados_id,'bys');

            $subject_correo = $tipo == 'bys' ? '¡Felicidades! ya formas parte del Padrón de Proveedores': '¡Felicidades! ya formas parte del Registro Estatal de Contratistas';
            self::sendEmail('/provider/correos/certificacion', null, $proveedor->email, $subject_correo, ['tipo_provider' => $tipo], $filenameFinal);
            self::AllSendNotification($proveedor->user_id, $tipo, null, 'CERTIFICADO', null, '¡Felicidades!', 'PROVIDER');

        } else {
            $error = new models\ErrorInesperado();
            $error->tipo_error = 'FIRMAR CERTIFICADO';
            $error->data = $datosFirma;
            $error->user_id = Yii::$app->user->getId();
            $error->save();
        }

        self::pushNotification($proveedor->user_id, "¡Felicidades, ya formas parte del Padrón de Proveedores!");

        return $this->redirect('/first-update-certificate/index');

    }


    public function actionDocumentoCer2($id = null, $tipo = null, $date = null)
    {

        $proveedor = models\Provider::findOne($id);

        if (!$proveedor) {
            return $this->redirect('/');
        }


        if ($date) {
            $dateVal = DatosValidados::find()->select(['created_date'])->where(['and', ['provider_id' => $id], ['status_bys' => 'VALIDADO']])->orderBy(['validado_id' => SORT_DESC])->limit(1)->one()['created_date'];
            $date = base64_encode($dateVal);
        }
        $datosFirma = ['firmante' => '', 'cargo' => '', 'firma' => ''];

        $datosQr = ['img' => ''];

        $path_doc = 'prov_certificado';
        if (!file_exists($path_doc)) {
            mkdir($path_doc, 0777, true);
        }

        $dataExperiencia = [];
        $capacidadFinanciera = 0;
        $capFin = 0;
        if ($tipo == 'op') {
            $dataExperiencia = Yii::$app->db->createCommand("
                    select cs.nombre as especialidad,e.id_obra as id,SUM(e.monto) as monto from provider.experiencia e
                    join provider.subespecialidad_category cs on cs.category_id = e.id_obra
                    where e.provider_id = :id and status_op = 'VALIDADO' AND e.activo is TRUE
                    group by especialidad,id order by monto DESC", [':id' => $id])->queryAll();


            $capFin = Yii::$app->db->createCommand("select capacidad_contratacion_resultado from provider.capacidad_contratacion
                      where provider_id = :id ORDER BY capacidad_contratacion_id DESC", [':id' => $id])->queryOne()['capacidad_contratacion_resultado'];
            $capacidadFinanciera = array_sum(ArrayHelper::getColumn($dataExperiencia, 'monto'));
        }


        $content = $this->renderPartial('/carta/certificado' . $tipo, [
            'proveedor' => $proveedor,
            'dataExperiencia' => $dataExperiencia,
            'capacidadFinanciera' => $capacidadFinanciera,
            'capFin' => $capFin,
            'datosFirma' => $datosFirma,
            'datosQr' => $datosQr,
            'date' => $date
        ]);

        $mpdf = new \mPDF();
        $mpdf->SetTitle('Certificado');
        $mpdf->SetHTMLHeader($this->renderPartial('/carta/header', ['tipo_provider' => $tipo]));
        $mpdf->SetHTMLFooter($this->renderPartial('/carta/certificado_footer', ['datosFirma' => $datosFirma, 'datosQr' => $datosQr, 'tipo_provider' => $tipo]));
        $mpdf->defaultfooterline = 0;
        $mpdf->WriteHTML($content);
        $filename = $path_doc . '/' . md5($proveedor->provider_id) . '_certificado_proveedor' . time() . '.pdf';
        $mpdf->Output($filename, 'F');

        chmod($filename, 0777);

        $date_year = date("Y");
        $date_month = date("m");
        $filenameFinal = 'documentos_firmados' . '/' . $proveedor->rfc . '/' . $proveedor->rfc . '_certificado_' . $date_year . '_' . $date_month . '_' . $tipo . time() . '.pdf';


        $codigoV = $this->codigoVerificacion($proveedor->provider_id);
        $token = $this->solicitaDatosGet(\Yii::$app->params['urlToken'])['token'];
        $datosQr = $this->solicitaDatosPost(\Yii::$app->params['urlQr'], ['encode' => $codigoV, 'base64' => 'true', 'validador' => true]);

        $protocol = self::isProduction()?'https':'http';
        $datosBarCode = $this->solicitaBarCode(\Yii::$app->params['urlBarCode'], $codigoV);
        $datosFirma = $this->solicitaDatosPost(\Yii::$app->params['urlFirma'], ['code' => $codigoV, 'token' => $token, 'type' => strtoupper($tipo), 'path' => \yii\helpers\Url::home($protocol) . $filename, 'signed_document' => \yii\helpers\Url::home('https') . $filenameFinal]);

        @unlink($filename);

        $content = $this->renderPartial('/carta/certificado' . $tipo, [
            'proveedor' => $proveedor,
            'datosFirma' => $datosFirma,
            'datosQr' => $datosQr,
            'dataExperiencia' => $dataExperiencia,
            'capacidadFinanciera' => $capacidadFinanciera,
            'capFin' => $capFin,
            'date' => $date
        ]);


        $mpdf = new \mPDF();
        $mpdf->SetTitle('Certificado');
        $mpdf->SetHTMLHeader($this->renderPartial('/carta/header', ['tipo_provider' => $tipo]));
        $mpdf->SetHTMLFooter($this->renderPartial('/carta/certificado_footer', ['datosFirma' => $datosFirma, 'datosQr' => $datosQr, 'tipo_provider' => $tipo, 'barCode' => $datosBarCode, 'code' => $codigoV]));
        $mpdf->defaultfooterline = 0;
        $mpdf->WriteHTML($content);

        $mpdf->Output($filenameFinal, 'F');
        chmod($filenameFinal, 0777);


        var_dump(\yii\helpers\Url::home('https') . $filenameFinal);
        var_dump($datosFirma);
        exit();

        if ($datosFirma['status'] != 'Error' && file_exists($filenameFinal)) {
            $hc = new models\HistoricoCertificados();
            $hc->provider_id = $proveedor->provider_id;
            $hc->codigo_verificacion = $codigoV;
            $hc->url_certificado = $filenameFinal;
            $hc->provider_type = $tipo;
            if (!empty($date)) {
                $date = base64_decode($date);
                $hc->created_at = $date;
            }
            $hc->save();
            $subject_correo = $tipo == 'bys' ? '¡Felicidades! ya formas parte del Padrón de Proveedores': '¡Felicidades! ya formas parte del Registro Estatal de Contratistas';
            self::sendEmail('/provider/correos/certificacion', null, $proveedor->email, $subject_correo, ['tipo_provider' => $tipo], $filenameFinal);
            self::AllSendNotification($proveedor->user_id, $tipo, null, 'CERTIFICADO', null, '¡Felicidades!', 'PROVIDER');

        } else {
            $error = new models\ErrorInesperado();
            $error->tipo_error = 'FIRMAR CERTIFICADO';
            $error->data = $datosFirma;
            $error->user_id = Yii::$app->user->getId();
            $error->save();
        }

        return $this->redirect('/first-update-certificate/index');

    }

    public function documentopreregistro($id)
    {

        $tipo = 'bys';
        $proveedor = models\Provider::findOne($id);

        if (!$proveedor) {
            return $this->redirect('/');
        }

        $path_doc = 'preRegistro';
        if (!file_exists($path_doc)) {
            mkdir($path_doc, 0777, true);
        }

        $datosFirma = ['firmante' => '', 'cargo' => '', 'firma' => ''];

        $datosQr = ['img' => ''];

        $content = $this->renderPartial('/carta/pdf/preregistro', [
            'model' => $proveedor,
            'datosFirma' => $datosFirma,
            'datosQr' => $datosQr
        ]);

        $date_year = date("Y");
        $date_month = date("m");
        $stylesheet = file_get_contents('css/pdf.css');
        $mpdf = new \mPDF('utf-8', 'A4', 0, '', 0, 0, 5, 15, 0, 0);
        $mpdf->SetTitle('PreRegistro');
        $mpdf->SetHTMLHeader($this->renderPartial('/carta/pdf/headerbys'));
        $mpdf->SetHTMLFooter($this->renderPartial('/carta/pdf/footer_pre_reg', ['datosFirma' => $datosFirma, 'datosQr' => $datosQr]));
        $mpdf->WriteHTML($stylesheet, 1);
        //$mpdf->defaultfooterline = 0;
        $mpdf->WriteHTML($content);
        $filename = 'preRegistro' . '/' . $proveedor->rfc . '_preregistro_' . $date_year . '_' . $date_month . '_' . $tipo . time() . '.pdf';
        $mpdf->Output($filename, 'F');
        chmod($filename, 0777);

        $codigoV = $this->codigoVerificacion($proveedor->provider_id);
        $token = $this->solicitaDatosGet(\Yii::$app->params['urlToken'])['token'];
        $datosQr = $this->solicitaDatosPost(\Yii::$app->params['urlQr'], ['encode' => $codigoV, 'base64' => 'true']);
        $datosFirma = $this->solicitaDatosPost(\Yii::$app->params['urlFirma'], ['code' => $codigoV, 'token' => $token, 'type' => strtoupper($tipo), 'path' => \yii\helpers\Url::home('https') . $filename]);


        @unlink($filename);

        $content = $this->renderPartial('/carta/pdf/preregistro', [
            'model' => $proveedor,
            'datosFirma' => $datosFirma,
            'datosQr' => $datosQr
        ]);

        $date_year = date("Y");
        $date_month = date("m");

        $mpdf = new \mPDF('utf-8', 'A4', 0, '', 0, 0, 5, 15, 0, 0);
        $mpdf->SetTitle('PreRegistro');
        $mpdf->SetHTMLHeader($this->renderPartial('/carta/pdf/headerbys'));
        $mpdf->SetHTMLFooter($this->renderPartial('/carta/pdf/footer_pre_reg', ['datosFirma' => $datosFirma, 'datosQr' => $datosQr]));
        $mpdf->WriteHTML($stylesheet, 1);

        $mpdf->WriteHTML($content);
        $filename = 'preRegistro' . '/' . $proveedor->rfc . '_preregistro_' . $date_year . '_' . $date_month . '_' . $tipo . time() . '.pdf';
        $mpdf->Output($filename, 'F');
        chmod($filename, 0777);


        $hc = new models\HistoricoCertificados();
        $hc->provider_id = $proveedor->provider_id;
        $hc->url_certificado = $filename;
        $hc->tipo = 'PREREGISTRO';
        $hc->save();

        self::sendEmail('/provider/correos/preregistro', null, $proveedor['email'], 'Pre-registro', ['tipo_provider' => $tipo], $filename);

    }


    public function verificarModulosPreregistro($status_global = null, $provider_id = null, $tipo_provider = null, $acta_firmada = null)
    {
        $status_return = false;
        $rfc_validado = models\Rfc::find()->select($status_global)->where(['provider_id' => $provider_id])->one()[$status_global];
        $alta_hacienda_validado = models\AltaHacienda::find()->select($status_global)->where(['provider_id' => $provider_id])->one()[$status_global];
        $perfilValidado = models\Perfil::find()->select($status_global)->where(['provider_id' => $provider_id])->one()[$status_global];
        $tipoPersona = models\Provider::find()->select('tipo_persona')->where(['provider_id' => $provider_id])->one()['tipo_persona'];

        $modActa = 'VALIDADO';
        $relAccinistas = 'VALIDADO';
        $repLegal = 'VALIDADO';
        $intervencion_bancaria_validado = 'VALIDADO';

        $expC = $this->porcentajeTablaClientesCarta('ClientesContratos', $status_global, $provider_id);
        $experiencia = intval($expC) == 100 ? 'VALIDADO' : 'PENDIENTE';

        if ($tipoPersona == 'Persona moral') {

            $mod = $exp = $this->porcentajeTablaCarta('ModificacionActa', $status_global, $provider_id);
            $modActa = intval($mod) == 100 || $mod === null ? 'VALIDADO' : 'PENDIENTE';

            $relA = $exp = $this->porcentajeTablaCarta('RelacionAccionistas', $status_global, $provider_id);
            $relAccinistas = intval($relA) == 100 ? 'VALIDADO' : 'PENDIENTE';

            $repL = $exp = $this->porcentajeTablaCarta('RepresentanteLegal', $status_global, $provider_id);
            $repLegal = intval($repL) == 100 ? 'VALIDADO' : 'PENDIENTE';
        }

        //$intervencion_bancaria_validado = models\IntervencionBancaria::find()->select($status_global)->where(['provider_id' => $provider_id])->one()[$status_global];

        $entidad = Yii::$app->db->createCommand("select state_fiscal from  provider.ubicacion
        where provider_id = :p and type_address_prov ='DOMICILIO FISCAL'", [':p' => $provider_id])->queryOne()['state_fiscal'];

        $statusDirNl = 'VALIDADO';
        if (isset($entidad) && $entidad != 19 || $entidad === null) {
            $statusDirNl = 'PENDIENTE';
            $modelDirNL = models\Ubicacion::find()->where(['and', ['provider_id' => $provider_id], ['type_address_prov' => 'NL']])->orderBy(['ubicacion_id' => SORT_DESC])->one();
            if ($modelDirNL !== null) {
                $statusDirNl = $modelDirNL->status_bys;
            }
        }

        $p_ub = $this->porcentajeTablaCarta('Ubicacion', 'status_bys', $provider_id);
        $p_ubicacion = $p_ub === null ? 0 : $p_ub;
        $ubicacion = (intval($p_ubicacion) == 100 || $p_ub === null) && $statusDirNl == 'VALIDADO' ? 'VALIDADO' : 'PENDIENTE';

        $p_fo = $this->porcentajeTablaCarta('FotografiaNegocio', 'status_bys', $provider_id);
        $p_fotografia = $p_fo === null ? 0 : $p_fo;
        $fotografia = (intval($p_fotografia) == 100) ? 'VALIDADO' : 'PENDIENTE';

        if ($perfilValidado == 'VALIDADO' &&
            $rfc_validado == 'VALIDADO' &&
            $alta_hacienda_validado == 'VALIDADO' &&
            $intervencion_bancaria_validado == 'VALIDADO' &&
            $experiencia == 'VALIDADO' &&
            $modActa == 'VALIDADO' &&
            $relAccinistas == 'VALIDADO' &&
            $repLegal == 'VALIDADO' &&
            $ubicacion == 'VALIDADO' &&
            $fotografia == 'VALIDADO') {
            $status_return = true;
        }

        return $status_return;

    }


    public function verificarModulosValidador($status_global = null, $provider_id = null, $tipo_provider = null, $acta_firmada = null)
    {
        $status_return = false;
        $rfc_validado = models\Rfc::find()->select($status_global)->where(['provider_id' => $provider_id])->one()[$status_global];
        $alta_hacienda_validado = models\AltaHacienda::find()->select($status_global)->where(['provider_id' => $provider_id])->one()[$status_global];
        $perfilValidado = models\Perfil::find()->select($status_global)->where(['provider_id' => $provider_id])->one()[$status_global];
        $tipoPersona = models\Provider::find()->select('tipo_persona')->where(['provider_id' => $provider_id])->one()['tipo_persona'];

        $modActa = 'VALIDADO';
        $relAccinistas = 'VALIDADO';
        $maquinaria = 'VALIDADO';
        $credenciales = 'VALIDADO';
        $organigrama = 'VALIDADO';
        $repLegal = 'VALIDADO';

        $expC = $this->porcentajeTablaClientesCarta('ClientesContratos', $status_global, $provider_id);
        $experienciaComercial = intval($expC) == 100 ? 'VALIDADO' : 'PENDIENTE';

        $entidad = Yii::$app->db->createCommand("select state_fiscal from  provider.ubicacion
        where provider_id = :p and type_address_prov ='DOMICILIO FISCAL'", [':p' => $provider_id])->queryOne()['state_fiscal'];

        $statusDirNl = 'VALIDADO';
        if (isset($entidad) && $entidad != 19 || $entidad === null) {
            $statusDirNl = 'PENDIENTE';
            $modelDirNL = models\Ubicacion::find()->where(['and', ['provider_id' => $provider_id], ['type_address_prov' => 'NL']])->orderBy(['ubicacion_id' => SORT_DESC])->one();
            if ($modelDirNL !== null) {
                $statusDirNl = $modelDirNL->$status_global;
            }
        }

        $p_ub = $this->porcentajeTablaCarta('Ubicacion', $status_global, $provider_id);
        $p_ubicacion = $p_ub === null ? 0 : $p_ub;

        $ubicacion = (intval($p_ubicacion) == 100 || $p_ub === null) && $statusDirNl == 'VALIDADO' ? 'VALIDADO' : 'PENDIENTE';

        $p_fo = $this->porcentajeTablaCarta('FotografiaNegocio', $status_global, $provider_id);
        $p_fotografia = $p_fo === null ? 0 : $p_fo;
        $fotografia = (intval($p_fotografia) == 100) ? 'VALIDADO' : 'PENDIENTE';


        if ($tipoPersona == 'Persona moral') {

            $mod = $exp = $this->porcentajeTablaCarta('ModificacionActa', $status_global, $provider_id);
            $modActa = intval($mod) == 100 || $mod === null ? 'VALIDADO' : 'PENDIENTE';

            $relA = $exp = $this->porcentajeTablaCarta('RelacionAccionistas', $status_global, $provider_id);
            $relAccinistas = intval($relA) == 100 ? 'VALIDADO' : 'PENDIENTE';

            $repL = $exp = $this->porcentajeTablaCarta('RepresentanteLegal', $status_global, $provider_id);
            $repLegal = intval($repL) == 100 ? 'VALIDADO' : 'PENDIENTE';

        }

        if ($tipo_provider == 'op') {

            $organigrama = models\Organigrama::find()->select('status_op')->where(['provider_id' => $provider_id])->one()['status_op'];

            $intervencion_bancaria_validado = 'VALIDADO';
            $financieros = models\EstadoFinanciero::find()->select($status_global)->where(['provider_id' => $provider_id])->one()[$status_global];
            $exp = $this->porcentajeTablaCarta('Experiencia', 'status_op', $provider_id);
            $maq = $this->porcentajeTablaCarta('MaquinariaEquipos', 'status_op', $provider_id);
            $tec = $this->porcentajeTablaCarta('PersonalTecnico', 'status_op', $provider_id);
            $experiencia = intval($exp) == 100 ? 'VALIDADO' : 'PENDIENTE';
            $tecnicos = intval($tec) == 100 ? 'VALIDADO' : 'PENDIENTE';
            $maquinaria = intval($maq) == 100 ? 'VALIDADO' : 'PENDIENTE';

        } else {
            $intervencion_bancaria_validado = models\IntervencionBancaria::find()->select($status_global)->where(['provider_id' => $provider_id])->one()[$status_global];
            $experiencia = 'VALIDADO';
            $tecnicos = 'VALIDADO';

            $p_cer = $exp = $this->porcentajeTablaCarta('Certificacion', 'status_bys', $provider_id);
            $credenciales = intval($p_cer) == 100 ? 'VALIDADO' : 'PENDIENTE';


            $modelUltimaDeclaracion = models\UltimaDeclaracion::find()->where(['provider_id' => $provider_id])->one();
            if ($modelUltimaDeclaracion == null) {
                $modelUltimaDeclaracion = new models\UltimaDeclaracion();
                $modelUltimaDeclaracion->provider_id = $provider_id;
                $modelUltimaDeclaracion->save();
            }

            $financieros = $modelUltimaDeclaracion->status_bys;
        }


        if ($perfilValidado == 'VALIDADO' &&
            $rfc_validado == 'VALIDADO' &&
            $alta_hacienda_validado == 'VALIDADO' &&
            $intervencion_bancaria_validado == 'VALIDADO' &&
            $experiencia == 'VALIDADO' &&
            $financieros == 'VALIDADO' &&
            $tecnicos == 'VALIDADO' &&
            $modActa == 'VALIDADO' &&
            $relAccinistas == 'VALIDADO' &&
            $repLegal == 'VALIDADO' &&
            $maquinaria == 'VALIDADO' &&
            $ubicacion == 'VALIDADO' &&
            $fotografia == 'VALIDADO' &&
            $credenciales == 'VALIDADO' &&
            $experienciaComercial == 'VALIDADO' &&
            $organigrama == 'VALIDADO' &&
            $acta_firmada == 'FIRMADA') {
            $status_return = true;
        }

        return $status_return;

    }


    public function allModulesComplete($provider)
    {
        if (($modules = GeneralController::verificarModulosValPen('status_bys', $provider, 'bys')) == 12) {
            $model = new ModulesComplete();
            $model->provider_id = $provider;
            $model->modules = intval($modules);
            $model->save();
        }
    }

    public function actionValidaModulos($id)
    {
        $mod = GeneralController::verificarModulosValPen('status_bys', $id, 'bys', null, 1);
        header('Content-Type: application/json; charset=utf-8');
        \Yii::$app->response->format = \yii\web\Response::FORMAT_JSON;
        return $mod;
    }


    public function verificarModulosValPen($status_global = null, $provider_id = null, $tipo_provider = null, $acta_firmada = null, $visor = null, $actualizacion = null)
    {
        $rfc_validado = models\Rfc::find()->select($status_global)->where(['provider_id' => $provider_id])->one()[$status_global];
        $alta_hacienda_validado = models\AltaHacienda::find()->select($status_global)->where(['provider_id' => $provider_id])->one()[$status_global];
        $perfilValidado = models\Perfil::find()->select($status_global)->where(['provider_id' => $provider_id])->one()[$status_global];
        $tipoPersona = models\Provider::find()->select('tipo_persona')->where(['provider_id' => $provider_id])->one()['tipo_persona'];

        $modActa = 'VALIDADO';
        $relAccinistas = 'VALIDADO';
        $credenciales = 'VALIDADO';
        $repLegal = 'VALIDADO';

        $expC = $this->porcentajeTablaClientesCartaPendienteValidado('ClientesContratos', $status_global, $provider_id);
        $experienciaComercial = intval($expC) == 100 ? 'VALIDADO' : 'PENDIENTE';

        $entidad = Yii::$app->db->createCommand("select state_fiscal from  provider.ubicacion
        where provider_id = :p and type_address_prov ='DOMICILIO FISCAL'", [':p' => $provider_id])->queryOne()['state_fiscal'];

        $statusDirNl = 'VALIDADO';
        if (isset($entidad) && $entidad != 19 || $entidad === null) {
            $statusDirNl = 'PENDIENTE';
            $modelDirNL = models\Ubicacion::find()->where(['and', ['provider_id' => $provider_id], ['type_address_prov' => 'NL']])->orderBy(['ubicacion_id' => SORT_DESC])->one();
            if ($modelDirNL !== null) {
                $statusDirNl = $modelDirNL->$status_global;
            }
        }

        $p_ub = $this->porcentajeTablaCartaValPen('Ubicacion', $status_global, $provider_id);
        $p_ubicacion = $p_ub === null ? 0 : $p_ub;

        $ubicacion = (intval($p_ubicacion) == 100 || $p_ub === null) ? 'VALIDADO' : (($p_ubicacion == -1) ? 'POR VALIDAR' : 'PENDIENTE');


        $p_fo = $this->porcentajeTablaCartaValPen('FotografiaNegocio', $status_global, $provider_id);
        $p_fotografia = $p_fo === null ? 0 : $p_fo;
        $fotografia = (intval($p_fotografia) == 100) ? 'VALIDADO' : (($p_fotografia == -1) ? 'POR VALIDAR' : 'PENDIENTE');


        if ($tipoPersona == 'Persona moral') {

            $mod = $exp = $this->porcentajeTablaCartaValPen('ModificacionActa', $status_global, $provider_id);
            $modActa = intval($mod) == 100 || $mod === null ? 'VALIDADO' : (($mod == -1) ? 'POR VALIDAR' : 'PENDIENTE');

            $relA = $exp = $this->porcentajeTablaCartaValPen('RelacionAccionistas', $status_global, $provider_id);
            $relAccinistas = intval($relA) == 100 ? 'VALIDADO' : (($relA == -1) ? 'POR VALIDAR' : 'PENDIENTE');

            $repL = $exp = $this->porcentajeTablaCartaValPen('RepresentanteLegal', $status_global, $provider_id);
            $repLegal = (intval($repL) == 100) ? 'VALIDADO' : (($repL == -1) ? 'POR VALIDAR' : 'PENDIENTE');

        }

        if ($tipo_provider == 'op') {

            $intervencion_bancaria_validado = 'VALIDADO';
            $financieros = models\EstadoFinanciero::find()->select($status_global)->where(['provider_id' => $provider_id])->one()[$status_global];

        } else {
            $intervencion_bancaria_validado = models\IntervencionBancaria::find()->select($status_global)->where(['provider_id' => $provider_id])->one()[$status_global];

            $p_cer = $this->porcentajeTablaCartaValPen('Certificacion', 'status_bys', $provider_id);
            $credenciales = intval($p_cer) == 100 ? 'VALIDADO' : (($p_cer == -1) ? 'POR VALIDAR' : 'PENDIENTE');


            $modelUltimaDeclaracion = models\UltimaDeclaracion::find()->where(['provider_id' => $provider_id])->one();
            if ($modelUltimaDeclaracion == null) {
                $modelUltimaDeclaracion = new models\UltimaDeclaracion();
                $modelUltimaDeclaracion->provider_id = $provider_id;
                $modelUltimaDeclaracion->save();
            }

            $financieros = $modelUltimaDeclaracion->status_bys;
        }

        $validados = 0;
        $validadosTotales = 0;

        if (in_array($perfilValidado, ['VALIDADO', 'POR VALIDAR'])) {
            if ($perfilValidado == 'VALIDADO') {
                $validadosTotales++;
            }
            $validados++;
        }
        if (in_array($rfc_validado, ['VALIDADO', 'POR VALIDAR'])) {
            if ($rfc_validado == 'VALIDADO') {
                $validadosTotales++;
            }
            $validados++;
        }

        if (in_array($alta_hacienda_validado, ['VALIDADO', 'POR VALIDAR'])) {
            if ($alta_hacienda_validado == 'VALIDADO') {
                $validadosTotales++;
            }
            $validados++;
        }

        if (in_array($intervencion_bancaria_validado, ['VALIDADO', 'POR VALIDAR'])) {
            if ($intervencion_bancaria_validado == 'VALIDADO') {
                $validadosTotales++;
            }
            $validados++;
        }

        if (in_array($financieros, ['VALIDADO', 'POR VALIDAR'])) {
            if ($financieros == 'VALIDADO') {
                $validadosTotales++;
            }
            $validados++;
        }

        if (in_array($modActa, ['VALIDADO', 'POR VALIDAR'])) {
            if ($modActa == 'VALIDADO') {
                $validadosTotales++;
            }
            $validados++;
        }

        if (in_array($relAccinistas, ['VALIDADO', 'POR VALIDAR'])) {
            if ($relAccinistas == 'VALIDADO') {
                $validadosTotales++;
            }
            $validados++;
        }

        if (in_array($repLegal, ['VALIDADO', 'POR VALIDAR'])) {
            if ($repLegal == 'VALIDADO') {
                $validadosTotales++;
            }
            $validados++;
        }

        if (in_array($ubicacion, ['VALIDADO', 'POR VALIDAR']) && in_array($statusDirNl, ['VALIDADO', 'POR VALIDAR'])) {
            if ($ubicacion == 'VALIDADO' && $statusDirNl == 'VALIDADO') {
                $validadosTotales++;
            }
            $validados++;
        }

        if (in_array($fotografia, ['VALIDADO', 'POR VALIDAR'])) {
            if ($fotografia == 'VALIDADO') {
                $validadosTotales++;
            }
            $validados++;
        }

        if (in_array($credenciales, ['VALIDADO', 'POR VALIDAR'])) {
            if ($credenciales == 'VALIDADO') {
                $validadosTotales++;
            }
            $validados++;
        }

        if (in_array($experienciaComercial, ['VALIDADO', 'POR VALIDAR'])) {
            if ($experienciaComercial == 'VALIDADO') {
                $validadosTotales++;
            }
            $validados++;
        }

        if ($actualizacion) {
            return ($validados == 12 && $validadosTotales == 12) ? 1 : ($validados == 12 ? 2 : 3);
        }
        if ($visor) {
            $data = [
                'validados' => [
                    'validados' => $validados,
                    'validadosTotales' => $validadosTotales,
                ],
                'estatusModulos' => [
                    'perfilValidado' => $perfilValidado,
                    'rfc_validado' => $rfc_validado,
                    'alta_hacienda_validado' => $alta_hacienda_validado,
                    'intervencion_bancaria_validado' => $intervencion_bancaria_validado,
                    'financieros' => $financieros,
                    'modActa' => $modActa,
                    'relAccinistas' => $relAccinistas,
                    'repLegal' => $repLegal,
                    'ubicacion' => $ubicacion,
                    'statusDirNl' => $statusDirNl,
                    'fotografia' => $fotografia,
                    'credenciales' => $credenciales,
                    'experienciaComercial' => $experienciaComercial,
                ],
                'porcentajesMultiples' => [
                    'p_ubicacion' => $p_ubicacion,
                    'p_fotografia' => $p_fotografia,
                    'p_modificacion' => $mod,
                    'p_accionistas' => $relA,
                    'p_representante' => $repL,
                    'p_certificacion' => $p_cer
                ]
            ];
            return $data;
        }
        return $validados == $validadosTotales ? 0 : $validados;

    }

    public static function codigoVerificacion($id)
    {

        $axu = 16 - strlen($id);

        $rnd_str = substr(str_shuffle("0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ"), 0, $axu);

        $real_password = $rnd_str . $id;

        return $real_password;
    }

    public static function solicitaDatosGet($url,$headers=[])
    {
        $curl = curl_init();
        curl_setopt($curl, CURLOPT_URL, $url);
        curl_setopt($curl, CURLOPT_HTTPHEADER, $headers);

        if (!self::isHttps()) {
            curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, false);
            curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, false);
        }
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($curl, CURLOPT_ENCODING, "");
        $curlData = curl_exec($curl);
        curl_close($curl);
        return json_decode($curlData, true);
    }


    public static function solicitaDatosPost($url, $params = [],$headers=['Content-Type: application/json'])
    {
        $curl = curl_init();
        curl_setopt($curl, CURLOPT_URL, $url);
        curl_setopt($curl, CURLOPT_POST, 1);
        curl_setopt($curl, CURLOPT_POSTFIELDS, json_encode($params));
        curl_setopt($curl, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
        $curlData = curl_exec($curl);
        curl_close($curl);
        return json_decode($curlData, true);
    }


    public static function solicitaBarCode($url, $code)
    {


        $params = [
            'encode' => $code,
            'base64' => 'true',
            'mostrar_valor' => 'false',
            'path' => 'uploads/datos/'
        ];

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($params));
        curl_setopt($ch, CURLOPT_HTTPHEADER, array('Content-Type: application/json'));
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

        $variableQr = curl_exec($ch);
        curl_close($ch);

        return json_decode($variableQr, true);
    }

    public static function getAllTipoObra($id = null, $id_obra = null)
    {


        return Yii::$app->db->createCommand("select cs.nombre as tipo_obra,SUM(e.monto) as monto from provider.experiencia e
                join provider.cat_subespecialidades cs on cs.subespecialidad_id = e.especialidades
                join provider.subespecialidad_category psc on psc.category_id = cs.category_id
                where e.provider_id = :id and e.id_obra = :id_obra and status_op = 'VALIDADO' AND e.activo is TRUE
                group by tipo_obra order by monto DESC", [':id' => $id, ':id_obra' => $id_obra])->queryAll();


    }

    /* function listFolderFiles('/var/www/html/SECOPNL/controllers/')
    {
        $fileInfo     = scandir($dir);

        $allFileLists = [];

        foreach ($fileInfo as $folder) {
            if ($folder !== '.' && $folder !== '..') {
                if (is_dir($dir . DIRECTORY_SEPARATOR . $folder) === true) {
                    $allFileLists[$folder] = self::listFolderFiles($dir . DIRECTORY_SEPARATOR . $folder);
                }
            }
        }

        return $allFileLists;
    } */


    public static function AllSendNotification($user_id = null, $bys_op = null, $model = null, $type_notification = null, $module_validation_name = null, $msg_extra = null, $role = 'VALIDADOR', $id = null, $register = null, $modeloUpdate = null)
    {

        $arrayVal = ['DATOS LEGALES', 'DATOS FINANCIEROS', 'DATOS TECNICOS', 'FOTOGRAFIA', 'FOTOGRAFIA NEGOCIO'];

        $userSendMsj = null;

        if (in_array($type_notification, $arrayVal) && $role == 'VALIDADOR') {
            $userSendMsj = self::getAllIdValidadores($type_notification, $bys_op);

        } else if ($user_id != null) {
            $userSendMsj[0] = ['user_id' => $user_id];
        }

        $bindParams = [':mnn' => $type_notification, ':role' => $role];
        $andWhere = '';
        if ($model != null) {
            $bindParams += [':model' => $model];
            $andWhere .= ' and mun.model_name = :model ';
        }

        $query = Yii::$app->db->createCommand("select mn.module_notification_name as mod, mun.module_notification_url_id, mun.url_module,mun.params,msg.notification from module_notification_url mun
                                                  join msg_notification msg using(msg_notification_id)
                                                  join module_notification mn on mn.module_notification_id = mun.module_notification_id
                                                  where mun.module_notification_name = :mnn and mun.type_user = :role $andWhere", $bindParams)->queryOne();

        $not_id = '';

        if (!empty($userSendMsj) && !empty($query)) {
            $save_notification = new models\SaveNotification();
            $save_notification->module_notification_url_id = $query['module_notification_url_id'];
            $save_notification->op_bys = $bys_op;
            $save_notification->module_validation_name = $module_validation_name;
            $save_notification->msg_extra = $msg_extra;
            if ($role == 'PROVIDER') {
                $url = self::generatorUrlModal($msg_extra, $user_id, $id, $model, $bys_op);
                $save_notification->url_modal = $url;
            }
            $save_notification->id = $id;
            $save_notification->column_search = $register;
            $save_notification->modelo = $modeloUpdate;
            if ($save_notification->save()) {
                $not_id = $save_notification->save_notification_id;
                foreach ($userSendMsj as $val) {
                    $user_notifiction = new models\UserNotification();
                    $user_notifiction->save_notification_id = $save_notification->save_notification_id;
                    $user_notifiction->user_id = $val['user_id'];
                    if ($user_notifiction->save()) {
                        if (($number_noti = models\NumberNotification::find()->where(['user_id' => $val['user_id']])->one()) !== null) {
                            $number_noti->number_notification = $number_noti->viewed == false ? $number_noti->number_notification + 1 : 1;
                            $number_noti->viewed = false;
                            $number_noti->save();
                        } else {
                            $number_noti = new models\NumberNotification();
                            $number_noti->number_notification = 1;
                            $number_noti->user_id = $val['user_id'];
                            $number_noti->save();
                        }
                    }
                }

                $implodeUsers = implode(',', ArrayHelper::getColumn($userSendMsj, 'user_id'));
                $titulo = str_replace(':name_mod:', $module_validation_name, $query['notification']);
                $html = '';

                $html .= '<p>' . $titulo . '</p><br>';

                $linea = '';
                if ($msg_extra !== null) {
                    $msg_extra = strtoupper($msg_extra);
                    $linea = ' - ';
                }

                $subtitulo = $query['mod'] . $linea;
                $url = null;
                if ($query['url_module'] != null) {
                    $url = $role == 'PROVIDER' && ($query['url_module'] == '/banco/view' || $query['url_module'] == '/calendar/calendar') ? $query['url_module'] : '/' . $bys_op . $query['url_module'];

                    $url .= strpos($url, '?') ? '&ntf=' . base64_encode($not_id) : '?ntf=' . base64_encode($not_id);

                    if ($query['params'] != null) {

                        /*
                         * checar las urls que requieren parametros
                         *
                         *
                         */
                    }
                    $html .= '<a href =' . $url . '>Ver</a>';
                }


                $color = strtoupper($msg_extra) == 'VALIDADO' ? '#8cc63f' : (strtoupper($msg_extra) == 'RECHAZADO' ? '#ce5b5b' : ($msg_extra == '¡Felicidades!' ? '#29abe2' : ''));

                $imgIcon = $type_notification == 'COTEJO' && $msg_extra == 'VALIDADO' ? 'RESPUESTA_DE_VALIDACION' : str_replace(' ', '_', $query['mod']);

                $img = Url::to('@web/imgs/notificaciones/' . $imgIcon) . ".png";

                self::sendNotification('success', $html, $implodeUsers, $color, $subtitulo, $msg_extra, $img, $url, $titulo);
            }
        }
    }


    public function generatorUrlModal($type, $user_id, $id, $modelo, $bysOp)
    {

        $url = '';
        $provider_id = models\Provider::find()->select('provider_id')->where(['user_id' => $user_id])->one()['provider_id'];
        if ($provider_id !== null) {
            if (in_array($modelo, ['clientes_contratos', 'certificacion', 'ubicacion', 'fotografia_negocio', 'clientes_contratos', 'relacion_accionistas', 'experiencia', 'personal_tecnico', 'maquinaria_equipos', 'modificacion_acta', 'representante_legal'])) {
                $action = $type == 'Validado' ? 'view' : 'update';
                if ($modelo == 'clientes_contratos') {
                    $url = $action == 'update' ? "update?user_id=$provider_id&id=$id" : "/$bysOp/perfil/view?user_id=" . base64_encode($provider_id) . "&id=" . base64_encode($id);
                } elseif ($modelo == 'modificacion_acta') {
                    $url = $action . 'mod?id=' . base64_encode($id);
                } elseif ($modelo == 'representante_legal') {
                    $url = $action . 'rep?id=' . base64_encode($id);
                } elseif ($modelo == 'relacion_accionistas') {
                    $url = $action . 'accionistas?id=' . base64_encode($id);
                } elseif ($modelo == 'ubicacion') {
                    $url = "/$bysOp/ubicacion/$action?user_id=$provider_id&id=$id";
                } elseif ($modelo == 'fotografia_negocio') {
                    $url = "/$bysOp/fotografia/$action?user_id=$provider_id&id=$id";
                } elseif ($modelo == 'certificacion') {
                    $url = $action == 'update' ? '/bys/credenciales/' . $action . '?id=' . base64_encode($id) : "/bys/credenciales/view?user_id=$provider_id&id=$id";
                } elseif (in_array($modelo, ['personal_tecnico', 'maquinaria_equipos', 'experiencia'])) {
                    $url = $action . '?id=' . base64_encode($id);
                }
            }
        }

        return $url;

    }

    public static function getAllIdValidadores($responsabilidad = null, $tipo = null)
    {

        $responsabilidad = $responsabilidad == 'FOTOGRAFIA NEGOCIO' ? 'FOTOGRAFIA' : $responsabilidad;

        $tipo_pro = $tipo == 'bys' ? 'VALIDADOR PROVEEDORES' : 'VALIDADOR CONTRATISTAS';

        $query = new Query;
        $query->select('usuarios.user_id')
            ->from('usuarios')
            ->innerJoin('usuarios_responsabilidades',
                'usuarios_responsabilidades.user_id = usuarios.user_id')
            ->innerJoin("cat_responsabilidades", "cat_responsabilidades.responsabilidad_id = usuarios_responsabilidades.responsabilidad_id")
            ->where(['and', ['usuarios.role' => $tipo_pro, 'usuarios.status' => 'ACTIVO'], ['cat_responsabilidades.descripcion' => $responsabilidad]]);
        $ids_validadores = $query->createCommand()->queryAll();


        return $ids_validadores;
    }

    public static function getDataNotification($id, $limit = true)
    {

        $limit = $limit ? ' limit 10' : null;

        return Yii::$app->db->createCommand("select mnu.type_user,sn.op_bys,mnu.module_notification_url_id,mnu.params,sn.save_notification_id,
                                                    mnu.url_module, msg.notification, sn.viewed,mn.module_notification_name,
                                                    sn.created_date,replace(mnu.module_notification_name,' ','_') as module_name,
                                                    sn.module_validation_name,sn.msg_extra from module_notification_url mnu
                                                    join module_notification mn using (module_notification_id)
                                                    join save_notification sn on sn.module_notification_url_id = mnu.module_notification_url_id
                                                    join msg_notification msg on msg.msg_notification_id = mnu.msg_notification_id
                                                    join user_notification un on un.save_notification_id = sn.save_notification_id
                                                    where un.user_id = :id
                                                    order by created_date DESC $limit", [':id' => $id])->queryAll();

    }

    public static function cotejoPendiente()
    {

        $fecha_actual = date("Y-m-d");

        $id = Yii::$app->user->getId();
        $status = 'SIN CITA';

        if (($calendar = models\Calendar::find()->where(['and', ['user' => $id], ['status' => 'CONFIRMADA']])->orderBy(['visit_day' => SORT_DESC])->one()) !== null) {
            $status = 'CITA MOD';
            $fecha = date("Y-m-d", strtotime($calendar->visit_day . "- 2 days"));
            if ($fecha <= $fecha_actual) {
                $status = 'CITA NO MOD';
            }
        }

        return $status;
    }

    public static function isHttps()
    {
        if (array_key_exists("HTTPS", $_SERVER) && 'on' === $_SERVER["HTTPS"]) {
            return true;
        }
        if (array_key_exists("SERVER_PORT", $_SERVER) && 443 === (int)$_SERVER["SERVER_PORT"]) {
            return true;
        }
        if (array_key_exists("HTTP_X_FORWARDED_SSL", $_SERVER) && 'on' === $_SERVER["HTTP_X_FORWARDED_SSL"]) {
            return true;
        }
        if (array_key_exists("HTTP_X_FORWARDED_PROTO", $_SERVER) && 'https' === $_SERVER["HTTP_X_FORWARDED_PROTO"]) {
            return true;
        }
        return false;
    }

    public static function warningCotejo()
    {
        $con = '';
        $con .= '<div class="p-3 mb-2 bg-warning text-dark color_cotejo"><span style="font-weight: bold">ADVERTENCIA:</span> en caso de continuar, su cita para cotejo de documentos que tiene programada será cancelada, deberá volver a programar.</div>';

        return $con;
    }

    public static function eliminarCita()
    {
        $id = Yii::$app->user->getId();

        $provider_id = models\Provider::find()->select('provider_id')->where(['user_id' => $id])->one()['provider_id'];

        models\Calendar::updateAll(['status' => 'CANCELADA'], ['and', ['status' => 'CONFIRMADA'], ['user' => $id]]);

        models\Provider::updateAll(['status_cotejar' => null], ['user_id' => $id]);

        models\Status::updateAll(['status_bys' => 'TERMINADO'], ['and', ['status_bys' => 'PENDIENTE'], ['modelo' => 'cotejar'], ['register_id' => $provider_id]]);

        $visit = Visit::find()->where(['and', ['provider_id' => $provider_id], ['status' => true]])->asArray()->all();

        if ($visit) {
            foreach ($visit as $v) {

                if ($v['modify']) {
                    Visit::updateAll(['status' => false], ['visit_id' => $v['visit_id']]);
                } else {
                    models\VisitDetails::deleteAll(['visit_id' => $v['visit_id']]);
                    Visit::deleteAll(['visit_id' => $v['visit_id']]);
                }

            }
        }


        return true;
    }

    public function actionSendEmailCertificado($email = null)
    {

        $file = Yii::$app->db->createCommand("select c.url_certificado from historico_certificados c
                join provider p using(provider_id)
                where p.email = '<EMAIL>'
                order by created_at DESC")->queryOne()['url_certificado'];

        self::sendEmail('/provider/correos/certificacion', null, $email, 'Certificado', ['tipo_provider' => 'op'], $file);
    }

    public static function calEspecialidadObra($id, $tipo)
    {

        if ($tipo == 'obra') {
            $as = 'tipo_obra';
            $table = 'provider.subespecialidad_category';
            $val = 'sc.category_id';
            $valTE = 'e.id_obra';
        } else {
            $as = 'tipo_especialidad';
            $table = 'provider.cat_subespecialidades';
            $val = 'sc.subespecialidad_id';
            $valTE = 'e.especialidades';
        }

        $data = Yii::$app->db->createCommand("select sc.nombre as $as, sum(e.monto) as total, count(1) as num
                    from provider.experiencia e
                    join $table sc on $val = $valTE
                    where e.provider_id = :id and e.activo is true
                     and e.status_op IN ('VALIDADO') group by $as order by num DESC", [':id' => $id])->queryAll();

        $numero_obras = array_sum(ArrayHelper::getColumn($data, 'num'));
        $total_obras = array_sum(ArrayHelper::getColumn($data, 'total'));
        $sumObraP1 = 0;
        $sumObraP2 = 0;
        $array_final_obra = [];
        $analisis_final = [];
        $sumAnalisisFinal = 0;
        foreach ($data as $k => $val) {
            $array_data_obra = [];
            $nombre_obra = $val[$as];
            $cantidad = $val['num'];
            $monto = $val['total'];
            $vp1 = ($cantidad / $numero_obras);
            $vp2 = ($monto / $total_obras);
            $valP1 = self::redondear_dos_decimal($vp1) * 100;
            $valP2 = self::redondear_dos_decimal($vp2) * 100;
            $sumObraP1 += $valP1;
            $sumObraP2 += $valP2;
            array_push($array_data_obra, $nombre_obra);
            array_push($array_data_obra, $cantidad);
            array_push($array_data_obra, '% ' . $valP1);
            array_push($array_data_obra, '$ ' . number_format($monto, 2, '.', ','));
            array_push($array_data_obra, '% ' . $valP2);
            array_push($array_final_obra, $array_data_obra);
            $sumAnalisis = self::redondear_dos_decimal((($valP1 + $valP2) / 200)) * 100;
            $sumAnalisisFinal += $sumAnalisis;
            $analisis_final[] = ['tipo' => $nombre_obra, 'monto' => $sumAnalisis];
        }

        return ['arreglo' => $array_final_obra, 'numero' => $numero_obras, 'total' => '$ ' . number_format($total_obras, 2, '.', ','),
            'sumOEP1' => '% ' . $sumObraP1, 'sumOEP2' => '% ' . $sumObraP2, 'analisis' => $analisis_final, 'sumAnalisis' => $sumAnalisisFinal];
    }


    function redondear_dos_decimal($valor)
    {
        $float_redondeado = round($valor * 100, 2) / 100;
        return $float_redondeado;
    }

    function sort_by_orden($array)
    {
        foreach ($array as $key => $row) {

            $aux[$key] = $row['monto'];
        }

        array_multisort($aux, SORT_DESC, $array);

        return $array;
    }


    public function actionApiSire($userName = null, $user_id = 1)
    {

        try {
            $request = new \SoapClient(
                'http://sistemas.nl.gob.mx/wsSA_ProveedoresSU/wsProveedores.asmx?WSDL', array('exceptions' => true));

            $userResponseDecode = $request->ConsulUsuario(['usuario' => $userName]);

            $error = new models\ErrorInesperado();
            $error->data = $userResponseDecode;
            $error->user_id = $user_id;

            if (isset($userResponseDecode->ConsulUsuarioResult) && !empty($userResponseDecode->ConsulUsuarioResult)) {
                $userResponseData = json_decode($userResponseDecode->ConsulUsuarioResult, true);
                if (isset($userResponseData[0]) && isset($userResponseData[0]['existe'])) {
                    $error->tipo_error = 'Consultar proveedor SECOP Existe';
                    $existe = $userResponseData[0]['existe'];
                    $estatus = $userResponseData[0]['estatus'];
                    $msg = 'Existe';
                } else {
                    $existe = false;
                    $estatus = true;
                    $msg = 'No Existe';
                    $error->tipo_error = 'Consultar proveedor SECOP No Existe';
                }
                $error->save();
                return json_encode(['existe' => $existe, 'status' => $estatus, 'msg' => $msg]);
            }
            $error->tipo_error = 'Consultar proveedor SECOP No retorno variable esperada';
            $error->save();
            return json_encode(['existe' => false, 'status' => false, 'msg' => 'Error']);
        } catch (\Exception $e) {
            return json_encode(['existe' => false, 'status' => false, 'msg' => 'Error inesperado']);
        }
    }

    //http://sistemas.nl.gob.mx/wsSA_Proveedores/wsProveedores.asmx?op=InsertaProv

    public static function verifyUserNameSecop($userName, $user_id = 1)
    {
        /*
        try {
            $request = new \SoapClient(
                'http://sistemas.nl.gob.mx/wsSA_ProveedoresSU/wsProveedores.asmx?WSDL', array('exceptions' => true));

            $userResponseDecode = $request->ConsulUsuario(['usuario' => $userName]);

            $error = new models\ErrorInesperado();
            $error->data = $userResponseDecode;
            $error->user_id = $user_id;

            if (isset($userResponseDecode->ConsulUsuarioResult) && !empty($userResponseDecode->ConsulUsuarioResult)) {
                $userResponseData = json_decode($userResponseDecode->ConsulUsuarioResult, true);
                if (isset($userResponseData[0]) && isset($userResponseData[0]['existe'])) {
                    $error->tipo_error = 'Consultar proveedor SECOP Existe';
                    $existe = true;
                    $estatus = true;
                    $msg = 'Existe';
                } else {
                    $existe = false;
                    $estatus = true;
                    $msg = 'No Existe';
                    $error->tipo_error = 'Consultar proveedor SECOP No Existe';
                }
                $error->save();
                return json_encode(['existe' => $existe, 'status' => $estatus, 'msg' => $msg]);
            }
            $error->tipo_error = 'Consultar proveedor SECOP No retorno variable esperada';
            $error->save();
            return json_encode(['existe' => false, 'status' => false, 'msg' => 'Error']);
        } catch (\Exception $e) {
            return json_encode(['existe' => false, 'status' => false, 'msg' => 'Error inesperado']);
        }*/

        return json_encode(['existe' => false, 'status' => true, 'msg' => 'No Existe']);
    }


    public static function insertProvSecopa($data, $user_id = null)
    {
        try {
            $request = new \SoapClient(
                'http://sistemas.nl.gob.mx/wsSA_ProveedoresSU/wsProveedores.asmx?WSDL', array('exceptions' => true));

            $provResponseDecode = $request->InsertaProv($data);

            $error = new models\ErrorInesperado();
            $error->data = $provResponseDecode;
            $error->user_id = $user_id;

            if (isset($provResponseDecode->InsertaProvResult) && !empty($provResponseDecode->InsertaProvResult)) {
                $userResponseData = json_decode($provResponseDecode->InsertaProvResult, true);
                $error->tipo_error = 'Insertar proveedor SECOP Success';
                $error->save();
                return json_encode(['status' => true, 'msg' => $userResponseData['msg']]);
            }
            $error->tipo_error = 'Insertar proveedor SECOP Error';
            $error->save();
            return json_encode(['status' => false, 'msg' => 'Error inesperado']);

        } catch (\Exception $e) {
            $error = new models\ErrorInesperado();
            $error->tipo_error = 'Insertar proveedor SECOP Exception';
            $error->data = $e;
            $error->user_id = $user_id;
            $error->save();

            return json_encode(['status' => false, 'msg' => 'Error inesperado']);
        }
    }


    public static function actionUsereSecop($userName, $user_id = 1)
    {
        try {
            $request = new \SoapClient(
                'http://sistemas.nl.gob.mx/wsSA_ProveedoresSU/wsProveedores.asmx?WSDL', array('exceptions' => true));

            $userResponseDecode = $request->ConsulUsuario(['usuario' => $userName]);

            $error = new models\ErrorInesperado();
            $error->data = $userResponseDecode;
            $error->user_id = $user_id;

            if (isset($userResponseDecode->ConsulUsuarioResult) && !empty($userResponseDecode->ConsulUsuarioResult)) {
                $userResponseData = json_decode($userResponseDecode->ConsulUsuarioResult, true);
                if (isset($userResponseData[0]) && isset($userResponseData[0]['existe'])) {
                    $error->tipo_error = 'Consultar proveedor SECOP Existe';
                    $existe = true;
                    $estatus = true;
                    $msg = 'Existe';
                } else {
                    $existe = false;
                    $estatus = true;
                    $msg = 'No Existe';
                    $error->tipo_error = 'Consultar proveedor SECOP No Existe';
                }
                $error->save();
                return json_encode(['existe' => $existe, 'status' => $estatus, 'msg' => $msg]);
            }
            $error->tipo_error = 'Consultar proveedor SECOP No retorno variable esperada';
            $error->save();
            return json_encode(['existe' => false, 'status' => false, 'msg' => 'Error']);
        } catch (\Exception $e) {
            return json_encode(['existe' => false, 'status' => false, 'msg' => 'Error inesperado']);
        }
    }

    public function actionFechaNow()
    {
        $arr_mes = ['01' => 'Enero', '02' => 'Febrero', '03' => 'Marzo', '04' => 'Abril', '05' => 'Mayo', '06' => 'Junio', '07' => 'Julio', '08' => 'Agosto',
            '09' => 'Septiembre', '10' => 'Octubre', '11' => 'Noviembre', '12' => 'Diciembre'];
        $messs = date("m") - 1;

        $mes_anterior = mktime(0, 0, 0, $messs, '01', date("Y"));

        $mes_anterior = date('Y-m-d', $mes_anterior);

        $explode_fecha = explode('-', $mes_anterior);

        $periodo = $arr_mes[$explode_fecha[1]] . '-' . $explode_fecha[0];

        return $periodo;
    }

    public function getDateNow()
    {


        date_default_timezone_set('America/Monterrey');

        return date("Y-m-d H:i:s");


    }

    public function actionDocumentoPreregistro($id)
    {

        $proveedor = models\Provider::findOne($id);

        if (!$proveedor) {
            return $this->redirect('/');
        }

        $path_doc = 'preRegistro';
        if (!file_exists($path_doc)) {
            mkdir($path_doc, 0777, true);
        }

        $datosFirma = ['firmante' => '', 'cargo' => '', 'firma' => ''];

        $datosQr = ['img' => ''];

        $content = $this->renderPartial('/carta/pdf/preregistro', [
            'model' => $proveedor,
            'datosFirma' => $datosFirma,
            'datosQr' => $datosQr
        ]);

        $date_year = date("Y");
        $date_month = date("m");
        $stylesheet = file_get_contents('css/pdf.css');
        $mpdf = new \mPDF('utf-8', 'A4', 0, '', 0, 0, 5, 15, 0, 0);
        $mpdf->SetTitle('PreRegistro');
        $mpdf->SetHTMLHeader($this->renderPartial('/carta/pdf/headerbys'));
        $mpdf->SetHTMLFooter($this->renderPartial('/carta/pdf/footer_pre_reg', ['datosFirma' => $datosFirma, 'datosQr' => $datosQr]));
        $mpdf->WriteHTML($stylesheet, 1);
        //$mpdf->defaultfooterline = 0;
        $mpdf->WriteHTML($content);
        $filename = 'preRegistro' . '/' . $proveedor->rfc . '_preregistro_' . $date_year . '_' . $date_month . '_' . $proveedor->tipo_provider . time() . '.pdf';
        $mpdf->Output($filename, 'F');
        chmod($filename, 0777);

        $codigoV = $this->codigoVerificacion($proveedor->provider_id);
        $token = $this->solicitaDatosGet(\Yii::$app->params['urlToken'])['token'];
        $datosQr = $this->solicitaDatosPost(\Yii::$app->params['urlQr'], ['encode' => $codigoV, 'base64' => 'true']);
        $datosFirma = $this->solicitaDatosPost(\Yii::$app->params['urlFirma'], ['code' => $codigoV, 'token' => $token, 'type' => strtoupper($proveedor->tipo_provider), 'path' => \yii\helpers\Url::home('https') . $filename]);


        @unlink($filename);

        $content = $this->renderPartial('/carta/pdf/preregistro', [
            'model' => $proveedor,
            'datosFirma' => $datosFirma,
            'datosQr' => $datosQr
        ]);

        $date_year = date("Y");
        $date_month = date("m");

        $mpdf = new \mPDF('utf-8', 'A4', 0, '', 0, 0, 5, 15, 0, 0);
        $mpdf->SetTitle('PreRegistro');
        $mpdf->SetHTMLHeader($this->renderPartial('/carta/pdf/headerbys'));
        $mpdf->SetHTMLFooter($this->renderPartial('/carta/pdf/footer_pre_reg', ['datosFirma' => $datosFirma, 'datosQr' => $datosQr]));
        $mpdf->WriteHTML($stylesheet, 1);

        $mpdf->WriteHTML($content);
        $filename = 'preRegistro' . '/' . $proveedor->rfc . '_preregistro_' . $date_year . '_' . $date_month . '_' . $proveedor->tipo_provider . time() . '.pdf';
        $mpdf->Output($filename, 'F');
        chmod($filename, 0777);


        $hc = new models\HistoricoCertificados();
        $hc->provider_id = $proveedor->provider_id;
        $hc->url_certificado = $filename;
        $hc->tipo = 'PREREGISTRO';
        $hc->save();

        self::sendEmail('/provider/correos/preregistro', null, $proveedor['email'], 'Pre-registro', ['tipo_provider' => $proveedor->tipo_provider], $filename);

    }

    public function actionGenerateAvisoPri()
    {

        $content = $this->renderPartial('/carta/aviso_privacidad');
        $stylesheet = file_get_contents('css/site.css');
        @$mpdf = new \mPDF('utf-8', 'A4', 0, '', 0, 0, 5, 15, 0, 0);
        @$mpdf->SetTitle('Aviso de privacidad');
        @$mpdf->SetHTMLHeader($this->renderPartial('/carta/headeraviso'));
        @$mpdf->SetHTMLFooter($this->renderPartial('/carta/footeraviso'));
        //$mpdf->defaultfooterline = 0;
        @$mpdf->WriteHTML($stylesheet, 1);
        @$mpdf->WriteHTML($content);
        $filename = 'aviso_de_privacidad_integral.pdf';
        @$mpdf->Output($filename, 'D');


    }


    public function actionGenerateReCotejo()
    {

        $content = $this->renderPartial('/carta/aviso_cotejo');
        $stylesheet = file_get_contents('css/site.css');
        $mpdf = new \mPDF('utf-8', 'A4', 0, '', 0, 0, 5, 15, 0, 0);
        $mpdf->SetTitle('Aviso de reprogramación de cotejos');
        $mpdf->SetHTMLHeader($this->renderPartial('/carta/headeraviso'));
        $mpdf->SetHTMLFooter($this->renderPartial('/carta/footeraviso'));
        //$mpdf->defaultfooterline = 0;
        $mpdf->WriteHTML($stylesheet, 1);
        $mpdf->WriteHTML($content);
        $filename = 'aviso_reprogramar_cotejo.pdf';
        $mpdf->Output($filename, 'D');


    }


    public function saveLastSendValidation($providier, $modVal, $modName, $proType)
    {


        if (($mod = models\LastSendValidationRelation::find()->where(['and', ['provider_id' => $providier], ['model_name' => $modName],
                ['provider_type' => $proType], ['status' => 'PENDIENTE']])->one()) === null) {


            $ids = ArrayHelper::getColumn(models\LastSendValidation::find()->select('last_send_validation_id')->where(['and', ['provider_id' => $providier], ['model_validar' => $modVal], ['model_name' => $modName], ['provider_type' => $proType]])->all(), 'last_send_validation_id');

            if (!empty($ids)) {
                models\LastSendValidationRelation::updateAll(['last_send_validation_id' => null], ['in', 'last_send_validation_id', $ids]);
            }

            models\LastSendValidation::deleteAll(['and', ['provider_id' => $providier], ['model_validar' => $modVal], ['model_name' => $modName], ['provider_type' => $proType]]);

            $mod = new models\LastSendValidation();
            $mod->provider_id = $providier;
            $mod->model_validar = $modVal;
            $mod->model_name = $modName;
            $mod->provider_type = $proType;
            $mod->save();
        }

        return $mod->last_send_validation_id;

    }


    public function saveLastSendValidationRelation($lastSendId, $providier, $modName, $registerId, $proType)
    {


        if ($lastSendId != null) {

            $proType = $proType == 'status_bys' ? 'bys' : ($proType == 'status_op' ? 'op' : $proType);
            $mod = new models\LastSendValidationRelation();
            $mod->last_send_validation_id = $lastSendId;
            $mod->provider_id = $providier;
            $mod->model_name = $modName;
            $mod->register_id = $registerId;
            $mod->provider_type = $proType;
            $mod->save();
        }
    }

    public function updateLastSendValidation($provider_id, $modulo, $modelo, $provider_type, $status, $fecha_update)
    {
        $last_validation = LastSendValidation::find()->where(['and', ['provider_id' => $provider_id], ['model_name' => $modelo],
            ['model_validar' => $modulo], ['provider_type' => $provider_type]])->one();
        if ($last_validation == null) {
            $last_validation = new LastSendValidation();
            $last_validation->provider_id = $provider_id;
            $last_validation->model_validar = $modulo;
            $last_validation->model_name = $modelo;
            $last_validation->provider_type = $provider_type;
        }
        $last_validation->status = $status;
        $last_validation->created_at = $fecha_update;
        $last_validation->save();

        return $last_validation->last_send_validation_id;
    }

    public function updateLastSendValidationRelation($last_send_val_id, $provider_id, $modelo, $register_id, $provider_type, $status, $fecha_update)
    {
        if ($last_send_val_id != null) {
            $last_validation_rel = LastSendValidationRelation::find()->where(['and', ['last_send_validation_id' => $last_send_val_id],
                ["provider_id" => $provider_id], ["model_name" => $modelo], ["provider_type" => $provider_type]])->one();
            if ($last_validation_rel == null) {
                $last_validation_rel = new LastSendValidationRelation();
                $last_validation_rel->last_send_validation_id = $last_send_val_id;
                $last_validation_rel->provider_id = $provider_id;
                $last_validation_rel->model_name = $modelo;
                $last_validation_rel->provider_type = $provider_type;
            }
            $last_validation_rel->register_id = $register_id;
            $last_validation_rel->status = $status;
            $last_validation_rel->created_at = $fecha_update;
            $last_validation_rel->save();
        }
    }


    public function actionConsulta($rfc = null)
    {
        $limite = 15;
        if ($rfc != 'nulo') {
            $sql = " select  date_part( 'year',current_date) as \"Ejercicio\" ,m.cv_municipio as \"Clave del municipio\", m.nombre as \"Nombre del municipio o delegación\", e.entidad_id as \"Clave de la Entidad Federativa\" , e.nombre as \"Entidad Federativa.\", p.cp_fiscal as \"Código postal\", r.nombre as \"Nombre(s) del representante legal\", r.ap_paterno as \"Primer Apellido del representante legal\", r.ap_materno as \"Segundo Apellido del representante legal\", r.telefono as \"Teléfono de contacto representante legal\", r.correo as \"Correo electrónico representante legal\", r.tipo_poder as \"Tipo acreditación legal representante legal\", p.pagina_web as \"Página web del proveedor o contratista\", p.telfono as \"Teléfono oficial del proveedor o contratista\", p.email as \"Correo electrónico comercial\", 'http://proveedores-test.gob.mx/' as \"Hipervínculo Registro Proveedores Contratistas\", 'http://proveedores-test.gob.mx/site/sancionados' as \"Hipervínculo proveedores contratistas sancionados\", current_date as \"Fecha de validación\", 'Jefatura de Tramite y Control de Proveedores' as \"Área(s) responsable(s) de la información\", '' as \"Nota\", date_part( 'year',current_date) as \"Año\", '' as \"Fecha de actualización\", concat_ws (' ','Del', to_char(date_trunc('month', CURRENT_DATE),'DD/MM/YYYY'), 'al',to_char(date_trunc('month', CURRENT_DATE) + interval '1 month - 1 day','DD/MM/YYYY')) as \"Periodo que se informa\", case when p.tipo_persona = 'Persona_moral' then 'PERSONA_MORAL' else 'PERSONA_FISICA' end as \"Personería Jurídica del proveedor\", p.pf_nombre as \"Nombre(s) del proveedor o contratist\", p.pf_ap_paterno as \"Primer Apellido del proveedor o contratis\", p.pf_ap_materno as \"Segundo Apellido del proveedor o contrati\", p.name_razon_social as \"Denominación o Razón social\", p.estratificacion as \"Estratificación\", case when cp.pais_nombre = 'México' then 'Nacional' else 'Internacional' end as \"Origen del proveedor\", e.nombre as \"Entidad Federativa.\", cp.pais_nombre as \"País de origen\", p.rfc as \"RFC de la persona física o moral\",
            p.subcontrataciones as \"Realiza subcontrataciones\", '' as \"Giro de la empresa\", v.descripcion as \"Tipo de vialidad\",
            p.calle_fiscal as \"Nombre vialidad\", p.numero_fiscal as \"Número Exterior.\", p.interior_fiscal as \"Número interior en su caso\",
            c.nombre \"Tipo de asentamiento\", a.nombre as \"Nombre del asentamiento\", l.cv_localidad as \"Clave de la localidad\",
            l.nombre as \"Nombre de la localidad\" from public.provider p
            inner join provider.representante_legal r on r.provider_id=p.provider_id
            inner join cat_municipios m on m.municipio_id = p.city_fiscal
            inner join cat_asentamientos a on a.asentamiento_id = p.tipo_asentamiento
            inner join cat_localidades l on l.localidad_id = p.clave_localidad
            inner join cat_entidades e on e.entidad_id = m.entidad_id
            inner join cat_paises cp on cp.pais_id = p.pais_id
            inner join cat_vialidad v on  v.vialidad_id = p.tipo_vialidad
            inner join cat_tipo_asentamientos c on c.tipo_asentamiento_id = a.tipo_asentamiento_id
            where upper(p.rfc) LIKE :rfc";
            $query = models\Provider::findBySql($sql, [':rfc' => '%' . strtoupper($rfc) . '%'])->asArray()->all();

            if (count($query) == 0) {
                return json_encode(['Nota' => 'Su búsqueda no arroja resultado alguno. Intente nuevamente'], JSON_UNESCAPED_UNICODE);
            } else if (count($query) == $limite) {
                array_push($query, 'Su solicitud excede el número máximo de respuestas que es posible enviarle, pruebe acotando su consulta o trate de ser más específico.');
                return json_encode($query, JSON_UNESCAPED_UNICODE);
            } else {
                return json_encode($query, JSON_UNESCAPED_UNICODE);
            }
        } else {
            return json_encode(['Error' => 'Es necesario adjuntar al menos un filtro de busqueda. Intente nuevamente']);
        }
    }


    public function actionDescargarSeg()
    {
        $seg = models\TmpSeguimiento::find()->select('tramite_id')->all();


        $data_all_final = [];

        foreach ($seg as $v) {
            $dataPre = [
                'nombres' => '',
                'primer_apellido' => '',
                'segundo_apellido' => '',
                'mesa' => '',
                'lugar_fecha' => '',
                'dia_hora' => ''
            ];
            $dataAll = models\TmpSeguimiento::find()->where(['tramite_id' => $v->tramite_id])->all();

            foreach ($dataAll as $v) {
                if ($v->nombre == 'nombres') {

                    $nombre = $this->Utf8_ansi(json_decode($v->valor, true));
                    $dataPre['nombres'] = $nombre;

                } else if ($v->nombre == 'primer_apellido') {

                    $p1 = $this->Utf8_ansi(json_decode($v->valor, true));
                    $dataPre['primer_apellido'] = $p1;
                } else if ($v->nombre == 'segundo_apellido') {

                    $p2 = $this->Utf8_ansi(json_decode($v->valor, true));
                    $dataPre['segundo_apellido'] = $p2;

                } else if ($v->nombre == 'mesa') {
                    $m = $this->Utf8_ansi(json_decode($v->valor, true)['value']);
                    $dataPre['mesa'] = $m;

                } else if ($v->nombre == 'lugar_fecha') {
                    $lf = $this->Utf8_ansi(json_decode($v->valor, true));
                    $dataPre['lugar_fecha'] = $lf;

                } else if ($v->nombre == 'dia_hora') {
                    $dh = $this->Utf8_ansi(json_decode($v->valor, true)['value']);
                    $dataPre['dia_hora'] = $dh;
                }
            }

            array_push($data_all_final, $dataPre);
        }


        $columnsData = array_keys($data_all_final[0]);

        $headersData = array_combine($columnsData, $columnsData);

        foreach ($headersData as $index => $header) {
            $headersData[$index] = str_replace("_", " ", $header);
        }

        $now = date("Y-m-d");
        $fi_name = 'data_boletos' . "_" . $now . ".xlsx";
        Excel::widget([
            'models' => $data_all_final,
            'mode' => 'export',
            'fileName' => $fi_name,
            'columns' => $columnsData,
            'headers' => $headersData,
        ]);
        exit();
        return true;

    }


    public function actionDescargarIns()
    {

        $data_all_final = Yii::$app->db->createCommand("select nombres, primer_apellido, segundo_apellido, mesa,lugar_fecha, dia_hora,
        date_scanning as fecha_asistencia  from tmp_insumos where nombres is not null")->queryAll();

        $columnsData = array_keys($data_all_final[0]);

        $headersData = array_combine($columnsData, $columnsData);

        foreach ($headersData as $index => $header) {
            $headersData[$index] = str_replace("_", " ", $header);
        }

        $now = date("Y-m-d");
        $fi_name = 'data_boletos_insumos' . "_" . $now . ".xlsx";
        Excel::widget([
            'models' => $data_all_final,
            'mode' => 'export',
            'fileName' => $fi_name,
            'columns' => $columnsData,
            'headers' => $headersData,
        ]);
        exit();
        return true;


    }


    public function actionDescargarDataProv()
    {

        $proveedores = Yii::$app->db->createCommand("select p.provider_id,CASE WHEN (p.name_razon_social is not null and p.name_razon_social != '') then p.name_razon_social else concat_ws(' ',p.pf_nombre,p.pf_ap_paterno,p.pf_ap_materno) end as proveedor,
                                                        rf.rfc  from provider p
                                                        join provider.rfc rf on rf.provider_id = p.provider_id
                                                        where p.permanently_disabled in( 'EN PROCESO','INACTIVO') and  p.tipo_provider = 'bys' ")->queryAll();

        $data_all_final = [];
        foreach ($proveedores as $k) {

            $data = (array)$this->getDataBysRep($k['provider_id']);
            $prov = $k['proveedor'];
            $rfc = $k['rfc'];
            $arrBefore = ['Proveedor' => $prov,
                'rfc' => $rfc];

            $arAb = ['arriba', 'abajo'];

            for ($x = 0; $x < count($arAb); $x++) {
                foreach ($data[$arAb[$x]] as $v) {
                    $arrBefore[$v['title']['title']] = $v['terminado'] == 1 ? 'Sí / ' . $v['porcentaje'] : 'No / ' . $v['porcentaje'];
                }
            }

            $data_all_final[] = $arrBefore;
        }

        $columnsData = array_keys($data_all_final[0]);

        $headersData = array_combine($columnsData, $columnsData);

        foreach ($headersData as $index => $header) {
            $headersData[$index] = str_replace("_", " ", $header);
        }

        $now = date("Y-m-d");
        $fi_name = 'proveedores_modulos' . "_" . $now . ".xlsx";
        Excel::widget([
            'models' => $data_all_final,
            'mode' => 'export',
            'fileName' => $fi_name,
            'columns' => $columnsData,
            'headers' => $headersData,
        ]);
        exit();
        return true;


    }


    private function getDataBysRep($id = null)
    {
        if ($id != null) {
            if (($provider = \app\models\Provider::find()->where(['provider_id' => $id])->one()) !== null) {
                $provider_id = $provider->provider_id;
            } else {
                return $this->goHome();
            }
        } else {
            $provider = \app\models\Provider::find()->where(['user_id' => Yii::$app->user->getId()])->one();
            $provider_id = $provider->provider_id;
        }

        /* legales */
        $modelRfc = models\Rfc::find()->where(['provider_id' => $provider_id])->one();

        $status_global = 'status_bys';

        /*perfil*/

        $p_cl = $this->porcentajeTablaClientes('ClientesContratos', 'status_bys', $id);
        $modelDomicilio = ComprobanteDomicilio::find()->where(['provider_id' => $provider_id])->one();
        $statusPer = Perfil::find()->where(['provider_id' => $provider_id])->one();
        $p_perfilPre = $this->porcentajeModelo($modelDomicilio);
        $p_clientes_contratos = (intval($p_cl) + $p_perfilPre) / 2;
        $url_perfil = '/bys/perfil/index';
        $img_perfil = 'curriculum';

        /*actividad economica*/

        $terminadoE = 0;

        $modelGiro = Giro::find()->where(['and',['provider_id' => $provider_id, 'active' => true]])->one();

        $modelProductos = $this->findModelProviderGiroRep($provider_id);
        $modelProductosPorcentaje = isset($modelProductos[0]['producto_id']) && !empty($modelProductos[0]['producto_id']) ? 100 : 0;

        $status_datos_hacienda = AltaHacienda::find()->select($status_global)->where(['provider_id' => $provider_id])->one()[$status_global];

        $porRepLegalCarta = 100;


        /*if ($provider->tipo_persona == 'Persona moral') {
            $porRepLegal = $this->porcentajeTabla('RepresentanteLegal', 'status_bys',$id);
            $porRepLegalCarta = $this->porcentajeTablaCarta('RepresentanteLegal', 'status_bys',$id);

            $p_hacienda = ($modelGiro)?
                intval(($this->porcentajeModelo($modelGiro) + $porRepLegal) / 2) :($porRepLegal>0?$porRepLegal/2:0);
        } else {*/

        $pRfcProv = !empty($modelRfc->url_rfc) ? 100 : 0;

        $p_hacienda = ($modelGiro) ?
            (intval($this->porcentajeModelo($modelGiro)) + $pRfcProv + $modelProductosPorcentaje) / 3 :
            0;
        //}

        if ($status_datos_hacienda == Status::STATUS_VALIDADO/*&& $porRepLegalCarta == 100*/) {
            $terminadoE = 1;
        }


        $url_hacienda = "/bys/economica/view";


        /*legales*/


        $modelCurp = Curp::find()->where(['provider_id' => $provider_id])->one();
        $modelOficial = IdOficial::find()->where(['provider_id' => $provider_id])->one();


        if ($provider->tipo_persona == 'Persona moral') {
            $p_modA = $this->porcentajeTabla('ModificacionActa', 'status_bys', $id);
            $p_modActa = $p_modA === null ? 100 : $p_modA;

            $actaCons = ActaConstitutiva::find()->where(['provider_id' => $provider_id])->one();
            $p_relAcc = $this->porcentajeTabla('RelacionAccionistas', 'status_bys', $id);

            $porRepLegal = $this->porcentajeTabla('RepresentanteLegal', 'status_bys', $id);

            $p_legales = 0;

            // if ($modelDomicilio) {
            //   $pLegales1 = intval($this->porcentajeModelo($modelDomicilio));
            $pactaConst = intval($this->porcentajeModelo($actaCons));
            $p_legales = (intval($p_modActa) + intval($porRepLegal) + intval($p_relAcc) + $pactaConst) / 4;
            //}

            $porMActa = $this->porcentajeTablaCarta('ModificacionActa', 'status_bys', $id);
            $porRelAc = $this->porcentajeTablaCarta('RelacionAccionistas', 'status_bys', $id);
            $porRepLegalCarta = $this->porcentajeTablaCarta('RepresentanteLegal', 'status_bys', $id);
        } else {
            $p_legales = ($modelCurp && $modelOficial) ?
                intval(($this->porcentajeModelo($modelCurp)
                        + $this->porcentajeModelo($modelOficial)) / 2) :
                0;

            $porMActa = 100;
            $porRelAc = 100;
            $porRepLegalCarta = 100;
        }

        $url_legales = "/bys/legales/view";
        $terminadoLegales = 0;

        if ($modelRfc->$status_global == Status::STATUS_VALIDADO && ($porMActa === null || $porMActa == 100) && $porRelAc == 100 & $porRepLegalCarta >= 100) {
            $terminadoLegales = 1;
        }


        /*Exdesmadre Intervencion*/

        $url_intervencion = "/banco/view";
        $modelIntervencion = IntervencionBancaria::find()->where(['provider_id' => $provider_id])->one();
        $p_intervencion = ($modelIntervencion) ? $this->porcentajeModelo($modelIntervencion) : 10;
        $terminadoMetodoPago = 0;
        if ($modelIntervencion->$status_global == 'VALIDADO') {
            $terminadoMetodoPago = 1;
        }


        $visibleIconosAbajo = 0;
        $ubicacionT = 0;
        $fotografiaT = 0;
        $financierosT = 0;
        $credencialesT = 0;
        $titleUbicacion = ['title' => 'Ubicación'];
        $titleFotografia = ['title' => 'Fotografía'];
        $titleCredenciales = ['title' => 'Permisos-Certificados'];
        $titleFiancieros = ['title' => 'Financieros'];
        $titlePerfil = ['title' => 'Mi perfil'];
        $visibleIconosAbajo = 1;

        /*ubicacion*/

        $p_ub = $this->porcentajeTabla('Ubicacion', 'status_bys', $id);
        $p_ubEnabled = $this->porcentajeTablaCarta('Ubicacion', 'status_bys', $id);

        $entidad = Ubicacion::find()->select('state_fiscal')->where(['and', ['provider_id' => $provider_id], ['type_address_prov' => 'DOMICILIO FISCAL']])->orderBy(['ubicacion_id' => SORT_DESC])->one()['state_fiscal'];
        $porcentrajeDirNL = 100;
        $statusDirNl = 'VALIDADO';
        $dirNl = false;
        if (isset($entidad) && $entidad != 19 || $entidad == null) {
            $dirNl = true;
            $modelDirNL = Ubicacion::find()->where(['and', ['provider_id' => $provider_id], ['type_address_prov' => 'NL']])->orderBy(['ubicacion_id' => SORT_DESC])->one();
            $statusDirNl = 'EN EDICION';
            $porcentrajeDirNL = 0;
            if ($modelDirNL != null) {
                $statusDirNl = $modelDirNL->status_bys;

                $porcentrajeDirNL = $this->porcentajeModelo($modelDirNL);
            }


        }

        $p_fo = $this->porcentajeTabla('FotografiaNegocio', 'status_bys', $id);
        $p_foEnabled = $this->porcentajeTablaCarta('FotografiaNegocio', 'status_bys', $id);


        $p_ub = $p_ub === null && $p_fo >= 100 ? 100 : $p_ub;
        $p_ubicacion = $p_ub === null && !$dirNl ? 0 : ($dirNl ? ($porcentrajeDirNL + $p_ub) / 2 : $p_ub);

        $ubicacionT = (((intval($p_ubEnabled) >= 100 || $p_ubEnabled === null) && $p_foEnabled >= 100) && $statusDirNl == 'VALIDADO') ? 1 : 0;
        $url_ubicacion = '/bys/ubicacion/index';
        $img_ubicacion = 'datos_propiedad';

        /*Fotografia*/


        $p_fotografia = $p_fo == null ? 0 : $p_fo;
        $fotografiaT = (intval($p_foEnabled) == 100) ? 1 : 0;
        $url_fotografia = '/bys/fotografia/index';
        $img_fotografia = 'fotografia';


        /*Credenciales*/
        $p_cer = $this->porcentajeTabla('Certificacion', 'status_bys', $id);
        $p_cerEnabled = $this->porcentajeTablaCarta('Certificacion', 'status_bys', $id);

        $p_certificacion = $p_cer == null ? 0 : $p_cer;
        $credencialesT = (intval($p_cerEnabled) == 100) ? 1 : 0;
        $url_credenciales = '/bys/credenciales/index';
        $img_credenciales = 'certificacion';


        /*Financieros*/

        $modelUltimaDeclaracion = UltimaDeclaracion::find()->where(['provider_id' => $provider->provider_id])->one();
        if ($modelUltimaDeclaracion == null) {
            $modelUltimaDeclaracion = new UltimaDeclaracion();
            $modelUltimaDeclaracion->provider_id = $provider->provider_id;
            $modelUltimaDeclaracion->save();
        }
        $p_ultima_declaracion = $this->porcentajeModelo($modelUltimaDeclaracion);
        $url_ultima_declaracion = ($p_ultima_declaracion > 0) ? '/bys/experiencia/view' : '/bys/experiencia/update';

        if ($modelUltimaDeclaracion->$status_global == Status::STATUS_VALIDADO) {
            $financierosT = 1;
        }
        $img_financieros = 'financieros';

        /* Perfil*/


        /*Desmadre carta*/
        $p_carta = 0;
        $url_carta = null;
        $terminadoCarta = 0;
        $img_carta = 'acta_const_bn';
        $sombra_carta = '';
        $visible_carta = 0;
        $titulo_target = ['title' => 'Carta protesta'];

        $p_clCarta = $this->porcentajeTablaClientesCarta('ClientesContratos', 'status_bys', $id);
        $p_clientes_contratosCarta = $p_clCarta == null ? 0 : $p_clCarta;
        $perfilT = (intval($p_clientes_contratosCarta) >= 100) && $statusPer->$status_global == Status::STATUS_VALIDADO ? 1 : 0;

        if ($perfilT == 1 && $terminadoLegales == 1 && $terminadoE == 1 && $ubicacionT == 1 && $fotografiaT == 1 && $credencialesT == 1 && $financierosT == 1 && $terminadoMetodoPago) {
            $modelProvider = Provider::find()->where(['provider_id' => $provider_id])->one();
            switch ($modelProvider->status_carta_bys) {
                case Status::CARTA_PENDIENTE :
                    $url_carta = '/carta/generacartaprotesta';
                    $titulo_target = ['title' => 'Carta protesta'];
                    $terminadoCarta = 0;
                    $img_carta = 'acta_const';
                    $sombra_carta = 'sombra_aqua';
                    $visible_carta = 1;
                    break;
                case Status::CARTA_GENERADA:
                    $titulo_target = ['title' => 'Carta protesta'];
                    $url_carta = '/carta/generacartaprotesta';
                    $img_carta = 'acta_const';
                    $terminadoCarta = 0;
                    $sombra_carta = 'sombra_aqua';
                    $visible_carta = 1;
                    break;
                case Status::CARTA_FIRMADA :
                    $url_carta = \yii\helpers\Url::to('@web/' . GeneralController::limpiarUrl($modelProvider->url_carta_bys) , true);
                    $p_carta = 100;
                    $terminadoCarta = 1;
                    $img_carta = 'acta_const';
                    $sombra_carta = 'sombra_aqua';
                    $titulo_target = ['title' => 'Carta protesta', 'target' => '_blank'];
                    $visible_carta = 1;
                    break;

            }
        }


        $data = (Object)[
            'arriba' => [
                [
                    'porcentaje' => $p_clientes_contratos,
                    'icono' => $img_perfil,
                    'terminado' => $perfilT,
                    'visible' => 1,
                    'url' => $url_perfil,
                    'title' => $titlePerfil,
                    'sombra' => 'sombra_verde'
                ],
                [
                    'porcentaje' => intval($p_hacienda),
                    'icono' => 'datos_hacienda',
                    'terminado' => $terminadoE,
                    'visible' => 1,
                    'url' => $url_hacienda,
                    'title' => ['title' => 'Actividad Económica'],
                    'sombra' => 'sombra_roja'
                ],
                [
                    'porcentaje' => $p_legales,
                    'icono' => 'legales',
                    'terminado' => $terminadoLegales,
                    'visible' => 1,
                    'url' => $url_legales,
                    'title' => ['title' => 'Legales'],
                    'sombra' => 'sombra_azul'
                ],
                [
                    'porcentaje' => $p_ubicacion,
                    'icono' => $img_ubicacion,
                    'terminado' => $ubicacionT,
                    'visible' => 1,
                    'url' => $url_ubicacion,
                    'title' => $titleUbicacion,
                    'sombra' => 'sombra_naranja'
                ],
                [
                    'porcentaje' => $p_fotografia,
                    'icono' => $img_fotografia,
                    'terminado' => $fotografiaT,
                    'visible' => 1,
                    'url' => $url_fotografia,
                    'title' => $titleFotografia,
                    'sombra' => 'sombra_arena'

                ],

            ],
            'abajo' => [

                [
                    'porcentaje' => $p_ultima_declaracion,
                    'icono' => $img_financieros,
                    'terminado' => $financierosT,
                    'visible' => 1,
                    'url' => $url_ultima_declaracion,
                    'title' => $titleFiancieros,
                    'sombra' => 'sombra_rojo_claro'

                ],
                [
                    'porcentaje' => $p_certificacion,
                    'icono' => $img_credenciales,
                    'terminado' => $credencialesT,
                    'visible' => 1,
                    'url' => $url_credenciales,
                    'title' => $titleCredenciales,
                    'sombra' => 'sombra_celeste'
                ],

                [
                    'porcentaje' => $p_intervencion,
                    'icono' => 'metodo_pago',
                    'terminado' => $terminadoMetodoPago,
                    'visible' => 1,
                    'url' => $url_intervencion,
                    'title' => ['title' => 'Metodo de pago'],
                    'sombra' => 'sombra_amarilla'
                ],
                [
                    'porcentaje' => $p_carta,
                    'icono' => $img_carta,
                    'terminado' => $terminadoCarta,
                    'visible' => $visible_carta,
                    'url' => $url_carta,
                    'title' => $titulo_target,
                    'sombra' => $sombra_carta
                ],
            ]
        ];

        return $data;
    }


    public function findModelProviderGiroRep($id)
    {

        $model = ProviderGiro::find()
            ->select("p.concepto_grupo_id as grupo")
            ->addSelect(new \yii\db\Expression("string_agg(provider_giro.producto_id::text,',') as producto_id"))
            ->from('provider_giro')
            ->innerJoin('productos_servicios.concepto_linea p', 'p.concepto_linea_id = provider_giro.producto_id')
            ->groupBy("concepto_grupo_id")->where(['provider_id' => $id])->asArray()->all();

        if ($model == null || empty($model)) {
            $model = [new ProviderGiro()];
        }
        return $model;
    }

    public static function Utf8_ansi($valor = '')
    {

        $utf8_ansi2 = array(
            "\u00c0" => "À",
            "\u00c1" => "Á",
            "\u00c2" => "Â",
            "\u00c3" => "Ã",
            "\u00c4" => "Ä",
            "\u00c5" => "Å",
            "\u00c6" => "Æ",
            "\u00c7" => "Ç",
            "\u00c8" => "È",
            "\u00c9" => "É",
            "\u00ca" => "Ê",
            "\u00cb" => "Ë",
            "\u00cc" => "Ì",
            "\u00cd" => "Í",
            "\u00ce" => "Î",
            "\u00cf" => "Ï",
            "\u00d1" => "Ñ",
            "\u00d2" => "Ò",
            "\u00d3" => "Ó",
            "\u00d4" => "Ô",
            "\u00d5" => "Õ",
            "\u00d6" => "Ö",
            "\u00d8" => "Ø",
            "\u00d9" => "Ù",
            "\u00da" => "Ú",
            "\u00db" => "Û",
            "\u00dc" => "Ü",
            "\u00dd" => "Ý",
            "\u00df" => "ß",
            "\u00e0" => "à",
            "\u00e1" => "á",
            "\u00e2" => "â",
            "\u00e3" => "ã",
            "\u00e4" => "ä",
            "\u00e5" => "å",
            "\u00e6" => "æ",
            "\u00e7" => "ç",
            "\u00e8" => "è",
            "\u00e9" => "é",
            "\u00ea" => "ê",
            "\u00eb" => "ë",
            "\u00ec" => "ì",
            "\u00ed" => "í",
            "\u00ee" => "î",
            "\u00ef" => "ï",
            "\u00f0" => "ð",
            "\u00f1" => "ñ",
            "\u00f2" => "ò",
            "\u00f3" => "ó",
            "\u00f4" => "ô",
            "\u00f5" => "õ",
            "\u00f6" => "ö",
            "\u00f8" => "ø",
            "\u00f9" => "ù",
            "\u00fa" => "ú",
            "\u00fb" => "û",
            "\u00fc" => "ü",
            "\u00fd" => "ý",
            "\u00ff" => "ÿ");

        return strtr($valor, $utf8_ansi2);

    }

    public function actionDescarga($fi = null, $ff = null, $type = null)
    {

        if ($fi != null && $ff != null && $type != null) {
            $file = './tmp/concentrado.csv';
            if (!file_exists('./tmp')) {
                mkdir('./tmp');
            }

            $now = date('Y-m-d');

            if (\Yii::$app->request->isGet) {
                $data_all_final = models\Provider::findBySql(" 
                select  distinct
                date_part( 'year','$fi'::date) as \"Ejercicio\",
                to_char(date_trunc('month', '$fi'::date),'DD/MM/YYYY') as \"Fecha de inicio del periodo que se informa\",
                to_char((date_trunc('month', '$fi'::date)+INTERVAL '1 month')- interval '1 day','DD/MM/YYYY') as \"Fecha de término del periodo que se informa\",
                p.tipo_persona as \"Personería Jurídica del proveedor o contratista(catálogo) \",
                CASE when p.pf_nombre !='' then p.pf_nombre else 'NO DATO' end  as \"Nombre(s) del proveedor o contratista\",
                CASE when p.pf_ap_paterno !='' then p.pf_ap_paterno else 'NO DATO' end as \"Primer apellido del proveedor o contratista\",
                CASE when p.pf_ap_materno !='' then p.pf_ap_materno else 'NO DATO' end as \"Segundo apellido del proveedor o contratista\",
                CASE when p.name_razon_social !='' then p.name_razon_social else 'NO DATO' end as \"Denominación o razón social del proveedor o contratista\",
                p.estratificacion as \"Estratificación\",
                case when cp.pais_nombre = 'México' then 'Nacional' else 'Internacional' end as \"Origen del proveedor o contratista(catálogo)\",
                cp.pais_nombre as \"País de origen, si la empresa es una filial extranjera\",
                p.rfc as \"RFC de la persona física o moral con homoclave incluida\",
                e.name_siet as \"Entidad federativa de la persona física o moral(catálogo)\",
                p.subcontrataciones as \"Realiza subcontrataciones(catálogo)\",
                STRING_AGG(distinct sec.nombre_sector,',')as \"Actividad económica de la empresa\",
                v.name_siet as \"Domicilio fiscal Tipo de vialidad(catálogo)\",
                ub.calle_fiscal as \"Domicilio fiscal Nombre de la vialidad\",
                ub.num_ext_fiscal as \"Domicilio fiscal Número exterior\",
                ub.num_int_fiscal as \"Domicilio fiscal Número interior, en su caso\",
                c.name_siet \"Domicilio fiscal Tipo de asentamiento(catálogo) \",
                a.nombre as \"Domicilio fiscal Nombre del asentamiento\",
                l.cv_localidad::integer as \"Domicilio fiscal Clave de la localidad\",
                l.nombre as \"Domicilio fiscal Nombre de la localidad\",
                m.cv_municipio::integer as \"Domicilio fiscal Clave del municipio\",
                m.nombre as \"Domicilio fiscal Nombre del municipio o delegación\",
                e.entidad_id as \"Domicilio fiscal Clave de la Entidad Federativa\" , 
                e.name_siet as \"Domicilio fiscal Entidad Federativa(catálogo) \" , 
                ub.cp_fiscal as \"Domicilio fiscal Código postal\",
                'NO DATO' as \"País del domicilio en el extranjero, en su caso\",
                'NO DATO' as \"Ciudad del domicilio en el extranjero, en su caso\",
                'NO DATO' as \"Calle del domicilio en el extranjero, en su caso\",
                'NO DATO' as \"Número del domicilio en el extranjero, en su caso\",
                r.nombre as \"Nombre(s) del representante legal de la empresa\",
                r.ap_paterno as \"Primer apellido del representante legal de la empresa\", 
                r.ap_materno as \"Segundo apellido del representante legal de la empresa\",
                r.telefono as \"Teléfono de contacto representante legal de la empresa\",
                r.correo as \"Correo electrónico representante legal, en su caso\",
                CASE when r.tipo_poder ='PODER' then 'PODER' when r.tipo_poder !='' then 'INE' else '' end as \"Tipo de acreditación legal representante legal\", 
                CASE when p.pagina_web !='' and p.protocolo !='' then concat_ws('',p.protocolo,p.pagina_web) when p.pagina_web !='' then concat_ws('','http://',p.pagina_web) else '' end as \"Página web del proveedor o contratista\",
                p.telfono as \"Teléfono oficial del proveedor o contratista\",
                p.email as \"Correo electrónico comercial del proveedor o contratista\",
                'https://proveedores.nl.gob.mx/' as \"Hipervínculo Registro Proveedores Contratistas, en su caso\", 
                'https://proveedores.nl.gob.mx/site/sancionados' as \"Hipervínculo al Directorio de Proveedores y Contratistas Sancionados\",
                'Dirección de Administración' as \"Área(s) responsable(s) que genera(n), posee(n), publica(n) y actualizan la información\", 
                to_char((date_trunc('month', '$fi'::date)+INTERVAL '1 month')- interval '1 day','DD/MM/YYYY') as \"Fecha de validación\",
                to_char((date_trunc('month', '$fi'::date)+INTERVAL '1 month')- interval '1 day','DD/MM/YYYY') as \"Fecha de actualización\",
                '' as \"Nota\",
                '' as \"Emisión Certificado\",
                vis.visit_date
                from public.provider p 
                left join provider.representante_legal r on r.provider_id=p.provider_id and rep_$type is TRUE 
                left join provider.ubicacion ub on ub.provider_id = p.provider_id
                left join cat_municipios m on m.municipio_id = ub.city_fiscal
                left join cat_asentamientos a on a.asentamiento_id = ub.colonia_fiscal 
                left join cat_localidades l on l.localidad_id = ub.localidad_id 
                left join cat_entidades e on e.entidad_id = m.entidad_id
                left join cat_paises cp on cp.pais_id = p.pais_id 
                left join cat_vialidad v on v.vialidad_id = ub.vialidad_id 
                left join cat_tipo_asentamientos c on c.tipo_asentamiento_id = a.tipo_asentamiento_id 
                left join provider.giro gi on gi.provider_id = p.provider_id
                left join provider.cat_actividades cata on cata.actividad_id=gi.actividad_id
                left join provider.cat_ramas ram on ram.rama_id = cata.rama_id
                left join provider.cat_sectores sec on sec.sector_id = ram.sector_id
                left join(                
                    with ct as
                    (select distinct provider_id, created_at, row_number() over (partition by provider_id order by created_at desc) rn
                    from
                    visit where provider_type = '$type' and status is true and modify is true order by provider_id)
                    select
                    provider_id, created_at::date as visit_date
                    from
                    ct
                    where
                    rn = 1)vis on vis.provider_id = p.provider_id               
                where ub.type_address_prov = 'DOMICILIO FISCAL' and p.creation_date::date between '$fi' and '$ff'
                and p.provider_id in(select provider_id from provider.datos_validados where status_op = 'VALIDADO' and created_date::date>= '$fi')
                and p.provider_id not in(select provider_id from historico_certificados where created_at::date between '$fi' and '$ff' and tipo = 'CERTIFICADO' AND provider_type = 'op')
                group by ub.calle_fiscal,cv_municipio,m.nombre,e.entidad_id,p.cp_fiscal,r.nombre,r.ap_paterno,r.ap_materno,r.telefono,r.correo,
                r.tipo_poder,p.protocolo,p.pagina_web,p.telfono,p.email,p.tipo_persona,p.pf_nombre,p.pf_ap_paterno,p.pf_ap_materno,p.name_razon_social,
                p.estratificacion,cp.pais_nombre,p.rfc,p.subcontrataciones,v.name_siet,ub.num_ext_fiscal,ub.num_int_fiscal,ub.cp_fiscal,
                c.name_siet,a.nombre,l.cv_localidad,l.nombre,e.name_siet,vis.visit_date
                
                union all
 
 
 
                select  distinct
                date_part( 'year','$fi'::date) as \"Ejercicio\",
                to_char(date_trunc('month', '$fi'::date),'DD/MM/YYYY') as \"Fecha de inicio del periodo que se informa\",
                to_char((date_trunc('month', '$fi'::date)+INTERVAL '1 month')- interval '1 day','DD/MM/YYYY') as \"Fecha de término del periodo que se informa\",
                p.tipo_persona as \"Personería Jurídica del proveedor o contratista (catálogo) \",
                CASE when p.pf_nombre !='' then p.pf_nombre else 'NO DATO' end  as \"Nombre(s) del proveedor o contratista\",
                CASE when p.pf_ap_paterno !='' then p.pf_ap_paterno else 'NO DATO' end as \"Primer apellido del proveedor o contratista\",
                CASE when p.pf_ap_materno !='' then p.pf_ap_materno else 'NO DATO' end as \"Segundo apellido del proveedor o contratista\",
                CASE when p.name_razon_social !='' then p.name_razon_social else 'NO DATO' end as \"Denominación o razón social del proveedor o contratista\",
                p.estratificacion as \"Estratificación\",
                case when cp.pais_nombre = 'México' then 'Nacional' else 'Internacional' end as \"Origen del proveedor o contratista (catálogo)\",
                cp.pais_nombre as \"País de origen, si la empresa es una filial extranjera\",
                p.rfc as \"RFC de la persona física o moral con homoclave incluida\",
                e.name_siet as \"Entidad federativa de la persona física o moral (catálogo)\",
                p.subcontrataciones as \"Realiza subcontrataciones (catálogo)\",
                STRING_AGG(distinct sec.nombre_sector,',')as \"Actividad económica de la empresa\",
                v.name_siet as \"Domicilio fiscal Tipo de vialidad (catálogo)\",
                ub.calle_fiscal as \"Domicilio fiscal Nombre de la vialidad\",
                ub.num_ext_fiscal as \"Domicilio fiscal Número exterior\",
                ub.num_int_fiscal as \"Domicilio fiscal Número interior, en su caso\",
                c.name_siet \"Domicilio fiscal Tipo de asentamiento (catálogo) \",
                a.nombre as \"Domicilio fiscal Nombre del asentamiento\",
                l.cv_localidad::integer as \"Domicilio fiscal Clave de la localidad\",
                l.nombre as \"Domicilio fiscal Nombre de la localidad\",
                m.cv_municipio::integer as \"Domicilio fiscal Clave del municipio\",
                m.nombre as \"Domicilio fiscal Nombre del municipio o delegación\",
                e.entidad_id as \"Domicilio fiscal Clave de la Entidad Federativa\" , 
                e.name_siet as \"Domicilio fiscal Entidad Federativa (catálogo) \" , 
                ub.cp_fiscal as \"Domicilio fiscal Código postal\",
                'NO DATO' as \"País del domicilio en el extranjero, en su caso\",
                'NO DATO' as \"Ciudad del domicilio en el extranjero, en su caso\",
                'NO DATO' as \"Calle del domicilio en el extranjero, en su caso\",
                'NO DATO' as \"Número del domicilio en el extranjero, en su caso\",
                r.nombre as \"Nombre(s) del representante legal de la empresa\",
                r.ap_paterno as \"Primer apellido del representante legal de la empresa\", 
                r.ap_materno as \"Segundo apellido del representante legal de la empresa\",
                r.telefono as \"Teléfono de contacto representante legal de la empresa\",
                r.correo as \"Correo electrónico representante legal, en su caso\",
                CASE when r.tipo_poder ='PODER' then 'PODER' when r.tipo_poder !='' then 'INE' else '' end as \"Tipo de acreditación legal representante legal\", 
                CASE when p.pagina_web !='' and p.protocolo !='' then concat_ws('',p.protocolo,p.pagina_web) when p.pagina_web !='' then concat_ws('','http://',p.pagina_web) else '' end as \"Página web del proveedor o contratista\",
                p.telfono as \"Teléfono oficial del proveedor o contratista\",
                p.email as \"Correo electrónico comercial del proveedor o contratista\",
                'https://proveedores.nl.gob.mx/' as \"Hipervínculo Registro Proveedores Contratistas, en su caso\", 
                'https://proveedores.nl.gob.mx/site/sancionados' as \"Hipervínculo al Directorio de Proveedores y Contratistas Sancionados\",
                'Dirección de Administración' as \"Área(s) responsable(s) que genera(n), posee(n), publica(n) y actualizan la información\", 
                to_char((date_trunc('month', '$fi'::date)+INTERVAL '1 month')- interval '1 day','DD/MM/YYYY') as \"Fecha de validación\",
                to_char((date_trunc('month', '$fi'::date)+INTERVAL '1 month')- interval '1 day','DD/MM/YYYY') as \"Fecha de actualización\",
                '' as \"Nota\",
                hc.created_at::date::text as \"Emisión Certificado\",
                vis.visit_date
                from public.provider p 
                left join provider.representante_legal r on r.provider_id=p.provider_id and rep_$type is TRUE 
                left join provider.ubicacion ub on ub.provider_id = p.provider_id
                left join cat_municipios m on m.municipio_id = ub.city_fiscal
                left join cat_asentamientos a on a.asentamiento_id = ub.colonia_fiscal 
                left join cat_localidades l on l.localidad_id = ub.localidad_id 
                left join cat_entidades e on e.entidad_id = m.entidad_id
                left join cat_paises cp on cp.pais_id = p.pais_id 
                left join cat_vialidad v on v.vialidad_id = ub.vialidad_id 
                left join cat_tipo_asentamientos c on c.tipo_asentamiento_id = a.tipo_asentamiento_id 
                left join historico_certificados hc on hc.provider_id = p.provider_id             
                left join provider.giro gi on gi.provider_id = p.provider_id
                left join provider.cat_actividades cata on cata.actividad_id=gi.actividad_id
                left join provider.cat_ramas ram on ram.rama_id = cata.rama_id
                left join provider.cat_sectores sec on sec.sector_id = ram.sector_id
                left join(                
                    with ct as
                    (select distinct provider_id, created_at, row_number() over (partition by provider_id order by created_at desc) rn
                    from
                    visit where provider_type = '$type' and status is true and modify is true order by provider_id)
                    select
                    provider_id, created_at::date as visit_date
                    from
                    ct
                    where
                    rn = 1)vis on vis.provider_id = p.provider_id
                where ub.type_address_prov = 'DOMICILIO FISCAL' and  hc.tipo = 'CERTIFICADO' and hc.provider_type= '$type'
                and hc.created_at::date between '$fi' and '$ff'
                group by ub.calle_fiscal,cv_municipio,m.nombre,e.entidad_id,p.cp_fiscal,r.nombre,r.ap_paterno,r.ap_materno,r.telefono,r.correo,
                r.tipo_poder,p.protocolo,p.pagina_web,p.telfono,p.email,p.tipo_persona,p.pf_nombre,p.pf_ap_paterno,p.pf_ap_materno,p.name_razon_social,
                p.estratificacion,cp.pais_nombre,p.rfc,p.subcontrataciones,v.name_siet,ub.num_ext_fiscal,ub.num_int_fiscal,ub.cp_fiscal,
                c.name_siet,a.nombre,l.cv_localidad,l.nombre,e.name_siet,hc.created_at ,vis.visit_date     
                ")->asArray()->all();

                $columnsData = array_keys($data_all_final[0]);

                $headersData = array_combine($columnsData, $columnsData);

                foreach ($headersData as $index => $header) {
                    $headersData[$index] = str_replace("_", " ", $header);
                }

                $now = date("Y-m-d");
                $fi_name = 'data_providers' . "_" . $now . ".xlsx";
                Excel::widget([
                    'models' => $data_all_final,
                    'mode' => 'export',
                    'fileName' => $fi_name,
                    'columns' => $columnsData,
                    'headers' => $headersData,
                ]);
                exit();
                return true;
            } else {
                return $this->disabled();
            }
        }

        return $this->goHome();
    }


    public function actionDescargaFull()
    {

        $file = './tmp/concentrado.csv';
        if (!file_exists('./tmp')) {
            mkdir('./tmp');
        }

        if (\Yii::$app->request->isGet) {
            $data_all_F = models\Provider::findBySql(" select  p.provider_id,
                        p.tipo_persona as \"Personería Jurídica del proveedor o contratista\",
                        CASE when p.pf_nombre !='' then p.pf_nombre else '' end  as \"Nombre(s) del proveedor\",
                        CASE when p.pf_ap_paterno !='' then p.pf_ap_paterno else '' end as \"Primer apellido del proveedor\",
                        CASE when p.pf_ap_materno !='' then p.pf_ap_materno else '' end as \"Segundo apellido del proveedor\",
                        CASE when p.name_razon_social !='' then p.name_razon_social else '' end as \"Denominación o razón social del proveedor\",
                        p.estratificacion as \"Estratificación\",
                        case when cp.pais_nombre = 'México' then 'Nacional' else 'Internacional' end as \"Origen del proveedor\",
                        cp.pais_nombre as \"País de origen, si la empresa es una filial extranjera\",
                        p.rfc as \"RFC de la persona física o moral con homoclave incluida\",
                        e.name_siet as \"Entidad federativa de la persona física o moral\",
                        p.subcontrataciones as \"Realiza subcontrataciones\",
                        STRING_AGG(distinct sec.nombre_sector,',')as \"Actividad económica de la empresa\",
                        v.name_siet as \"Domicilio fiscal Tipo de vialidad\",
                        ub.calle_fiscal as \"Domicilio fiscal Nombre de la vialidad\",
                        ub.num_ext_fiscal as \"Domicilio fiscal Número exterior\",
                        ub.num_int_fiscal as \"Domicilio fiscal Número interior, en su caso\",
                        c.name_siet \"Domicilio fiscal Tipo de asentamiento\",
                        a.nombre as \"Domicilio fiscal Nombre del asentamiento\",
                        l.cv_localidad::integer as \"Domicilio fiscal Clave de la localidad\",
                        l.nombre as \"Domicilio fiscal Nombre de la localidad\",
                        m.cv_municipio::integer as \"Domicilio fiscal Clave del municipio\",
                        m.nombre as \"Domicilio fiscal Nombre del municipio o delegación\",
                        e.entidad_id as \"Domicilio fiscal Clave de la Entidad Federativa\" ,
                        e.name_siet as \"Domicilio fiscal Entidad Federativa\" ,
                        ub.cp_fiscal as \"Domicilio fiscal Código postal\",
                        r.nombre as \"Nombre(s) del representante legal de la empresa\",
                        r.ap_paterno as \"Primer apellido del representante legal de la empresa\",
                        r.ap_materno as \"Segundo apellido del representante legal de la empresa\",
                        r.telefono as \"Teléfono de contacto representante legal de la empresa\",
                        r.correo as \"Correo electrónico representante legal, en su caso\",
                        CASE when r.tipo_poder ='PODER' then 'PODER' when r.tipo_poder !='' then 'INE' else '' end as \"Tipo de acreditación legal representante legal\",
                        CASE when p.pagina_web !='' and p.protocolo !='' then concat_ws('',p.protocolo,p.pagina_web) when p.pagina_web !='' then concat_ws('','http://',p.pagina_web) else '' end as \"Página web del proveedor\",
                        p.telfono as \"Teléfono oficial del proveedor\",
                        p.email as \"Correo electrónico comercial del proveedor\",
                        ib.nombre_titular_cuenta as \"Nombre titular de la cuneta\",
                        b.banco as \"Banco\",
                        ib.cuenta_clave as \"Cuenta\",
                        '' as representantes,
                        '' as modificaciones,
                        '' as accionistas
                        from public.provider p
                        left join provider.intervencion_bancaria ib on ib.provider_id = p.provider_id
                        left join bancos b on b.id_banco = ib.banco
                        left join provider.representante_legal r on r.provider_id=p.provider_id and rep_bys is TRUE
                        left join provider.ubicacion ub on ub.provider_id = p.provider_id
                        left join cat_municipios m on m.municipio_id = ub.city_fiscal
                        left join cat_asentamientos a on a.asentamiento_id = ub.colonia_fiscal
                        left join cat_localidades l on l.localidad_id = ub.localidad_id
                        left join cat_entidades e on e.entidad_id = m.entidad_id
                        left join cat_paises cp on cp.pais_id = p.pais_id
                        left join cat_vialidad v on v.vialidad_id = ub.vialidad_id
                        left join cat_tipo_asentamientos c on c.tipo_asentamiento_id = a.tipo_asentamiento_id
                        left join historico_certificados hc on hc.provider_id = p.provider_id
                        left join provider.giro gi on gi.provider_id = p.provider_id
                        left join provider.cat_actividades cata on cata.actividad_id=gi.actividad_id
                        left join provider.cat_ramas ram on ram.rama_id = cata.rama_id
                        left join provider.cat_sectores sec on sec.sector_id = ram.sector_id
                        where ub.type_address_prov = 'DOMICILIO FISCAL' and  hc.tipo = 'CERTIFICADO' and hc.provider_type= 'bys'
                        group by p.provider_id,ib.nombre_titular_cuenta,b.banco,ib.cuenta_clave, ub.calle_fiscal,cv_municipio,m.nombre,e.entidad_id,p.cp_fiscal,r.nombre,r.ap_paterno,r.ap_materno,r.telefono,r.correo,
                        r.tipo_poder,p.protocolo,p.pagina_web,p.telfono,p.email,p.tipo_persona,p.pf_nombre,p.pf_ap_paterno,p.pf_ap_materno,p.name_razon_social,
                        p.estratificacion,cp.pais_nombre,p.rfc,p.subcontrataciones,v.name_siet,ub.num_ext_fiscal,ub.num_int_fiscal,ub.cp_fiscal,
                        c.name_siet,a.nombre,l.cv_localidad,l.nombre,e.name_siet")->asArray()->all();

            $data_all_final = [];
            foreach ($data_all_F as $v) {

                $dataRep = Yii::$app->db->createCommand("select concat_ws(' ',nombre,ap_paterno,ap_materno) as \"Nombre\", tipo_poder as \"Tipo poder\" from provider.representante_legal where provider_id = :id and status_bys = 'VALIDADO' and activo is true", [':id' => $v['provider_id']])->queryAll();
                $dataMod = Yii::$app->db->createCommand("select nombre_documento as \"Modificación\", descripcion_cambios as \"Cambios\" from provider.modificacion_acta where provider_id = :id and status_bys = 'VALIDADO' and activo is true", [':id' => $v['provider_id']])->queryAll();
                $dataAcc = Yii::$app->db->createCommand("select concat_ws(' ',nombre,ap_paterno,ap_materno) as \"Nombre\", tipo_relacion as \"Relación\" from provider.relacion_accionistas where provider_id = :id and status_bys = 'VALIDADO' and activo is true", [':id' => $v['provider_id']])->queryAll();

                $v['representantes'] = $dataRep;
                $v['modificaciones'] = $dataMod;
                $v['accionistas'] = $dataAcc;

                array_push($data_all_final, $v);
            }

            $columnsData = array_keys($data_all_final[0]);

            $headersData = array_combine($columnsData, $columnsData);

            foreach ($headersData as $index => $header) {
                $headersData[$index] = str_replace("_", " ", $header);
            }

            $abc = ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z',
                'AA', 'AB', 'AC', 'AD', 'AE', 'AF', 'AG', 'AH', 'AI', 'AJ', 'AK', 'AL', 'AM', 'AN', 'AO', 'AP', 'AQ', 'AR', 'AS', 'AT', 'AU', 'AV', 'AW', 'AX', 'AY', 'AZ', 'BA', 'BB',
                'BC', 'BD', 'BE', 'BF', 'BG', 'BH', 'BI', 'BJ', 'BK', 'BL', 'BM', 'BN', 'BO', 'BP', 'BQ', 'BR', 'BS', 'BT', 'BU', 'BV', 'BW', 'BX', 'BY', 'BZ',
                'CA', 'CB', 'CC', 'CD', 'CE', 'CF', 'CG', 'CH', 'CI', 'CJ', 'CK', 'CL', 'CM', 'CN', 'CO', 'CP', 'CQ', 'CR', 'CS', 'CT', 'CU', 'CV', 'CW', 'CX', 'CY', 'CZ'];
            header('Content-Type: application/vnd.ms-excel;charset=UTF-8');
            header('Content-Disposition: attachment;filename=padron_proveedores.xlsx');
            header('Cache-Control: max-age=0');

            $objPHPExcel = new \PHPExcel();
            $objPHPExcel->getProperties()->setCreator("Gobierno de Nuevo Léon");
            $objPHPExcel->getProperties()->setLastModifiedBy("Gobierno de Nuevo Léon");
            $objPHPExcel->getProperties()->setTitle("padron_proveedores");
            $objPHPExcel->getProperties()->setSubject("padron_proveedores");
            $catOptions = [];

            $objPHPExcel->createSheet(0)->setTitle('Padron');
            $objPHPExcel->setActiveSheetIndex(0);

            $rowCount = 1;
            $con = 0;
            foreach ($headersData as $x => $d) {
                $objPHPExcel->getActiveSheet()->SetCellValue($abc[$con] . $rowCount, $d);
                $con++;
            }

            $rowCount = 2;
            foreach ($data_all_final as $dato) {
                $con = 0;
                foreach ($dato as $kk => $result) {
                    if (in_array($kk, ['representantes', 'modificaciones', 'accionistas'])) {
                        if (!empty($result)) {
                            $concat = '';
                            for ($o = 0; $o < count($result); $o++) {
                                foreach ($result[$o] as $in => $v) {
                                    $concat .= $in . ': ' . $v . ' ' . "\n";
                                }
                            }
                            $result = $concat;
                        } else {
                            $result = '';
                        }
                    }

                    $objPHPExcel->getActiveSheet()->SetCellValue($abc[$con] . $rowCount, $result);
                    $objPHPExcel->getActiveSheet()->getStyle($abc[$con] . $rowCount)->getAlignment()->setWrapText(true);
                    $objPHPExcel->getActiveSheet()->getColumnDimension($abc[$con])->setAutoSize(true);
                    $con++;
                }
                $rowCount++;
            }
            $objPHPExcel->removeSheetByIndex(
                $objPHPExcel->getIndex(
                    $objPHPExcel->getSheetByName('Worksheet')
                )
            );

            ob_clean();
            $objWriter = \PHPExcel_IOFactory::createWriter($objPHPExcel, 'Excel2007');
            $objWriter->save('php://output');

            exit();

        } else {
            return $this->disabled();
        }

        return $this->goHome();
    }

    public static function downloadFile($file, $sql)
    {
        header('Pragma: public');
        header('Expires: 0');
        header('Cache-Control: must-revalidate, post-check=0, pre-check=0');
        header('Content-Description: File Transfer');
        header('Content-Type: text/csv');
        header('Content-Disposition: attachment; filename=' . $file . ';');
        header('Content-Transfer-Encoding: binary');

        $fileCSV = fopen('php://output', 'w');
        fputs($fileCSV, $bom = (chr(0xEF) . chr(0xBB) . chr(0xBF)));
        fputcsv($fileCSV, array_keys($sql[0]), ";");
        foreach ($sql as $row) {
            fputcsv($fileCSV, $row, ";");
        }
        fclose($fileCSV);
        exit();
        //return file_get_contents($file);
    }

    public static function downloadExcel($data,$name='archivo'){
        $columns = array_keys($data[0]);
        $headers = array_combine($columns, $columns);
        foreach ($headers as $index => $header) {
            $headers[$index] = str_replace("_", " ", strtoupper($header));
        }
        Excel::widget([
            'models' => $data,
            'mode' => 'export',
            'fileName' => $name."_" . date("Y-m-d_H:i:s") . ".xlsx",
            'columns' => $columns,
            'headers' => $headers,
        ]);
        exit();
    }

    public function disabled()
    {
        return json_encode(['Error' => 'No Disponible']);
    }


    public function actionInsertDataAddres()
    {

        $data = Yii::$app->db->createCommand("select provider_id,tipo_persona,name_razon_social, concat_ws(' ',pf_nombre,pf_ap_paterno,pf_ap_materno) as nombre,
        calle_fiscal,numero_fiscal,interior_fiscal,colonia_fiscal,cp_fiscal,city_fiscal,telfono,email,registro_complet,tipo_vialidad,clave_localidad from provider
        where provider_id not in(select provider_id from provider.ubicacion where type_address_prov = 'DOMICILIO FISCAL')")->queryAll();


        foreach ($data as $val) {

            $ubi = new models\Ubicacion();


            $statefiscal = '';
            if (!empty($val['city_fiscal'])) {
                $statefiscal = models\CatMunicipios::find()->select('entidad_id')->where(['municipio_id' => $val['city_fiscal']])->one()['entidad_id'];

            }

            $encargado = 'PENDIENTE';

            if ($val['tipo_persona'] == 'Persona moral' && !empty($val['name_razon_social'])) {
                $encargado = $val['name_razon_social'];
            } elseif ($val['tipo_persona'] == 'Persona física' && !empty($va['nombre'])) {
                $encargado = $va['nombre'];
            }

            $ubi->encargado = $encargado;
            $ubi->tipo = 'DOMICILIO FISCAL';
            $ubi->descripcion = 'DOMICILIO FISCAL';
            $ubi->type_address_prov = 'DOMICILIO FISCAL';
            $ubi->correo = $val['email'];
            $ubi->telefono = $val['telfono'];
            $ubi->calle_fiscal = $val['calle_fiscal'];

            $ubi->num_ext_fiscal = $val['numero_fiscal'];
            $ubi->num_int_fiscal = $val['interior_fiscal'];
            $ubi->colonia_fiscal = $val['colonia_fiscal'];
            $ubi->cp_fiscal = $val['cp_fiscal'];
            $ubi->city_fiscal = $val['city_fiscal'];
            $ubi->state_fiscal = $statefiscal;
            $ubi->provider_id = $val['provider_id'];
            $ubi->localidad_id = $val['clave_localidad'];
            $ubi->vialidad_id = $val['tipo_vialidad'];


            if ($val['registro_complet']) {
                $ubi->status_bys = 'VALIDADO';
                $ubi->status_op = 'VALIDADO';
            }

            $location = [];
            $colonia = '';
            $city = '';

            if (!empty($val['colonia_fiscal'])) {
                $colonia = models\CatAsentamientos::find()->select('nombre')->where(['asentamiento_id' => $val['colonia_fiscal']])->one()['nombre'];

            }

            if (!empty($val['city_fiscal'])) {
                $city = models\CatMunicipios::find()->select('nombre')->where(['municipio_id' => $val['city_fiscal']])->one()['nombre'];

            }

            if (!empty($statefiscal) && !empty($val['calle_fiscal']) && !empty($val['colonia_fiscal']) && !empty($val['city_fiscal'])) {

                $location = $this->getLocation2(
                    $val['calle_fiscal'],
                    $val['numero_fiscal'],
                    $colonia,
                    $val['cp_fiscal'],
                    $city,
                    $statefiscal);
            }

            if (isset($location[0]) && !empty($location[0]) && isset($location[1]) && !empty($location[1])) {
                $val_geo = models\Ubicacion::findBySql("select ST_MakePoint(" . $location[0] . "," . $location[1] . ")")->asArray()->all();
                $ubi->geo_ubicacion = $val_geo[0]['st_makepoint'];
            }

            if (!$ubi->save()) {
                return json_encode($ubi->errors);
            }


        }


    }

    public static function getLocation2($street, $num, $suburb, $zip, $city, $state)
    {
        //$zip = '';
        $address = $street . ' ' . $num . ' ' . $suburb . ' ' . $zip . ' ' . $city . ', ' . $state;
        $dir = urlencode($address);
        $url = "https://maps-api-ssl.google.com/maps/api/geocode/json?address=$dir&sensor=false&key=AIzaSyAxWfwRmQG4PfsVQfD96np1SZMqklYIo2k";

        $curl = curl_init();
        curl_setopt($curl, CURLOPT_URL, $url);
        curl_setopt($curl, CURLOPT_HEADER, false);
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($curl, CURLOPT_ENCODING, "");
        $curlData = curl_exec($curl);
        curl_close($curl);

        $address = json_decode($curlData, true);
        $location = [];
        if ($address && isset($address['results'][0]['geometry']['location'])) {
            $location = [$address['results'][0]['geometry']['location']['lat'], $address['results'][0]['geometry']['location']['lng']];
        }
        return $location;
    }


    public function actionSearchActivities($q = null)
    {

        \Yii::$app->response->format = \yii\web\Response::FORMAT_JSON;
        $out = ['results' => ['id' => '', 'text' => '']];
        if (!is_null($q)) {
            $data = Yii::$app->db->createCommand("select actividad_id as id,nombre_actividad as text from provider.cat_actividades
                     where unaccent (nombre_actividad) ilike unaccent (:q) limit 30", [':q' => '%' . $q . '%'])->queryAll();
            $out['results'] = array_values($data);

        }

        return $out;

    }


    public function actionNotificationCotejo()
    {

        $idMo = Yii::$app->db->createCommand("select user_id from provider where
                provider_id not in (select provider_id from historico_Certificados
                 where tipo = 'CERTIFICADO' and provider_type = 'bys')")->queryAll();

        foreach ($idMo as $valaa) {

            $userSendMsj[0] = $valaa;


            $idMo = Yii::$app->db->createCommand("select module_notification_url_id
            from module_notification_url
            where module_notification_name = 'VIGENCIA REGISTRO'")->queryOne()['module_notification_url_id'];
            $save_notification = new models\SaveNotification();
            $save_notification->module_notification_url_id = $idMo;
            $save_notification->op_bys = 'bys';
            $save_notification->module_validation_name = 'Vigencia de registro.';
            $save_notification->msg_extra = '';
            if ($save_notification->save()) {
                $not_id = $save_notification->save_notification_id;
                foreach ($userSendMsj as $val) {
                    $user_notifiction = new models\UserNotification();
                    $user_notifiction->save_notification_id = $save_notification->save_notification_id;
                    $user_notifiction->user_id = $val['user_id'];
                    if ($user_notifiction->save()) {
                        if (($number_noti = models\NumberNotification::find()->where(['user_id' => $val['user_id']])->one()) !== null) {
                            $number_noti->number_notification = $number_noti->viewed == false ? $number_noti->number_notification + 1 : 1;
                            $number_noti->viewed = false;
                            $number_noti->save();
                        } else {
                            $number_noti = new models\NumberNotification();
                            $number_noti->number_notification = 1;
                            $number_noti->user_id = $val['user_id'];
                            $number_noti->save();
                        }
                    }
                }

            }

        }


    }


    public function actionCartaPendent()
    {

        $data = Yii::$app->db->createCommand("select provider_id,url_carta_bys,firma_bys,firma_corta_bys
                from provider where url_carta_bys is not null")->queryAll();

        foreach ($data as $v) {
            if (($verify = models\HistoricoCartaProtesta::find()
                    ->where(['and', ['provider_id' => $v['provider_id']],
                        ['is not', 'firma_bys', null], ['is not', 'firma_corta_bys', null]])->one()) === null) {
                $cp = new models\HistoricoCartaProtesta();
                $cp->provider_id = $v['provider_id'];
                $cp->url_carta = $v['url_carta_bys'];
                $cp->firma_bys = $v['firma_bys'];
                $cp->firma_corta_bys = $v['firma_corta_bys'];
                $cp->save();
            }
        }
    }

    public function actionCartaPendentOp()
    {

        $data = Yii::$app->db->createCommand("select provider_id,url_carta_op,firma_op,firma_corta_op
                from provider where url_carta_op is not null")->queryAll();

        foreach ($data as $v) {

            if (($verify = models\HistoricoCartaProtesta::find()
                    ->where(['and', ['provider_id' => $v['provider_id']],
                        ['is not', 'firma_op', null], ['is not', 'firma_corta_op', null]])->one()) === null) {
                $cp = new models\HistoricoCartaProtesta();
                $cp->provider_id = $v['provider_id'];
                $cp->url_carta = $v['url_carta_op'];
                $cp->firma_op = $v['firma_op'];
                $cp->firma_corta_op = $v['firma_corta_op'];
                $cp->save();
            }
        }
    }


    public function actionGenName()
    {

        $name = md5('DECLARACIÓN DEL EJERCICIO FISCAL MAS RECIENTE') . strval(microtime(true) * 10000);

        var_dump($name);
        exit();

    }

    public function actionSaveProviderVal()
    {

        $data = Yii::$app->db->createCommand("select * from provider.datos_validados where provider_id is null")->queryAll();

        $column = ["alta_hacienda" => 'provider_id',
            "clientes_contratos" => 'clientes_contratos_id',
            "intervencion_bancaria" => 'provider_id',
            "modificacion_acta" => 'modificacion_acta_id',
            "relacion_accionistas" => 'relacion_accionistas_id',
            "rfc" => 'provider_id'
        ];

        foreach ($data as $val) {
            $modeloUpdate = $val['modelo'];

            $columnaSearch = $column[$modeloUpdate];
            $idSearch = $val['register_id'];
            if ($columnaSearch == 'provider_id') {
                $rfc = Yii::$app->db->createCommand();
                $rfc->update('provider.datos_validados', ["provider_id" => $idSearch],
                    ['validado_id' => $val['validado_id']]);
                $rfc->execute();
            } else {

                $pId = Yii::$app->db->createCommand("select provider_id from provider.$modeloUpdate
                        where $columnaSearch = $idSearch")->queryOne()['provider_id'];

                $rfc = Yii::$app->db->createCommand();
                $rfc->update('provider.datos_validados', ["provider_id" => $pId],
                    ['validado_id' => $val['validado_id']]);
                $rfc->execute();

            }
        }

    }

    public function actionSaveProviderRec()
    {

        $data = Yii::$app->db->createCommand("select * from provider.status where provider_id is null")->queryAll();

        $column = ["alta_hacienda" => 'provider_id',
            "clientes_contratos" => 'clientes_contratos_id',
            "intervencion_bancaria" => 'provider_id',
            "modificacion_acta" => 'modificacion_acta_id',
            "relacion_accionistas" => 'relacion_accionistas_id',
            "rfc" => 'provider_id',
            "cotejar" => 'provider_id',
            'historico_carta_protesta' => 'historico_carta_protesta_id',
            'estado_financiero' => 'estado_financiero_id',
            'perfil' => 'provider_id',
            'ultima_declaracion' => 'provider_id',
            'experiencia' => 'experiencia_id',
            'representante_legal' => 'representante_legal_id',
            'direccion_nl' => 'ubicacion_id',
            'ubicacion' => 'ubicacion_id',
            'fotografia_negocio' => 'fotografia_negocio_id',
            'certificacion' => 'certificacion_id',
            'maquinaria_equipos' => 'maquinaria_equipos_id',
            'personal_tecnico' => 'personal_tecnico_id',
            'organigrama' => 'organigrama_id'
        ];

        foreach ($data as $val) {
            $modeloUpdate = $val['modelo'];

            $modeloUpdate = $modeloUpdate == 'direccion_nl' ? 'ubicacion' : $modeloUpdate;
            $columnaSearch = $column[$modeloUpdate];
            $idSearch = $val['register_id'];
            if ($columnaSearch == 'provider_id') {
                $rfc = Yii::$app->db->createCommand();
                $rfc->update('provider.status', ["provider_id" => $idSearch],
                    ['status_id' => $val['status_id']]);
                $rfc->execute();
            } else {

                $pId = Yii::$app->db->createCommand("select provider_id from provider.$modeloUpdate
                        where $columnaSearch = $idSearch")->queryOne()['provider_id'];

                $rfc = Yii::$app->db->createCommand();
                $rfc->update('provider.status', ["provider_id" => $pId],
                    ['status_id' => $val['status_id']]);
                $rfc->execute();

            }
        }

    }

    public function getNameProvider($id = null)
    {
        $data = '';
        if ($id != null) {
            $data = \Yii::$app->db->createCommand("select CASE WHEN (name_razon_social is not null and name_razon_social != '') then name_razon_social else concat_ws(' ',pf_nombre,pf_ap_paterno,pf_ap_materno) end as name_provider
        from provider where provider_id = :id", [':id' => intval($id)])->queryOne()['name_provider'];
        }

        return $data;
    }

    public function yearOld()
    {
        $fecha_actual = date("Y-m");

        $dateYear = date("Y-m", strtotime($fecha_actual . "- 1 year"));

        $dateFinal = intval(str_replace('-', '', date("Y-m", strtotime($dateYear . "- 1 month"))));

        return $dateFinal;
    }

    public function monthToNumber($monthSearch)
    {

        $month = ['ENE' => '01', 'FEB' => '02', 'MAR' => '03', 'ABR' => '04', 'MAY' => '05', 'JUN' => '06',
            'JUL' => '07', 'AGO' => '08', 'SEP' => '09', 'OCT' => '10', 'NOV' => '11', 'DIC' => '12'];

        return $month[$monthSearch];
    }

    public function countClient($provider_id = 0)
    {


        $total = models\ClientesContratos::find()->select("sum(case when ( status_bys = '" . Status::STATUS_ENEDICION . "' or status_bys = '" . Status::STATUS_PORVALIDAR . "' or status_bys = '" . Status::STATUS_VALIDADO . "')
        then 1 else 0 end) as total")
            ->where(['and', ['activo' => TRUE], ['provider_id' => $provider_id], ['tipo' => 'bys']])->asArray()->one()['total'];


        return $total;

    }


    public function countClientDelete($provider_id = 0)
    {


        $total = models\ClientesContratos::find()->select("sum(case when ( status_bys = '" . Status::STATUS_ENEDICION . "' or status_bys = '" . Status::STATUS_PORVALIDAR . "' or status_bys = '" . Status::STATUS_VALIDADO . "' or status_bys = '" . Status::STATUS_RECHAZADO . "')
        then 1 else 0 end) as total")
            ->where(['and', ['activo' => TRUE], ['provider_id' => $provider_id], ['tipo' => 'bys']])->asArray()->one()['total'];


        return $total;

    }

    public function actionRetornar($id = null)
    {
        if ($id != null) {

            $provider_id = intval($id);

            if (($mod = models\Provider::findOne($provider_id)) !== null) {
                $mod->status_cotejar = null;
                if ($mod->save(false)) {
                    $this->insertMovProv($provider_id, 'RETORNAR ETAPA EDICION');
                }
            }

        }

        return $this->redirect('/provider/index-provider-cotejar');

    }

    public function actionActivarcita($id = null)
    {

        if ($id != null) {

            $provider_id = intval($id);

            if (($mod = models\Provider::findOne($provider_id)) !== null) {
                $mod->status_cotejar = 'PENDIENTE ACTIVAR AGENDA';
                $email = $mod->email;
                if ($mod->save(false)) {
                    if (($vi = Visit::find()->where(['and', ['provider_id' => $provider_id], ['status' => true]])->one()) === null) {
                        $modVisit = new Visit();
                        $modVisit->provider_id = $provider_id;
                        $modVisit->save(false);
                    }
                    $this->insertMovProv($provider_id, 'PENDIENTE ACTIVAR AGENDA');
                    self::sendEmail('/provider/correos/activar_agenda', null, $email, 'Agendar cita', [], null);
                }
            }

        }

        return $this->redirect('/provider/index-admin-provider');

    }

    public function insertMovProv($id, $tipoMov)
    {

        $mov = new models\MovProvider();
        $mov->provider_id = $id;
        $mov->created_by = Yii::$app->user->getId();
        $mov->movimiento = $tipoMov;
        $mov->save();

    }


    public function verifyChange($modelOld, $modelNew)
    {

        $arrayKeys = array_keys($modelNew);
        $updateStatus = false;
        foreach ($arrayKeys as $v) {
            if (!in_array($v, ['rep_bys', 'rep_op'])) {
                if ($modelOld[$v] !== $modelNew[$v]) {
                    $updateStatus = true;
                }
            }
        }
        return $updateStatus;

    }

    /* public function actionNoJala($rfc = null)
    {
        $request = new \SoapClient(
            'http://sistemas.nl.gob.mx/wsSA_ProveedoresSU/wsProveedores.asmx?WSDL', array('exceptions' => true));

        $userResponseDecode = $request->ConsultaRFC(['rfc' => $rfc]);

        var_dump($userResponseDecode);
        exit();
    } */

    public function actionEmailRenovacion()
    {
        self::sendEmail3('/provider/correos/renovacion', '<EMAIL>', '<EMAIL>', 'Actualiza tu información', ['nombre' => 'System, S.A, C.V', 'modulo' => 'Datos Financieros', 'tipo_provider' => 'bys', 'tipo' => '30 DIAS', 'campos' => 'ho']);

    }

    public static function sendEmail3($view = null, $from = null, $email = null, $subject = null, $params = [], $attach = null)
    {

        $from = '<EMAIL>';

        $arr = ['/provider/correos/nuevo'];
        if (!in_array($view, $arr)) {
            //try {

            $mailer = Yii::$app->mailer->compose($view, $params)
                ->setFrom($from)
                ->setTo($email)
                ->setSubject($subject);
            if ($attach != null) {
                $mailer->attach($attach);
            }
            $mailer->send();
            //} catch (\Exception $e) {
            //var_dump($e);exit();
            //}
        }
    }

    /* Validacion ya no se utiliza */
    public function actionVerifyExpirationUltimaDeclaracion()
    {
        /* $data = Yii::$app->db->createCommand("with ct as(select distinct
                historico_certificados_id,provider_id, created_at,
                row_number() over (partition by provider_id order by created_at desc) rn
                from
                historico_certificados where tipo ='CERTIFICADO' and provider_type = 'bys' order by created_at
                )
                select
                ct.provider_id
                from
                ct
                where
                rn = 1")->queryAll();

        $dateNow = date('Y-m-d');

        foreach ($data as $d) {

            $ultimaDec = models\UltimaDeclaracion::find()->select(['ultima_declaracion_id', new \yii\db\Expression('created_date::date')])->where(['provider_id' => $d['provider_id']])->one();
            if ($ultimaDec) {
                $fechaUp = $ultimaDec->created_date;
                $fecha_actual = new \DateTime($dateNow);
                $fecha_final = new \DateTime($fechaUp);
                $dias = $fecha_actual->diff($fecha_final)->format('%r%a');

                if ($dias == 30) {
                    if ((models\ExpirationDocuments::find()->where(['and', ['register_id' => $ultimaDec->ultima_declaracion_id], ['type' => '30 DIAS'], ['module' => 'ultima_declaracion'], ['expitation_date' => $fechaUp], ['permanent' => false]])->count('1')) == 0) {
                        $expDoc = new models\ExpirationDocuments();
                        $expDoc->provider_id = $d['provider_id'];
                        $expDoc->module = 'ultima_declaracion';
                        $expDoc->register_id = $ultimaDec->ultima_declaracion_id;
                        $expDoc->msg = 'Modulo proximo a vencer en (30 días)';
                        $expDoc->expitation_date = $fechaUp;
                        $expDoc->type = "30 DIAS";
                        $expDoc->save();
                    }
                } else if ($dias == 15) {
                    if ((models\ExpirationDocuments::find()->where(['and', ['register_id' => $ultimaDec->ultima_declaracion_id], ['type' => '15 DIAS'], ['module' => 'ultima_declaracion'], ['expitation_date' => $fechaUp], ['permanent' => false]])->count('1')) == 0) {
                        $expDoc = new models\ExpirationDocuments();
                        $expDoc->provider_id = $d['provider_id'];
                        $expDoc->module = 'ultima_declaracion';
                        $expDoc->register_id = $ultimaDec->ultima_declaracion_id;
                        $expDoc->msg = 'Modulo proximo a vencer en (15 días)';
                        $expDoc->expitation_date = $fechaUp;
                        $expDoc->type = "15 DIAS";
                        $expDoc->save();
                    }
                } else if ($dias == 0 || $dias < -366) {
                    if ((models\ExpirationDocuments::find()->where(['and', ['register_id' => $ultimaDec->ultima_declaracion_id], ['type' => 'EXPIRED'], ['module' => 'ultima_declaracion'], ['expitation_date' => $fechaUp], ['permanent' => false]])->count('1')) == 0) {
                        $expDoc = new models\ExpirationDocuments();
                        $expDoc->provider_id = $d['provider_id'];
                        $expDoc->module = 'ultima_declaracion';
                        $expDoc->register_id = $ultimaDec->ultima_declaracion_id;
                        $expDoc->msg = 'Modulo vencido';
                        $expDoc->expitation_date = $fechaUp;
                        $expDoc->type = "EXPIRED";
                        $expDoc->save();
                        if (models\UltimaDeclaracion::updateAll(['status_bys' => 'RECHAZADO'], ['ultima_declaracion_id' => $ultimaDec->ultima_declaracion_id])) {
                            $rech = new Status();
                            $rech->modelo = 'ultima_declaracion';
                            $rech->register_id = $d['provider_id'];
                            $rech->created_id = 168;
                            $rech->motivo = "ESTIMADO INTERESADO:<br>
                            CON EL FIN DE MANTENER SU REGISTRO ACTIVO EN EL PADRON DE PROVEEDORES, LO INVITAMOS A ACTUALIZAR LA SIGUIENTE INFORMACIÓN,<br>
                            BALANCE GENERAL Y ESTADO DE RESULTADOS, OPINION DE CUMPLIMIENTO, PAGOS PROVISIONALES  DE IMPUESTOS FEDERALES,IMPUESTO DE NOMINA";
                            $rech->status_bys = 'PENDIENTE';
                            $rech->expiration = true;
                            $rech->save();
                        }
                    }
                }
            } 
        }*/
    }


    public function actionVerifyExpirationUltimaDeclaracionPermanent($provider, $type)
    {

        /* $providerTipe = $provider == 'pf' ? 'Persona física' : 'Persona moral';
        $data = Yii::$app->db->createCommand("with ct as(select distinct
                historico_certificados_id,provider_id, created_at,
                row_number() over (partition by provider_id order by created_at desc) rn
                from
                historico_certificados where tipo ='CERTIFICADO' and provider_type = 'bys' order by created_at
                )
                select
                ct.provider_id
                from
                ct
                join provider p on p.provider_id = ct.provider_id
                where
                rn = 1 and p.tipo_persona = :p", [':p' => $providerTipe])->queryAll();


        foreach ($data as $d) {

            $ultimaDec = models\UltimaDeclaracion::find()->select('ultima_declaracion_id')->where(['provider_id' => $d['provider_id']])->one();

            if ($type == 30) {
                $expDoc = new models\ExpirationDocuments();
                $expDoc->provider_id = $d['provider_id'];
                $expDoc->module = 'ultima_declaracion';
                $expDoc->register_id = $ultimaDec->ultima_declaracion_id;
                $expDoc->msg = 'Modulo proximo a vencer en (30 días)';
                $expDoc->expitation_date = $provider == 'pf' ? date('Y') . '-04-30' : date('Y') . '-03-31';
                $expDoc->type = "30 DIAS";
                $expDoc->permanent = true;
                $expDoc->save();
            } else if ($type == 15) {
                $expDoc = new models\ExpirationDocuments();
                $expDoc->provider_id = $d['provider_id'];
                $expDoc->module = 'ultima_declaracion';
                $expDoc->register_id = $ultimaDec->ultima_declaracion_id;
                $expDoc->msg = 'Modulo proximo a vencer en (15 días)';
                $expDoc->expitation_date = $provider == 'pf' ? date('Y') . '-04-30' : date('Y') . '-03-31';
                $expDoc->type = "15 DIAS";
                $expDoc->permanent = true;
                $expDoc->save();
            } else if ($type == 0) {
                $expDoc = new models\ExpirationDocuments();
                $expDoc->provider_id = $d['provider_id'];
                $expDoc->module = 'ultima_declaracion';
                $expDoc->register_id = $ultimaDec->ultima_declaracion_id;
                $expDoc->msg = 'Modulo vencido';
                $expDoc->expitation_date = $provider == 'pf' ? date('Y') . '-04-30' : date('Y') . '-03-31';
                $expDoc->type = "EXPIRED";
                $expDoc->permanent = true;
                $expDoc->save();
                if (models\UltimaDeclaracion::updateAll(['status_bys' => 'RECHAZADO'], ['ultima_declaracion_id' => $ultimaDec->ultima_declaracion_id])) {
                    $rech = new Status();
                    $rech->modelo = 'ultima_declaracion';
                    $rech->register_id = $d['provider_id'];
                    $rech->created_id = 168;
                    $rech->motivo = "ESTIMADO INTERESADO:<br>
                            CON EL FIN DE MANTENER SU REGISTRO ACTIVO EN EL PADRON DE PROVEEDORES, LO INVITAMOS A ACTUALIZAR LA SIGUIENTE INFORMACIÓN,<br>
                            DECLARACION ANUAL";
                    $rech->status_bys = 'PENDIENTE';
                    $rech->expiration = true;
                    $rech->save();
                }
            }
        } */
    }

    public function actionVerifyExpirationUltimaDeclaracionPermanentNow()
    {
        /* $data = Yii::$app->db->createCommand("with ct as(select distinct
                    historico_certificados_id,provider_id, created_at,
                    row_number() over (partition by provider_id order by created_at desc) rn
                    from
                    historico_certificados where tipo ='CERTIFICADO' and provider_type = 'bys' and created_at::date<='2020-04-30' order by created_at
                    )
                    select
                    ct.provider_id,p.tipo_persona
                    from
                    ct
                    join provider p on p.provider_id = ct.provider_id
                    where
                    rn = 1")->queryAll();


        foreach ($data as $d) {

            $ultimaDec = models\UltimaDeclaracion::find()->select('ultima_declaracion_id')->where(['provider_id' => $d['provider_id']])->one();

            $expDoc = new models\ExpirationDocuments();
            $expDoc->provider_id = $d['provider_id'];
            $expDoc->module = 'ultima_declaracion';
            $expDoc->register_id = $ultimaDec->ultima_declaracion_id;
            $expDoc->msg = 'Modulo vencido';
            $expDoc->expitation_date = $d['tipo_persona'] == 'Persona moral' ? '2020-03-31' : '2020-04-30';
            $expDoc->type = "EXPIRED";
            $expDoc->permanent = true;
            $expDoc->save();
            if (models\UltimaDeclaracion::updateAll(['status_bys' => 'RECHAZADO'], ['ultima_declaracion_id' => $ultimaDec->ultima_declaracion_id])) {
                $rech = new Status();
                $rech->modelo = 'ultima_declaracion';
                $rech->register_id = $d['provider_id'];
                $rech->created_id = 168;
                $rech->motivo = "ESTIMADO INTERESADO:<br>
                            CON EL FIN DE MANTENER SU REGISTRO ACTIVO EN EL PADRON DE PROVEEDORES, LO INVITAMOS A ACTUALIZAR LA SIGUIENTE INFORMACIÓN,<br>
                            DECLARACION ANUAL";
                $rech->status_bys = 'PENDIENTE';
                $rech->expiration = true;
                $rech->save();
            }
        } */
    }

    public function actionVerifyExpirationComprobanteDomicilio()
    {
        /* $data = Yii::$app->db->createCommand("with ct as(select distinct
                historico_certificados_id,provider_id, created_at,
                row_number() over (partition by provider_id order by created_at desc) rn
                from
                historico_certificados where tipo ='CERTIFICADO' and provider_type = 'bys' order by created_at
                )
                select
                ct.provider_id
                from
                ct
                where
                rn = 1")->queryAll();

        $dateNow = date('Y-m-d');

        foreach ($data as $d) {

            $compDom = models\ComprobanteDomicilio::find()->select(['comprobante_domicilio_id', new \yii\db\Expression('created_date::date')])->where(['provider_id' => $d['provider_id']])->one();
            if ($compDom) {
                $fechaUp = $compDom->created_date;
                $fecha_actual = new \DateTime($dateNow);
                $fecha_final = new \DateTime($fechaUp);
                $dias = $fecha_actual->diff($fecha_final)->format('%r%a');

                if ($dias == 30) {
                    if ((models\ExpirationDocuments::find()->where(['and', ['register_id' => $compDom->comprobante_domicilio_id], ['type' => '30 DIAS'], ['module' => 'rfc'], ['expitation_date' => $fechaUp], ['permanent' => false], ['column_name' => 'Comprobante de domicilio']])->count('1')) == 0) {
                        $expDoc = new models\ExpirationDocuments();
                        $expDoc->provider_id = $d['provider_id'];
                        $expDoc->module = 'rfc';
                        $expDoc->column_name = 'Comprobante de domicilio';
                        $expDoc->register_id = $compDom->comprobante_domicilio_id;
                        $expDoc->msg = 'Modulo proximo a vencer en (30 días)';
                        $expDoc->expitation_date = $fechaUp;
                        $expDoc->type = "30 DIAS";
                        $expDoc->save();
                    }
                } else if ($dias == 15) {
                    if ((models\ExpirationDocuments::find()->where(['and', ['register_id' => $compDom->comprobante_domicilio_id], ['type' => '15 DIAS'], ['module' => 'rfc'], ['expitation_date' => $fechaUp], ['permanent' => false], ['column_name' => 'Comprobante de domicilio']])->count('1')) == 0) {
                        $expDoc = new models\ExpirationDocuments();
                        $expDoc->provider_id = $d['provider_id'];
                        $expDoc->module = 'rfc';
                        $expDoc->column_name = 'Comprobante de domicilio';
                        $expDoc->register_id = $compDom->comprobante_domicilio_id;
                        $expDoc->msg = 'Modulo proximo a vencer en (15 días)';
                        $expDoc->expitation_date = $fechaUp;
                        $expDoc->type = "15 DIAS";
                        $expDoc->save();
                    }
                } else if ($dias == 0 || $dias < -366) {
                    if ((models\ExpirationDocuments::find()->where(['and', ['register_id' => $compDom->comprobante_domicilio_id], ['type' => 'EXPIRED'], ['module' => 'rfc'], ['expitation_date' => $fechaUp], ['permanent' => false], ['column_name' => 'Comprobante de domicilio']])->count('1')) == 0) {
                        $expDoc = new models\ExpirationDocuments();
                        $expDoc->provider_id = $d['provider_id'];
                        $expDoc->module = 'rfc';
                        $expDoc->column_name = 'Comprobante de domicilio';
                        $expDoc->register_id = $compDom->comprobante_domicilio_id;
                        $expDoc->msg = 'Modulo vencido';
                        $expDoc->expitation_date = $fechaUp;
                        $expDoc->type = "EXPIRED";
                        $expDoc->save();
                        if (models\rfc::updateAll(['status_bys' => 'RECHAZADO'], ['provider_id' => $d['provider_id']])) {
                            $rech = new Status();
                            $rech->modelo = 'rfc';
                            $rech->register_id = $d['provider_id'];
                            $rech->created_id = 168;
                            $rech->motivo = "ESTIMADO INTERESADO:<br>
                            CON EL FIN DE MANTENER SU REGISTRO ACTIVO EN EL PADRON DE PROVEEDORES, LO INVITAMOS A ACTUALIZAR LA SIGUIENTE INFORMACIÓN,<br>
                            COMPROBANTE DE DOMICILIO";
                            $rech->status_bys = 'PENDIENTE';
                            $rech->expiration = true;
                            $rech->save();
                        }
                    }
                }
            }
        } */
    }

    public function actionVerifyExpirationPasaporte()
    {
        $data = Yii::$app->db->createCommand("with ct as(select distinct
                historico_certificados_id,provider_id, created_at,
                row_number() over (partition by provider_id order by created_at desc) rn
                from
                historico_certificados where tipo ='CERTIFICADO' and provider_type = 'bys' order by created_at
                )
                select
                ct.provider_id
                from
                ct
                where
                rn = 1")->queryAll();

        $dateNow = date('Y-m-d');

        foreach ($data as $d) {

            $PasaPorte = models\IdOficial::find()->select(['idoficial_id', 'expiration_date'])->where(['and', ['provider_id' => $d['provider_id']], ['tipo_identificacion' => 'PASAPORTE']])->one();
            if ($PasaPorte) {
                $fechaUp = $PasaPorte->expiration_date;
                $fecha_actual = new \DateTime($dateNow);
                $fecha_final = new \DateTime($fechaUp);
                $dias = $fecha_actual->diff($fecha_final)->format('%r%a');

                if ($dias == 30) {
                    if ((models\ExpirationDocuments::find()->where(['and', ['register_id' => $PasaPorte->idoficial_id], ['type' => '30 DIAS'], ['module' => 'rfc'], ['expitation_date' => $fechaUp], ['permanent' => false], ['column_name' => 'Pasaporte']])->count('1')) == 0) {
                        $expDoc = new models\ExpirationDocuments();
                        $expDoc->provider_id = $d['provider_id'];
                        $expDoc->module = 'rfc';
                        $expDoc->column_name = 'Pasaporte';
                        $expDoc->register_id = $PasaPorte->idoficial_id;
                        $expDoc->msg = 'Modulo proximo a vencer en (30 días)';
                        $expDoc->expitation_date = $fechaUp;
                        $expDoc->type = "30 DIAS";
                        $expDoc->save();
                    }
                } else if ($dias == 15) {
                    if ((models\ExpirationDocuments::find()->where(['and', ['register_id' => $PasaPorte->idoficial_id], ['type' => '15 DIAS'], ['module' => 'rfc'], ['expitation_date' => $fechaUp], ['permanent' => false], ['column_name' => 'Pasaporte']])->count('1')) == 0) {
                        $expDoc = new models\ExpirationDocuments();
                        $expDoc->provider_id = $d['provider_id'];
                        $expDoc->module = 'rfc';
                        $expDoc->column_name = 'Pasaporte';
                        $expDoc->register_id = $PasaPorte->idoficial_id;
                        $expDoc->msg = 'Modulo proximo a vencer en (15 días)';
                        $expDoc->expitation_date = $fechaUp;
                        $expDoc->type = "15 DIAS";
                        $expDoc->save();
                    }
                } else if ($dias == 0 || $dias < -366) {
                    if ((models\ExpirationDocuments::find()->where(['and', ['register_id' => $PasaPorte->idoficial_id], ['type' => 'EXPIRED'], ['module' => 'rfc'], ['expitation_date' => $fechaUp], ['permanent' => false], ['column_name' => 'Pasaporte']])->count('1')) == 0) {
                        $expDoc = new models\ExpirationDocuments();
                        $expDoc->provider_id = $d['provider_id'];
                        $expDoc->module = 'rfc';
                        $expDoc->column_name = 'Pasaporte';
                        $expDoc->register_id = $PasaPorte->idoficial_id;
                        $expDoc->msg = 'Modulo vencido';
                        $expDoc->expitation_date = $fechaUp;
                        $expDoc->type = "EXPIRED";
                        $expDoc->save();
                        if (models\rfc::updateAll(['status_bys' => 'RECHAZADO'], ['provider_id' => $d['provider_id']])) {
                            $rech = new Status();
                            $rech->modelo = 'rfc';
                            $rech->register_id = $d['provider_id'];
                            $rech->created_id = 168;
                            $rech->motivo = "ESTIMADO INTERESADO:<br>
                            CON EL FIN DE MANTENER SU REGISTRO ACTIVO EN EL PADRON DE PROVEEDORES, LO INVITAMOS A ACTUALIZAR LA SIGUIENTE INFORMACIÓN,<br>
                            IDENTIFICACIÓN OFICIAL PASAPORTE";
                            $rech->status_bys = 'PENDIENTE';
                            $rech->expiration = true;
                            $rech->save();
                        }
                    }
                }
            }
        }
    }

    public function actionVerifyExpirationIne($type)
    {
        $data = Yii::$app->db->createCommand("with ct as(select distinct
                historico_certificados_id,provider_id, created_at,
                row_number() over (partition by provider_id order by created_at desc) rn
                from
                historico_certificados where tipo ='CERTIFICADO' and provider_type = 'bys' order by created_at
                )
                select
                ct.provider_id
                from
                ct
                where
                rn = 1")->queryAll();

        foreach ($data as $d) {

            $vig = $type == 30 || $type == 15 ? date('Y') . '-12-31' : date('Y') . '-01-01';

            $INE = models\IdOficial::find()->select(['idoficial_id', 'expiration_date'])->where(['and', ['provider_id' => $d['provider_id']], ['tipo_identificacion' => 'IFE'], ['<', 'expiration_date', $vig]])->one();

            if ($INE && isset($INE['expiration_date']) && !empty($INE['expiration_date'])) {
                $expl = explode('-', $INE['expiration_date']);
                if ($type == 30) {
                    if ($expl[0] <= date('Y')) {
                        $expDoc = new models\ExpirationDocuments();
                        $expDoc->provider_id = $d['provider_id'];
                        $expDoc->module = 'rfc';
                        $expDoc->column_name = 'INE';
                        $expDoc->register_id = $INE->idoficial_id;
                        $expDoc->msg = 'Modulo proximo a vencer en (30 días)';
                        $expDoc->expitation_date = date('Y') . '-12-31';
                        $expDoc->type = "30 DIAS";
                        $expDoc->save();
                    }
                } else if ($type == 15) {
                    if ($expl[0] <= date('Y')) {
                        $expDoc = new models\ExpirationDocuments();
                        $expDoc->provider_id = $d['provider_id'];
                        $expDoc->module = 'rfc';
                        $expDoc->column_name = 'INE';
                        $expDoc->register_id = $INE->idoficial_id;
                        $expDoc->msg = 'Modulo proximo a vencer en (15 días)';
                        $expDoc->expitation_date = date('Y') . '-12-31';
                        $expDoc->type = "15 DIAS";
                        $expDoc->save();
                    }
                } else if ($type == 0) {
                    if ($expl[0] < date('Y')) {
                        $expDoc = new models\ExpirationDocuments();
                        $expDoc->provider_id = $d['provider_id'];
                        $expDoc->module = 'rfc';
                        $expDoc->column_name = 'INE';
                        $expDoc->register_id = $INE->idoficial_id;
                        $expDoc->msg = 'Modulo vencido';
                        $expDoc->expitation_date = (intval(date('Y')) - 1) . '-12-31';
                        $expDoc->type = "EXPIRED";
                        $expDoc->save();
                        if (models\rfc::updateAll(['status_bys' => 'RECHAZADO'], ['provider_id' => $d['provider_id']])) {
                            $rech = new Status();
                            $rech->modelo = 'rfc';
                            $rech->register_id = $d['provider_id'];
                            $rech->created_id = 168;
                            $rech->motivo = "ESTIMADO INTERESADO:<br>
                            CON EL FIN DE MANTENER SU REGISTRO ACTIVO EN EL PADRON DE PROVEEDORES, LO INVITAMOS A ACTUALIZAR LA SIGUIENTE INFORMACIÓN,<br>
                            IDENTIFICACIÓN OFICIAL INE";
                            $rech->status_bys = 'PENDIENTE';
                            $rech->expiration = true;
                            $rech->save();
                        }
                    }
                }
            }
        }
    }

    public function actionVerifyExpirationIneJunio($type)
    {
        /* $data = Yii::$app->db->createCommand("with ct as(select distinct
                historico_certificados_id,provider_id, created_at,
                row_number() over (partition by provider_id order by created_at desc) rn
                from
                historico_certificados where tipo ='CERTIFICADO' and provider_type = 'bys' order by created_at
                )
                select
                ct.provider_id
                from
                ct
                where
                rn = 1")->queryAll();

        foreach ($data as $d) {

            $INE = models\IdOficial::find()->select(['idoficial_id', 'expiration_date'])->where(['and', ['provider_id' => $d['provider_id']], ['tipo_identificacion' => 'IFE'], ['<', 'expiration_date', '2021-01-01']])->one();

            if ($INE && isset($INE['expiration_date']) && !empty($INE['expiration_date'])) {
                if ($type == 30) {
                    $expDoc = new models\ExpirationDocuments();
                    $expDoc->provider_id = $d['provider_id'];
                    $expDoc->module = 'rfc';
                    $expDoc->column_name = 'INE';
                    $expDoc->register_id = $INE->idoficial_id;
                    $expDoc->msg = 'Modulo proximo a vencer en (30 días)';
                    $expDoc->expitation_date = '2021-06-07';
                    $expDoc->type = "30 DIAS";
                    $expDoc->save();
                } else if ($type == 15) {
                    $expDoc = new models\ExpirationDocuments();
                    $expDoc->provider_id = $d['provider_id'];
                    $expDoc->module = 'rfc';
                    $expDoc->column_name = 'INE';
                    $expDoc->register_id = $INE->idoficial_id;
                    $expDoc->msg = 'Modulo proximo a vencer en (15 días)';
                    $expDoc->expitation_date = '2021-06-07';
                    $expDoc->type = "15 DIAS";
                    $expDoc->save();
                } else if ($type == 0) {
                    $expDoc = new models\ExpirationDocuments();
                    $expDoc->provider_id = $d['provider_id'];
                    $expDoc->module = 'rfc';
                    $expDoc->column_name = 'INE';
                    $expDoc->register_id = $INE->idoficial_id;
                    $expDoc->msg = 'Modulo vencido';
                    $expDoc->expitation_date = '2021-06-07';
                    $expDoc->type = "EXPIRED";
                    $expDoc->save();
                    if (models\rfc::updateAll(['status_bys' => 'RECHAZADO'], ['provider_id' => $d['provider_id']])) {
                        $rech = new Status();
                        $rech->modelo = 'rfc';
                        $rech->register_id = $d['provider_id'];
                        $rech->created_id = 168;
                        $rech->motivo = "ESTIMADO INTERESADO:<br>
                            CON EL FIN DE MANTENER SU REGISTRO ACTIVO EN EL PADRON DE PROVEEDORES, LO INVITAMOS A ACTUALIZAR LA SIGUIENTE INFORMACIÓN,<br>
                            IDENTIFICACIÓN OFICIAL INE";
                        $rech->status_bys = 'PENDIENTE';
                        $rech->expiration = true;
                        $rech->save();
                    }
                }
            }
        } */
    }

    public function actionVerifyExpirationIneRp($type)
    {
        $data = Yii::$app->db->createCommand("with ct as(select distinct
                historico_certificados_id,provider_id, created_at,
                row_number() over (partition by provider_id order by created_at desc) rn
                from
                historico_certificados where tipo ='CERTIFICADO' and provider_type = 'bys' order by created_at
                )
                select
                ct.provider_id
                from
                ct
                where
                rn = 1")->queryAll();

        foreach ($data as $d) {

            $vig = $type == 30 || $type == 15 ? date('Y') . '-12-31' : date('Y') . '-01-01';

            $dat = models\RepresentanteLegal::find()->select(['representante_legal_id', 'vencimiento_identificacion'])
                ->where(['and', ['provider_id' => $d['provider_id']], ['tipo_identificacion' => 'IFE'], ['activo' => true], ['<', 'vencimiento_identificacion', $vig], ['status_bys' => 'VALIDADO']])->asArray()->all();

            foreach ($dat as $INE) {
                if ($INE && isset($INE['vencimiento_identificacion']) && !empty($INE['vencimiento_identificacion'])) {
                    $expl = explode('-', $INE['vencimiento_identificacion']);
                    if ($type == 30) {
                        if ($expl[0] <= date('Y')) {
                            $expDoc = new models\ExpirationDocuments();
                            $expDoc->provider_id = $d['provider_id'];
                            $expDoc->module = 'representante_legal';
                            $expDoc->column_name = 'INE';
                            $expDoc->register_id = $INE['representante_legal_id'];
                            $expDoc->msg = 'Modulo proximo a vencer en (30 días)';
                            $expDoc->expitation_date = date('Y') . '-12-31';
                            $expDoc->type = "30 DIAS";
                            $expDoc->save();
                        }
                    } else if ($type == 15) {
                        if ($expl[0] <= date('Y')) {
                            $expDoc = new models\ExpirationDocuments();
                            $expDoc->provider_id = $d['provider_id'];
                            $expDoc->module = 'representante_legal';
                            $expDoc->column_name = 'INE';
                            $expDoc->register_id = $INE['representante_legal_id'];
                            $expDoc->msg = 'Modulo proximo a vencer en (15 días)';
                            $expDoc->expitation_date = date('Y') . '-12-31';
                            $expDoc->type = "15 DIAS";
                            $expDoc->save();
                        }
                    } else if ($type == 0) {
                        if ($expl[0] < date('Y')) {
                            $expDoc = new models\ExpirationDocuments();
                            $expDoc->provider_id = $d['provider_id'];
                            $expDoc->module = 'representante_legal';
                            $expDoc->column_name = 'INE';
                            $expDoc->register_id = $INE['representante_legal_id'];
                            $expDoc->msg = 'Modulo vencido';
                            $expDoc->expitation_date = (intval(date('Y')) - 1) . '-12-31';
                            $expDoc->type = "EXPIRED";
                            $expDoc->save();
                            if (models\RepresentanteLegal::updateAll(['status_bys' => 'RECHAZADO'], ['representante_legal_id' => $INE['representante_legal_id']])) {
                                $rech = new Status();
                                $rech->modelo = 'representante_legal';
                                $rech->register_id = $INE['representante_legal_id'];
                                $rech->created_id = 168;
                                $rech->motivo = "ESTIMADO INTERESADO:<br>
                                CON EL FIN DE MANTENER SU REGISTRO ACTIVO EN EL PADRON DE PROVEEDORES, LO INVITAMOS A ACTUALIZAR LA SIGUIENTE INFORMACIÓN,<br>
                                REPRESENTANTE LEGAL IDENTIFICACIÓN OFICIAL INE";
                                $rech->status_bys = 'PENDIENTE';
                                $rech->expiration = true;
                                $rech->save();
                            }
                        }
                    }
                }
            }
        }
    }

    public function actionVerifyExpirationPasaporteRp()
    {
        $data = Yii::$app->db->createCommand("with ct as(select distinct
                historico_certificados_id,provider_id, created_at,
                row_number() over (partition by provider_id order by created_at desc) rn
                from
                historico_certificados where tipo ='CERTIFICADO' and provider_type = 'bys' order by created_at
                )
                select
                ct.provider_id
                from
                ct
                where
                rn = 1")->queryAll();

        $dateNow = date('Y-m-d');

        foreach ($data as $d) {

            $dat = models\RepresentanteLegal::find()->select(['representante_legal_id', 'vencimiento_identificacion'])
                ->where(['and', ['provider_id' => $d['provider_id']], ['tipo_identificacion' => 'PASAPORTE'], ['activo' => true], ['status_bys' => 'VALIDADO']])->asArray()->all();
            foreach ($dat as $PasaPorte) {
                if ($PasaPorte && isset($PasaPorte['vencimiento_identificacion']) && !empty($PasaPorte['vencimiento_identificacion'])) {
                    $fechaUp = $PasaPorte['vencimiento_identificacion'];
                    $fecha_actual = new \DateTime($dateNow);
                    $fecha_final = new \DateTime($fechaUp);
                    $dias = $fecha_actual->diff($fecha_final)->format('%r%a');

                    if ($dias == 30) {
                        if ((models\ExpirationDocuments::find()->where(['and', ['register_id' => $PasaPorte['representante_legal_id']], ['type' => '30 DIAS'], ['module' => 'representante_legal'], ['expitation_date' => $fechaUp], ['permanent' => false], ['column_name' => 'Pasaporte']])->count('1')) == 0) {
                            $expDoc = new models\ExpirationDocuments();
                            $expDoc->provider_id = $d['provider_id'];
                            $expDoc->module = 'representante_legal';
                            $expDoc->column_name = 'Pasaporte';
                            $expDoc->register_id = $PasaPorte['representante_legal_id'];
                            $expDoc->msg = 'Modulo proximo a vencer en (30 días)';
                            $expDoc->expitation_date = $fechaUp;
                            $expDoc->type = "30 DIAS";
                            $expDoc->save();
                        }
                    } else if ($dias == 15) {
                        if ((models\ExpirationDocuments::find()->where(['and', ['register_id' => $PasaPorte['representante_legal_id']], ['type' => '15 DIAS'], ['module' => 'representante_legal'], ['expitation_date' => $fechaUp], ['permanent' => false], ['column_name' => 'Pasaporte']])->count('1')) == 0) {
                            $expDoc = new models\ExpirationDocuments();
                            $expDoc->provider_id = $d['provider_id'];
                            $expDoc->module = 'representante_legal';
                            $expDoc->column_name = 'Pasaporte';
                            $expDoc->register_id = $PasaPorte['representante_legal_id'];
                            $expDoc->msg = 'Modulo proximo a vencer en (15 días)';
                            $expDoc->expitation_date = $fechaUp;
                            $expDoc->type = "15 DIAS";
                            $expDoc->save();
                        }
                    } else if ($dias == 0 || $dias < -366) {
                        if ((models\ExpirationDocuments::find()->where(['and', ['register_id' => $PasaPorte['representante_legal_id']], ['type' => 'EXPIRED'], ['module' => 'representante_legal'], ['expitation_date' => $fechaUp], ['permanent' => false], ['column_name' => 'Pasaporte']])->count('1')) == 0) {
                            $expDoc = new models\ExpirationDocuments();
                            $expDoc->provider_id = $d['provider_id'];
                            $expDoc->module = 'representante_legal';
                            $expDoc->column_name = 'Pasaporte';
                            $expDoc->register_id = $PasaPorte['representante_legal_id'];
                            $expDoc->msg = 'Modulo vencido';
                            $expDoc->expitation_date = $fechaUp;
                            $expDoc->type = "EXPIRED";
                            $expDoc->save();
                            if (models\RepresentanteLegal::updateAll(['status_bys' => 'RECHAZADO'], ['provider_id' => $PasaPorte['representante_legal_id']])) {
                                $rech = new Status();
                                $rech->modelo = 'representante_legal';
                                $rech->register_id = $PasaPorte['representante_legal_id'];
                                $rech->created_id = 168;
                                $rech->motivo = "ESTIMADO INTERESADO:<br>
                            CON EL FIN DE MANTENER SU REGISTRO ACTIVO EN EL PADRON DE PROVEEDORES, LO INVITAMOS A ACTUALIZAR LA SIGUIENTE INFORMACIÓN,<br>
                            REPRESENTANTE LEGAL IDENTIFICACIÓN OFICIAL PASAPORTE";
                                $rech->status_bys = 'PENDIENTE';
                                $rech->expiration = true;
                                $rech->save();
                            }
                        }
                    }
                }
            }

        }
    }

    public function actionVerifyExpirationIneJunioRp($type)
    {
        /* $data = Yii::$app->db->createCommand("with ct as(select distinct
                historico_certificados_id,provider_id, created_at,
                row_number() over (partition by provider_id order by created_at desc) rn
                from
                historico_certificados where tipo ='CERTIFICADO' and provider_type = 'bys' order by created_at
                )
                select
                ct.provider_id
                from
                ct
                where
                rn = 1")->queryAll();

        foreach ($data as $d) {

            $dat = models\RepresentanteLegal::find()->select(['representante_legal_id', 'vencimiento_identificacion'])
                ->where(['and', ['provider_id' => $d['provider_id']], ['tipo_identificacion' => 'IFE'], ['activo' => true], ['<', 'vencimiento_identificacion', '2021-01-01'], ['status_bys' => 'VALIDADO']])->asArray()->all();

            foreach ($dat as $INE) {
                if ($INE && isset($INE['vencimiento_identificacion']) && !empty($INE['vencimiento_identificacion'])) {
                    if ($type == 30) {
                        $expDoc = new models\ExpirationDocuments();
                        $expDoc->provider_id = $d['provider_id'];
                        $expDoc->module = 'representante_legal';
                        $expDoc->column_name = 'INE';
                        $expDoc->register_id = $INE['representante_legal_id'];
                        $expDoc->msg = 'Modulo proximo a vencer en (30 días)';
                        $expDoc->expitation_date = '2021-06-07';
                        $expDoc->type = "30 DIAS";
                        $expDoc->save();
                    } else if ($type == 15) {
                        $expDoc = new models\ExpirationDocuments();
                        $expDoc->provider_id = $d['provider_id'];
                        $expDoc->module = 'representante_legal';
                        $expDoc->column_name = 'INE';
                        $expDoc->register_id = $INE['representante_legal_id'];
                        $expDoc->msg = 'Modulo proximo a vencer en (15 días)';
                        $expDoc->expitation_date = '2021-06-07';
                        $expDoc->type = "15 DIAS";
                        $expDoc->save();
                    } else if ($type == 0) {
                        $expDoc = new models\ExpirationDocuments();
                        $expDoc->provider_id = $d['provider_id'];
                        $expDoc->module = 'representante_legal';
                        $expDoc->column_name = 'INE';
                        $expDoc->register_id = $INE['representante_legal_id'];
                        $expDoc->msg = 'Modulo vencido';
                        $expDoc->expitation_date = '2021-06-07';
                        $expDoc->type = "EXPIRED";
                        $expDoc->save();
                        if (models\RepresentanteLegal::updateAll(['status_bys' => 'RECHAZADO'], ['representante_legal_id' => $INE['representante_legal_id']])) {
                            $rech = new Status();
                            $rech->modelo = 'representante_legal';
                            $rech->register_id = $INE['representante_legal_id'];
                            $rech->created_id = 168;
                            $rech->motivo = "ESTIMADO INTERESADO:<br>
                            CON EL FIN DE MANTENER SU REGISTRO ACTIVO EN EL PADRON DE PROVEEDORES, LO INVITAMOS A ACTUALIZAR LA SIGUIENTE INFORMACIÓN,<br>
                            REPRESENTANTE LEGAL IDENTIFICACIÓN OFICIAL INE";
                            $rech->status_bys = 'PENDIENTE';
                            $rech->expiration = true;
                            $rech->save();
                        }
                    }
                }
            }

        } */
    }


    public function actionVerifyExpirationPermisos()
    {
        /* $data = Yii::$app->db->createCommand("with ct as(select distinct
                historico_certificados_id,provider_id, created_at,
                row_number() over (partition by provider_id order by created_at desc) rn
                from
                historico_certificados where tipo ='CERTIFICADO' and provider_type = 'bys' order by created_at
                )
                select
                ct.provider_id
                from
                ct
                where
                rn = 1")->queryAll();

        $dateNow = date('Y-m-d');

        foreach ($data as $d) {

            $dat = models\Certificacion::find()->select(['certificacion_id', 'vigencia'])->where(['and', ['provider_id' => $d['provider_id']], ['undefined' => false], ['activo' => true], ['status_bys' => 'VALIDADO']])->asArray()->all();
            foreach ($dat as $cert) {
                $fechaUp = $cert['vigencia'];
                $fecha_actual = new \DateTime($dateNow);
                $fecha_final = new \DateTime($fechaUp);
                $dias = $fecha_actual->diff($fecha_final)->format('%r%a');

                if ($dias == 30) {
                    if ((models\ExpirationDocuments::find()->where(['and', ['register_id' => $cert['certificacion_id']], ['type' => '30 DIAS'], ['module' => 'certificacion'], ['expitation_date' => $fechaUp], ['permanent' => false], ['column_name' => 'Permiso']])->count('1')) == 0) {
                        $expDoc = new models\ExpirationDocuments();
                        $expDoc->provider_id = $d['provider_id'];
                        $expDoc->module = 'certificacion';
                        $expDoc->column_name = 'Permiso';
                        $expDoc->register_id = $cert['certificacion_id'];
                        $expDoc->msg = 'Modulo proximo a vencer en (30 días)';
                        $expDoc->expitation_date = $fechaUp;
                        $expDoc->type = "30 DIAS";
                        $expDoc->save();
                    }
                } else if ($dias == 15) {
                    if ((models\ExpirationDocuments::find()->where(['and', ['register_id' => $cert['certificacion_id']], ['type' => '15 DIAS'], ['module' => 'certificacion'], ['expitation_date' => $fechaUp], ['permanent' => false], ['column_name' => 'Permiso']])->count('1')) == 0) {
                        $expDoc = new models\ExpirationDocuments();
                        $expDoc->provider_id = $d['provider_id'];
                        $expDoc->module = 'certificacion';
                        $expDoc->column_name = 'Permiso';
                        $expDoc->register_id = $cert['certificacion_id'];
                        $expDoc->msg = 'Modulo proximo a vencer en (15 días)';
                        $expDoc->expitation_date = $fechaUp;
                        $expDoc->type = "15 DIAS";
                        $expDoc->save();
                    }
                } else if ($dias == 0 || $dias < -366) {
                    if ((models\ExpirationDocuments::find()->where(['and', ['register_id' => $cert['certificacion_id']], ['type' => 'EXPIRED'], ['module' => 'certificacion'], ['expitation_date' => $fechaUp], ['permanent' => false], ['column_name' => 'Permiso']])->count('1')) == 0) {
                        $expDoc = new models\ExpirationDocuments();
                        $expDoc->provider_id = $d['provider_id'];
                        $expDoc->module = 'certificacion';
                        $expDoc->column_name = 'Permiso';
                        $expDoc->register_id = $cert['certificacion_id'];
                        $expDoc->msg = 'Modulo vencido';
                        $expDoc->expitation_date = $fechaUp;
                        $expDoc->type = "EXPIRED";
                        $expDoc->save();
                        if (models\Certificacion::updateAll(['status_bys' => 'RECHAZADO'], ['certificacion_id' => $cert['certificacion_id']])) {
                            $rech = new Status();
                            $rech->modelo = 'certificacion';
                            $rech->register_id = $cert['certificacion_id'];
                            $rech->created_id = 168;
                            $rech->motivo = "ESTIMADO INTERESADO:<br>
                            CON EL FIN DE MANTENER SU REGISTRO ACTIVO EN EL PADRON DE PROVEEDORES, LO INVITAMOS A ACTUALIZAR LA SIGUIENTE INFORMACIÓN,<br>
                            PERMISO";
                            $rech->status_bys = 'PENDIENTE';
                            $rech->expiration = true;
                            $rech->save();
                        }
                    }
                }
            }

        } */
    }

    public static function compareModels($model_old, $model_new = null, $id = null, $model_name = null, $provider_id = null, $trueFalse = true)
    {

        $all_modifi = [];
        $user_id = \Yii::$app->user->getId();
        $created_at = date('Y-m-d H:i:s');
        $id_new = '';
        $id_old = '';

        $modName = self::getModeloAndName($model_name);
        $modelNameTags = $modName['modulo'];
        $modelNamePro = $modName['name'];
        $models_comment = self::getVisibleColumnsAndTags($modelNameTags);
        $model_name = $model_name == 'giro' ? 'alta_hacienda' : $model_name;
        foreach ($model_old as $key => $value) {
            if (!in_array($key, ['status_bys', 'status_op', 'geo_ubicacion', 'expirado'])) {
                if ($model_new[$key] != $value) {
                    $value_old = $value;
                    $value_new = $model_new[$key];
                    $exist = self::verify_id($key);
                    if ($exist) {
                        $id_new = $value_new;
                        $id_old = $value_old;
                        $data_search = self::getNameById($exist, $value_old, $value_new);
                        $value_old = $data_search['old_name'];
                        $value_new = $data_search['new_name'];
                    }

                    $movimiento = 'Actualización de ' . $modelNamePro;
                    $comment_name = $models_comment[$key];
                    $all_modifi[] = [
                        'created_by' => $user_id,
                        'provider_id' => $provider_id,
                        'model' => $model_name,
                        'action' => $movimiento,
                        'origin_id' => $id,
                        'column' => $comment_name,
                        'column_data_old' => $value_old,
                        'column_date_new' => $value_new,
                        'created_at' => $created_at,
                        'column_data_id_old' => $id_old,
                        'column_data_id_new' => $id_new,
                        'certificate' => $trueFalse,
                        'full_model' => false,
                        'data_model' => null
                    ];
                }
            }
        }

        if ($all_modifi) {
            $status = self::insertAllMovements($all_modifi, 'multiple');
        } else {
            $status = true;
        }

        return $status;

    }


    public function getModeloAndName($modelo)
    {

        $modulo = ['modulo' => '', 'name' => ''];
        switch ($modelo) {
            case 'bys_perfil':
                $modulo = ['modulo' => 'bys_perfil', 'name' => 'Mi perfil'];
                break;
            case 'bys_legales':
                $modulo = ['modulo' => 'bys_legales', 'name' => 'Legales'];
                break;
            case 'bys_bys':
                $modulo = ['modulo' => 'bys_bys', 'name' => 'Bienes y/o Servicios'];
                break;
            case 'bys_experiencia':
                $modulo = ['modulo' => 'bys_experiencia', 'name' => 'Experiencia Comercial'];
                break;
            case 'bys_economica':
                $modulo = ['modulo' => 'bys_economica', 'name' => 'Capacidad Económica'];
                break;
            case 'bys_domicilio':
                $modulo = ['modulo' => 'bys_domicilio', 'name' => 'Establecimientos'];
                break;
            case 'bys_bancos':
                $modulo = ['modulo' => 'bys_bancos', 'name' => 'Datos Bancarios'];
                break;



            case 'cotejar':
                $modulo = ['modulo' => 'cotejar', 'name' => 'Cotejo'];
                break;
            case 'perfil_provider':
                $modulo = ['modulo' => 'Provider', 'name' => 'Perfil Información'];
                break;
            case 'perfil':
                $modulo = ['modulo' => 'Provider', 'name' => 'Perfil Información'];
                break;
            case 'perfil_rfc':
                $modulo = ['modulo' => 'Rfc', 'name' => 'Perfil Información'];
                break;
            case 'perfil_ubicacion':
                $modulo = ['modulo' => 'Ubicacion', 'name' => 'Perfil Información'];
                break;
            case 'perfil_comprobante_domicilio':
                $modulo = ['modulo' => 'ComprobanteDomicilio', 'name' => 'Perfil Información'];
                break;
            case 'rfc':
                $modulo = ['modulo' => 'Rfc', 'name' => 'Datos legales'];
                break;
            case 'rfc_economica':
                $modulo = ['modulo' => 'Rfc', 'name' => 'Actividad economica'];
                break;
            case 'rfc_acta_constitutiva':
                $modulo = ['modulo' => 'ActaConstitutiva', 'name' => 'Datos legales'];
                break;
            case 'rfc_id_oficial':
                $modulo = ['modulo' => 'IdOficial', 'name' => 'Datos legales'];
                break;
            case 'rfc_comprobante_domicilio':
                $modulo = ['modulo' => 'ComprobanteDomicilio', 'name' => 'Datos legales'];
                break;
            case 'comprobante_domicilio':
                $modulo = ['modulo' => 'ComprobanteDomicilio', 'name' => 'Perfil Información'];
                break;
            case 'rfc_curp':
                $modulo = ['modulo' => 'Curp', 'name' => 'Datos legales'];
                break;
            case 'alta_hacienda':
                $modulo = ['modulo' => 'AltaHacienda', 'name' => 'Actividad económica'];
                break;
            case 'giro':
                $modulo = ['modulo' => 'Giro', 'name' => 'Actividad económica'];
                break;
            case 'relacion_accionistas':
                $modulo = ['modulo' => 'RelacionAccionistas', 'name' => 'Relación accionistas'];
                break;
            case 'intervencion_bancaria':
                $modulo = ['modulo' => 'IntervencionBancaria', 'name' => 'Método de pago'];
                break;
            case 'ultima_declaracion':
                $modulo = ['modulo' => 'UltimaDeclaracion', 'name' => 'Datos financieros'];
                break;
            case 'certificacion':
                $modulo = ['modulo' => 'Certificacion', 'name' => 'Permisos'];
                break;
            case 'ubicacion':
                $modulo = ['modulo' => 'Ubicacion', 'name' => 'Ubicación'];
                break;
            case 'direccion_nl':
                $modulo = ['modulo' => 'Ubicacion', 'name' => 'Direccion Nl'];
                break;
            case 'fotografia_negocio':
                $modulo = ['modulo' => 'FotografiaNegocio', 'name' => 'Fotografía negocio'];
                break;
            case 'clientes_contratos':
                $modulo = ['modulo' => 'ClientesContratos', 'name' => 'Experiencia comercial'];
                break;
            case 'modificacion_acta':
                $modulo = ['modulo' => 'ModificacionActa', 'name' => 'Modificación acta'];
                break;

            case 'representante_legal':
                $modulo = ['modulo' => 'RepresentanteLegal', 'name' => 'Representante legal'];
                break;
            case 'historico_carta_protesta':
                $modulo = ['modulo' => 'historico_carta_protesta', 'name' => 'Carta protesta'];
                break;

            case 'estado_financiero':
                $modulo = ['modulo' => 'estado_financiero', 'name' => 'Estado Financiero'];
                break;
            case 'experiencia':
                $modulo = ['modulo' => 'experiencia', 'name' => 'Experiencia'];
                break;
            case 'maquinaria_equipos':
                $modulo = ['modulo' => 'maquinaria_equipos', 'name' => 'Maquinaria y Equipos'];
                break;
            case 'personal_tecnico':
                $modulo = ['modulo' => 'personal_tecnico', 'name' => 'Personal Técnico'];
                break;
            case 'organigrama':
                $modulo = ['modulo' => 'organigrama', 'name' => 'Organigrama'];
                break;
        }

        return $modulo;

    }

    public function getModeloAndNameMovements($modelo)
    {

        $modulo = ['modulos' => []];
        switch ($modelo) {
            case 'perfil':
                $modulo = ['modulos' => ['perfil_provider', 'perfil_rfc', 'perfil_ubicacion', 'perfil_comprobante_domicilio']];
                break;
            case 'rfc':
                $modulo = ['modulos' => ['rfc', 'rfc_acta_constitutiva', 'rfc_id_oficial', 'rfc_comprobante_domicilio', 'rfc_curp']];
                break;
            case 'alta_hacienda':
                $modulo = ['modulos' => ['rfc_economica', 'alta_hacienda']];
                break;
            case 'relacion_accionistas':
                $modulo = ['modulos' => ['relacion_accionistas']];
                break;
            case 'intervencion_bancaria':
                $modulo = ['modulos' => ['intervencion_bancaria']];
                break;
            case 'ultima_declaracion':
                $modulo = ['modulos' => ['ultima_declaracion']];
                break;
            case 'certificacion':
                $modulo = ['modulos' => ['certificacion']];
                break;
            case 'ubicacion':
                $modulo = ['modulos' => ['ubicacion']];
                break;
            case 'direccion_nl':
                $modulo = ['modulos' => ['direccion_nl']];
                break;
            case 'fotografia_negocio':
                $modulo = ['modulos' => ['fotografia_negocio']];
                break;
            case 'clientes_contratos':
                $modulo = ['modulos' => ['clientes_contratos']];
                break;
            case 'modificacion_acta':
                $modulo = ['modulos' => ['modificacion_acta']];
                break;

            case 'representante_legal':
                $modulo = ['modulos' => ['representante_legal']];
                break;
            case 'historico_carta_protesta':
                $modulo = ['modulos' => ['historico_carta_protesta']];
                break;
        }

        return $modulo;

    }

    public static function insertAllMovements($model_insert, $tipo = null)
    {
        if ($tipo == null) {
            $user_id = \Yii::$app->user->getId();

            $model = new models\Movements();
            $model->load($model_insert, '');
            $model->created_by = $user_id;
            $model->created_at = date('Y-m-d H:i:s');
            $status = $model->save(false);
        } else {
            $status = \Yii::$app->db->createCommand()
                ->batchInsert('movements',
                    ['created_by', 'provider_id', 'model', 'action', 'origin_id',
                        'column', 'column_data_old', 'column_date_new', 'created_at',
                        'column_data_id_old', 'column_data_id_new', 'certificate', 'full_model', 'data_model'], $model_insert)->execute();
        }

        return $status;
    }


    public static function getVisibleColumnsAndTags($model)
    {
        $model_name = 'app\models\\' . $model;
        /**
         * @var $model ActiveRecord
         */
        $model = new $model_name();
        /**
         * @var $schema TableSchema
         */
        $schema = $model_name::getTableSchema();
        $ids = $schema->primaryKey;
        $cols = $schema->columns;
        $visible_columns = [];

        foreach ($cols as $col) {
            $label = $model->getAttributeLabel($col->name);
            $visible_columns[$col->name] = trim($label);
        }

        return $visible_columns;
    }


    public static function verify_id($data)
    {
        $data_return = '';
        $array = [
            'banco_id' => ['table_name' => 'bancos', 'campo' => 'banco_id', 'campo_search' => 'banco'],
            'created_by' => ['table_name' => 'principal', 'campo' => 'principal_id', 'campo_search' => 'first_name'],
            'client_id' => ['table_name' => 'client', 'campo' => 'client_id', 'campo_search' => 'name'],
            'structure_id' => ['table_name' => 'structure', 'campo' => 'structure_id', 'campo_search' => 'description'],
            'city' => ['table_name' => 'city', 'campo' => 'city_id', 'campo_search' => 'name'],
            'suburb' => ['table_name' => 'suburb', 'campo' => 'suburb_id', 'campo_search' => 'name'],
            'state' => ['table_name' => 'state', 'campo' => 'state_id', 'campo_search' => 'name'],
            'position_id' => ['table_name' => 'position', 'campo' => 'position_id', 'campo_search' => 'identifier'],
            'concept_id' => ['table_name' => 'concept', 'campo' => 'concept_id', 'campo_search' => 'concept'],
            'type_pay_id' => ['table_name' => 'type_pay', 'campo' => 'type_pay_id', 'campo_search' => 'type_pay'],
            'origin_padre_id' => ['table_name' => 'origin', 'campo' => 'origin_id', 'campo_search' => 'name'],
            'state_id' => ['table_name' => 'state', 'campo' => 'state_id', 'campo_search' => 'name'],
            'principal_id' => ['table_name' => 'principal', 'campo' => 'principal_id', 'campo_search' => 'first_name'],
        ];

        if (isset($array[$data])) {
            $data_return = $array[$data];
        }

        return $data_return;
    }


    public static function getNameById($arr_searh, $old_id, $new_id)
    {
        $old_id = !empty($old_id) ? intval($old_id) : 0;
        $new_id = !empty($new_id) ? intval($new_id) : 0;
        $tabla = $arr_searh['table_name'];
        $campo = $arr_searh['campo'];
        $search = $arr_searh['campo_search'];

        $arr_return = \Yii::$app->db->createCommand("select (select $search from $tabla where $campo = $old_id) as old_name,
          (select $search from $tabla where $campo = $new_id) as new_name")->queryOne();

        return $arr_return;

    }

    public function verifyProviderCert($p, $pt)
    {

        return Yii::$app->db->createCommand("select count(1) from historico_certificados where provider_id = :p and tipo= 'CERTIFICADO' and provider_type = :pt", [':p' => $p, ':pt' => $pt])->queryOne()['count'];

    }

    public function actionAllMovements($origin_id, $modelo)
    {
        $movements = Movements::find()->where(['and', ['model' => $modelo], ['origin_id' => $origin_id], ['first_update_certificate_id' => null], ['active' => true]])->all();

        $modelData = $this->getModeloAndName($modelo);
        return $this->renderAjax('/movements/all-movements', [
            'movements' => $movements,
            'modelData' => $modelData
        ]);
    }

    public static function verifiExistCert($origin_id, $modelo)
    {
        $movements = Movements::find()->select('certificate')->where(['and', ['model' => $modelo], ['origin_id' => $origin_id], ['active' => true]])->one()['certificate'];

        return $movements;
    }

    public static function verifiExistCertRfc($origin_id)
    {
        $movements = Movements::find()->select('certificate')->where(['and', ['in', 'model', ['rfc', 'rfc_acta_constitutiva', 'rfc_id_oficial', 'perfil_comprobante_domicilio', 'rfc_curp']], ['origin_id' => $origin_id], ['active' => true]])->one()['certificate'];

        return $movements;
    }

    public function actionAllMovementsRfc($origin_id)
    {
        $movements = Movements::find()->where(['and', ['in', 'model', ['rfc', 'rfc_acta_constitutiva', 'rfc_id_oficial', 'perfil_comprobante_domicilio', 'rfc_curp']], ['origin_id' => $origin_id], ['first_update_certificate_id' => null], ['active' => true]])->all();

        $modelData = $this->getModeloAndName('rfc');
        return $this->renderAjax('/movements/all-movements', [
            'movements' => $movements,
            'modelData' => $modelData
        ]);
    }

    public function countAllMovements($origin_id, $module)
    {
        return Movements::find()->where(['and', ['model' => $module], ['origin_id' => $origin_id], ['first_update_certificate_id' => null], ['active' => true]])->count('1');
    }


    public function countAllMovementRfc($origin_id)
    {
        return Movements::find()->where(['and', ['in', 'model', ['rfc', 'rfc_acta_constitutiva', 'rfc_id_oficial', 'perfil_comprobante_domicilio', 'rfc_curp']], ['origin_id' => $origin_id], ['first_update_certificate_id' => null], ['active' => true]])->count('1');
    }

    public static function verifyValRec($model, $register)
    {

        return Yii::$app->db->createCommand("
                        with a as(
                        select count(1) as total from provider.status where register_id = :id and modelo = :m and status_bys in('PENDIENTE','TERMINADO','TERMINADO PRO')
                        union
                        select count(1) as total from provider.datos_validados where register_id = :id and modelo = :m and status_bys = 'VALIDADO')
                        select sum(total) as total from a;", [':id' => $register, ':m' => $model])->queryOne()['total'];

    }

    public static function verifyNotModal($data)
    {

        $statusType = 'status_' . Yii::$app->user->identity->tipo;
        try {
            return Yii::$app->db->createCommand("select provider_id from provider.$data->modelo where $data->column_search = $data->id and activo = true
        and $statusType in('EN EDICION','RECHAZADO','VALIDADO')")->queryOne()['provider_id'];
        } catch (\Exception $e) {

        }

    }


    public function actionCertificate($provider_id = null, $type = null,$provider_type='bys')
    {

        if(Yii::$app->request->isGet){
            if ($provider_id) {

                $visit = Visit::find()->where(['and', ['provider_id' => $provider_id], ['status' => true],['provider_type' => $provider_type]])->asArray()->all();
    
                if ($visit) {
                    foreach ($visit as $v) {
                        if ($v['modify']) {
                            Visit::updateAll(['status' => false], ['visit_id' => $v['visit_id']]);
                        } else {
                            VisitDetails::deleteAll(['visit_id' => $v['visit_id']]);
                            Visit::deleteAll(['visit_id' => $v['visit_id']]);
                        }
    
                    }
                }
    
    
                if($provider_type=='bys'){
                    $date = DatosValidados::find()->select(['created_date'])->where(['and', ['provider_id' => $provider_id], ['status_bys' => 'VALIDADO']])->orderBy(['validado_id' => SORT_DESC])->limit(1)->one()['created_date'];
                    $hcId = $this->documentocertificado($provider_id, $provider_type, base64_encode($date));
                    $providerData = Provider::find()->where(['provider_id' => $provider_id])->one();
                    $statusCot = 'VALIDADO';
                    $hcDateOld = Yii::$app->db->createCommand("select * from historico_certificados
                                            where provider_id  = $provider_id and tipo = 'CERTIFICADO' and provider_type = 'bys' and historico_certificados_id not in($hcId)
                                                order by historico_certificados_id DESC limit 1")->queryOne();
                    $first_update_certificate = new FirstUpdateCertificate();
                    $first_update_certificate->provider_id = $provider_id;
                    $first_update_certificate->update_certificate = true;
                    $first_update_certificate->historico_certificado_id = $hcId;
                    $first_update_certificate->date_old_certificate = $hcDateOld['created_at'];
                    $first_update_certificate->save();
    
                    Movements::updateAll(['first_update_certificate_id' => $first_update_certificate->first_update_certificate_id], ['and', ['provider_id' => $provider_id], ['first_update_certificate_id' => null], ['certificate' => true]]);
                    Provider::updateAll(['status_cotejar' => $statusCot], ['provider_id' => $provider_id]);
                    models\Calendar::updateAll(['status' => 'FINALIZADA'], ['and', ['user' => $providerData->user_id], ['status' => 'CONFIRMADA']]);
                }else{
                    $hcId = $this->documentocertificado($provider_id, $provider_type);
                }
    
    
                self::AllSendNotification($providerData->user_id, $provider_type, null, 'CERTIFICADO', null, '¡Felicidades!', 'PROVIDER');
    
                Yii::$app->session->setFlash('success', 'Certificado de actualización Generado exitosamente!!!');
            }
            if ($type == 'visit') {
                $urlRetorn = '/visit/index';
            } else {
                $urlRetorn = '/provider/index-provider-cotejar';
            }
            return $this->redirect($urlRetorn);
        }
        
    }



    public function actionCertificateOp()
    {


        $data = ArrayHelper::getColumn(Yii::$app->db->createCommand("select distinct provider_id from visit where provider_type = 'op' and status is false")->queryAll(),'provider_id');

        foreach ($data as $provider_id){
            if((HistoricoCertificados::find()->where(['and',['provider_id' => $provider_id],['tipo' => 'CERTIFICADO'],['provider_type' => 'op']])->count())==0){
                $hcId = $this->documentocertificado($provider_id, 'op');
            }
        }
    }



    public function actionVisitGen()
    {

        $prov = Visit::find()->asArray()->all();
        foreach ($prov as $v) {
            $viD = ArrayHelper::getColumn(VisitDetails::find()->where(['visit_id' => $v['visit_id']])->all(), 'visit_details_id');
            if ($viD) {
                VisitDetails::updateAll(['fecha_inspeccion' => $v['fecha_inspeccion'], 'hora_entrada' => $v['hora_entrada'], 'hora_salida' => $v['hora_salida']], ['in', 'visit_details_id', $viD]);
            }
        }

    }


    public function getResponsabilidades()
    {
        return \yii\helpers\ArrayHelper::map(Yii::$app->user->getIdentity()->responsabilidades, 'responsabilidad_id', 'descripcion');
    }

    /**
     * Devuelve un arreglo con valores unicos de acuerdo al atributo, relacion y alias de la consulta.
     * @param object $modelo -> Instancia del modelo al cual realizar consulta
     * @param string $atributo -> Nombre del atributo a consultar
     * @param bool $isJoin -> Marcar true si la consulta requiere de algun join, especificar $atributo en el formato relacion.atributo:alias_relacion
     * @return array
     */

    public static function obtenerValoresUnicosArray($modelo, $atributo, $isJoin)
    {
        $arregloValores = array();
        if (is_null($modelo)) {
            return $arregloValores;
        }
        if (is_null($atributo)) {
            return $arregloValores;
        }

        if ($isJoin) {
            if (self::str_contains($atributo, ':')) {
                $relacion = strtok($atributo, '.'); //Nombre registrado de la relacion en el modelo
                $nombreAtributo = self::str_contains($atributo, ':') ? strtok(substr($atributo, strpos($atributo, '.') + 1), ':') : substr($atributo, strpos($atributo, '.') + 1); //Nombre del atributo/columna
                $alias = self::str_contains($atributo, ':') ? substr($atributo, strpos($atributo, ':') + 1) : null; //alias de la relacion en caso de necesitarse
                $atributo = is_null($alias) ? $nombreAtributo : $alias . '.' . $nombreAtributo; //nombre a llamar de la propiedad
                $arrayModelo = $modelo->find()->select($atributo)->joinWith($relacion)->orderBy([$atributo => SORT_ASC])->distinct()->createCommand()->queryAll();
                return $arregloValores = \yii\helpers\ArrayHelper::map($arrayModelo, $nombreAtributo, $nombreAtributo);
            } else {
                $tabla = strtok($atributo, '.');
                $atributo = substr($atributo, strpos($atributo, '.') + 1);
                $arrayModelo = $modelo->find()->select([$atributo])->joinWith($tabla)->orderBy([$atributo => SORT_ASC])->distinct()->createCommand()->queryAll();
            }
        } else {
            $arrayModelo = $modelo->find()->select([$atributo])->orderBy([$atributo => SORT_ASC])->distinct()->createCommand()->queryAll();
        }
        $arregloValores = \yii\helpers\ArrayHelper::map($arrayModelo, $atributo, $atributo);
        return $arregloValores;
    }

    /**
     * Devuelve un arreglo de configuracion con un widget kartik\Select2
     * @param array $datos -> Arreglo de datos con la configuracion requerida del widget
     * @return array
     */

    public static function Select2Simplificado($datos)
    {

        $modelo = array_key_exists('modelo', $datos) ? $datos['modelo'] : null;
        $atributo = array_key_exists('atributo', $datos) ? $datos['atributo'] : null;
        $label = array_key_exists('label', $datos) ? $datos['label'] : null;
        $ajaxActive = array_key_exists('useAjax', $datos) ? $datos['useAjax'] : false;
        $hasJoin = array_key_exists('hasJoin', $datos) ? $datos['hasJoin'] : false;
        $valores = array_key_exists('valores', $datos) ? $datos['valores'] : false;

        if (is_null($atributo) || trim($atributo) === '') {
            throw new Exception("El nombre del atributo es necesario");
        }
        if (is_null($modelo) && !$ajaxActive) {
            throw new Exception("Al no utilizar ajax en su vista es necesario proporcionar un modelo");
        }
        if (is_null($modelo) && is_null($valores)) {
            throw new Exception("De no usar un modelo debe proporcionar un arreglo de valores");
        }

        $nombreAtributo = strtok($atributo, ':');

        $columnaOpciones = [];

        if ($ajaxActive) {
            $columnaOpciones = [
                'attribute' => $nombreAtributo,
                'label' => $label,
                'filterType' => GridView::FILTER_SELECT2,
                'filter' => $valores ? $valores : GeneralController::obtenerValoresUnicosArray($modelo, $atributo, $hasJoin),
                'filterWidgetOptions' => [
                    'id' => 'select-custom-1',
                    'theme' => Select2::THEME_BOOTSTRAP,
                    'hideSearch' => true,
                    'options' => ['placeholder' => 'Selecciona ...'],
                    'pluginOptions' => ['allowClear' => true],
                ]
            ];
        } else {
            $columnaOpciones = [
                'attribute' => $nombreAtributo,
                'label' => $label,
                'filter' => Select2::Widget([
                    'model' => $modelo,
                    'attribute' => $nombreAtributo,
                    'data' => $valores ? $valores : GeneralController::obtenerValoresUnicosArray($modelo, $atributo, $hasJoin),
                    'theme' => Select2::THEME_BOOTSTRAP,
                    'hideSearch' => true,
                    'options' => ['placeholder' => 'Selecciona ...'],
                    'pluginOptions' => ['allowClear' => true],
                ])
            ];
        }

        if (is_null($columnaOpciones['label']) || trim($columnaOpciones['label']) === '') {
            unset($columnaOpciones['label']);
        }

        return $columnaOpciones;

    }

    /**
     * Devuelve un arreglo de configuracion con un widget de tipo Select donde se necesite muchas instancias del mismo
     * @param object $modelo -> Instancia del modelo al cual realizar consulta
     * @param string $nombreAtributo -> Nombre del atributo a consultar
     * @param bool $isJoin -> Marcar true si la consulta requiere de algun join, especificar $atributo en el formato relacion.atributo:alias_relacion
     * @return array
     */

    public static function Select2TabSimplificado($modelo, $nombreAtributo, $isJoin = false){

        return [
            'attribute' => $nombreAtributo,
            'filter' => GeneralController::obtenerValoresUnicosArray($modelo, $nombreAtributo, $isJoin),
            'filterInputOptions' => ['class' => 'form-control']
        ];

    }

    public static function Select2NoModel($datos){
        $atributo = array_key_exists('atributo', $datos) ? $datos['atributo'] : null;
        $label = array_key_exists('label', $datos) ? $datos['label'] : null;
        $nombre = array_key_exists('nombre', $datos) ? $datos['nombre'] : false;
        $valores = array_key_exists('valores', $datos) ? $datos['valores'] : false;

        if (is_null($atributo) || trim($atributo) === '') {
            throw new Exception("El nombre del atributo es necesario");
        }

        return [
            'attribute' => $atributo,
            'label' => $label,
            'filter' => Select2::Widget([
                'name' => $nombre,
                'attribute' => $atributo,
                'data' => $valores,
                'value' => isset($_GET[$nombre]) ? $_GET[$nombre] : null,
                'theme' => Select2::THEME_BOOTSTRAP,
                'hideSearch' => true,
                'options' => ['placeholder' => 'Selecciona ...'],
                'pluginOptions' => ['allowClear' => true],])
        ];
    }

    public static function inputSearchFilter($atributo, $nombre, $label){
        return [
            'attribute' => $atributo,
            'label' => $label,
            'filter' => yii\helpers\Html::input('search', $nombre, isset($_GET[$nombre]) ? $_GET[$nombre] : '', ['id' => $nombre, 'class' => 'form-control'])

        ];
    }

    public function addPrefixKeyArray($arreglo, $prefix){
        $copiaArreglo = $arreglo;
        $arrayPrefix = [];
        foreach ($copiaArreglo as $key => $valor) {
            $arrayPrefix[$prefix . '.' . $key] = $valor;
        }
        return $arrayPrefix;
    }

    //Metodo ultilizado para filtrar atributos rechazados de STATUS json
    /*  function hasObjRechazo($jsonStr, $model, $id){
         try{
             if(empty($jsonStr)){ return null; }
             $arr_json = json_decode($jsonStr, true);
             $filter_array = array_filter( $arr_json, function($item) use($model, $id){
                 return ($item["modelo"] == $model && $item['id'] == $id );
             });
             $key = array_keys($filter_array);
             if(count($key) == 0){ return null; }
             else{ return $filter_array[$key[0]]; }
         }catch(Exception $e){ return null; }
     } */

    static function hasObjRechazo($jsonStr, $model, $id){
        try {
            if (empty($jsonStr)) {
                return false;
            }
            $arr_json = json_decode($jsonStr, true);
            $filter_array = array_filter($arr_json, function ($item) use ($model, $id) {
                return ($item["modelo"] == $model && $item['id'] == $id);
            });
            if (count($filter_array) == 0) {
                return false;
            } else {
                return true;
            }
        } catch (Exception $e) {
            return false;
        }
    }

    static function hasAtrRechazo($jsonStr, $model, $id, $atributo){
        try {
            if (empty($jsonStr)) {
                return false;
            }
            if (empty($id)) {
                return false;
            }
            $arr_json = json_decode($jsonStr, true);
            $filter_array = array_filter($arr_json, function ($item) use ($model, $id) {
                return ($item["modelo"] == $model && $item['id'] == $id);
            });
            $key = array_keys($filter_array);
            if (count($key) == 0) {
                return false;
            }
            return in_array($atributo, $filter_array[$key[0]]['atributos']);
        } catch (Exception $e) {
            return false;
        }
    }

    /**
     * Codigo de str_contains de PHP 8 para la version 7
     * @return bool
     */

    public static function str_contains(string $haystack=null, string $needle): bool{
        if(is_null($haystack)) return false;
        return '' === $needle || false !== strpos($haystack, $needle);
    }

    public static function arr_contains(array $haystack, array $needle) : bool {
        if(empty($haystack)){ return false; }
        foreach($haystack as $elem){
            if( in_array($elem, $needle) ) { return true; }
        }

        return false;
    }

    public static function limpiarUrl( $url = ''){
        return str_replace("&", "%26",$url);
    }

    public static function obtenerModulosPorTitulo($titulos){
        $modulos = [];
        $interpretaciones = [
            "Perfil" => "Mi perfil",
            "Legales" => "Legales",
            "BYS" => "Bienes y/o Servicios",
            "Economica" => "Capacidad Económica",
            "Experiencia" => "Experiencia Comercial",
            "Domicilio" => "Domicilio",
            "Banco" => "Datos bancarios",
            /* "Carta" => "Carta protesta" */
        ];
        foreach ($titulos as $titulo) {
            $valor = array_filter($interpretaciones, function ($valor) use ($titulo) {
                return $valor == $titulo;
            });
            $modulos = array_merge($modulos, $valor);
        }
        return array_keys($modulos);
    }

    public function getModuloAtributos($nombre_modulo, $tipo_persona){
        $atributos_modulo = null;
        switch ($nombre_modulo) {
            case "bys_perfil":
                $atributos_modulo = $tipo_persona == 'Persona moral' ? Modulos::MODULO_PERFIL_MORAL : Modulos::MODULO_PERFIL_FISICA;
                break;
            case "bys_legales" :
                $atributos_modulo = $tipo_persona == 'Persona moral' ? Modulos::MODULO_MORAL_LEGALES : Modulos::MODULO_FISICO_LEGALES;
                break;
            case "bys_bys" :
                $atributos_modulo = Modulos::MODULO_BIENES_SERVICIOS;
                break;
            case "bys_economica" :
                $atributos_modulo = Modulos::MODULO_CAPACIDAD_ECONOMICA;
                break;
            case "bys_experiencia" :
                $atributos_modulo = Modulos::MODULO_EXPERIENCIA_COMERCIAL;
                break;
            case "bys_domicilio" :
                $atributos_modulo = Modulos::MODULO_DOMICILIO;
                break;
            case "bys_bancos" :
                $atributos_modulo = Modulos::MODULO_DATOS_BANCARIOS;
                break;
        }
        return $atributos_modulo;
    }

    public static function defaultModulosOpciones(){
        return [
            'bys_legales' =>[
                "ModificacionActa" => [ "opciones" => ["and", ["activo" => true]] ],
                "RepresentanteLegal" => [ "opciones" => ["and", ["activo" => true]] ],
                "RelacionAccionistas" => [ "opciones" => ["and", ["activo" => true]] ]
            ],
            "bys_experiencia" => [ 
                "ClientesContratos" =>[ "opciones" => ["and", ["activo" => true], ["tipo" => "bys"] ] ],
                "Certificacion" => [ "opciones" => [ "and", ["activo" => true] ] ]
            ],
            "bys_domicilio" => [
                "Ubicacion" =>[ "opciones" => ["and", ["activo" => true], ['is', 'type_address_prov', null] ] ],
                "FotografiaNegocio" =>[ "opciones" => ["and", ["activo" => true]] ]
            ],
            "bys_bys"=>[
                "Giro" => [ "opciones" => ["and", ["active" => true]] ],
                "ProviderGiro" => [ "opciones" => ["and", ["active" => true]] ]
            ]
        ];
    }

    public function getNameModuloByKey($key){
        $arr_nombres = [
            'bys_perfil' => "Mi perfil",
            'bys_legales' => "Legales",
            'bys_bys' => "Bienes y/o Servicios",
            'bys_economica' => "Capacidad Económica",
            'bys_experiencia' => "Experiencia Comercial",
            'bys_domicilio' => "Establecimientos",
            'bys_bancos' => "Datos bancarios",
            'bys_carta' => "Carta protesta"
        ];

        return $arr_nombres[$key];
    }

    public function canEdit($prov, $model){
        $asignacion = Asignacion::find()->where(['and', ['activo' => 'true'], ['id_proveedor' => $prov]])->one();
        if($asignacion){
            return false;
        }
        $module = Module::find()->where(['provider_id' => $prov])->asArray()->all();

        $mod = array_filter($module, function ($row) use ($model) {
            return $row['model'] == $model;
        });

        if (isset(array_values($mod)[0]) && in_array(array_values($mod)[0]['status_bys'], [Status::STATUS_RECHAZADO, Status::STATUS_ENEDICION]))
            return true;
        //var_dump(json_encode(array_values($mod)[0]['status_bys'])).exit();

        foreach ($module as $m) {
            if ($m['status_bys'] == Status::STATUS_RECHAZADO) {
                return false;
            }
        }
        return true;
    }

    public static function btnEdit(Provider $provider, Module $modelStatus, $model, $title){

        $data = (object)[
            'dToggle' => "",
            'dPlacement' => "",
            'class' => " modalButton ",
            'title' => " $title ",
            'canEdit' => self::canEdit($provider->provider_id, $model)
        ];

        if (!$data->canEdit) {
            $data->title = 'No es posible editar, verifica en el tablero los módulos rechazados.';
            $data->dToggle = 'tooltip';
            $data->dPlacement = 'left';
            $data->class = 'disabled';
        }

        if (in_array($modelStatus->status_bys ,[ Status::STATUS_PORVALIDAR ])) {
            $data->title = 'No es posible editar, el expediente se encuentra en revisión.';
            $data->dToggle = 'tooltip';
            $data->dPlacement = 'left';
            $data->class = 'disabled';
        }

        return $data;
    }

    public static function isProduction(){
        return in_array($_SERVER['SERVER_NAME'], ['proveedores.nl.gob.mx','beta.proveedores.nl.gob.mx']);
    }

    public function actionGetAddressAws($address = null){

        return json_encode(['status' => false, 'lat' => null, 'lng' => null]);
        $lat = null;
        $lng = null;
        $status = false;
        if($address){
            $params = array(
                'credentials' => array(
                    'key' => '********************',
                    'secret' => 'KsuuBZ/ViSm0mkkb9CqzZBoEfxb59vlrEfUndIeS',
                ),
                'region' => 'us-west-2',
                'version' => 'latest'
            );

            $client = LocationServiceClient::factory($params);
            $result = $client->SearchPlaceIndexForText([
                'IndexName' => 'explore.place',
                'Text' => $address,
                "MaxResults" => 1

            ]);

            if (isset($result["Results"]) && !empty($result["Results"])) {
                $status = true;
                $lat = $result["Results"][0]['Place']['Geometry']['Point'][1];
                $lng = $result["Results"][0]['Place']['Geometry']['Point'][0];
            }
        }

        return json_encode(['status' => $status, 'lat' => $lat, 'lng' => $lng]);

    }

    public function verificarPorcentajeActividades($actividades){
        $suma_porcentaje = 0;
        foreach($actividades as $actividad){
            $suma_porcentaje += $actividad->porcentaje ? $actividad->porcentaje : 0;
        }
        return $suma_porcentaje == 100;
    }

    public function limpiezaAtributosRelacionAccionista($atributos, $valor=null){
        $response = $atributos;
        if (!empty($valor['rfc'])) {
            if ($valor['rfc'] == 'XEXX010101000') {
                if (!empty($valor['razon_social'])) {
                    $response = self::eliminarElementoPorValor($response, "nombre");
                    $response = self::eliminarElementoPorValor($response, "ap_paterno");
                    $response = self::eliminarElementoPorValor($response, "ap_materno");
                } else {
                    $response = self::eliminarElementoPorValor($response, "razon_social");
                }
            } elseif (strlen($valor['rfc']) == 13) {
                $response = self::eliminarElementoPorValor($response, "razon_social");
            } elseif (strlen($valor['rfc']) == 12) {
                $response = self::eliminarElementoPorValor($response, "nombre");
                $response = self::eliminarElementoPorValor($response, "ap_paterno");
                $response = self::eliminarElementoPorValor($response, "ap_materno");
            }
        }

        return $response; // Devuelve el conteo de atributos a ignorar
    }

    /**
     * Funcion que elimina un elemento de un arreglo unidimensional
     * @param Array $source Arreglo al cual se le hara la modificacion
     * @param String $valor Valor el cual se busca eliminar
     * @return Array Arreglo resultante de la eliminacion del elemento
     * */
    public function eliminarElementoPorValor($source, $valor){
        $response = $source;
        $key_array = array_search($valor, $source);
        if($key_array){ unset($response[$key_array]); }
        return $response;
    }

    public function porcentajeActividades($actividades){
        $suma_porcentaje = 0;
        foreach($actividades as $actividad){
            $suma_porcentaje += $actividad->porcentaje ? $actividad->porcentaje : 0;
        }
        return $suma_porcentaje;
    }

    public function actionPrevee22(){
        $data = Yii::$app->db->createCommand("select CASE WHEN (p.name_razon_social is not null and p.name_razon_social != '') then p.name_razon_social else concat_ws(' ',p.pf_nombre,p.pf_ap_paterno,p.pf_ap_materno) end as proveedor,
                                p.tipo_persona, p.rfc, STRING_AGG(d.descripcion,' | ') as divicion, upper(p.estratificacion) as estratificacion, p.creation_date as \"Día de registro\",
                                CASE when  cer.created_at is not null then 'SI' else 'NO' end \"Cuenta con certificado\",
                                cer.created_at as \"Fecha certifcado\" from provider p
                                join boletos b on b.nombre_com_tp = p.rfc
                                left join provider_giro pg on pg.provider_id = p.provider_id
                                left join productos.producto pro on pro.producto_id = pg.producto_id
                                left join productos.clase c on c.clase_id = pro.clase_id
                                left join productos.grupo g on g.grupo_id = c.grupo_id
                                left join productos.division d on d.division_id = d.division_id
                                left join (with ct as (select distinct provider_id, created_at,row_number() over (partition by provider_id order by created_at desc) rn
                                from historico_certificados  where provider_type = 'bys' and tipo='CERTIFICADO' order by provider_id)
                                select provider_id, created_at from ct
                                where rn = 1)cer on cer.provider_id = p.provider_id
                                group by proveedor,tipo_persona, p.rfc,p.estratificacion,p.creation_date,cer.created_at")->queryAll();



    }

    public function statusProv($id){
        $models = Module::find()->select('model,status_bys')->where(['and',['provider_id'=>$id],['!=','model','bys_carta']])->asArray()->all();
        $status = 'En edición de datos';
        $auxR = 0;
        $auxV = 0;
        foreach ($models as $m){
            if($m['status_bys']=='RECHAZADO'){
                $status = "En edición (Tiene uno o mas rechazos)";
                break;
            }
            if($m['status_bys']=='POR VALIDAR'){
                $status = "En revisión";
                break;
            }
            if($m['status_bys']=='EN EDICION')
                break;

            if($m['status_bys']=='REVISADO')
                $auxR++;
            if($m['status_bys']=='VALIDADO')
                $auxV++;
        }

        if($auxV==7)
            $status = "Todo validado";

        if($auxR==7)
            $status = "En validación";

        return $status;

    }

    public static function getSendValidation($id){
        $model = models\Asignacion::find()->select("date(created_at) as created_at")->where(['and',['id_proveedor'=>$id],['activo'=>true]])->one();
        if($model)
            return " <br>Envió a revisión el: ". date_format(date_create($model->created_at), 'd/m/Y');
        return '-';

    }


    public static function getSendDias($conteo_dias){

        $provider = \app\models\Provider::find()->where(['user_id' => Yii::$app->user->getId()])->one();
        $model = \app\models\Asignacion::find()->select("date(created_at) as created_at")->where(['and',['id_proveedor'=>$provider->provider_id],['activo'=>true]])->one();
        $diasFeriados = ArrayHelper::getColumn( \app\models\NonWorkingDays::find()->where(['and', ['holiday' => true], ['user_type' => 'ADMIN']])->all(), 'non_working_day');
        $diaActualStr = date("Y-m-d");

        // se condiciona a tener respuesta del modelo para mandar días contables
        if($model){
            $diasHabiles = GeneralController::getFechaHabiles($model->created_at, $diaActualStr, $diasFeriados);
            $dias = (($conteo_dias + 1) - count($diasHabiles));
        }else{
            $dias = 0;
        }

        // evita que los días salgan negativos
        if( $dias <= 0){
            $dias = 0;
        }

        return $dias;

    }

    
    /**
     * Metodo getFechaHabiles
     *
     * Devuelve un array con fechas [(string)] Y-m-d
     * entre los rangos de fechas dados excluyendo los
     * dias feriados dados (Si existen)
     *
     * @param string $p_fechaInicio Fecha de inicio en formato Y-m-d
     * @param string $p_fechaFin Fecha de fin en formato Y-m-d
     * @param array $diasFeriados Arreglo de dias feriados[(string)] en formato Y-m-d
     * @return array $diashabiles Arreglo de dias habiles [(string)] en formato Y-m-d
     */
    public static function getFechaHabiles($p_fechaInicio, $p_fechaFin, $diasFeriados){
        // Convirtiendo en timestamp las fechas
        $fechainicio = strtotime($p_fechaInicio . " +1 day"); //Se cambio porque se busca que se valie al dia siguiente de su creacion
        $fechafin = strtotime($p_fechaFin);

        $timeIncremento = 24*60*60; // Incremento en 1 dia

        $diasHabiles = array(); // Arreglo de dias habiles, inicianlizacion

        // Se recorre desde la fecha de inicio a la fecha fin, incrementando en 1 dia
        for ($diaActual = $fechainicio; $diaActual <= $fechafin; $diaActual += $timeIncremento) {
            // Si el dia indicado, no es sabado o domingo es habil
            if (!in_array(date('N', $diaActual), array(6,7))) {
                    // Si no es un dia feriado entonces es habil
                    if (!in_array(date('Y-m-d', $diaActual), $diasFeriados)) {
                            array_push($diasHabiles, date('Y-m-d', $diaActual));
                    }
            }
        }
   
        return $diasHabiles;

    }

    /**
     * Devuelve un string con el código del SVG almacenado en cache o crea un cache en caso de que no exista
     * @param string $nombresvg -> Nombre del atributo
     * @param string $rutasvg -> Ruta extra si está fuera de imgs del atributo
     * @return string
     */

    public static function getSVG($nombresvg, $rutasvg=null){
        $variablesvg = $nombresvg.$rutasvg;
        if(!Yii::$app->cache->get($variablesvg)){
            Yii::$app->cache->set($variablesvg, file_get_contents('imgs/'.$rutasvg.$nombresvg.'.svg'), 60*60*24*15);
        }

        $archivosvg = Yii::$app->cache->get($variablesvg);

        return $archivosvg;
    }

    public static function is_multidimentional($array){
        return !(count($array) == count($array, COUNT_RECURSIVE));
    }

    public function actionDescargarDataProvider($encode_data){
        $sec = new Security();
        $b64_decode = base64_decode($encode_data);
        $tstd = $sec->decryptByPassword($b64_decode, Yii::$app->params['key_encode'] );//Este es el query
    }

    /**
     * Transforma un array multidimensional en CSV data
     * @param mixed $data Array multidimensional con los datos a insertar
     * @param mixed $columns Array de strings con los nombres de encabezados
     * @return string Datos del CSV con el delimitador y configuracion seteada
     */
    public static function multiarrayToCVS($data, $columns = [], $delimiter = ',', $enclosure = '"', $escape_char = "\\"){
        $file = fopen('php://memory', 'r+');
        if( !is_null($columns) && count($columns) > 0){ fputcsv($file, $columns, $delimiter, $enclosure, $escape_char); }
        foreach ($data as $item) { fputcsv($file, $item, $delimiter, $enclosure, $escape_char); }
        rewind($file);
        return stream_get_contents($file);
    }

    public static function setHeadersDownloadCSV($filename){
        $response = Yii::$app->response;
        $response->format = Response::FORMAT_RAW;
        $headers = $response->getHeaders();
        $headers->set('Content-Type', "application/csv; charset=utf-8");
        $headers->set('Content-Transfer-Encoding', 'utf-8');
        $headers->set('Cache-Control', 'public, must-revalidate, max-age=0');
        $headers->set('Pragma', 'public');
        $headers->set('Last-Modified', gmdate('D, d M Y H:i:s') . ' GMT');
        $headers->set('Content-Disposition', "attachment; filename=${filename}.csv");
    }

    /**
     * Transforma la informacion del active directory en un array manipulable
     * @param string $data string de datos en formato "CN=XXXXXXX,OU=XXXXX,OU=XXXXX,DC=genl,DC=nl,DC=gob"
     * @return mixed Array de multiple dimension con los valores separados por identificador
     */
    public static function parseLdapDn($dn)
    {
        $parsr = ldap_explode_dn($dn, 0);
        $out = array();
        foreach($parsr as $key => $value){
            
            if(FALSE !== strstr($value, '=')){
                list($prefix,$data) = explode("=",$value);
                if($prefix == 'CN'){
                    $out[$prefix] = $data;
                }else{
                    if(isset($current_prefix) && $prefix == $current_prefix){
                        $out[$prefix][] = $data;
                    } else {
                        $current_prefix = $prefix;
                        $out[$prefix][] = $data;
                    }
                }
                
            }
        }
        return $out;
    }

    /**
     * Funcion de escuela que divide el nombre en apellidos materno y paterno 
     * @param string $fullname Nombre completo el cual sera dividido
     * @return mixed Array con nombres, apellido_paterno y apellido_materno
     */
    public static function splitNames($fullname){

        $clean_fullname = $fullname;
        /* separar el nombre completo en espacios */
        $aux_names = explode(' ', $clean_fullname);
        /* arreglo donde se guardan las "palabras" del nombre */
        $nombres = array();
        /* palabras de apellidos (y nombres) compuetos */
        $special_tokens = array('DA', 'DE', 'DEL', 'LA', 'LAS', 'LOS', 'MAC', 'MC', 'VAN', 'VON', 'Y', 'I', 'SAN', 'SANTA');

        $prev = "";
        foreach ($aux_names as $name) {
            $_name = $name;
            if (in_array($_name, $special_tokens)) {
                $prev .= "$name ";
            } else {
                $nombres[] = $prev . $name;
                $prev = "";
            }
        }

        $apellido_paterno = '';
        $apellido_materno = '';
        $comp_nombres = '';


        $num_nombres = count($nombres);

        switch ($num_nombres) {
            case 0:
                break;
            case 1:
                $comp_nombres = $nombres[0];
                break;
            case 2:
                $comp_nombres = $nombres[0];
                $apellido_paterno = $nombres[1];
                break;
            case 3:
                $comp_nombres = $nombres[0];
                $apellido_paterno = $nombres[1];
                $apellido_materno = $nombres[2];
                break;
            default:
                $comp_nombres = "{$nombres[0]} {$nombres[1]}";
                $apellido_paterno = $nombres[2];
                $apellido_materno = $nombres[3];
                break;
        }

        return  ['apellido_paterno' => $apellido_paterno, 'apellido_materno' => $apellido_materno, 'nombres' => $comp_nombres];

    }

    public static function buttonTooltip($icono, $id, $botones, $menu_title){
        $html_botones = "";
        foreach($botones as $boton){
            $aux_url = $boton['url'];
            $aux_text = $boton['text'];
            $aux_title = $boton['title'];
            $html_botones .= "<div class=\'btn btn-success showModalButton\' value=\'$aux_url\' title=\'$aux_title\'>$aux_text</div>";
        }
        $plantilla = "<i id=\"$id\" class=\"material-icons help-label\" onclick=\"pinta_tooltip('<div class=\'tooltip-buttons moral_area_botones\'>$html_botones</div>', '$id', 'bottom', '$menu_title')\">$icono</i>";
        return $plantilla;
    }

    //Se tuvo que copiar porque no pude acceder al renderPartial de manera estatica aun cambiando a self
    public function generarDocumentoPreguntas($concurso, $preguntas, $solicitud, $provider, $firma = null){
        $name_file_save = null;
        define('_MPDF_TTFONTPATH', './fonts/'); //cambia la ruta de las fuentes utilizadas en la creación del pdf (la configuración default puede solicitar alguans fuentes necesarias)
    
        $mpdf = new mPDF('utf-8', 'A4', 0, '', 0, 0, 5, 15, 0, 0);
        $mpdf->SetTitle('Documento preguntas');
        $stylesheet = file_get_contents('css/pdf.css');

        GeneralController::add_custom_fonts_to_mpdf($mpdf);
        $mpdf->SetHTMLHeader($this->renderPartial('/concursos/solicitud/header-solicitud'));
        $mpdf->SetHTMLFooter($this->renderPartial('/concursos/solicitud/footer-solicitud'));
        
        $datos_doc = [ 'solicitud' => $solicitud, 'concurso' => $concurso, 'preguntas' => $preguntas, 'provider' => $provider ];
        $datos_doc = is_null($firma) ? $datos_doc : array_merge($datos_doc, ['firma' => $firma]);
        $content = $this->renderPartial('/concursos/solicitud/plantilla-solicitud', $datos_doc);

        $mpdf->WriteHTML($stylesheet, 1);
        $mpdf->WriteHTML($content);

        $name_file_save = (is_null($firma) ? Firmar::pre_path : Firmar::path) . md5($solicitud->solicitud_id) . '.pdf';

        if(!is_dir(Firmar::pre_path)){ mkdir(Firmar::pre_path,0775,true); }
        if(!is_dir(Firmar::path)){ mkdir(Firmar::path,0775,true); }

        $mpdf->Output($name_file_save, 'F');

        return $name_file_save;
    }

    public static function zipInstance($name_zip){
        $zip = new ZipArchive();
        $zip->open($name_zip, ZipArchive::CREATE);

        return $zip;
    }

    public static function vigenciaCertificado($provider_id): string{
        $provider = Provider::find()->where(['provider_id' => $provider_id])->one();
        if($provider->vigencia !== null){
            $timestamp = strtotime($provider->vigencia);
            $year = date("Y", $timestamp);
            /*$fechaFisica = strtotime("{$year}-12-31");
            $fechaMoral = strtotime("{$year}-12-31");
            if($provider->tipo_persona == 'Persona física'){
                if($timestamp >= $fechaFisica){
                    $year = ++$year;
                }
            }else{
                if($timestamp >= $fechaMoral){
                    $year = ++$year;
                }
            }*/
        }else{
            $year = 'Pendiente';
        }
        return $year;
    }


    public static function isYTVideo($url) {
        // Patrón de expresión regular para URL de YouTube, tiene que ser embed si no marca error de bloqueo
        $patronYouTube = '/^(https?:\/\/)?(www\.)?(youtube\.com\/(watch\?v=|embed\/)|youtu\.be\/)([\w-]+)(\S*)?$/';

        return preg_match($patronYouTube, $url);
    }

    /* Simple simplificacion de sintaxis */
    public static function isNullOrEmpty($val) : bool{
        return !isset($val) || empty($val);
    }

    public static function pushNotification($user_id, $message)
    {
        $tokenUrl = $_ENV['URL_COMPRONL'] . '/api/auth/token/';

        $tokenData = [
            'username' => $_ENV['USER_COMPRONL'],
            'password' => $_ENV['PASSWORD_COMPRONL'],
        ];

        $tokenDataString = http_build_query($tokenData);

        $ch = curl_init($tokenUrl);

        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $tokenDataString);

        $tokenResponse = curl_exec($ch);

        if ($tokenResponse === false) {
            $error = curl_error($ch);
            curl_close($ch);
            return ['error' => $error];
        }

        $tokenData = json_decode($tokenResponse, true);

        $accessToken = $tokenData["access"];

        curl_close($ch);

        $url_notification = $_ENV['URL_COMPRONL'] . '/api/notificaciones/push_notificacion';

        $data = json_encode([
            'titulo' => "Compro NL",
            'mensaje' => $message,
            'tipo' => 'U_Normal',
            'extra_data' => [
                'users_id' => [$user_id]
            ]
        ]);

        $ch = curl_init($url_notification);

        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Content-Type: application/json',
            'Authorization: Bearer ' . $accessToken
        ]);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $data);

        $response = curl_exec($ch);

        if ($response === false) {
            $error = curl_error($ch);
            curl_close($ch);
        }

        curl_close($ch);

    }


    public function actionSendntf($user=null,$msg=null){
        if(!$user || !$msg)
            return json_encode(['status'=>false]);
        else
            return $this->pushNotification($user,$msg);
    }

}
?>