<?php


namespace app\helpers;



use app\models\ErrorInesperado;
use app\models\Provider;
use app\models\RepresentanteLegal;
use app\models\Ubicacion;
use Yii;
use yii\web\Controller;

class SireController extends Controller
{

    public function init(){
        date_default_timezone_set('America/Monterrey');
    }

    /*
     * Funcion de ejempolo de consulta de un proveedor a la plataforma SIRE
     * Es necesario un idque puede ser un RFC o un numero de sire
     * y un tipo, que por defecto es RFC, por tanto el primer parámetro
     * tendría que se el RFC del proveedor
     */
    public function consultaProveedor($id=0,$tipo='RFC'){


        //$numeroProv = "0030264"; //GOCE880428NEA
        //$rfcProv = 'GOCE880428NEH';
        $date = date('YmdHis');
        $method = 'RConsultaProveedorWS';
        $apiPublic = $_ENV['SIREAPIPUBLIC'];
        $apiSecret = $_ENV['SIREAPISECRET'];
        $baseUrl = $_ENV['SIREBASEURL']."catalogos/";
        $url = $baseUrl.$method;

        $sig = base64_encode(
            hash_hmac(
                'SHA1',
                strtoupper(
                    sha1($date.$method.$apiPublic)
                ),
                $apiSecret,
                true
            )
        );


        $postData = [
            "Input"=>[
                "Request"=>[
                    "Acceso"=>[
                        "ApiPublic"=> $apiPublic,
                        "Firma"=> $sig,
                        "Fecha"=> $date
                    ],
                    "Consulta"=>[
                        "Actualizacion"=>[
                            //"ClaveProveedor"=> $numeroProv,
                            //"RFCProveedor"=> $rfcProv,
                            "InfoCompleta"=> "S"
                        ]
                    ]
                ]
            ]
        ];

        if(strtoupper($tipo)=='RFC'){
            $postData['Input']['Request']['Consulta']['Actualizacion']['RFCProveedor'] = $id;
        }else{
            $postData['Input']['Request']['Consulta']['Actualizacion']['ClaveProveedor'] = $id;
        }

        $ch = curl_init($url);
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($postData));
        curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type:application/json']);

        $respuesta = curl_exec($ch);
        curl_close($ch);
        return $respuesta;


    }


    /*
     * Curl a la plataforma SIRE donde se envía como parametro el arreglo de datos
     * la URL a consultar. si ocurre un error,
     * guarda registro en tabla errorInesperado
     */
    public static function getCurlSire($postData,$url,$type = 'CONSULTA'){

        $ch = curl_init($url);
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($postData));
        curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type:application/json']);

        try{

            $respuesta = json_decode(curl_exec($ch));

            curl_close($ch);
        }catch(Exception $e){
            $error = new ErrorInesperado();
            $error->tipo_error = $type.' SIRE';
            $error->data = 'Mensaje:'.$e->getMessage().' | Archivo:'.$e->getFile().' | Linea:'.$e->getLine();
            $error->user_id = Yii::$app->user->getId();
            $error->save();
        }


        return $respuesta;
    }

    /*
     * funcion que crea la llave de firmado para poder consultar/insertar en SIRE, son necesarias
     * dos llaves previas que comparte el equipo de sire para su utilización. Estan en el .env
     */
    public static function getSignature($method = 'RConsultaProveedorWS', $date, $apiPublic ){

        $apiSecret = $_ENV['SIREAPISECRET'];

        return base64_encode(
            hash_hmac(
                'SHA1',
                strtoupper(
                    sha1($date.$method.$apiPublic)
                ),
                $apiSecret,
                true
            )
        );
    }


    /*
     * Devuelve un listado de datos propios del proveedor para
     * poder insertarlos dentro de SIRE
     * Este arreglo ya esta dado segun las especificaciones de
     * ingreso de datos en SIRE y los propios de padrón
     */
    public static function getDataProviderRfc($rfc=null){
        //buscar data de Proveedor por RFC
        $prov = Provider::find()->where(['rfc'=>$rfc])->one();
        $dom = Ubicacion::find()->where(['and',['activo'=>true],['tipo'=>'DOMICILIO FISCAL'],['provider_id'=>$prov->provider_id]])->one();
        $tp = ($prov->tipo_persona == 'Persona moral')?'M':'F';
        $rl = RepresentanteLegal::find()->where(['and',['rep_bys'=>true],['provider_id'=>$prov->provider_id]])->one();
        $moral = ($tp == 'M');
        $ubNL = Yii::$app->db->createCommand("
            select concat_ws(' ','Calle',ubi.calle_fiscal,'No',ubi.num_ext_fiscal,', CP',ubi.cp_fiscal,',',col.nombre, mun.nombre,est.nombre) as direccion
            from provider.ubicacion ubi 
            inner join cat_entidades est on est.entidad_id = ubi.state_fiscal 
            inner join cat_municipios mun on mun.municipio_id = ubi.city_fiscal
            inner join cat_asentamientos col on col.asentamiento_id = ubi.colonia_fiscal
            where ubi.provider_id = $prov->provider_id and ubi.tipo = 'DIRECCIÓN NUEVO LEÓN' and activo is true limit 1
        ")->queryAll();
        $ubNL = isset($ubNL[0]['direccion'])?$ubNL[0]['direccion']:'';

        $prods = Yii::$app->db->createCommand("
            select string_agg(concat_ws(' ',prd.clave,'-', prd.descripcion),', ') as productos
            from productos.producto prd 
            inner join public.provider_giro gir on gir.producto_id = prd.producto_id 
            where provider_id = $prov->provider_id
        ")->queryAll()[0]['productos'];



        //var_dump($tp,$moral,json_encode($prov->pf_nombre),$rfc).exit();

        return [
            "Modo"=>"I",
            "TipoPerson"=>$tp,
            "Tipo_Representante"=>$moral?"A":"P", //Propietario PF, Apoderado PM
            "Nombre_Representante"=>$moral ? $rl->nombre.' '.$rl->ap_paterno.' '.$rl->ap_materno  :'' ,
            "Tipo_Identificacion"=>$moral ? ($rl->tipo_identificacion=='IFE'?'C':'P')  :'' ,
            "RFC"=>$rfc,
            "Correo_Electronico"=> $prov->email ,//Perfil
            "Correo_Electronico_Dos"=>$dom->correo ,//ubicacion
            "RegistraRFCDuplicado" => false,
            "Nombre_Empresa"=>$moral?$prov->name_razon_social:$prov->pf_ap_paterno.' '.$prov->pf_ap_materno.' '.$prov->pf_nombre,
            "Nombre_Comercial"=>$prov->name_comercial,
            "Nombre"=>!$moral?$prov->pf_nombre:'',
            "Apellido_Paterno"=>!$moral?$prov->pf_ap_paterno:'',
            "Apellido_Materno"=>!$moral?$prov->pf_ap_materno:'',
            "Telefono"=>$dom->telefono,
            "Telefono_Particular"=> $moral?$rl->telefono:'',
            "Datos_Domicilio"=>[
                "Calle"=>$dom->calle_fiscal,
                "Numero_Exterior"=>$dom->num_ext_fiscal,
                "Numero_Interior"=>$dom->num_int_fiscal,
                "Codigo_Postal"=>$dom->cp_fiscal,
                "Colonia"=>["Clave_Colonia"=>"000001"], //por defecto  000001 ya que no es seguro tener la misma clave de la ciudad
                "Pais"=>["Clave_Pais"=>"001"],
                "Estado"=>["Clave_Estado"=>str_pad($dom->state_fiscal,2,"0",STR_PAD_LEFT)],
                "Ciudad"=>["Nombre_Ciudad"=>$dom->NameCiudad]
            ],
            "FechaVigencia"=>(date('Y')+1)."-12-31",
            "Observaciones"=> $prods, //productos y servicios
            "URL"=> $dom->state_fiscal != 19 ? $ubNL :'' ,//domicilio en NL para los foraneos, solicitado por padron
            "RealizaRefrendo"=> "S",
            "Giro_Empresarial"=>"001",//Padron solicita poner 001 (giro por defecto del proveedo)
            "Clasificacion"=>"ZPAD",
            "Tipo_Entrega"=>"EEA", //Siempre seleccionar EEA (Entrega en almacen)
            "Clave_Divisa"=>"01",
            "DiasCredito"=>30,
            "Clave_Periodo"=>"01",
            "Fecha_Registro"=>date('Y-m-d')
        ];
    }


    /*
     * Consulta a SIRE por de un proveedor en especifico, se pasa como parámetro el RFC del proveedor
     * en data retorna objeto con to do lo consultado por la api, y en num el numero del proveedor en caso de existir
     * si no existe el proveedor, exist será falso
     */
    public static function consultaProviderByRfc($rfc=null){
        if(!$rfc)
            return (object)['exist'=>false];
        $date = date('YmdHis');
        $method = 'RConsultaProveedorWS';
        $apiPublic = $_ENV['SIREAPIPUBLIC'];

        $postData = [
            "Input"=>[
                "Request"=>[
                    "Acceso"=>[
                        "ApiPublic"=> $apiPublic,
                        "Firma"=> self::getSignature($method,$date, $apiPublic),
                        "Fecha"=> $date
                    ],
                    "Consulta"=>[
                        "Actualizacion"=>[
                            "RFCProveedor"=> $rfc,
                            "InfoCompleta"=> "S"
                        ]
                    ]
                ]
            ]
        ];

        $baseUrl = $_ENV['SIREBASEURL']."catalogos/";
        $url = $baseUrl.$method;

        $data = self::getCurlSire($postData,$url);
        $resSire = ['exist'=>false,'num'=>false,'error'=>true];

        //var_dump(json_encode($data)).exit();
        if(isset($data->Result->Response->proveedores->Proveedor[0]->claveprov)){
            $resSire = [
                'exist'=>true,
                'num'=>$data->Result->Response->proveedores->Proveedor[0]->claveprov,
                'data'=>$data
            ];
        }
        if(isset($data->Result->Response->proveedores)){
            $resSire['error'] = false;
        }
        return (object)$resSire;

    }


    /*
     * funcion de insersion de datos en SIRE, se tiene que pasar los datos del proveedor a insertar
     * si ocurre un error, guarda registro en tabla errorInesperado. Retorna objeto con "status", "num" de proveedor
     * "data" si es que hay o "error" si sucede un incidente
     */
    public static function insertProviderSire($dataProv){
        $date = date('YmdHis');
        $method = 'RRegistraProveedorWS';
        $apiPublic = $_ENV['SIREAPIPUBLIC'];

        $postData = [
            "Input"=>[
                "Request"=>[
                    "Acceso"=>[
                        "ApiPublic"=> $apiPublic,
                        "Firma"=> self::getSignature($method,$date, $apiPublic),
                        "Fecha"=> $date
                    ],
                    "Proveedores"=>[
                        $dataProv
                    ]
                ]
            ]
        ];


        $baseUrl = $_ENV['SIREBASEURL']."operaciones/";
        $url = $baseUrl.$method;

        $data = self::getCurlSire($postData,$url,'INSERTA');


        if(isset($data->Result) && $data->Result->Response->Proveedores->Proveedor[0]->Estado == 'REGISTRADO'){
            $resSire = [
                'status'=>true,
                'num'=>$data->Result->Response->Proveedores->Proveedor[0]->ClaveProveedor,
                'data'=>$data
            ];
        }else{
            $error = new ErrorInesperado();
            $error->tipo_error = 'Insertando SIRE';
            $error->data = json_encode(['dataProd'=>$dataProv,'data'=>$data]);
            $error->user_id = Yii::$app->user->getId();
            $error->save(false);
            $resSire = [
                'status'=>false,'num'=>false,
                'error'=>isset($data->Result->Response->Proveedores->Proveedor[0]->Error)? $data->Result->Response->Proveedores->Proveedor[0]->Error : json_encode($data)
            ];
        }
        return (object)$resSire;

    }







    /*


    Busqueda por RFC que NO EXISTE //GIRM920620PJA

string(63) "http://************:1522/apirest/catalogos/RConsultaProveedorWS"
string(205) "{"Input":{"Request":{"Acceso":{"ApiPublic":"TBDYOMCNBJ","Firma":"ui8NLWfniI2\/PxwEs8050FmR5j0=","Fecha":"20230525163622"},"Consulta":{"Actualizacion":{"RFCProveedor":"GIRM920620PJA","InfoCompleta":"S"}}}}}"
string(206) "{"Result": {
  "Response": {
    "Error": "",
    "actualizacion": {
      "act_proveedor": {
        "fecha": "2023-05-25",
        "hora": "16362280"
      }
    },
    "proveedores": ""
  }
}}"


    ------------------------------------------------------




Busqueda por RFC //TEGC800922I47

string(63) "http://************:1522/apirest/catalogos/RConsultaProveedorWS"
string(205) "{"Input":{"Request":{"Acceso":{"ApiPublic":"TBDYOMCNBJ","Firma":"nSOE9Y8F0NqHPs+Mvh8c\/v6wZQA=","Fecha":"20230525163333"},"Consulta":{"Actualizacion":{"RFCProveedor":"TEGC800922I47","InfoCompleta":"S"}}}}}"
string(3291) "{"Result": {
  "Response": {
    "Error": "",
    "actualizacion": {
      "act_proveedor": {
        "fecha": "2023-05-25",
        "hora": "16333403"
      }
    },
    "proveedores": {
      "Proveedor": [
        {
          "claveprov": "0034629",
          "nombreprov": "TEJEDA GONZALEZ CARLA VIOLETA",
          "nomb_abrev": "",
          "nombre": "CARLA VIOLETA",
          "pater": "TEJEDA",
          "mater": "GONZALEZ",
          "tipoperson": "F",
          "rfc": "TEGC800922I47",
          "curp": "TEGC800922MNLJNR03",
          "cvealm": "",
          "tipo_prov": "",
          "slot1": "",
          "direcprov": "EL ATLAS",
          "num_exter": "13",
          "num_inter": "",
          "cp": "64116",
          "ap_postal": "",
          "congrega": "",
          "cvecol": "002854",
          "descol": "CUMBRE ALTA",
          "cve_pais": "001",
          "pais_abrev_pais": "MEX",
          "cveest": "19",
          "desest": "NUEVO LEON",
          "ciudad": "058",
          "desciudad": "MONTERREY",
          "cve_loc": "",
          "cve_zona": "",
          "observa": "COORDINADOR DE PROYECTOS | SECRETARIA DE CULTURA |",
          "tel_movil": "",
          "telsprov": "",
          "tel_ofic": "",
          "refer2": "",
          "dir_elect": "",
          "url": "",
          "cve_divisa": "01",
          "abrev_divi": "M.N.",
          "tiempo_e": 0,
          "clasifica": "ZFUN",
          "desc_clasi": "Funcionario",
          "dias_cred": 0,
          "cve_peri": "02",
          "dias_peri": 0,
          "desc_peri": "Inmediata",
          "limit_cre": 0,
          "giro_emp": "001",
          "desc_giro": "Giro por Defecto de Proveedor",
          "tipo_entr": "EAD",
          "desc_tipoe": "A Domicilio",
          "nom_comerc": "",
          "nom_repre": "",
          "tipo_repre": "",
          "clavesat": "",
          "tipo_ident": "",
          "cve_ident": "",
          "email": "",
          "email2": "",
          "numtarje": "",
          "c2": 0,
          "totcpra": 0,
          "totade": 0,
          "status": "",
          "at_vta": "",
          "at_cred": "",
          "at_cobran": "",
          "cve_divis2": "",
          "cve_divis3": "",
          "totcpra2": 0,
          "totcpra3": 0,
          "totade2": 0,
          "totade3": 0,
          "ult_cpra": "",
          "codigo_c": "",
          "codigo_c2": "",
          "codigo_c3": "",
          "si_saldo": true,
          "cve_comp": 0,
          "fecha_vig": "20240531",
          "pagoaterce": "",
          "mancadpro": "",
          "cvetipo": "",
          "tipo_ch": "",
          "hab_reingast": "S",
          "cvefactor": "",
          "intdefact": "",
          "r_refrendo": "N",
          "fidsinest": "N",
          "numregfis": "",
          "env_aviso": "N",
          "es_b2b": false,
          "es_acreedor": false,
          "hora_mod": "13212162",
          "fecha_mod": "20230403",
          "geolatitud": 0,
          "geolongitud": 0,
          "t_entrega": "",
          "fecha_a": "20230403",
          "eslogan": "",
          "descrip": "",
          "porcentaje_comision": 0
        }
      ]
    }
  }
}}"









     */

}