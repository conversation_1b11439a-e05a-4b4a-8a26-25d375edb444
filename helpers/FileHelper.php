<?php

namespace app\helpers;

use app\models\Firmar;
use Exception;
use FPDI;
use yii\web\Controller;
//use Mpdf\Mpdf;
use mPDF;
use Yii;
use ZipArchive;

if (!defined('_MPDF_TTFONTPATH')) {
    define('_MPDF_TTFONTPATH', './fonts/'); //cambia la ruta de las fuentes utilizadas en la creación del pdf (la configuración default puede solicitar alguans fuentes necesarias)
}

class FileHelper{
    
    const MPDF_GENERIC_CONFIG = [ 'fontDir'=>'./fonts/', 'mode'=>'utf-8', 'format'=>'A4',
        'margin_left' => 0, 'margin_right' => 0, 'margin_top' => 5, 'margin_bottom' => 15,
        'margin_header' => 0, 'margin_footer' => 0, 'transparency' => .1 ];

    /**
     * Crea la ruta especificada en el proyecto
     * 
     * @param String $path Ruta del directorio a crear
     *  */
    public static function makeDir($path){
        if ( !file_exists($path) ) { mkdir($path, 0755, true); }
    }

    /**
     * Crea una copia del documento origen en la ruta especificada
     * 
     * @param String $origin_file Ruta del archivo a realizar copia
     * @param String $subject_path Ruta de destino del archivo
     *  */
    public static function copyFile($origin_file, $subject_path){
        if (file_exists($origin_file)) { rename($origin_file, $subject_path); }
    }

    /**
     * Copia el archivo a la ruta final del modelo, evalua tambien si es necesario ejecutarse
     * 
     * @param String $ruta_origen Ruta de origen en archivos_temp
     * @param String $ruta_final Ruta de destino del archivo
     * @param String $string_replace Parte de la ruta que se va a remover, por default es archivos_tmp
     *  */
    public static function moveFile($ruta_origen, $ruta_final, $string_replace = "archivos_tmp"){
        $respuesta = $ruta_origen;
        if (!empty($ruta_origen) && str_contains($ruta_origen, $string_replace) ) {            
            self::makeDir($ruta_final);
            $ruta_completa_final = str_replace("{$string_replace}/", $ruta_final, $ruta_origen);
            self::copyFile($ruta_origen, $ruta_completa_final);
            $respuesta = $ruta_completa_final;
        }

        return $respuesta;
    }

    /** Funcion que crea un archivo zip con la configuracion dada
     * @param String $nombre_zip Nombre final del archivo
     * @param String $ruta_zip Path donde se guardara el archivo
     * @param Array $files Arreglo con los archivos que se añadiran al zip
     *                      Los indices esperados son: 
     *                      - 'path': Ruta original del archivo
     *                      - 'nombre': Nombre que tendra el archivo dentro del zip
     * @param String $extension Un archivo comprimido puede tener varios formatos (zip, iso, rar, etc)
     * @param String $prefix Prefijo que ira antes del nombre del zip como identificador
     * 
     * @return String Nombre y ruta final del archivo
      */
    public static function generateZip($nombre_zip, $ruta_zip, $files = [], $extension = '.zip', $prefix = ""){
        self::makeDir($ruta_zip);
        $zipname = "{$ruta_zip}{$prefix}{$nombre_zip}{$extension}";
        $zip = new ZipArchive;
        $zip->open($zipname, ZipArchive::CREATE);
        foreach($files as $file){
            $zip->addFile($file['path'], $file['nombre']);
        }
        $zip->close();

        return $zipname;
    }

    /**
     * Funcion encargada de generar un documento PDF(Mpdf) con la configuracion y datos proporcionados
     * @param String $titulo Titulo del documento. Defualt = 'Documento'
     * @param Array $config Arreglo de configuracion con los datos necesarios para generar el pdf
     *                      Los indices esperados son: 
     *                      - 'header': Ruta del layout de header a utilizar, por ejemplo 'carta/header'
     *                      - 'footer': Ruta del layout de footer a utilizar, por ejemplo 'carta/footer'
     *                      - 'content': Arreglo de datos con la informacion del contenido
     *                      ```
     *                              // header, footer, content son arreglos que contienen la siguiente estructura
     *                              [
     *                                'layout': 'Ruta del layout de header a utilizar, por ejemplo "carta/header", "carta/footer", etc.',
     *                                'datos': 'Arreglo de datos que se le pasaran como parametros al render del contenido'
     *                                  ]
     *                             
     *                      ```
     *                      - 'watermark': Ruta de la marca de agua
     *                      - 'path': Ruta donde se almacenara el documento
     *                      - 'prefix' Prefijo que sera usado para generar el nombre del documento para facil identificacion
     *                      - 'identificador': Datos que sera usado para generar un valor unico en el nombre del documento
     * @param String $filename En caso de necesitarse, se puede establecer la ruta y el nombre del archivo manualmente;
     *
     * @return String Ruta completa del documento generado 
     *  */
    public static function generatePdf($titulo = 'Documento', $config = [], $filename = null){
        $name_file_save = null;
    
        $controller = new Controller(Yii::$app->id, Yii::$app->module);

        //$mpdf = new Mpdf(self::MPDF_GENERIC_CONFIG);
        $mpdf = new mPDF('utf-8', 'A4', 0, '', 0, 0, 5, 15, 0, 0);
        $mpdf->SetTitle($titulo);
        $stylesheet = file_get_contents('css/pdf.css');

        self::add_custom_fonts_to_mpdf($mpdf);
        if( isset ($config['header']) && !is_null($config['header']) ){
            $mpdf->SetHTMLHeader($controller->renderPartial($config['header']['layout'], $config['header']['datos']));
        }
        if( isset ($config['footer']) && !is_null($config['footer']) ){
            $mpdf->SetHTMLFooter($controller->renderPartial($config['footer']['layout'], $config['footer']['datos']));
        }
        if( isset ($config['watermark']) && !is_null($config['watermark']) ){
            if(is_array($config['watermark'])){
                $mpdf->SetWatermarkImage($config['watermark']['image'],$config['watermark']['transparency'],array(100,100));
            }else{
                $mpdf->SetWatermarkImage($config['watermark'],self::MPDF_GENERIC_CONFIG['transparency'],array(100,100));
            }
            $mpdf->showWatermarkImage = true;
        }

        $content = $controller->renderPartial($config['content']['layout'], $config['content']['datos']);

        $mpdf->WriteHTML($stylesheet, 1);
        $mpdf->WriteHTML($content);

        if( is_null($filename) ){
            $path = (isset ($config['path']) && !is_null($config['path'])) ? $config['path'] : 'documentos/';
            $prefix = (isset ($config['prefix']) && !is_null($config['prefix'])) ? $config['prefix'] ."_" : "";
            $identificador = (isset ($config['identificador']) && !is_null($config['identificador'])) ? $config['identificador'] : uniqid();
    
            $name_file_save = $path . $prefix . md5($identificador) . time() . ".pdf";

            self::makeDir($path);
        }else{
            $name_file_save = $filename;
        }

        $mpdf->Output($name_file_save, 'F');

        return $name_file_save;
    }

    /** 
     * Funcion que une 2 o mas archivos pdfs en uno resultante 
     * @param Array $files Arreglo con los paths de los archivos a unir
     * @param String $path_filename Nombre que tendra el archivo resultante
     * @return String Path del archivo resultante
    */
    public static function mergePdfFiles($files, $path_filename){
        $pdf_i = new FPDI;
        if(count($files) > 0){
            foreach($files as $file){
                $pageCount = $pdf_i->setSourceFile($file); //Lectura del archivo en curso
                for ($i = 0; $i < $pageCount; $i++) {
                    $page_template = $pdf_i->importPage($i + 1);
                    $size = $pdf_i->getTemplateSize($page_template);
                    $width = isset($size['width']) ? $size['width'] : (isset($size['w']) ? ($size['w']) : 216);
                    $height = isset($size['height']) ? $size['height'] : (isset($size['h']) ? ($size['h']) : 280);
                    $pdf_i->AddPage($height > $width ? 'P' : 'L', [$width, $height]);
                    $pdf_i->useTemplate($page_template);
                } //Al finalizar la misma instancia anexa las hojas del archivo con la lectura anterior
            }
            $pdf_i->Output($path_filename,'F'); //Nose porque marca error el VSC, cosa chafa
        }else{ throw new Exception("Se intento hacer un merge file pero sin proporcionar archivos para trabajar"); }

    }

    /** Metodo que actualiza la informacion de firmado del proveedor
     * @param String $rfc RFC del proveedor a actualizar los datos de firma en el servidor
     * @param mixed $datos Datos del certificado/firma
     * 
     * @return String Path del archivo
     */
    public static function updateFirmaData($rfc, $datos){
        $path_dir = Firmar::path;
        $path_file = "{$path_dir}{$rfc}/firma.fir";
        $file_firma = fopen($path_file, 'w');
        fwrite($file_firma, $datos);
        fclose($file_firma);

        return $path_file;
    }

    /**
     * Metodo para setear fuentes externas a la instancia mpdf en uso
     * @param Mpdf $mpdf Instancia de mpdf inicializada
     *  
     * */
    public static function add_custom_fonts_to_mpdf($mpdf) {

        $fontdata = [
            'poppins' => [
                'R' => 'Poppins-Regular.ttf',
                'B' => 'Poppins-Bold.ttf',
            ],
            'axiforma' => [
                'R' => 'Axiforma-Regular.ttf',
                'B' => 'Axiforma-Heavy.ttf',
            ],
            'branding' => [
                'R' => 'Branding-Medium.ttf',
                'B' => 'Branding-Bold.ttf',
            ],
            'maven' => [
                'R' => 'MavenPro-Regular.ttf',
                'B' => 'MavenPro-Black.ttf',
            ],
            'branding-black' => [
                'R' => 'Branding SF Black.ttf'
            ],
            'branding-sf' => [
                'R' => 'Branding SF Medium.ttf',
                'B' => 'Branding SF Bold.ttf',
            ],
            'branding-light' => [
                'R' => 'Branding SF Light.ttf'
            ],

        ];

        foreach ($fontdata as $f => $fs) {

            $mpdf->fontdata[$f] = $fs;

            foreach (['R', 'B', 'I', 'BI'] as $style) {
                if (isset($fs[$style]) && $fs[$style]) {
                    $mpdf->available_unifonts[] = $f . trim($style, 'R');
                }
            }

        }

        $mpdf->default_available_fonts = $mpdf->available_unifonts;
    }

    /**
     * Metodo que obtiene recursos remotos y los devuelve como Base64
     * @param String $url URL del recurso remoto
     */
    public static function remoteImageToB64(string $url){

        //Configuracion bypass SSL error, verificar si es optimo en prod
        $arrContextOptions=array(
            "ssl"=>array(
                "verify_peer"=>false,
                "verify_peer_name"=>false,
            ),
        );
    
        $imageData = file_get_contents($url, false, stream_context_create($arrContextOptions));
    
        //Verifica si fue accesible la imagen.
        if ($imageData === FALSE) {
            return false; // Failed to fetch the image from the URL
        }
    
        //Lectura de los headers para identificar el tipo de recurso.
        $finfo = finfo_open(FILEINFO_MIME_TYPE);
        $mimeType = finfo_buffer($finfo, $imageData);
        finfo_close($finfo);
    
        // Base64 encode the image data
        $base64Data = base64_encode($imageData);
        switch ($mimeType) {
            case 'image/svg+xml':
                return 'data:image/svg+xml;base64,' . $base64Data;
            case 'image/png':
                return 'data:image/png;base64,' . $base64Data;
            case 'image/jpeg':
                return 'data:image/jpeg;base64,' . $base64Data;
            default:
                return false; // Unsupported image format
        }
    }


}
?>