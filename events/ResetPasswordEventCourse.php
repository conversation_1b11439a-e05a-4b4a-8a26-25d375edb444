<?php

/*
 * This file is part of the Dektrium project.
 *
 * (c) Dektrium project <http://github.com/dektrium/>
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace app\events;

use app\models\RecoveryCourseForm;
use app\models\RecoveryForm;
use app\models\TokenCourse;
use yii\base\Event;

/**
 * @property TokenCourse        $token
 * @property RecoveryForm $form
 * <AUTHOR> <<EMAIL>>
 */
class ResetPasswordEventCourse extends Event
{
    /**
     * @var RecoveryForm
     */
    private $_form;

    /**
     * @var TokenCourse
     */
    private $_token;

    /**
     * @return TokenCourse
     */
    public function getToken()
    {
        return $this->_token;
    }

    /**
     * @param TokenCourse $token
     */
    public function setToken(TokenCourse $token = null)
    {
        $this->_token = $token;
    }

    /**
     * @return RecoveryForm
     */
    public function getForm()
    {
        return $this->_form;
    }

    /**
     * @param RecoveryForm $form
     */
    public function setForm(RecoveryCourseForm $form = null)
    {
        $this->_form = $form;
    }
}
