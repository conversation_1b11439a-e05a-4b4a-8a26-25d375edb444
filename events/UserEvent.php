<?php

/*
 * This file is part of the Dektrium project.
 *
 * (c) Dektrium project <http://github.com/dektrium/>
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace app\events;

use app\models\Usuarios;
use yii\base\Event;

/**
 * @property Usuarios $model
 * <AUTHOR> <<EMAIL>>
 */
class UserEvent extends Event
{
    /**
     * @var Usuarios
     */
    private $_user;

    /**
     * @return Usuarios
     */
    public function getUser()
    {
        return $this->_user;
    }

    /**
     * @param Usuarios $form
     */
    public function setUser(Usuarios $form)
    {
        $this->_user = $form;
    }
}
