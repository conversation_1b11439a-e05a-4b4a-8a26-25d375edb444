<?php

/* @var $this yii\web\View */
/* @var $form yii\bootstrap\ActiveForm */
/* @var $model app\models\LoginForm */
/* @var $searchModel app\models\Concursos */
/* @var $dataProvider yii\data\ActiveDataProvider */

use kartik\file\FileInput;
use yii\helpers\Html;
use yii\bootstrap\ActiveForm;
use kartik\popover\PopoverX;
use yii\helpers\Url;
use yii\bootstrap\Alert;
use yii\bootstrap\NavBar;
use yii\bootstrap\Nav;

$this->title = 'Login';

// Solo cargar JavaScript de FIEL si es necesario
try {
    $this->registerJsFile('@web/js/fiel.js?v='.time(), ['depends' => [\yii\web\JqueryAsset::class]]);
    $this->registerJsFile('@web/js/consolidadas.js', ['depends' => [\yii\web\JqueryAsset::class]]);
} catch (Exception $e) {
    // Si hay error cargando archivos JS, continuar sin ellos
    Yii::warning("Error cargando archivos JS: " . $e->getMessage());
}

if (Yii::$app->session->hasFlash('error') || Yii::$app->session->hasFlash('success')) {
    //$this->registerJs(" new PNotify({title: '<strong>" . ((Yii::$app->session->hasFlash('success')) ? ' Exito! ' : ' Incidente ') . "</strong>' , text:'" . ((Yii::$app->session->hasFlash('success')) ? Yii::$app->session->getFlash('success') : Yii::$app->session->getFlash('error')) . "',type:'" . ((Yii::$app->session->hasFlash('success')) ? 'success' : 'error') . "'});");
}

/*/
$logo_nuevoleon_header = $_ENV['VEDA_ELECTORAL'] ? 'https://recursos.digital.nl.gob.mx/assets/img/logos/logo_genl.svg': Url::to("@web/imgs/logo-nl.svg");
$logo_gobierno_horizontal = $_ENV['VEDA_ELECTORAL']  ? 'https://recursos.digital.nl.gob.mx/assets/img/logos/logo_estado.svg' : Url::to('@web/imgs/logo-login.png', true);
//*/
$logo_nuevoleon_header = 'https://recursos.digital.nl.gob.mx/assets/img/logos/logo_genl.svg';
$logo_gobierno_horizontal = 'https://recursos.digital.nl.gob.mx/assets/img/logos/logo_estado.svg';
//*/


?>

<div class="contendor-registro">

<?= Yii::$app->controller->renderPartial('/layouts/whats'); ?>

<div class="wrap">

    <?php
        /* NavBar::begin([
            'brandLabel' => '<img src="'.$logo_nuevoleon_header.'" height="100%" width="auto" />',
            'brandUrl' => Yii::$app->homeUrl,
            'options' => [
                'class' => 'navbar-inverse ',
            ],
        ]); */
        
        NavBar::begin(['brandLabel' => '', 'brandUrl' => Yii::$app->homeUrl, 'options' => [ 'class' => 'logo_header navbar-fixed-top logo-centro']]);

            echo Nav::widget([
                'options' => ['class' => 'navbar-nav navbar-right'],
                'items' => [
                        //['label' => 'Eventos', 'url' => ['/site/eventos']],
                        Html::a('Regístrate', ['/usuarios/create'], ['class' => 'btnempezarBarra']),
                ],
            ]);

        NavBar::end();

    ?>
</div>

    <div class="col-lg-6 col-lg-offset-3 col-xs-12 col-xs-offset-0">
        <?php if(Yii::$app->session->hasFlash('error') || Yii::$app->session->hasFlash('success')){ ?>
            <div class="alert <?= Yii::$app->session->hasFlash('error') ? " alert-danger " : " alert-success " ?> mensaje" role="alert" style="position:absolute;display:block;z-index: 1100;">
                <?= Yii::$app->session->hasFlash('error') ?  Yii::$app->session->getFlash('error') : Yii::$app->session->getFlash('success')?>
            </div>
        <?php }?>
    </div>

   


<div class="img-sit-login"></div>
<div class="site-login">
    <?php //if ($module->enableFlashMessages): ?>
        <div class="row messageRecovery"  style="margin-top: 80px;">
            <div class="col-xs-12">
                <?php foreach (Yii::$app->session->getAllFlashes() as $type => $message): ?>
                    <?php if (in_array($type, ['danger', 'warning', 'info'])): ?>
                        <?= Alert::widget([
                            'options' => ['class' => 'alert-dismissible alert-'.$type],
                            'body' => $message
                        ]) ?>
                    <?php endif ?>
                <?php endforeach ?>
            </div>
        </div>
    <?php // endif ?>

    <div class="cont_registro">
        <div class="cont_form" data-aos="fade-up" data-aos-duration="1000">

            <img class="logo_login" src="<?= $logo_gobierno_horizontal ?>">

            <div class="titulo_form">Inicia sesión en el Registro Único<br> de Proveedores y Contratistas</div>

            <?php $form = ActiveForm::begin(['id' => 'login-form','enableAjaxValidation' => true,'options' => ['class' => 'form-horizontal']]); ?>

            <div class="col-xs-12">


                <div>
                    <?= $form->field($model, 'username',['template' => "<div class='error-cont'>{label}\n{input}</div><div class='error-div'><div class='flecha-up'></div>{error}</div>"])->textInput(['placeholder'=>'Correo, usuario o RFC','autofocus' => true,'class'=>'txtCorreo form-control']) ?>
                </div>

                <div>
                    <?= $form->field($model, 'password',['template' => "<div class='error-cont'>{label}\n{input}</div><div class='error-div'><div class='flecha-up'></div>{error}</div>"])->passwordInput(['placeholder'=>'Contraseña','class'=>'form-control']) ?>
                </div>

                <?php $form->field($model, 'rememberMe')->checkbox([
                    'template' => "<div class=\"col-lg-offset-3 col-lg-7\">{input} {label}</div>",
                ]) ?>

                <div class="text-right">
                    <div>
                        <?= $form->field($model, 'es_funcionario')->checkbox([
                            'template' => "<div class=\"col-md-12 check-funcionario-login\"> {input} {label} </div>",
                        ])->label("Acceso interno") ?>
                    </div>
                </div>
            </div>

                <div class="col-lg-offset-2 col-lg-8 recuperaPass"><a href="#" id="form_certificado" onclick="formCertificado()">O inicia sesión con e.firma.</a></div>


                <div class="form-group col-xs-12">
                        <?= Html::submitButton('Iniciar sesión', ['class' => 'btn btn-success2 btn-enviar', 'name' => 'login-button', 'id'=>'boton-event']) ?>
                </div>

                <div class="col-lg-offset-2 col-lg-8 recuperaPass"><a href="<?= Url::to("/recovery/request");?>">¿Has olvidado tu contraseña?</a></div>

            <?php ActiveForm::end(); ?>


            <!-- Formulario FIEL simplificado -->
            <form id="login-form-certificado" class="col-xs-12 form-horizontal invisible" enctype="multipart/form-data">
                <div class="col-md-12 text-left label_register" style="margin-bottom: -10px;"> Contraseña FIEL </div>
                <div class="form-group">
                    <input type="password" id="fiel-password" name="password" class="txtCorreo form-control" placeholder="Contraseña de su e.firma" required>
                </div>

                <div class="col-md-12 text-left label_register"> Archivo *.CER </div>
                <div class="form-group">
                    <input type="file" id="fiel-certificado" name="certificado" accept=".cer" class="form-control" required>
                </div>

                <div class="col-md-12 text-left label_register"> Archivo *.KEY </div>
                <div class="form-group">
                    <input type="file" id="fiel-llave" name="llave" accept=".key" class="form-control" required>
                </div>

                <div id="fiel-info" style="margin: 15px 0 15px 0;position: relative"></div>

                <div class="col-lg-offset-2 col-lg-8 recuperaPass"><a href="#" id="form_usuario" onclick="formUsuario()">O inicia sesión con Correo, Usuario o RFC.</a></div>
                <div class="form-group">
                    <button type="submit" class="btn btn-success2 btn-enviar" id="boton-fiel">Iniciar sesión con FIEL</button>
                </div>
                <div class="col-lg-offset-2 col-lg-8 recuperaPass"><a href="<?= Url::to("/recovery/request");?>">¿Has olvidado tu contraseña?</a></div>
            </form>

        </div>
    </div>

</div>

</div>
<style>
    .label_register{
        display: inline-block;
        max-width: 100%;
        margin-bottom: 5px;
        margin-top: 15px;
        font-weight: 700;
    }
    .btn-file, .btn-file input[type="file"], .fileinput-remove-button {
        height: 42px !important;
    }
    .site-login .form-control {
        height: 42px;
        width: 100%;
        margin: 0px 0px;
    }

    .lds-dual-ring {
        display: inline-block;
        width: 80px;
        height: 80px;
    }
    .lds-dual-ring:after {
        content: " ";
        display: block;
        width: 64px;
        height: 64px;
        margin: 8px;
        border-radius: 50%;
        border: 6px solid #fff;
        border-color: #bbb transparent #bbb transparent;
        animation: lds-dual-ring 1.2s linear infinite;
    }
    @keyframes lds-dual-ring {
        0% {
            transform: rotate(0deg);
        }
        100% {
            transform: rotate(360deg);
        }
    }
</style>

<script>
    function formCertificado() {
        $("#login-form").addClass('invisible');
        $("#login-form-certificado").removeClass('invisible');
    }

    function formUsuario() {
        $("#login-form-certificado").addClass('invisible');
        $("#login-form").removeClass('invisible');
    }

    // Manejar envío del formulario FIEL
    $(document).ready(function() {
        $('#login-form-certificado').on('submit', function(e) {
            e.preventDefault();

            var formData = new FormData(this);
            var submitBtn = $('#boton-fiel');
            var originalText = submitBtn.text();
            var infoDiv = $('#fiel-info');

            // Validar que todos los campos estén llenos
            var password = $('#fiel-password').val();
            var certificado = $('#fiel-certificado')[0].files[0];
            var llave = $('#fiel-llave')[0].files[0];

            if (!password || !certificado || !llave) {
                infoDiv.html('<div class="alert alert-danger">Todos los campos son requeridos</div>');
                return;
            }

            // Mostrar loading
            submitBtn.prop('disabled', true).text('Procesando...');
            infoDiv.html('<div class="lds-dual-ring"></div><br><span class="text-center">Validando FIEL...</span>');

            $.ajax({
                url: '<?= Url::to(['/site/login-fiel']) ?>',
                type: 'POST',
                data: formData,
                processData: false,
                contentType: false,
                success: function(response) {
                    if (response.success) {
                        infoDiv.html('<div class="alert alert-success">' + response.message + '</div>');
                        // Redirigir después de un breve delay
                        setTimeout(function() {
                            window.location.href = response.redirect || '/';
                        }, 1000);
                    } else {
                        var errorMsg = response.errors ? response.errors.join('<br>') : 'Error desconocido';
                        infoDiv.html('<div class="alert alert-danger">' + errorMsg + '</div>');
                    }
                },
                error: function(xhr, status, error) {
                    console.error('Error AJAX:', error);
                    infoDiv.html('<div class="alert alert-danger">Error de conexión: ' + error + '</div>');
                },
                complete: function() {
                    // Rehabilitar botón
                    submitBtn.prop('disabled', false).text(originalText);
                }
            });
        });
    });
</script>