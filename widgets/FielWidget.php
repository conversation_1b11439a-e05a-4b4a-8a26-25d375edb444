<?php

namespace app\widgets;

use yii\base\Widget;
use yii\helpers\Html;

/**
 * Widget para manejar la funcionalidad de FIEL (e.firma)
 */
class FielWidget extends Widget
{
    public $formId = 'fiel-form';
    public $action = '/recovery/validate-fiel-recovery';
    public $showPasswordField = true;
    public $showRfcField = false;
    public $submitButtonText = 'Validar FIEL';
    public $submitButtonClass = 'btn btn-primary';

    public function run()
    {
        $formId = $this->formId;
        $action = $this->action;
        
        $html = Html::beginForm($action, 'post', [
            'id' => $formId,
            'enctype' => 'multipart/form-data',
            'class' => 'fiel-form'
        ]);

        // Campo para certificado .cer
        $html .= '<div class="form-group">';
        $html .= Html::label('Certificado (.cer)', 'certificado', ['class' => 'control-label']);
        $html .= Html::fileInput('certificado', null, [
            'id' => 'certificado',
            'class' => 'form-control',
            'accept' => '.cer',
            'required' => true
        ]);
        $html .= '</div>';

        // Campo para llave privada .key
        $html .= '<div class="form-group">';
        $html .= Html::label('Llave Privada (.key)', 'llave', ['class' => 'control-label']);
        $html .= Html::fileInput('llave', null, [
            'id' => 'llave',
            'class' => 'form-control',
            'accept' => '.key',
            'required' => true
        ]);
        $html .= '</div>';

        // Campo para contraseña de la FIEL
        if ($this->showPasswordField) {
            $html .= '<div class="form-group">';
            $html .= Html::label('Contraseña de la FIEL', 'password', ['class' => 'control-label']);
            $html .= Html::passwordInput('password', null, [
                'id' => 'password',
                'class' => 'form-control',
                'required' => true,
                'placeholder' => 'Ingrese la contraseña de su e.firma'
            ]);
            $html .= '</div>';
        }

        // Campo para RFC (opcional)
        if ($this->showRfcField) {
            $html .= '<div class="form-group">';
            $html .= Html::label('RFC', 'rfc', ['class' => 'control-label']);
            $html .= Html::textInput('rfc', null, [
                'id' => 'rfc',
                'class' => 'form-control',
                'placeholder' => 'RFC del certificado',
                'maxlength' => 13
            ]);
            $html .= '</div>';
        }

        // Botón de envío
        $html .= '<div class="form-group">';
        $html .= Html::submitButton($this->submitButtonText, [
            'class' => $this->submitButtonClass,
            'id' => 'submit-fiel'
        ]);
        $html .= '</div>';

        $html .= Html::endForm();

        // JavaScript para manejar el formulario
        $this->registerJs();

        return $html;
    }

    protected function registerJs()
    {
        $js = "
        $('#{$this->formId}').on('submit', function(e) {
            e.preventDefault();
            
            var formData = new FormData(this);
            var submitBtn = $('#submit-fiel');
            var originalText = submitBtn.text();
            
            // Deshabilitar botón y mostrar loading
            submitBtn.prop('disabled', true).text('Procesando...');
            
            $.ajax({
                url: '{$this->action}',
                type: 'POST',
                data: formData,
                processData: false,
                contentType: false,
                success: function(response) {
                    if (response.success) {
                        alert('FIEL validada correctamente');
                        if (response.redirect) {
                            window.location.href = response.redirect;
                        }
                    } else {
                        alert('Error: ' + (response.errors ? response.errors.join(', ') : 'Error desconocido'));
                    }
                },
                error: function(xhr, status, error) {
                    alert('Error de conexión: ' + error);
                },
                complete: function() {
                    // Rehabilitar botón
                    submitBtn.prop('disabled', false).text(originalText);
                }
            });
        });
        ";
        
        $this->view->registerJs($js);
    }
}
