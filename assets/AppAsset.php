<?php
/**
 * @link http://www.yiiframework.com/
 * @copyright Copyright (c) 2008 Yii Software LLC
 * @license http://www.yiiframework.com/license/
 */

namespace app\assets;

use yii\web\AssetBundle;

/**
 * <AUTHOR> <<EMAIL>>
 * @since 2.0
 */
class AppAsset extends AssetBundle
{
    public $basePath = '@webroot';
    public $baseUrl = '@web';
    public $css = [];
    public $js = [];
    
    public $depends = [
        'yii\web\YiiAsset',
        'yii\jui\JuiAsset',
        'yii\bootstrap\BootstrapAsset'
    ];

    public function init()
    {

        parent::init(); // TODO: Change the autogenerated stub



        $this->css = AppAsset::versionado([
            'css/site.css',
            'css/cambio_imagen_2025.css',
            'css/pnotify.css',
            'css/HoldOn.min.css',
            'css/aos.css',
        ]);

        $this->js = AppAsset::versionado([
            'js/lib/material.min.js',
            'js/lib/material-kit.js',
            'js/lib/modal-ajax.js',
            'js/lib/HoldOn.min.js',
            'js/sweetalert.min.js',
            'js/socket.io.js',
            'js/pnotify.js',
            'js/lib/aos.js',
            'js/remove_button.js',
            'js/menu_aside.js',
            'js/centrar.js',
            'js/modal.js',
            'js/modal_events.js',
            'js/select2_events.js'
        ]);
    }

    public function versionado($archivo=null){
        $items = array();
        foreach($archivo as $nombres){

            $items[] = $nombres.'?v='.filemtime($nombres);

        }

        return $items;
        // return $archivo.'?v='.filemtime($archivo);
    }

}
