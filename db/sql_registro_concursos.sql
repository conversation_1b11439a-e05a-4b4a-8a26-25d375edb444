CREATE TABLE IF NOT EXISTS public.registro_concursos (
    concurso_id bigserial NOT NULL,
    user_id bigint NOT NULL,
    numero_proceso text NOT NULL,
    tipo text NOT NULL,

    descripcion text,
    tipo_adjudicacion int,
    numero_concurso text,
    grupo_producto int, 
    fecha_inicio_inscripcion timestamp without time zone,
    fecha_limite_inscripcion timestamp without time zone,
    fecha_apertura_propuestas date,
    fecha_fallo_definitivo date,
    fecha_foro_aclaraciones timestamp without time zone,
    fecha_apertura_tecnica timestamp without time zone,
    fecha_apertura_fallo timestamp without time zone,
    fecha_operacion_subasta timestamp without time zone,
    fecha_acta_comite timestamp without time zone,
    fecha_h_fallo_definitivo timestamp without time zone,
    imagen_concurso text, 
    archivo_convocatoria text,
    ficha_tecnica text,
    archivo_ppte text,
    junta_aclaracion text,
    archivo_ftae text,
    acta_comite text,
    archivo_fd text,
    archivo_bft text,
    archivo_propuestas text,
    archivo_comite text,
    archivo_fallo text,
    archivo_aclaraciones text,
    archivo_ap_tecnica text,
    archivo_ft text,
    archivo_op_subasta text,
    video_aclaraciones text,
    video_ppte text,
    video_ftae text,
    video_fd text,
    video_apertura text,
    video_fallo text,
    video_diferir_1 text,
    video_diferir_2 text,
    video_ap_tecnica text,
    video_ft text,
    created_at timestamp without time zone NOT NULL DEFAULT now(),
    activo bool NOT NULL DEFAULT true,
    CONSTRAINT concurso_pk PRIMARY KEY (concurso_id)
)