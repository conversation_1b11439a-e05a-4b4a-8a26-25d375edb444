ALTER TABLE ONLY provider.alta_hacienda ALTER COLUMN status_bys SET DEFAULT 'EN EDICION';
ALTER TABLE ONLY provider.alta_hacienda ALTER COLUMN status_op SET DEFAULT 'EN EDICION';

ALTER TABLE ONLY provider.balance_estado ALTER COLUMN status_bys SET DEFAULT 'EN EDICION';
ALTER TABLE ONLY provider.balance_estado ALTER COLUMN status_op SET DEFAULT 'EN EDICION';

ALTER TABLE ONLY provider.capacidad_contratacion ALTER COLUMN status_bys SET DEFAULT 'EN EDICION';
ALTER TABLE ONLY provider.capacidad_contratacion ALTER COLUMN status_op SET DEFAULT 'EN EDICION';

ALTER TABLE ONLY provider.certificacion ALTER COLUMN status_bys SET DEFAULT 'EN EDICION';
ALTER TABLE ONLY provider.certificacion ALTER COLUMN status_op SET DEFAULT 'EN EDICION';

ALTER TABLE ONLY provider.clientes_contratos ALTER COLUMN status_bys SET DEFAULT 'EN EDICION';
ALTER TABLE ONLY provider.clientes_contratos ALTER COLUMN status_op SET DEFAULT 'EN EDICION';

ALTER TABLE ONLY provider.curriculum ALTER COLUMN status_bys SET DEFAULT 'EN EDICION';
ALTER TABLE ONLY provider.curriculum ALTER COLUMN status_op SET DEFAULT 'EN EDICION';

ALTER TABLE ONLY provider.datos_validados ALTER COLUMN status_bys SET DEFAULT 'EN EDICION';
ALTER TABLE ONLY provider.datos_validados ALTER COLUMN status_op SET DEFAULT 'EN EDICION';

ALTER TABLE ONLY provider.declaracion_isr ALTER COLUMN status_bys SET DEFAULT 'EN EDICION';
ALTER TABLE ONLY provider.declaracion_isr ALTER COLUMN status_op SET DEFAULT 'EN EDICION';

ALTER TABLE ONLY provider.declaracion_iva ALTER COLUMN status_bys SET DEFAULT 'EN EDICION';
ALTER TABLE ONLY provider.declaracion_iva ALTER COLUMN status_op SET DEFAULT 'EN EDICION';

ALTER TABLE ONLY provider.escritura_publica ALTER COLUMN status_bys SET DEFAULT 'EN EDICION';
ALTER TABLE ONLY provider.escritura_publica ALTER COLUMN status_op SET DEFAULT 'EN EDICION';

ALTER TABLE ONLY provider.estado_financiero ALTER COLUMN status_bys SET DEFAULT 'EN EDICION';
ALTER TABLE ONLY provider.estado_financiero ALTER COLUMN status_op SET DEFAULT 'EN EDICION';

ALTER TABLE ONLY provider.experiencia ALTER COLUMN status_bys SET DEFAULT 'EN EDICION';
ALTER TABLE ONLY provider.experiencia ALTER COLUMN status_op SET DEFAULT 'EN EDICION';

ALTER TABLE ONLY provider.fotografia_negocio ALTER COLUMN status_bys SET DEFAULT 'EN EDICION';
ALTER TABLE ONLY provider.fotografia_negocio ALTER COLUMN status_op SET DEFAULT 'EN EDICION';

ALTER TABLE ONLY provider.intervencion_bancaria ALTER COLUMN status_bys SET DEFAULT 'EN EDICION';
ALTER TABLE ONLY provider.intervencion_bancaria ALTER COLUMN status_op SET DEFAULT 'EN EDICION';

ALTER TABLE ONLY provider.maquinaria_equipos ALTER COLUMN status_bys SET DEFAULT 'EN EDICION';
ALTER TABLE ONLY provider.maquinaria_equipos ALTER COLUMN status_op SET DEFAULT 'EN EDICION';

ALTER TABLE ONLY provider.organigrama ALTER COLUMN status_bys SET DEFAULT 'EN EDICION';
ALTER TABLE ONLY provider.organigrama ALTER COLUMN status_op SET DEFAULT 'EN EDICION';

ALTER TABLE ONLY provider.personal_tecnico ALTER COLUMN status_bys SET DEFAULT 'EN EDICION';
ALTER TABLE ONLY provider.personal_tecnico ALTER COLUMN status_op SET DEFAULT 'EN EDICION';

ALTER TABLE ONLY provider.relacion_accionistas ALTER COLUMN status_bys SET DEFAULT 'EN EDICION';
ALTER TABLE ONLY provider.relacion_accionistas ALTER COLUMN status_op SET DEFAULT 'EN EDICION';

ALTER TABLE ONLY provider.rfc ALTER COLUMN status_bys SET DEFAULT 'EN EDICION';
ALTER TABLE ONLY provider.rfc ALTER COLUMN status_op SET DEFAULT 'EN EDICION';

ALTER TABLE ONLY provider.ubicacion ALTER COLUMN status_bys SET DEFAULT 'EN EDICION';
ALTER TABLE ONLY provider.ubicacion ALTER COLUMN status_op SET DEFAULT 'EN EDICION';

ALTER TABLE ONLY provider.ultima_declaracion ALTER COLUMN status_bys SET DEFAULT 'EN EDICION';
ALTER TABLE ONLY provider.ultima_declaracion ALTER COLUMN status_op SET DEFAULT 'EN EDICION';