CREATE TABLE provider.experiencia
(
  experiencia_id bigserial NOT NULL,
  descripcion text,
  monto text,
  especialidades text,
  tipo_obra text,
  id_obra bigint,
  fecha_inicio timestamp without time zone,
  fecha_termino timestamp without time zone,
  url_contrato text,
  contrato_validado text,
  url_acta_entrega text,
  acta_entrega_validado text,
  url_factura_pdf text,
  factura_pdf_validado text,
  url_factura_xml text,
  factura_xml_validado text,
  provider_id bigint NOT NULL,
  CONSTRAINT experiencia_pk PRIMARY KEY (experiencia_id),
  CONSTRAINT fk_experiencia_provider_id FOREIGN KEY (provider_id)
      REFERENCES public.provider (provider_id) MATCH SIMPLE
      ON UPDATE NO ACTION ON DELETE NO ACTION
);

CREATE TABLE provider.clientes_contratos
(
  clientes_contratos_id bigserial NOT NULL,
  nombre_razon_social text,
  persona_dirigirse text,
  telefono text,
  fecha_cliente timestamp without time zone,
  provider_id bigint NOT NULL,
  CONSTRAINT clientes_contratos_pk PRIMARY KEY (clientes_contratos_id),
  CONSTRAINT fk_clientes_provider_id FOREIGN KEY (provider_id)
      REFERENCES public.provider (provider_id) MATCH SIMPLE
      ON UPDATE NO ACTION ON DELETE NO ACTION
);
CREATE TABLE provider.certificacion
(
    certificacion_id bigserial NOT NULL,
    certificador text,
    vigencia timestamp without time zone,
    url_archivo text,
    archivo_validado text,
    provider_id bigint NOT NULL,
    CONSTRAINT certificacion_pk PRIMARY KEY (certificacion_id),
    CONSTRAINT fk_certificacion_provider_id FOREIGN KEY (provider_id)
        REFERENCES public.provider (provider_id) MATCH SIMPLE
        ON UPDATE NO ACTION ON DELETE NO ACTION
);

CREATE TABLE provider.organigrama
(
  organigrama_id bigserial NOT NULL,
  url_archivo text,
  archivo_validado text,
  provider_id bigint NOT NULL,
    CONSTRAINT organigrama_pk PRIMARY KEY (organigrama_id),
    CONSTRAINT fk_organigrama_provider_id FOREIGN KEY (provider_id)
        REFERENCES public.provider (provider_id) MATCH SIMPLE
        ON UPDATE NO ACTION ON DELETE NO ACTION
);

CREATE TABLE provider.maquinaria_equipos
(
  maquinaria_equipos_id bigserial NOT NULL,
  descripcion text,
  marca text,
  modelo text,
  capacidad_equipo text,
  num_serie text,
  factura text,
  provider_id bigint NOT NULL,
    CONSTRAINT maquinaria_equipos_pk PRIMARY KEY (maquinaria_equipos_id),
    CONSTRAINT fk_maquinaria_provider_id FOREIGN KEY (provider_id)
        REFERENCES public.provider (provider_id) MATCH SIMPLE
        ON UPDATE NO ACTION ON DELETE NO ACTION
);

CREATE TABLE provider.personal_tecnico
(
  personal_tecnico_id bigserial NOT NULL,
  rfc text,
  curp text,
  url_id_oficial text,
  id_oficial_validado bigserial,
  nombre_tecnico text,
  num_cedula_titulo text,
  url_cedula text,
  cedula_validado text,
  url_curriculum text,
  curriculum_validado text,
  provider_id bigint NOT NULL,
    CONSTRAINT personal_tecnico_pk PRIMARY KEY (personal_tecnico_id),
    CONSTRAINT fk_per_tec_provider_id FOREIGN KEY (provider_id)
        REFERENCES public.provider (provider_id) MATCH SIMPLE
        ON UPDATE NO ACTION ON DELETE NO ACTION
);

CREATE TABLE provider.fotografia_negocio
(
  fotografia_negocio_id bigserial NOT NULL,
  url_archivo text,
  archivo_validado text,
  descripcion text,
  provider_id bigint NOT NULL,
    CONSTRAINT fotografia_negocio_pk PRIMARY KEY (fotografia_negocio_id),
    CONSTRAINT fk_foto_negocio_provider_id FOREIGN KEY (provider_id)
        REFERENCES public.provider (provider_id) MATCH SIMPLE
        ON UPDATE NO ACTION ON DELETE NO ACTION
);

CREATE TABLE provider.curriculum
(
  curriculum_id bigserial NOT NULL,
  url_archivo text,
  archivo_validado text,
  provider_id bigint NOT NULL,
    CONSTRAINT curriculum_pk PRIMARY KEY (curriculum_id),
    CONSTRAINT fk_curriculum_provider_id FOREIGN KEY (provider_id)
        REFERENCES public.provider (provider_id) MATCH SIMPLE
        ON UPDATE NO ACTION ON DELETE NO ACTION
);

CREATE TABLE provider.capacidad_produccion
(
  capacidad_produccion_id bigserial NOT NULL,
  oficina text,
  oficina_domicilio text,
  taller text,
  taller_domicilio text,
  sucursal text,
  sucursal_domicilio text,
  bodega text,
  bodega_domicilio text,
  otra text,
  otra_domicilio text,
  vehiculos text,
  vehiculos_cantidad numeric,
  maquinaria text,
  maquinaria_cantidad numeric,
  equipo_computo text,
  equipo_computo_cantidad numeric,
  equipo_oficina text,
  equipo_oficina_cantidad numeric,
  otro text,
  otro_cantidad numeric,
  provider_id bigint NOT NULL,
    CONSTRAINT capacidad_produccion_pk PRIMARY KEY (capacidad_produccion_id),
    CONSTRAINT fk_capacidad_provider_id FOREIGN KEY (provider_id)
        REFERENCES public.provider (provider_id) MATCH SIMPLE
        ON UPDATE NO ACTION ON DELETE NO ACTION
  );