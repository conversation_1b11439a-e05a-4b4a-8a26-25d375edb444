Insert into unidad_medida(nombre) values('UNIDAD');
Insert into unidad_medida(nombre) values('NO APLICA');
Insert into unidad_medida(nombre) values('LOTE');
Insert into unidad_medida(nombre) values('LITROS');
Insert into unidad_medida(nombre) values('N/A');
Insert into unidad_medida(nombre) values('BOLSA');
Insert into unidad_medida(nombre) values('METRO');
Insert into unidad_medida(nombre) values('PIEZA');
Insert into unidad_medida(nombre) values('BLOCK');
Insert into unidad_medida(nombre) values('METRO CUADRADO');
Insert into unidad_medida(nombre) values('CIENTO');
Insert into unidad_medida(nombre) values('PQUETE');
Insert into unidad_medida(nombre) values('MILES');
Insert into unidad_medida(nombre) values('ROLLOS');
Insert into unidad_medida(nombre) values('ROLLO');
Insert into unidad_medida(nombre) values('SERVICIO');
Insert into unidad_medida(nombre) values('MILLAR');
Insert into unidad_medida(nombre) values('CAJA');
Insert into unidad_medida(nombre) values('LITRO');
Insert into unidad_medida(nombre) values('PAR');
Insert into unidad_medida(nombre) values('CAJAS');
Insert into unidad_medida(nombre) values('GALÓN (EE.UU.)');
Insert into unidad_medida(nombre) values('PAQUETE');
Insert into unidad_medida(nombre) values('KILOGRAMO');
Insert into unidad_medida(nombre) values('GALÓN');
Insert into unidad_medida(nombre) values('JUEGO');
Insert into unidad_medida(nombre) values('METRO CÚBICO');
Insert into unidad_medida(nombre) values('BULTO');