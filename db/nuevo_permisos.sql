TRUNCATE public.permission_view_module_user RESTART IDENTITY;
TRUNCATE public.permission_view_module RESTART IDENTITY CASCADE;

INSERT INTO public.permission_view_module(permission, module) values ('Perfil (ByS)','bys_perfil');
INSERT INTO public.permission_view_module(permission, module) values ('Datos Legales (ByS)','bys_legales');
INSERT INTO public.permission_view_module(permission, module) values ('Actividad Económica (ByS)', 'bys_bys');
INSERT INTO public.permission_view_module(permission, module) values ('Experiencia Comercial (ByS)','bys_experiencia');
INSERT INTO public.permission_view_module(permission, module) values ('Capacidad Economica (ByS)','bys_economica');
INSERT INTO public.permission_view_module(permission, module) values ('Establecimientos (ByS)','bys_domicilio');
INSERT INTO public.permission_view_module(permission, module) values ('<PERSON><PERSON> (ByS)','bys_bancos');
INSERT INTO public.permission_view_module(permission, module) values ('Carta Protesta (ByS)','bys_carta');
INSERT INTO public.permission_view_module(permission, module) values ('Acreditacion de Curso (ByS)','bys_cursos');
INSERT INTO public.permission_view_module(permission, module) values ('Certificado (ByS)','bys_certificado');
INSERT INTO public.permission_view_module(permission, module) values ('Visita (ByS)','bys_visita');

INSERT INTO public.permission_view_module(permission, module) values ('Perfil (OP)','op_perfil');
INSERT INTO public.permission_view_module(permission, module) values ('Actividad Económica (OP)', 'op_economica');
INSERT INTO public.permission_view_module(permission, module) values ('Datos Legales (OP)','op_legales');
INSERT INTO public.permission_view_module(permission, module) values ('Ubicación (OP)','op_ubicacion');
INSERT INTO public.permission_view_module(permission, module) values ('Fotografía del Negocio (OP)','op_fotografia');
INSERT INTO public.permission_view_module(permission, module) values ('Experiencia (OP)','op_experiencia');
INSERT INTO public.permission_view_module(permission, module) values ('Datos Financieros (OP)','op_financieros');
INSERT INTO public.permission_view_module(permission, module) values ('Datos Técnicos (OP)','op_tecnicos');
INSERT INTO public.permission_view_module(permission, module) values ('Maquinaria (OP)','op_maquinaria');
INSERT INTO public.permission_view_module(permission, module) values ('Método de pago (OP)','op_mpago');
INSERT INTO public.permission_view_module(permission, module) values ('Carta Protesta (OP)','op_carta');
INSERT INTO public.permission_view_module(permission, module) values ('Acreditacion de Curso (OP)','op_cursos');
INSERT INTO public.permission_view_module(permission, module) values ('Certificado (OP)','op_certificado');
INSERT INTO public.permission_view_module(permission, module) values ('Visita (OP)','op_visita');