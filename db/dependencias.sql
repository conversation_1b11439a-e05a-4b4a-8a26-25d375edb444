insert into provider.auditor_dependencia(clave_dependencia,nombre) values('CODETUR','CORPORACIÓN PARA EL DESARROLLO TURÍSTICO DE NUEVO LEÓN');
insert into provider.auditor_dependencia(clave_dependencia,nombre) values('CTG','CONTRALORÍA Y TRANSPARENCIA GUBERNAMENTAL');
insert into provider.auditor_dependencia(clave_dependencia,nombre) values('FECNL','FIDEICOMISO PARA LAS ESCUELAS DE CALIDAD DEL ESTADO DE NUEVO LEÓN');
insert into provider.auditor_dependencia(clave_dependencia,nombre) values('FIDEPROES-003','FIDEICOMISO DE PROYECTOS ESTRATÉGICOS');
insert into provider.auditor_dependencia(clave_dependencia,nombre) values('CRPI','COORDINACIÓN DE RELACIONES PUBLICAS INSTITUCIONALES');
insert into provider.auditor_dependencia(clave_dependencia,nombre) values('SRT','SISTEMA DE RADIO Y TELEVISIÓN');
insert into provider.auditor_dependencia(clave_dependencia,nombre) values('FIRECOM','FIDEICOMISO PARA LA REORDENACIÓN COMERCIAL');
insert into provider.auditor_dependencia(clave_dependencia,nombre) values('CEAPE','COORDINACIÓN EJECUTIVA DE LA ADMINISTRACIÓN PÚBLICA DEL ESTADO');
insert into provider.auditor_dependencia(clave_dependencia,nombre) values('OEG','OFICINA EJECUTIVA DEL GOBERNADOR');
insert into provider.auditor_dependencia(clave_dependencia,nombre) values('SINTRAM','FIDEICOMISO PARA EL SISTEMA INTEGRAL DE TRÁNSITO METROPOLITANO');
insert into provider.auditor_dependencia(clave_dependencia,nombre) values('IEPAM','INSTITUTO ESTATAL DE LAS PERSONAS ADULTAS MAYORES');
insert into provider.auditor_dependencia(clave_dependencia,nombre) values('TJA','TRIBUNAL DE JUSTICIA ADMINISTRATIVA');
insert into provider.auditor_dependencia(clave_dependencia,nombre) values('JLCyA','JUNTA LOCAL DE CONCILIACIÓN Y ARBITRAJE');
insert into provider.auditor_dependencia(clave_dependencia,nombre) values('CBM','COLEGIO DE BACHILLERES MILITARIZADO GENERAL MARIANO ESCOBEDO');
insert into provider.auditor_dependencia(clave_dependencia,nombre) values('TPEySPG','TITULAR DEL PODER EJECUTIVO Y SECRETARIA PARTICULAR DEL GOBERNADOR');
insert into provider.auditor_dependencia(clave_dependencia,nombre) values('CDANL','CORPORACION PARA EL DESARROLLO AGROPECUARIO DE NUEVO LEON');
insert into provider.auditor_dependencia(clave_dependencia,nombre) values('CEE','COMISION ESTATAL ELECTORAL');
insert into provider.auditor_dependencia(clave_dependencia,nombre) values('SPG','SECRETARÍA PARTICULAR DEL GOBERNADOR');
insert into provider.auditor_dependencia(clave_dependencia,nombre) values('FIDEIN','FIDEICOMISO REVOCABLE DE TRASLATIVO DE DOMINIO Y DE ADMINISTRACIÓN DE INMUEBLES');
insert into provider.auditor_dependencia(clave_dependencia,nombre) values('TA','TRIBUNAL DE ARBITRAJE');
insert into provider.auditor_dependencia(clave_dependencia,nombre) values('FIDEMEJORA','FIDEICOMISO DE INVERSIÓN Y ADMINISTRACIÓN');
insert into provider.auditor_dependencia(clave_dependencia,nombre) values('IEENL','INSTITUTO DE EVALUACION EDUCATIVA DE NUEVO LEON');
insert into provider.auditor_dependencia(clave_dependencia,nombre) values('FIDEESAGRO','FIDEICOMISO ESTATAL AGROPECUARIO');
insert into provider.auditor_dependencia(clave_dependencia,nombre) values('COCyTENL','COORDINACIÓN DE CIENCIA Y TECNOLOGÍA DE NUEVO LEÓN');
insert into provider.auditor_dependencia(clave_dependencia,nombre) values('CODEFRONT','CORPORACIÓN PARA EL DESARROLLO DE LA ZONA FRONTERIZA DE NUEVO LEÓN');
insert into provider.auditor_dependencia(clave_dependencia,nombre) values('CEV','CONSEJO ESTATAL DE VALORES');
insert into provider.auditor_dependencia(clave_dependencia,nombre) values('FIDEESCAL','FIDEICOMISO ESCUELAS DE CALIDAD');
insert into provider.auditor_dependencia(clave_dependencia,nombre) values('FIDEVALLE','FIDEICOMISO PARA LA REALIZACIÓN DE OBRAS VIALES EN LA ZONA DE VALLE ORIENTE Y ÁREAS ADYACENTES.');
insert into provider.auditor_dependencia(clave_dependencia,nombre) values('FEM','FIDEICOMISO ELEVEMOS MÉXICO');
insert into provider.auditor_dependencia(clave_dependencia,nombre) values('FIDECITRUS','FIDEICOMISO PARA EL DESARROLLO DE LA ZONA CITRÍCOLA DEL ESTADO DE NUEVO LEON');
insert into provider.auditor_dependencia(clave_dependencia,nombre) values('CONALEP','COLEGIO DE EDUCACIÓN PROFESIONAL TÉCNICA DE NUEVO LEÓN');
insert into provider.auditor_dependencia(clave_dependencia,nombre) values('CONARTE','CONSEJO PARA LA CULTURA Y LAS ARTES DE NUEVO LEÓN');
insert into provider.auditor_dependencia(clave_dependencia,nombre) values('IVNL','INSTITUTO DE LA VIVIENDA DE NUEVO LEÓN');
insert into provider.auditor_dependencia(clave_dependencia,nombre) values('FONDEN','FIDEICOMISO DE INVERSIÓN Y FUENTE DE PAGO 1976 FONDEN, NUEVO LEÓN');
insert into provider.auditor_dependencia(clave_dependencia,nombre) values('DIFNL','SISTEMA PARA EL DESARROLLO INTEGRAL DE LA FAMILIA');
insert into provider.auditor_dependencia(clave_dependencia,nombre) values('FFENL','FIDEICOMISO FONDO EDITORIAL DE NUEVO LEÓN');
insert into provider.auditor_dependencia(clave_dependencia,nombre) values('FOGALEON','FIDEICOMISO FONDO DE GARANTÍA PARA LAS EMPRESAS EN SOLIDARIDAD DEL ESTADO DE NUEVO LEÓN');
insert into provider.auditor_dependencia(clave_dependencia,nombre) values('CETyV','CONSEJO ESTATAL DE TRANSPORTE Y VIALIDAD');
insert into provider.auditor_dependencia(clave_dependencia,nombre) values('FMNHN','FIDEICOMISO MUSEO NACIONAL DE HISTORIA NATURAL');
insert into provider.auditor_dependencia(clave_dependencia,nombre) values('FOMERREY','FOMENTO METROPOLITANO DE MONTERREY');
insert into provider.auditor_dependencia(clave_dependencia,nombre) values('FOVILEON-BUROCRATAS','FIDEICOMISO FONDO PARA LA VIVIENDA DE LOS TRABAJADORES AL SERVICIO DEL ESTADO');
insert into provider.auditor_dependencia(clave_dependencia,nombre) values('ISSSTELEON','INSTITUTO DE SEGURIDAD Y SERVICIOS SOCIALES DE LOS TRABAJADORES DEL ESTADO DE N.L.');
insert into provider.auditor_dependencia(clave_dependencia,nombre) values('UT','UNIVERSIDAD BILINGÜE FRANCO MEXICANA DE NUEVO LEON');
insert into provider.auditor_dependencia(clave_dependencia,nombre) values('INDE','INSTITUTO ESTATAL DE CULTURA FÍSICA Y DEPORTE');
insert into provider.auditor_dependencia(clave_dependencia,nombre) values('IESP','INSTITUTO ESTATAL DE SEGURIDAD PÚBLICA');
insert into provider.auditor_dependencia(clave_dependencia,nombre) values('FECTEC','FIDEICOMISO FONDO PARA LA EDUCACIÓN, LA CIENCIA Y TECNOLOGÍA APLICADAS AL CAMPO DE NUEVO LEÓN');
insert into provider.auditor_dependencia(clave_dependencia,nombre) values('IEM','INSTITUTO ESTATAL DE LAS MUJERES');
insert into provider.auditor_dependencia(clave_dependencia,nombre) values('PRUEBA','DEPENDENCIA PRUEBA');
insert into provider.auditor_dependencia(clave_dependencia,nombre) values('FOMIX','FIDEICOMISO FONDO MIXTO CONACYT - ESTADO DE NUEVO LEÓN');
insert into provider.auditor_dependencia(clave_dependencia,nombre) values('IEJ','INSTITUTO ESTATAL DE LA JUVENTUD');
insert into provider.auditor_dependencia(clave_dependencia,nombre) values('IDPNL','INSTITUTO DE DEFENSORÍA PÚBLICA DE NUEVO LEÓN');
insert into provider.auditor_dependencia(clave_dependencia,nombre) values('ICET','INSTITUTO DE CAPACITACIÓN Y EDUCACIÓN PARA EL TRABAJO DEL ESTADO DE NUEVO LEÓN, A.C.');
insert into provider.auditor_dependencia(clave_dependencia,nombre) values('IIIEPENL','INSTITUTO DE INVESTIGACIÓN, INNOVACIÓN Y ESTUDIOS DE POSGRADO PARA LA EDUCACIÓN DEL ESTADO DE NUEVO LEÓN.');
insert into provider.auditor_dependencia(clave_dependencia,nombre) values('CDSOLIDARIDAD','FIDEICOMISO PÚBLICO DE ADMINISTRACIÓN Y TRASLATIVO DE DOMINIO CIUDAD SOLIDARIDAD');
insert into provider.auditor_dependencia(clave_dependencia,nombre) values('CECyTENL','COLEGIO DE ESTUDIOS CIENTÍFICOS Y TECNOLÓGICOS DEL ESTADO DE NUEVO LEÓN');
insert into provider.auditor_dependencia(clave_dependencia,nombre) values('FIDEZA','FIDEICOMISO ZARAGOZA');
insert into provider.auditor_dependencia(clave_dependencia,nombre) values('FISL','FIDEICOMISO FESTIVAL INTERNACIONAL DE SANTA LUCÍA');
insert into provider.auditor_dependencia(clave_dependencia,nombre) values('FOCRECE','FIDEICOMISO FONDO DE APOYO PARA LA CREACIÓN Y CONSOLIDACIÓN DEL EMPLEO PRODUCTIVO EN EL ESTADO DE NUEVO LEÓN');
insert into provider.auditor_dependencia(clave_dependencia,nombre) values('PRONABES-ESTATAL','FIDEICOMISO PROGRAMA NACIONAL DE BECAS DE EDUCACIÓN PARA EL ESTADO DE NUEVO LEÓN');
insert into provider.auditor_dependencia(clave_dependencia,nombre) values('LINEA3','FIDEICOMISO NO. 2209 LÍNEA 3 DEL SISTEMA DE TRANSPORTE COLECTIVO METRORREY');
insert into provider.auditor_dependencia(clave_dependencia,nombre) values('ICVNL','INSTITUTO DE CONTROL VEHICULAR');
insert into provider.auditor_dependencia(clave_dependencia,nombre) values('ICIFED','INSTITUTO CONSTRUCTOR DE INFRAESTRUCTURA FÍSICA EDUCATIVA Y DEPORTIVA DE NUEVO LEÓN');
insert into provider.auditor_dependencia(clave_dependencia,nombre) values('IANL','INSTITUTO DEL AGUA DEL ESTADO DE NUEVO LEÓN');
insert into provider.auditor_dependencia(clave_dependencia,nombre) values('I2T2','INSTITUTO DE INNOVACIÓN Y TRANSFERENCIA DE TECNOLOGÍA DE NUEVO LEÓN');
insert into provider.auditor_dependencia(clave_dependencia,nombre) values('FVSNL','FIDEICOMISO DE VIDA SILVESTRE');
insert into provider.auditor_dependencia(clave_dependencia,nombre) values('FTNL','FIDEICOMISO TURISMO NUEVO LEÓN');
insert into provider.auditor_dependencia(clave_dependencia,nombre) values('FPTEIMNL','FIDEICOMISO PROGRAMA DE TECNOLOGÍAS EDUCATIVAS Y DE LA INFORMACIÓN PARA EL MAGISTERIO DEL ESTADO DE NUEVO LEÓN');
insert into provider.auditor_dependencia(clave_dependencia,nombre) values('FPIS','FIDEICOMISO PUENTE INTERNACIONAL SOLIDARIDAD');
insert into provider.auditor_dependencia(clave_dependencia,nombre) values('FOVILEON-EDUCACION','FIDEICOMISO FONDO PARA LA VIVIENDA DE LOS TRABAJADORES DE LA EDUCACIÓN');
insert into provider.auditor_dependencia(clave_dependencia,nombre) values('FOFAE','FIDEICOMISO FONDO DE FOMENTO AGROPECUARIO DEL ESTADO DE NUEVO LEÓN');
insert into provider.auditor_dependencia(clave_dependencia,nombre) values('FIDESUR','FIDEICOMISO PARA EL DESARROLLO DEL SUR DEL ESTADO DE NUEVO LEÓN');
insert into provider.auditor_dependencia(clave_dependencia,nombre) values('RGECM','REPRESENTACIÓN DEL GOBIERNO DEL ESTADO EN LA CIUDAD DE MÉXICO');
insert into provider.auditor_dependencia(clave_dependencia,nombre) values('SAyDM','SERVICIOS DE AGUA Y DRENAJE DE MONTERREY, I.P.D.');
insert into provider.auditor_dependencia(clave_dependencia,nombre) values('SSNL','SERVICIOS DE SALUD DE NUEVO LEÓN, O.P.D.');
insert into provider.auditor_dependencia(clave_dependencia,nombre) values('FIDEPROES','FIDEICOMISO PROMOTOR DE PROYECTOS ESTRATÉGICOS URBANOS');
insert into provider.auditor_dependencia(clave_dependencia,nombre) values('PGJ','PROCURADURÍA GENERAL DE JUSTICIA');
insert into provider.auditor_dependencia(clave_dependencia,nombre) values('UCS','UNIVERSIDAD DE CIENCIAS DE LA SEGURIDAD');
insert into provider.auditor_dependencia(clave_dependencia,nombre) values('SIJUPE','FIDEICOMISO NO. 2236 DEL ESTADO DE NUEVO LEÓN PARA LA IMPLEMENTACIÓN DEL SISTEMA DE JUSTICIA PENAL');
insert into provider.auditor_dependencia(clave_dependencia,nombre) values('UTSC','UNIVERSIDAD TECNOLÓGICA SANTA CATARINA');
insert into provider.auditor_dependencia(clave_dependencia,nombre) values('UTL','UNIVERSIDAD TECNOLÓGICA LINARES');
insert into provider.auditor_dependencia(clave_dependencia,nombre) values('UTGME','UNIVERSIDAD TECNOLÓGICA GRAL. MARIANO ESCOBEDO');
insert into provider.auditor_dependencia(clave_dependencia,nombre) values('UTC','UNIVERSIDAD TECNOLÓGICA CADEREYTA');
insert into provider.auditor_dependencia(clave_dependencia,nombre) values('UPG','UNIVERSIDAD POLITÉCNICA DE GARCÍA');
insert into provider.auditor_dependencia(clave_dependencia,nombre) values('UPA','UNIVERSIDAD POLITÉCNICA DE APODACA');
insert into provider.auditor_dependencia(clave_dependencia,nombre) values('UIE','UNIDAD DE INTEGRACIÓN EDUCATIVA');
insert into provider.auditor_dependencia(clave_dependencia,nombre) values('SSP','SECRETARÍA DE SEGURIDAD PÚBLICA');
insert into provider.auditor_dependencia(clave_dependencia,nombre) values('SS','SECRETARÍA DE SALUD');
insert into provider.auditor_dependencia(clave_dependencia,nombre) values('SIMEPRODE','SISTEMA INTEGRAL PARA EL MANEJO ECOLÓGICO Y PROCESAMIENTO DE DESECHOS');
insert into provider.auditor_dependencia(clave_dependencia,nombre) values('SI','SECRETARÍA DE INFRAESTRUCTURA');
insert into provider.auditor_dependencia(clave_dependencia,nombre) values('SGG','SECRETARÍA GENERAL DE GOBIERNO');
insert into provider.auditor_dependencia(clave_dependencia,nombre) values('SFyTGE','SECRETARÍA DE FINANZAS Y TESORERÍA GENERAL DEL ESTADO');
insert into provider.auditor_dependencia(clave_dependencia,nombre) values('SET','SECRETARÍA DE ECONOMÍA Y TRABAJO');
insert into provider.auditor_dependencia(clave_dependencia,nombre) values('SEDESOL','SECRETARÍA DE DESARROLLO SOCIAL');
insert into provider.auditor_dependencia(clave_dependencia,nombre) values('SE','SECRETARÍA DE EDUCACIÓN');
insert into provider.auditor_dependencia(clave_dependencia,nombre) values('SDSUS','SECRETARÍA DE DESARROLLO SUSTENTABLE');
insert into provider.auditor_dependencia(clave_dependencia,nombre) values('SDA','SECRETARÍA DE DESARROLLO AGROPECUARIO');
insert into provider.auditor_dependencia(clave_dependencia,nombre) values('SCNL','SISTEMA DE CAMINOS DE NUEVO LEÓN');
insert into provider.auditor_dependencia(clave_dependencia,nombre) values('REANL','RED ESTATAL DE AUTOPISTAS DE NUEVO LEÓN');
insert into provider.auditor_dependencia(clave_dependencia,nombre) values('PVSNL','PARQUES Y VIDA SILVESTRE DE NUEVO LEÓN');
insert into provider.auditor_dependencia(clave_dependencia,nombre) values('PRODERLEON','PROMOTORA DE DESARROLLO RURAL DE NUEVO LEÓN');
insert into provider.auditor_dependencia(clave_dependencia,nombre) values('PF','PARQUE FUNDIDORA');
insert into provider.auditor_dependencia(clave_dependencia,nombre) values('OSETUR','OPERADORA DE SERVICIOS TURÍSTICOS DE NUEVO LEÓN');
insert into provider.auditor_dependencia(clave_dependencia,nombre) values('MHM','MUSEO DE HISTORIA MEXICANA');
insert into provider.auditor_dependencia(clave_dependencia,nombre) values('METRORREY','SISTEMA DE TRANSPORTE COLECTIVO');
insert into provider.auditor_dependencia(clave_dependencia,nombre) values('IRCNL','INSTITUTO REGISTRAL Y CATASTRAL DEL ESTADO DE NUEVO LEÓN');
insert into provider.auditor_dependencia(clave_dependencia,nombre) values('SA','SECRETARIA DE ADMINISTRACIÓN');
insert into provider.auditor_dependencia(clave_dependencia,nombre) values('FONMETRO','FIDEICOMISO FONDO METROPOLITANO CIUDAD DE MONTERREY');
insert into provider.auditor_dependencia(clave_dependencia,nombre) values('FIPREBOCA','FIDEICOMISO PRESA DE LA BOCA (FIPREBOCA)');
insert into provider.auditor_dependencia(clave_dependencia,nombre) values('RPSS','RÉGIMEN DE PROTECCIÓN SOCIAL EN LA SALUD');
insert into provider.auditor_dependencia(clave_dependencia,nombre) values('AETNL','AGENCIA PARA LA RACIONALIZACIÓN Y MODERNIZACIÓN DEL SISTEMA DE TRANSPORTE PÚBLICO DE NUEVO LEÓN');
insert into provider.auditor_dependencia(clave_dependencia,nombre) values('IMANL','INSTITUTO DE MOVILIDAD Y ACCESIBILIDAD DE NUEVO LEÓN');