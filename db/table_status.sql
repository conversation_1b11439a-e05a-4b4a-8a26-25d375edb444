
CREATE TABLE provider.status
(
  status_id    BIGSERIAL NOT NULL,
  register_id  BIGINT NOT NULL,
  modelo       TEXT NOT NULL,
  motivo       TEXT NOT NULL,
  created_date TIMESTAMP WITHOUT TIME ZONE NOT NULL DEFAULT NOW(),
  created_id   BIGINT NULL,
  status       TEXT NOT NULL,
  CONSTRAINT status_pk PRIMARY KEY (status_id),
  CONSTRAINT fk_created_id_status FOREIGN KEY (created_id)
      REFERENCES public.usuarios (user_id) MATCH SIMPLE
      ON UPDATE NO ACTION ON DELETE NO ACTION
);
