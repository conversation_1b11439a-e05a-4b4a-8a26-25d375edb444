insert into dependencia(nombre) values('AGEN<PERSON><PERSON> PARA LA RACIONALIZACIÓN Y MODERNIZ<PERSON>IÓN DEL SISTEMA DE TRANSPORTE PÚBLICO DE NUEVO LEÓN');
insert into dependencia(nombre) values('COLE<PERSON><PERSON> DE BACHILLERES MILITARIZADO GENERAL MARIANO ESCOBEDO');
insert into dependencia(nombre) values('COLEGIO DE EDUCACIÓN PROFESIONAL TÉCNICA DE NUEVO LEÓN');
insert into dependencia(nombre) values('COLEGIO DE ESTUDIOS CIENTÍFICOS Y TECNOLÓGICOS DEL ESTADO DE NUEVO LEÓN');
insert into dependencia(nombre) values('COMISION ESTATAL ELECTORAL');
insert into dependencia(nombre) values('CONSEJO ESTATAL DE TRANSPORTE Y VIALIDAD');
insert into dependencia(nombre) values('CONSEJO ESTATAL DE VALORES');
insert into dependencia(nombre) values('CONSEJO PARA LA CULTURA Y LAS ARTES DE NUEVO LEÓN');
insert into dependencia(nombre) values('CONTRA<PERSON>ORÍA Y TRANSPARENCIA GUBERNAMENTAL');
insert into dependencia(nombre) values('COORDINACIÓN DE CIENCIA Y TECNOLOGÍA DE NUEVO LEÓN');
insert into dependencia(nombre) values('COORDINACIÓN DE RELACIONES PUBLICAS INSTITUCIONALES');
insert into dependencia(nombre) values('COORDINACIÓN EJECUTIVA DE LA ADMINISTRACIÓN PÚBLICA DEL ESTADO');
insert into dependencia(nombre) values('CORPORACION PARA EL DESARROLLO AGROPECUARIO DE NUEVO LEON');
insert into dependencia(nombre) values('CORPORACIÓN PARA EL DESARROLLO DE LA ZONA FRONTERIZA DE NUEVO LEÓN');
insert into dependencia(nombre) values('CORPORACIÓN PARA EL DESARROLLO TURÍSTICO DE NUEVO LEÓN');
insert into dependencia(nombre) values('DEPENDENCIA PRUEBA');
insert into dependencia(nombre) values('FIDEICOMISO DE INVERSIÓN Y ADMINISTRACIÓN');
insert into dependencia(nombre) values('FIDEICOMISO DE INVERSIÓN Y FUENTE DE PAGO 1976 FONDEN, NUEVO LEÓN');
insert into dependencia(nombre) values('FIDEICOMISO DE PROYECTOS ESTRATÉGICOS');
insert into dependencia(nombre) values('FIDEICOMISO DE VIDA SILVESTRE');
insert into dependencia(nombre) values('FIDEICOMISO ELEVEMOS MÉXICO');
insert into dependencia(nombre) values('FIDEICOMISO ESCUELAS DE CALIDAD');
insert into dependencia(nombre) values('FIDEICOMISO ESTATAL AGROPECUARIO');
insert into dependencia(nombre) values('FIDEICOMISO FESTIVAL INTERNACIONAL DE SANTA LUCÍA');
insert into dependencia(nombre) values('FIDEICOMISO FONDO DE APOYO PARA LA CREACIÓN Y CONSOLIDACIÓN DEL EMPLEO PRODUCTIVO EN EL ESTADO DE NUEVO LEÓN');
insert into dependencia(nombre) values('FIDEICOMISO FONDO DE FOMENTO AGROPECUARIO DEL ESTADO DE NUEVO LEÓN');
insert into dependencia(nombre) values('FIDEICOMISO FONDO DE GARANTÍA PARA LAS EMPRESAS EN SOLIDARIDAD DEL ESTADO DE NUEVO LEÓN');
insert into dependencia(nombre) values('FIDEICOMISO FONDO EDITORIAL DE NUEVO LEÓN');
insert into dependencia(nombre) values('FIDEICOMISO FONDO METROPOLITANO CIUDAD DE MONTERREY');
insert into dependencia(nombre) values('FIDEICOMISO FONDO MIXTO CONACYT - ESTADO DE NUEVO LEÓN');
insert into dependencia(nombre) values('FIDEICOMISO FONDO PARA LA EDUCACIÓN, LA CIENCIA Y TECNOLOGÍA APLICADAS AL CAMPO DE NUEVO LEÓN');
insert into dependencia(nombre) values('FIDEICOMISO FONDO PARA LA VIVIENDA DE LOS TRABAJADORES AL SERVICIO DEL ESTADO');
insert into dependencia(nombre) values('FIDEICOMISO FONDO PARA LA VIVIENDA DE LOS TRABAJADORES DE LA EDUCACIÓN');
insert into dependencia(nombre) values('FIDEICOMISO MUSEO NACIONAL DE HISTORIA NATURAL');
insert into dependencia(nombre) values('FIDEICOMISO NO. 2209 LÍNEA 3 DEL SISTEMA DE TRANSPORTE COLECTIVO METRORREY');
insert into dependencia(nombre) values('FIDEICOMISO NO. 2236 DEL ESTADO DE NUEVO LEÓN PARA LA IMPLEMENTACIÓN DEL SISTEMA DE JUSTICIA PENAL');
insert into dependencia(nombre) values('FIDEICOMISO PARA EL DESARROLLO DE LA ZONA CITRÍCOLA DEL ESTADO DE NUEVO LEON');
insert into dependencia(nombre) values('FIDEICOMISO PARA EL DESARROLLO DEL SUR DEL ESTADO DE NUEVO LEÓN');
insert into dependencia(nombre) values('FIDEICOMISO PARA EL SISTEMA INTEGRAL DE TRÁNSITO METROPOLITANO');
insert into dependencia(nombre) values('FIDEICOMISO PARA LA REALIZACIÓN DE OBRAS VIALES EN LA ZONA DE VALLE ORIENTE Y ÁREAS ADYACENTES.');
insert into dependencia(nombre) values('FIDEICOMISO PARA LA REORDENACIÓN COMERCIAL');
insert into dependencia(nombre) values('FIDEICOMISO PARA LAS ESCUELAS DE CALIDAD DEL ESTADO DE NUEVO LEÓN');
insert into dependencia(nombre) values('FIDEICOMISO PRESA DE LA BOCA (FIPREBOCA)');
insert into dependencia(nombre) values('FIDEICOMISO PROGRAMA DE TECNOLOGÍAS EDUCATIVAS Y DE LA INFORMACIÓN PARA EL MAGISTERIO DEL ESTADO DE NUEVO LEÓN');
insert into dependencia(nombre) values('FIDEICOMISO PROGRAMA NACIONAL DE BECAS DE EDUCACIÓN PARA EL ESTADO DE NUEVO LEÓN');
insert into dependencia(nombre) values('FIDEICOMISO PROMOTOR DE PROYECTOS ESTRATÉGICOS URBANOS');
insert into dependencia(nombre) values('FIDEICOMISO PÚBLICO DE ADMINISTRACIÓN Y TRASLATIVO DE DOMINIO CIUDAD SOLIDARIDAD');
insert into dependencia(nombre) values('FIDEICOMISO PUENTE INTERNACIONAL SOLIDARIDAD');
insert into dependencia(nombre) values('FIDEICOMISO REVOCABLE DE TRASLATIVO DE DOMINIO Y DE ADMINISTRACIÓN DE INMUEBLES');
insert into dependencia(nombre) values('FIDEICOMISO TURISMO NUEVO LEÓN');
insert into dependencia(nombre) values('FIDEICOMISO ZARAGOZA');
insert into dependencia(nombre) values('FOMENTO METROPOLITANO DE MONTERREY');
insert into dependencia(nombre) values('INSTITUTO CONSTRUCTOR DE INFRAESTRUCTURA FÍSICA EDUCATIVA Y DEPORTIVA DE NUEVO LEÓN');
insert into dependencia(nombre) values('INSTITUTO DE CAPACITACIÓN Y EDUCACIÓN PARA EL TRABAJO DEL ESTADO DE NUEVO LEÓN, A.C.');
insert into dependencia(nombre) values('INSTITUTO DE CONTROL VEHICULAR');
insert into dependencia(nombre) values('INSTITUTO DE DEFENSORÍA PÚBLICA DE NUEVO LEÓN');
insert into dependencia(nombre) values('INSTITUTO DE EVALUACION EDUCATIVA DE NUEVO LEON');
insert into dependencia(nombre) values('INSTITUTO DE INNOVACIÓN Y TRANSFERENCIA DE TECNOLOGÍA DE NUEVO LEÓN');
insert into dependencia(nombre) values('INSTITUTO DE INVESTIGACIÓN, INNOVACIÓN Y ESTUDIOS DE POSGRADO PARA LA EDUCACIÓN DEL ESTADO DE NUEVO LEÓN.');
insert into dependencia(nombre) values('INSTITUTO DE LA VIVIENDA DE NUEVO LEÓN');
insert into dependencia(nombre) values('INSTITUTO DE SEGURIDAD Y SERVICIOS SOCIALES DE LOS TRABAJADORES DEL ESTADO DE N.L.');
insert into dependencia(nombre) values('INSTITUTO DEL AGUA DEL ESTADO DE NUEVO LEÓN');
insert into dependencia(nombre) values('INSTITUTO ESTATAL DE CULTURA FÍSICA Y DEPORTE');
insert into dependencia(nombre) values('INSTITUTO ESTATAL DE LA JUVENTUD');
insert into dependencia(nombre) values('INSTITUTO ESTATAL DE LAS MUJERES');
insert into dependencia(nombre) values('INSTITUTO ESTATAL DE LAS PERSONAS ADULTAS MAYORES');
insert into dependencia(nombre) values('INSTITUTO ESTATAL DE SEGURIDAD PÚBLICA');
insert into dependencia(nombre) values('INSTITUTO REGISTRAL Y CATASTRAL DEL ESTADO DE NUEVO LEÓN');
insert into dependencia(nombre) values('JUNTA LOCAL DE CONCILIACIÓN Y ARBITRAJE');
insert into dependencia(nombre) values('MUSEO DE HISTORIA MEXICANA');
insert into dependencia(nombre) values('OFICINA EJECUTIVA DEL GOBERNADOR');
insert into dependencia(nombre) values('OPERADORA DE SERVICIOS TURÍSTICOS DE NUEVO LEÓN');
insert into dependencia(nombre) values('PARQUE FUNDIDORA');
insert into dependencia(nombre) values('PARQUES Y VIDA SILVESTRE DE NUEVO LEÓN');
insert into dependencia(nombre) values('PROCURADURÍA GENERAL DE JUSTICIA');
insert into dependencia(nombre) values('PROMOTORA DE DESARROLLO RURAL DE NUEVO LEÓN');
insert into dependencia(nombre) values('RED ESTATAL DE AUTOPISTAS DE NUEVO LEÓN');
insert into dependencia(nombre) values('RÉGIMEN DE PROTECCIÓN SOCIAL EN LA SALUD');
insert into dependencia(nombre) values('REPRESENTACIÓN DEL GOBIERNO DEL ESTADO EN LA CIUDAD DE MÉXICO');
insert into dependencia(nombre) values('SECRETARIA DE ADMINISTRACIÓN');
insert into dependencia(nombre) values('SECRETARÍA DE DESARROLLO AGROPECUARIO');
insert into dependencia(nombre) values('SECRETARÍA DE DESARROLLO SOCIAL');
insert into dependencia(nombre) values('SECRETARÍA DE DESARROLLO SUSTENTABLE');
insert into dependencia(nombre) values('SECRETARÍA DE ECONOMÍA Y TRABAJO');
insert into dependencia(nombre) values('SECRETARÍA DE EDUCACIÓN');
insert into dependencia(nombre) values('SECRETARÍA DE FINANZAS Y TESORERÍA GENERAL DEL ESTADO');
insert into dependencia(nombre) values('SECRETARÍA DE INFRAESTRUCTURA');
insert into dependencia(nombre) values('SECRETARÍA DE SALUD');
insert into dependencia(nombre) values('SECRETARÍA DE SEGURIDAD PÚBLICA');
insert into dependencia(nombre) values('SECRETARÍA GENERAL DE GOBIERNO');
insert into dependencia(nombre) values('SECRETARÍA PARTICULAR DEL GOBERNADOR');
insert into dependencia(nombre) values('SERVICIOS DE AGUA Y DRENAJE DE MONTERREY, I.P.D.');
insert into dependencia(nombre) values('SERVICIOS DE SALUD DE NUEVO LEÓN, O.P.D.');
insert into dependencia(nombre) values('SISTEMA DE CAMINOS DE NUEVO LEÓN');
insert into dependencia(nombre) values('SISTEMA DE RADIO Y TELEVISIÓN');
insert into dependencia(nombre) values('SISTEMA DE TRANSPORTE COLECTIVO');
insert into dependencia(nombre) values('SISTEMA INTEGRAL PARA EL MANEJO ECOLÓGICO Y PROCESAMIENTO DE DESECHOS');
insert into dependencia(nombre) values('SISTEMA PARA EL DESARROLLO INTEGRAL DE LA FAMILIA');
insert into dependencia(nombre) values('TITULAR DEL PODER EJECUTIVO Y SECRETARIA PARTICULAR DEL GOBERNADOR');
insert into dependencia(nombre) values('TRIBUNAL DE ARBITRAJE');
insert into dependencia(nombre) values('TRIBUNAL DE JUSTICIA ADMINISTRATIVA');
insert into dependencia(nombre) values('UNIDAD DE INTEGRACIÓN EDUCATIVA');
insert into dependencia(nombre) values('UNIVERSIDAD DE CIENCIAS DE LA SEGURIDAD');
insert into dependencia(nombre) values('UNIVERSIDAD POLITÉCNICA DE APODACA');
insert into dependencia(nombre) values('UNIVERSIDAD POLITÉCNICA DE GARCÍA');
insert into dependencia(nombre) values('UNIVERSIDAD TECNOLÓGICA CADEREYTA');
insert into dependencia(nombre) values('UNIVERSIDAD TECNOLÓGICA GRAL. MARIANO ESCOBEDO');
insert into dependencia(nombre) values('UNIVERSIDAD TECNOLÓGICA LINARES');
insert into dependencia(nombre) values('UNIVERSIDAD TECNOLÓGICA SANTA CATARINA');