CREATE TABLE cat_entidades (
   entidad_id      BIGSERIAL,
   cv_entidad        text NOT NULL,
   nombre            text NOT NULL,
   cv_entidad_siet  text NOT NULL,
   PRIMARY KEY (entidad_id)
);

CREATE TABLE cat_municipios (
   municipio_id      BIGSERIAL,
   cv_municipio      text NOT NULL,
   cv_estado         text NOT NULL,
   entidad_id        bigint NOT NULL REFERENCES cat_entidades(entidad_id),
   nombre            text NOT NULL,
   PRIMARY KEY (municipio_id)
);

CREATE TABLE cat_localidades (
   localidad_id      BIGSERIAL,
   cv_localidad      text not null,
   nombre            text NOT NULL,
   entidad_id        bigint NOT NULL REFERENCES cat_entidades(entidad_id),
   municipio_id        bigint NOT NULL REFERENCES cat_municipios(municipio_id),
   PRIMARY KEY (localidad_id)
);

CREATE TABLE cat_tipo_asentamientos (
   tipo_asentamiento_id      BIGSERIAL,
   cv_tipo_asentamiento   text NOT NULL,
   nombre            text NOT NULL,
   cv_asent_siet  bigint not null,
   PRIMARY KEY (tipo_asentamiento_id)
);

CREATE TABLE cat_asentamientos (
   asentamiento_id      BIGSERIAL,
   cv_asentamiento   text NOT NULL,
   nombre            text NOT NULL,
   tipo_asentamiento_id   bigint NOT NULL REFERENCES cat_tipo_asentamientos(tipo_asentamiento_id),
   entidad_id        bigint NOT NULL REFERENCES cat_entidades(entidad_id),
   municipio_id      bigint NOT NULL REFERENCES cat_municipios(municipio_id),
   cp                text NOT NULL,
   PRIMARY KEY (asentamiento_id)
);