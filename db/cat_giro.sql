
CREATE TABLE provider.cat_sectores
(
  sector_id bigserial NOT NULL,
  nombre_sector text,
  CONSTRAINT pk_cat_sectores PRIMARY KEY (sector_id)
);
CREATE TABLE provider.cat_ramas
(
  rama_id bigserial NOT NULL,
  nombre_rama text,
  sector_id bigint,
  CONSTRAINT pk_cat_ramas PRIMARY KEY (rama_id),
  CONSTRAINT fk_ramas_sectores FOREIGN KEY (sector_id)
      REFERENCES provider.cat_sectores (sector_id) MATCH SIMPLE
      ON UPDATE NO ACTION ON DELETE NO ACTION
);
CREATE TABLE provider.cat_actividades
(
  actividad_id bigserial NOT NULL,
  clave bigint,
  nombre_actividad text,
  descripcion text,
  rama_id bigint,
  clave_sat text,
  CONSTRAINT pk_cat_actividades PRIMARY KEY (actividad_id),
  CONSTRAINT fk_actividades_ramas FOREIGN KEY (rama_id)
      REFERENCES provider.cat_ramas (rama_id) MATCH SIMPLE
      ON UPDATE NO ACTION ON DELETE NO ACTION
);
CREATE TABLE provider.giro
(
  giro_id bigserial NOT NULL,
  actividad_id bigint,
  provider_id bigint,
  porcentaje SMALLINT NOT NULL DEFAULT 0,
  CONSTRAINT pk_giro PRIMARY KEY (giro_id),
  CONSTRAINT fk_actividad_id FOREIGN KEY (actividad_id)
      REFERENCES provider.cat_actividades (actividad_id) MATCH SIMPLE
      ON UPDATE NO ACTION ON DELETE NO ACTION,
  CONSTRAINT fk_provider_id FOREIGN KEY (provider_id)
      REFERENCES public.provider (provider_id) MATCH SIMPLE
      ON UPDATE NO ACTION ON DELETE NO ACTION
);