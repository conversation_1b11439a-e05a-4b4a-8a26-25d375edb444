CREATE OR REPLACE FUNCTION get_expiration() RETURNS TABLE(
  id bigint,
  provider_id bigint,
  module text,
  register_id bigint,
  msg text,
  type text,
  expiration_date date,
  permanent boolean,
  column_name text,
  user_id bigint,
  email text,
  proveedor text
) AS
$BODY$
DECLARE
  expirations_id bigint[];
BEGIN
  expirations_id := (SELECT array_agg(expiration_documents_id) FROM (SELECT * FROM expiration_documents WHERE send is false limit 100) tmp);

  IF (array_length(expirations_id,1) != 0) THEN

    UPDATE public.expiration_documents set send = true WHERE public.expiration_documents.expiration_documents_id = ANY(expirations_id);

    RETURN QUERY select ed.expiration_documents_id as id, ed.provider_id, ed.module, ed.register_id, ed.msg, ed.type, ed.expitation_date,ed.permanent,ed.column_name,p.user_id,p.email,
                CASE WHEN (p.name_razon_social is not null and p.name_razon_social != '') then p.name_razon_social else concat_ws(' ',p.pf_nombre,p.pf_ap_paterno,p.pf_ap_materno) end as proveedor
                from expiration_documents ed
                join provider p on p.provider_id = ed.provider_id
                where ed.expiration_documents_id = ANY(expirations_id);
  END IF;
  RETURN QUERY select ed.expiration_documents_id as id, ed.provider_id, ed.module, ed.register_id, ed.msg, ed.type, ed.expitation_date,ed.permanent,ed.column_name,p.user_id,p.email,
                CASE WHEN (p.name_razon_social is not null and p.name_razon_social != '') then p.name_razon_social else concat_ws(' ',p.pf_nombre,p.pf_ap_paterno,p.pf_ap_materno) end as proveedor
                from expiration_documents ed
                join provider p on p.provider_id = ed.provider_id
                where 0=1;
END
$BODY$
LANGUAGE plpgsql;