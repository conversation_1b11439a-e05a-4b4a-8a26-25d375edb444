
CREATE TABLE secretaria(
  secretaria_id BIGSERIAL NOT NULL,
  clave TEXT NOT NULL,
  nombre TEXT NOT NULL,
  CONSTRAINT fk_secretaria_id PRIMARY KEY (secretaria_id)
);

CREATE TABLE dependencia(
  dependencia_id Bigserial NOT NULL,
  nombre text NOT NULL,
  created_by BIGINT NULL,
  created_date timestamp without time zone NULL DEFAULT now(),
  updated_by BIGINT NULL,
  updated_date timestamp without time zone NULL,
  status TEXT null DEFAULT 'ACTIVA',
  tipo_entidad TEXT NULL,
  responsable TEXT NULL,
  secretaria_id bigint NULL,
  CONSTRAINT pk_dependencia PRIMARY KEY (dependencia_id),
  CONSTRAINT fk_usurios_create FOREIGN KEY (created_by)
  REFERENCES usurios (user_id) MATCH SIMPLE
  ON UPDATE NO ACTION ON DELETE NO ACTION,
  CONSTRAINT fk_usurios_update FOREIGN KEY (updated_by)
  REFERENCES usurios (user_id) MATCH SIMPLE
  ON UPDATE NO ACTION ON DELETE NO ACTION,
  CONSTRAINT fk_sec_id_dep FOREIGN KEY (secretaria_id)
  REFERENCES secretaria (secretaria_id) MATCH SIMPLE
  ON UPDATE NO ACTION ON DELETE NO ACTION
);

CREATE TABLE user_dependencia(
  user_id BIGINT NOT NULL,
  dependencia_id BIGINT NOT NULL,
  CONSTRAINT pk_user_dependencia PRIMARY KEY (user_id, dependencia_id),
  CONSTRAINT fk_user_id_depen FOREIGN KEY (user_id)
  REFERENCES usurios (user_id) MATCH SIMPLE
  ON UPDATE NO ACTION ON DELETE NO ACTION,  
  CONSTRAINT fk_dependencia_id_depen FOREIGN KEY (dependencia_id)
  REFERENCES dependencia (dependencia_id) MATCH SIMPLE
  ON UPDATE NO ACTION ON DELETE NO ACTION
);

CREATE TABLE unidad_medida(
  unidad_medida_id BIGSERIAL NOT NULL,
  nombre  TEXT NOT NULL,
  abreviacion TEXT NULL,
  created_by BIGINT NULL,
  created_date timestamp without time zone NULL DEFAULT NOW(),
  updated_by BIGINT NULL,
  updated_date timestamp without time zone NULL,
  CONSTRAINT pk_unidad_medida PRIMARY KEY (unidad_medida_id),
  CONSTRAINT fk_usurios_create FOREIGN KEY (created_by)
  REFERENCES usurios (user_id) MATCH SIMPLE
  ON UPDATE NO ACTION ON DELETE NO ACTION,
  CONSTRAINT fk_usurios_update FOREIGN KEY (updated_by)
  REFERENCES usurios (user_id) MATCH SIMPLE
  ON UPDATE NO ACTION ON DELETE NO ACTION
);

CREATE TABLE grupo_producto (
  grupo_producto_id BIGSERIAL NOT NULL,
  clave TEXT NOT NULL,
  nombre TEXT NOT NULL,
  created_by BIGINT NULL,
  created_date timestamp without time zone NOT NULL DEFAULT NOW(),
  updated_by BIGINT NULL,
  updated_date timestamp without time zone NULL,
  CONSTRAINT pk_grupo_prod_id PRIMARY KEY (grupo_producto_id),
  CONSTRAINT fk_usurios_create FOREIGN KEY (created_by)
  REFERENCES usurios (user_id) MATCH SIMPLE
  ON UPDATE NO ACTION ON DELETE NO ACTION,
  CONSTRAINT fk_usurios_update FOREIGN KEY (updated_by)
  REFERENCES usurios (user_id) MATCH SIMPLE
  ON UPDATE NO ACTION ON DELETE NO ACTION
);

CREATE TABLE producto (
  producto_id BIGSERIAL NOT NULL,
  clave TEXT NOT NULL,
  nombre TEXT NOT NULL,
  unidad_medida_id BIGINT NULL,
  grupo_producto_id BIGINT NULL,
  created_by BIGINT NULL,
  created_date timestamp without time zone NOT NULL DEFAULT NOW(),
  updated_by BIGINT NULL,
  updated_date timestamp without time zone NULL,
  CONSTRAINT pk_producto_id PRIMARY KEY (producto_id),
  CONSTRAINT fk_usurios_create FOREIGN KEY (created_by)
  REFERENCES usurios (user_id) MATCH SIMPLE
  ON UPDATE NO ACTION ON DELETE NO ACTION,
  CONSTRAINT fk_usurios_update FOREIGN KEY (updated_by)
  REFERENCES usurios (user_id) MATCH SIMPLE
  ON UPDATE NO ACTION ON DELETE NO ACTION,
  CONSTRAINT fk_prod_unidad_medida FOREIGN KEY (unidad_medida_id)
  REFERENCES unidad_medida (unidad_medida_id) MATCH SIMPLE
  ON UPDATE NO ACTION ON DELETE NO ACTION,
  CONSTRAINT fk_produc_grupo_producto_id FOREIGN KEY (grupo_producto_id)
  REFERENCES grupo_producto (grupo_producto_id) MATCH SIMPLE
  ON UPDATE NO ACTION ON DELETE NO ACTION
);

CREATE TABLE partida(
  partida_id bigserial NOT NULL,
  numero BIGINT NULL,
  nombre text NULL,
  ejercicio integer null,
  created_by BIGINT NULL,
  created_date timestamp without time zone NOT NULL DEFAULT NOW(),
  updated_by BIGINT NULL,
  updated_date timestamp without time zone NULL,
  status integer NULL,
  anio integer NULL,
  CONSTRAINT pk_partida PRIMARY KEY (partida_id)
);

CREATE TABLE requisicion
(
  requisicion_id bigserial NOT NULL,
  solicitante text NOT NULL,
  puesto text NULL,
  dependencia_id BIGINT NOT NULL,
  tipo_imputacion text NULL,
  entrega_almacen TEXT NULL,
  tipo_requisicion text NULL,
  sociedad TEXT NULL,
  descripcion TEXT NULL,
  status TEXT NOT NULL DEFAULT 'PENDIENTE AUTORIZAR',   
  fecha_deseada timestamp without time zone NOT NULL,
  grupo_producto_id BIGINT NOT NULL, 
  created_by BIGINT NULL,
  created_date timestamp without time zone NOT NULL,
  updated_by BIGINT NULL,
  updated_date  timestamp without time zone NULL,
  CONSTRAINT pk_usurios PRIMARY KEY (requisicion_id),
  CONSTRAINT fk_usurios_create FOREIGN KEY (created_by)
  REFERENCES usurios (user_id) MATCH SIMPLE
  ON UPDATE NO ACTION ON DELETE NO ACTION,
  CONSTRAINT fk_usurios_update FOREIGN KEY (updated_by)
  REFERENCES usurios (user_id) MATCH SIMPLE
  ON UPDATE NO ACTION ON DELETE NO ACTION,
  CONSTRAINT fk_produc_grupo_producto_id FOREIGN KEY (grupo_producto_id)
  REFERENCES grupo_producto (grupo_producto_id) MATCH SIMPLE
  ON UPDATE NO ACTION ON DELETE NO ACTION,
  CONSTRAINT fk_usurios_dependencia FOREIGN KEY (dependencia_id)
  REFERENCES dependencia (dependencia_id) MATCH SIMPLE
  ON UPDATE NO ACTION ON DELETE NO ACTION
);

CREATE TABLE requisicion_producto(
  requisicion_producto_id bigserial NOT NULL,
  requisicion_id BIGINT NOT NULL,
  producto_id bigint NOT NULL,
  cantidad numeric NULL,
  partida_id BIGINT NOT NULL,
  precio_referencia numeric NULL,
  CONSTRAINT pk_requi_detalle PRIMARY KEY(requisicion_producto_id),
  CONSTRAINT fk_requi_produc_requisicion FOREIGN KEY (requisicion_id)
  REFERENCES requisicion (requisicion_id) MATCH SIMPLE
  ON UPDATE NO ACTION ON DELETE NO ACTION,
  CONSTRAINT fk_requi_produc_producto_id FOREIGN KEY (producto_id)
  REFERENCES producto (producto_id) MATCH SIMPLE
  ON UPDATE NO ACTION ON DELETE NO ACTION,
  CONSTRAINT fk_requi_produc_partida_id FOREIGN KEY (partida_id)
  REFERENCES partida (partida_id) MATCH SIMPLE
  ON UPDATE NO ACTION ON DELETE NO ACTION
);

CREATE TABLE usuario_negociador(
  user_negociador_id BIGINT NOT NULL,
  clave TEXT NOT NULL,
  created_by BIGINT NULL,
  created_date timestamp without time zone NOT NULL DEFAULT NOW(),
  updated_by BIGINT NULL,
  updated_date  timestamp without time zone NULL,
  CONSTRAINT pk_user_id_neg PRIMARY KEY(user_negociador_id),
  CONSTRAINT fk_usurios_create FOREIGN KEY (created_by)
  REFERENCES usurios (user_id) MATCH SIMPLE
  ON UPDATE NO ACTION ON DELETE NO ACTION,
  CONSTRAINT fk_usurios_update FOREIGN KEY (updated_by)
  REFERENCES usurios (user_id) MATCH SIMPLE
  ON UPDATE NO ACTION ON DELETE NO ACTION,
  CONSTRAINT fk_usurios_negocia FOREIGN KEY (user_negociador_id)
  REFERENCES usurios (user_id) MATCH SIMPLE
  ON UPDATE NO ACTION ON DELETE NO ACTION
);

CREATE TABLE negociador_grupo_producto(
  user_negociador_id BIGINT NOT NULL,
  grupo_producto_id BIGINT NOT NULL,
  CONSTRAINT pk_user_id_neg_cate PRIMARY KEY(user_negociador_id,grupo_producto_id),
  CONSTRAINT fk_negociador_neg_cate FOREIGN KEY (user_negociador_id)
  REFERENCES usuario_negociador (user_negociador_id) MATCH SIMPLE
  ON UPDATE NO ACTION ON DELETE NO ACTION,
  CONSTRAINT fk_grupo_prod_cate FOREIGN KEY (grupo_producto_id)
  REFERENCES grupo_producto (grupo_producto_id) MATCH SIMPLE
  ON UPDATE NO ACTION ON DELETE NO ACTION
);


CREATE TABLE provider_giro(
  provider_id BIGINT NOT NULL,
  grupo_producto_id BIGINT NOT NULL,
  CONSTRAINT pk_provider_giro PRIMARY KEY (provider_id, grupo_producto_id),
  CONSTRAINT fk_user_prove FOREIGN KEY (provider_id)
  REFERENCES provider (provider_id) MATCH SIMPLE
  ON UPDATE NO ACTION ON DELETE NO ACTION,  
  CONSTRAINT fk_provider_giro_grup_prod FOREIGN KEY (grupo_producto_id)
  REFERENCES grupo_producto (grupo_producto_id) MATCH SIMPLE
  ON UPDATE NO ACTION ON DELETE NO ACTION
);

CREATE TABLE condiciones_pago(
  condiciones_pago_id text NOT NULL,
  dias integer null,
  descripcion TEXT NULL,
  created_by BIGINT NULL,
  created_date timestamp without time zone NOT NULL DEFAULT NOW(),
  updated_by BIGINT NULL,
  updated_date  timestamp without time zone NULL,
  CONSTRAINT pk_condiciones PRIMARY KEY (condiciones_pago_id),
  CONSTRAINT fk_usurios_create FOREIGN KEY (created_by)
  REFERENCES usurios (user_id) MATCH SIMPLE
  ON UPDATE NO ACTION ON DELETE NO ACTION,  
  CONSTRAINT fk_usurios_update FOREIGN KEY (updated_by)
  REFERENCES usurios (user_id) MATCH SIMPLE
  ON UPDATE NO ACTION ON DELETE NO ACTION
);

CREATE TABLE consolidadas(
  consolidadas_id BIGSERIAL NOT NULL,
  requisicion_producto_id BIGINT NULL,
  status TEXT NULL,
  CONSTRAINT pk_consolidadas PRIMARY KEY (consolidadas_id),
  CONSTRAINT fk_consolidadas_req_pro FOREIGN KEY (requisicion_producto_id)
  REFERENCES requisicion_producto (requisicion_producto_id) MATCH SIMPLE
  ON UPDATE NO ACTION ON DELETE NO ACTION
);

CREATE TABLE solicitud_cotizacion(
  solicitud_id BIGSERIAL NOT NULL,
  requisicion_id BIGINT NOT NULL,
  provider_id BIGINT NOT NULL,
  condiciones_pago_id TEXT NULL,
  vigencia_cotizacion timestamp without time zone NULL,
  status TEXT NULL DEFAULT 'PENDIENTE COTIZAR',
  created_by BIGINT NULL,
  created_date timestamp without time zone NOT NULL DEFAULT NOW(),
  updated_by BIGINT NULL,
  updated_date timestamp without time zone NULL,
  CONSTRAINT pk_solicitud_id PRIMARY KEY (solicitud_id),
  CONSTRAINT fk_usurios_create FOREIGN KEY (created_by)
  REFERENCES usurios (user_id) MATCH SIMPLE
  ON UPDATE NO ACTION ON DELETE NO ACTION,  
  CONSTRAINT fk_usurios_update FOREIGN KEY (updated_by)
  REFERENCES usurios (user_id) MATCH SIMPLE
  ON UPDATE NO ACTION ON DELETE NO ACTION,
  CONSTRAINT fk_requisicion FOREIGN KEY (requisicion_id)
  REFERENCES requisicion (requisicion_id) MATCH SIMPLE
  ON UPDATE NO ACTION ON DELETE NO ACTION,  
  CONSTRAINT fk_provider FOREIGN KEY (provider_id)
  REFERENCES provider (provider_id) MATCH SIMPLE
  ON UPDATE NO ACTION ON DELETE NO ACTION,
  CONSTRAINT fk_codi_pago_solicitud FOREIGN KEY (condiciones_pago_id)
  REFERENCES condiciones_pago (condiciones_pago_id) MATCH SIMPLE
  ON UPDATE NO ACTION ON DELETE NO ACTION
);

CREATE TABLE solicitud_detalle(
  solicitud_detalle_id BIGSERIAL NOT NULL,
  solicitud_id BIGINT NOT NULL,
  requisicion_producto_id BIGINT NULL,
  consolidadas_id BIGINT NULL,
  subtotal numeric NULL,
  iva numeric NULL,
  total numeric NULL,
  CONSTRAINT pk_solicitud_detalle_id PRIMARY KEY (solicitud_detalle_id),
  CONSTRAINT fk_solicitud_id FOREIGN KEY (solicitud_id)
  REFERENCES solicitud_cotizacion (solicitud_id) MATCH SIMPLE
  ON UPDATE NO ACTION ON DELETE NO ACTION,
  CONSTRAINT fk_requi_prod FOREIGN KEY (requisicion_producto_id)
  REFERENCES requisicion_producto (requisicion_producto_id) MATCH SIMPLE
  ON UPDATE NO ACTION ON DELETE NO ACTION,  
  CONSTRAINT fk_consolidadas_id FOREIGN KEY (consolidadas_id)
  REFERENCES consolidadas (consolidadas_id) MATCH SIMPLE
  ON UPDATE NO ACTION ON DELETE NO ACTION
);