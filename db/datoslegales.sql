CREATE SCHEMA provider;


CREATE TABLE provider.rfc
(
  rfc_id bigserial NOT NULL,
  url_rfc text,
  validado_rfc boolean NOT NULL DEFAULT false,
  provider_id bigint NOT NULL,
  CONSTRAINT rfc_pk PRIMARY KEY (rfc_id),
  CONSTRAINT fk_provider_provider_id FOREIGN KEY (provider_id)
      REFERENCES public.provider (provider_id) MATCH SIMPLE
      ON UPDATE NO ACTION ON DELETE NO ACTION
);

CREATE TABLE provider.curp
(
  curp_id bigserial NOT NULL,
  url_curp text,
  validado_curp boolean NOT NULL DEFAULT false,
  provider_id bigint NOT NULL,
  CONSTRAINT curp_pk PRIMARY KEY (curp_id),
  CONSTRAINT fk_provider_provider_id FOREIGN KEY (provider_id)
      REFERENCES public.provider (provider_id) MATCH SIMPLE
      ON UPDATE NO ACTION ON DELETE NO ACTION
);

CREATE TABLE provider.id_oficial
(
  idoficial_id bigserial NOT NULL,
  expiration_date timestamp without time zone,
  url_idoficial text,
  validado_idoficial boolean NOT NULL DEFAULT false,
  provider_id bigint NOT NULL,
  CONSTRAINT idoficial_pk PRIMARY KEY (idoficial_id),
  CONSTRAINT fk_provider_provider_id FOREIGN KEY (provider_id)
      REFERENCES public.provider (provider_id) MATCH SIMPLE
      ON UPDATE NO ACTION ON DELETE NO ACTION
);
CREATE TABLE provider.comprobante_domicilio
(
  comprobante_domicilio_id bigserial NOT NULL,
  url_comprobante_domicilio text,
  validado_comprobante_domicilio boolean NOT NULL DEFAULT false,
  creation_date timestamp without time zone,
  expiration_date timestamp without time zone,
  provider_id bigint NOT NULL,
  CONSTRAINT comprobante_domicilio_pk PRIMARY KEY (comprobante_domicilio_id),
  CONSTRAINT fk_provider_provider_id FOREIGN KEY (provider_id)
      REFERENCES public.provider (provider_id) MATCH SIMPLE
      ON UPDATE NO ACTION ON DELETE NO ACTION
);
CREATE TABLE provider.registro_imss
(
  registro_imss_id bigserial NOT NULL,
  registro_imss text,
  url_registro_imss text,
  validado_registro_imss boolean NOT NULL DEFAULT false,
  provider_id bigint NOT NULL,
  CONSTRAINT registro_imss_pk PRIMARY KEY (registro_imss_id),
  CONSTRAINT fk_provider_provider_id FOREIGN KEY (provider_id)
      REFERENCES public.provider (provider_id) MATCH SIMPLE
      ON UPDATE NO ACTION ON DELETE NO ACTION
);

CREATE TABLE provider.socio
(
  socio_id bigserial NOT NULL,
  camara text,
  vigencia timestamp without time zone,
  url_socio text,
  validado_socio boolean NOT NULL DEFAULT false,
  provider_id bigint NOT NULL,
  CONSTRAINT socio_pk PRIMARY KEY (socio_id),
  CONSTRAINT fk_provider_provider_id FOREIGN KEY (provider_id)
      REFERENCES public.provider (provider_id) MATCH SIMPLE
      ON UPDATE NO ACTION ON DELETE NO ACTION
);

CREATE TABLE provider.relacion_accionistas
(
  relacion_accionistas_id bigserial NOT NULL,
  rfc text,
  curp text,
  nombre text,
  provider_id bigint NOT NULL,
  CONSTRAINT relacion_accionistas_pk PRIMARY KEY (relacion_accionistas_id),
  CONSTRAINT fk_provider_provider_id FOREIGN KEY (provider_id)
      REFERENCES public.provider (provider_id) MATCH SIMPLE
      ON UPDATE NO ACTION ON DELETE NO ACTION
);

CREATE TABLE provider.escritura_publica
(
  escritura_publica_id bigserial NOT NULL,
  url_escritura_publica text,
  num_escritura text,
  fecha timestamp without time zone,
  notario text,
  federatario text,
  city_id bigint,
  capital_social text,
  objeto_sociedad text,
  provider_id bigint NOT NULL,
  CONSTRAINT escritura_publica_pk PRIMARY KEY (escritura_publica_id),
  CONSTRAINT fk_provider_provider_id FOREIGN KEY (provider_id)
      REFERENCES public.provider (provider_id) MATCH SIMPLE
      ON UPDATE NO ACTION ON DELETE NO ACTION,
  CONSTRAINT fk_city_city_id FOREIGN KEY (city_id)
      REFERENCES public.city (city_id) MATCH SIMPLE
      ON UPDATE NO ACTION ON DELETE NO ACTION
);

CREATE TABLE provider.registro_publico_propiedad
(
  rpublico_propiedad_id bigserial NOT NULL,
  folio text,
  volumen text,
  libro text,
  seccion text,
  fecha timestamp without time zone,
  city_id bigint,
  provider_id bigint NOT NULL,
  CONSTRAINT registro_publico_propiedad_pk PRIMARY KEY (rpublico_propiedad_id),
  CONSTRAINT fk_provider_provider_id FOREIGN KEY (provider_id)
      REFERENCES public.provider (provider_id) MATCH SIMPLE
      ON UPDATE NO ACTION ON DELETE NO ACTION,
  CONSTRAINT fk_city_city_id FOREIGN KEY (city_id)
      REFERENCES public.city (city_id) MATCH SIMPLE
      ON UPDATE NO ACTION ON DELETE NO ACTION
);

CREATE TABLE provider.cambio_situacion_fiscal
(
  cambio_situacion_fiscal_id bigserial NOT NULL,
  fecha timestamp without time zone,
  url_cambio_situacion_fiscal text,
  validado_cambio_situacion_fiscal boolean NOT NULL DEFAULT false,
  provider_id bigint NOT NULL,
  CONSTRAINT cambio_situacion_fiscal_pk PRIMARY KEY (cambio_situacion_fiscal_id),
  CONSTRAINT fk_provider_provider_id FOREIGN KEY (provider_id)
      REFERENCES public.provider (provider_id) MATCH SIMPLE
      ON UPDATE NO ACTION ON DELETE NO ACTION
);

CREATE TABLE provider.facturas
(
  facturas_id bigserial NOT NULL,
  fecha timestamp without time zone,
  url_facturas text,
  validado_facturas boolean NOT NULL DEFAULT false,
  provider_id bigint NOT NULL,
  CONSTRAINT facturas_pk PRIMARY KEY (facturas_id),
  CONSTRAINT fk_provider_provider_id FOREIGN KEY (provider_id)
      REFERENCES public.provider (provider_id) MATCH SIMPLE
      ON UPDATE NO ACTION ON DELETE NO ACTION
);

CREATE TABLE provider.acta_constitutiva
(
  acta_constitutiva_id bigserial NOT NULL,
  fecha timestamp without time zone,
  url_acta_constitutiva text,
  validado_acta_constitutiva boolean NOT NULL DEFAULT false,
  provider_id bigint NOT NULL,
  CONSTRAINT acta_constitutiva_pk PRIMARY KEY (acta_constitutiva_id),
  CONSTRAINT fk_provider_provider_id FOREIGN KEY (provider_id)
      REFERENCES public.provider (provider_id) MATCH SIMPLE
      ON UPDATE NO ACTION ON DELETE NO ACTION
);

CREATE TABLE provider.representante_legal
(
  representante_legal_id bigserial NOT NULL,
  rfc text,
  curp text,
  url_idoficial_representante text,
  validado_idoficial_representante boolean NOT NULL DEFAULT false,
  nombre text,
  provider_id bigint NOT NULL,
  url_confiere_facultad text,
  num_escritura text,
  fecha timestamp without time zone,
  notario text,
  federatario text,
  city_id bigint,
  poder_otorgado text,
  CONSTRAINT representante_legal_pk PRIMARY KEY (representante_legal_id),
  CONSTRAINT fk_provider_provider_id FOREIGN KEY (provider_id)
      REFERENCES public.provider (provider_id) MATCH SIMPLE
      ON UPDATE NO ACTION ON DELETE NO ACTION,
  CONSTRAINT fk_city_city_id FOREIGN KEY (city_id)
      REFERENCES public.city (city_id) MATCH SIMPLE
      ON UPDATE NO ACTION ON DELETE NO ACTION
);