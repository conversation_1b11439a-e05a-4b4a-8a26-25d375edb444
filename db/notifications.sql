insert into module_notification(module_notification_name,module_name) values('<PERSON><PERSON><PERSON><PERSON><PERSON> POR VALIDAR','<PERSON><PERSON> VALIDAR');
insert into module_notification(module_notification_name,module_name) values('RESPUESTA DE VALIDACION','VALIDADO-RECHAZADO');
insert into module_notification(module_notification_name,module_name) values('CERTIFICADO','CERTIFICADO');


insert into msg_notification(notification) values('Un proveedor ha solicitado validar la sección :name_mod:.');
insert into msg_notification(notification) values('La sección de :name_mod: ya tiene una respuesta.');
insert into msg_notification(notification) values('Has completado correctamente el registro.');


INSERT INTO module_notification_url (module_notification_id,url_module,type_user,module_notification_name,msg_notification_id)  WITH   t1 AS (SELECT module_notification_id     FROM module_notification     WHERE module_notification_name='<PERSON><PERSON><PERSON><PERSON><PERSON> POR VALIDAR'),   t2 AS (SELECT msg_notification_id     FROM msg_notification      WHERE notification='Un proveedor ha solicitado validar la sección :name_mod:.')   select  t1.module_notification_id,'/tecnicos/index-validador','VALIDADOR','FOTOGRAFIA NEGOCIO',t2.msg_notification_id   from t1,t2;
INSERT INTO module_notification_url (module_notification_id,url_module,type_user,module_notification_name,msg_notification_id)  WITH   t1 AS (SELECT module_notification_id     FROM module_notification     WHERE module_notification_name='MODULO POR VALIDAR'),   t2 AS (SELECT msg_notification_id     FROM msg_notification      WHERE notification='Un proveedor ha solicitado validar la sección :name_mod:.')   select  t1.module_notification_id,'/financieros/index-validador','VALIDADOR','DATOS FINANCIEROS',t2.msg_notification_id   from t1,t2;
INSERT INTO module_notification_url (module_notification_id,url_module,type_user,module_notification_name,msg_notification_id)  WITH   t1 AS (SELECT module_notification_id     FROM module_notification     WHERE module_notification_name='MODULO POR VALIDAR'),   t2 AS (SELECT msg_notification_id     FROM msg_notification      WHERE notification='Un proveedor ha solicitado validar la sección :name_mod:.')   select  t1.module_notification_id,'/tecnicos/index-validador','VALIDADOR','DATOS TECNICOS',t2.msg_notification_id   from t1,t2;
INSERT INTO module_notification_url (module_notification_id,url_module,type_user,module_notification_name,msg_notification_id)  WITH   t1 AS (SELECT module_notification_id     FROM module_notification     WHERE module_notification_name='MODULO POR VALIDAR'),   t2 AS (SELECT msg_notification_id     FROM msg_notification      WHERE notification='Un proveedor ha solicitado validar la sección :name_mod:.')   select  t1.module_notification_id,'/legales/index-validador','VALIDADOR','DATOS LEGALES',t2.msg_notification_id   from t1,t2;
INSERT INTO module_notification_url (module_notification_id,url_module,type_user,module_notification_name,model_name,msg_notification_id)  WITH   t1 AS (SELECT module_notification_id     FROM module_notification     WHERE module_notification_name='RESPUESTA DE VALIDACION'),   t2 AS (SELECT msg_notification_id     FROM msg_notification      WHERE notification='La sección de :name_mod: ya tiene una respuesta.')   select  t1.module_notification_id,'/financieros/view','PROVIDER','ESTADOS FINANCIERO','estado_financiero',t2.msg_notification_id   from t1,t2;
INSERT INTO module_notification_url (module_notification_id,url_module,type_user,module_notification_name,model_name,msg_notification_id)  WITH   t1 AS (SELECT module_notification_id     FROM module_notification     WHERE module_notification_name='RESPUESTA DE VALIDACION'),   t2 AS (SELECT msg_notification_id     FROM msg_notification      WHERE notification='La sección de :name_mod: ya tiene una respuesta.')   select  t1.module_notification_id,'/tecnicos/index','PROVIDER','ORGANIGRAMA','organigrama',t2.msg_notification_id   from t1,t2;
INSERT INTO module_notification_url (module_notification_id,url_module,type_user,module_notification_name,model_name,msg_notification_id)  WITH   t1 AS (SELECT module_notification_id     FROM module_notification     WHERE module_notification_name='RESPUESTA DE VALIDACION'),   t2 AS (SELECT msg_notification_id     FROM msg_notification      WHERE notification='La sección de :name_mod: ya tiene una respuesta.')   select  t1.module_notification_id,'/maquinaria/index','PROVIDER','MAQUINARIA Y EQUIPOS','maquinaria_equipos',t2.msg_notification_id   from t1,t2;
INSERT INTO module_notification_url (module_notification_id,url_module,type_user,module_notification_name,model_name,msg_notification_id)  WITH   t1 AS (SELECT module_notification_id     FROM module_notification     WHERE module_notification_name='RESPUESTA DE VALIDACION'),   t2 AS (SELECT msg_notification_id     FROM msg_notification      WHERE notification='La sección de :name_mod: ya tiene una respuesta.')   select  t1.module_notification_id,'/tecnicos/index','PROVIDER','PERSONAL TECNICO','personal_tecnico',t2.msg_notification_id   from t1,t2;
INSERT INTO module_notification_url (module_notification_id,url_module,type_user,module_notification_name,model_name,msg_notification_id)  WITH   t1 AS (SELECT module_notification_id     FROM module_notification     WHERE module_notification_name='RESPUESTA DE VALIDACION'),   t2 AS (SELECT msg_notification_id     FROM msg_notification      WHERE notification='La sección de :name_mod: ya tiene una respuesta.')   select  t1.module_notification_id,'/experiencia/index','PROVIDER','EXPERIENCIA COMERCIAL','experiencia',t2.msg_notification_id   from t1,t2;
INSERT INTO module_notification_url (module_notification_id,url_module,type_user,module_notification_name,model_name,msg_notification_id)  WITH   t1 AS (SELECT module_notification_id     FROM module_notification     WHERE module_notification_name='RESPUESTA DE VALIDACION'),   t2 AS (SELECT msg_notification_id     FROM msg_notification      WHERE notification='La sección de :name_mod: ya tiene una respuesta.')   select  t1.module_notification_id,'/legales/view','PROVIDER','MODIFICACION ACTA','modificacion_acta',t2.msg_notification_id   from t1,t2;
INSERT INTO module_notification_url (module_notification_id,url_module,type_user,module_notification_name,model_name,msg_notification_id)  WITH   t1 AS (SELECT module_notification_id     FROM module_notification     WHERE module_notification_name='RESPUESTA DE VALIDACION'),   t2 AS (SELECT msg_notification_id     FROM msg_notification      WHERE notification='La sección de :name_mod: ya tiene una respuesta.')   select  t1.module_notification_id,'/legales/view','PROVIDER','RELACION ACCIONISTAS','relacion_accionistas',t2.msg_notification_id   from t1,t2;
INSERT INTO module_notification_url (module_notification_id,url_module,type_user,module_notification_name,model_name,msg_notification_id)  WITH   t1 AS (SELECT module_notification_id     FROM module_notification     WHERE module_notification_name='RESPUESTA DE VALIDACION'),   t2 AS (SELECT msg_notification_id     FROM msg_notification      WHERE notification='La sección de :name_mod: ya tiene una respuesta.')   select  t1.module_notification_id,'/ubicacion/index','PROVIDER','UBICACION','ubicacion',t2.msg_notification_id   from t1,t2;
INSERT INTO module_notification_url (module_notification_id,url_module,type_user,module_notification_name,model_name,msg_notification_id)  WITH   t1 AS (SELECT module_notification_id     FROM module_notification     WHERE module_notification_name='RESPUESTA DE VALIDACION'),   t2 AS (SELECT msg_notification_id     FROM msg_notification      WHERE notification='La sección de :name_mod: ya tiene una respuesta.')   select  t1.module_notification_id,'/credenciales/index','PROVIDER','CREDENCIALES','certificacion',t2.msg_notification_id   from t1,t2;
INSERT INTO module_notification_url (module_notification_id,url_module,type_user,module_notification_name,model_name,msg_notification_id)  WITH   t1 AS (SELECT module_notification_id     FROM module_notification     WHERE module_notification_name='RESPUESTA DE VALIDACION'),   t2 AS (SELECT msg_notification_id     FROM msg_notification      WHERE notification='La sección de :name_mod: ya tiene una respuesta.')   select  t1.module_notification_id,'/financieros/update','PROVIDER','DATOS FINANCIEROS','ultima_declaracion',t2.msg_notification_id   from t1,t2;
INSERT INTO module_notification_url (module_notification_id,url_module,type_user,module_notification_name,model_name,msg_notification_id)  WITH   t1 AS (SELECT module_notification_id     FROM module_notification     WHERE module_notification_name='RESPUESTA DE VALIDACION'),   t2 AS (SELECT msg_notification_id     FROM msg_notification      WHERE notification='La sección de :name_mod: ya tiene una respuesta.')   select  t1.module_notification_id,'/banco/view','PROVIDER','METODO DE PAGO','intervencion_bancaria',t2.msg_notification_id   from t1,t2;
INSERT INTO module_notification_url (module_notification_id,url_module,type_user,module_notification_name,model_name,msg_notification_id)  WITH   t1 AS (SELECT module_notification_id     FROM module_notification     WHERE module_notification_name='RESPUESTA DE VALIDACION'),   t2 AS (SELECT msg_notification_id     FROM msg_notification      WHERE notification='La sección de :name_mod: ya tiene una respuesta.')   select  t1.module_notification_id,'/legales/view','PROVIDER','DATOS LEGALES','rfc',t2.msg_notification_id   from t1,t2;
INSERT INTO module_notification_url (module_notification_id,url_module,type_user,module_notification_name,model_name,msg_notification_id)  WITH   t1 AS (SELECT module_notification_id     FROM module_notification     WHERE module_notification_name='RESPUESTA DE VALIDACION'),   t2 AS (SELECT msg_notification_id     FROM msg_notification      WHERE notification='La sección de :name_mod: ya tiene una respuesta.')   select  t1.module_notification_id,'/economica/view','PROVIDER','ACTIVIDAD ECONOMICA','alta_hacienda',t2.msg_notification_id   from t1,t2;
INSERT INTO module_notification_url (module_notification_id,url_module,type_user,module_notification_name,model_name,msg_notification_id)  WITH   t1 AS (SELECT module_notification_id     FROM module_notification     WHERE module_notification_name='RESPUESTA DE VALIDACION'),   t2 AS (SELECT msg_notification_id     FROM msg_notification      WHERE notification='La sección de :name_mod: ya tiene una respuesta.')   select  t1.module_notification_id,'/perfil/index','PROVIDER','CURRICULUM','clientes_contratos',t2.msg_notification_id   from t1,t2;
INSERT INTO module_notification_url (module_notification_id,type_user,module_notification_name,msg_notification_id)  WITH   t1 AS (SELECT module_notification_id     FROM module_notification     WHERE module_notification_name='CERTIFICADO'),   t2 AS (SELECT msg_notification_id     FROM msg_notification      WHERE notification='Has completado correctamente el registro.')   select  t1.module_notification_id,'PROVIDER','CERTIFICADO',t2.msg_notification_id   from t1,t2;