CREATE TABLE public.movements_op
(
    movements_id bigserial,
    created_by bigint,
    provider_id bigint,
    model text COLLATE pg_catalog."default",
    action text COLLATE pg_catalog."default",
    origin_id bigint,
    "column" text COLLATE pg_catalog."default",
    column_data_old text COLLATE pg_catalog."default",
    column_date_new text COLLATE pg_catalog."default",
    created_at timestamp with time zone,
    column_data_id_old text COLLATE pg_catalog."default",
    column_data_id_new text COLLATE pg_catalog."default",
    first_update_certificate_id bigint,
    certificate boolean DEFAULT true,
    active boolean DEFAULT true,
    full_model boolean DEFAULT false,
    data_model json,
    CONSTRAINT pk_movements_op_provider PRIMARY KEY (movements_id)
)
WITH (
    OIDS = FALSE
)
TABLESPACE pg_default;

CREATE INDEX movements_op_origin_id_id
    ON public.movements_op USING btree
    (origin_id ASC NULLS LAST)
    TABLESPACE pg_default;
-- Index: movements_user_id_user_id

-- DROP INDEX public.movements_user_id_user_id;

CREATE INDEX movements_op_user_id_user_id
    ON public.movements_op USING btree
    (provider_id ASC NULLS LAST, model COLLATE pg_catalog."default" ASC NULLS LAST, first_update_certificate_id ASC NULLS LAST)
    TABLESPACE pg_default;