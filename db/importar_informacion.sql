CREATE SCHEMA temporal;

CREATE TABLE temporal.proveedores
(
  clave_proveedor           TEXT,
  clave_auxiliar            TEXT,
  nombre_proveedor          TEXT,
  rfc                       TEXT,
  habilitado                TEXT,
  calle                     TEXT,
  no_exterior               TEXT,
  no_interior               TEXT,
  codigo_postal             TEXT,
  telefono_particular       TEXT,
  telefono                  TEXT,
  fecha_vigencia            TEXT,
  nombre_comercial1         TEXT,
  nombre_representante      TEXT,
  tipo_representante        TEXT,
  pais                      TEXT,
  descripcion_pais          TEXT,
  estado                    TEXT,
  descripcion_estado        TEXT,
  municipio                 TEXT,
  decripcion_municipio      TEXT,
  localidad                 TEXT,
  descripcion_localidad     TEXT,
  zona                      TEXT,
  descripcion_zona          TEXT,
  colonia                   TEXT,
  descripcion_colonia       TEXT,
  realiza_refrendos         TEXT,
  tipo_entrega              TEXT,
  descripcion_entrega       TEXT,
  clasificacion             TEXT,
  descripcion_clasificacion TEXT,
  observacion               TEXT,
  direccion_electronica     TEXT,
  referencia                TEXT,
  nombre_comercial2         TEXT,
  correo_1                  TEXT,
  correo_2                  TEXT,
  sector_empresa            TEXT,
  descripcion_sector        TEXT
);

--psql -U postgres -d proveedores

--\COPY temporal.proveedores(clave_proveedor, clave_auxiliar, nombre_proveedor, rfc, habilitado, calle, no_exterior, no_interior, codigo_postal, telefono_particular, telefono, fecha_vigencia, nombre_comercial1, nombre_representante, tipo_representante, pais, descripcion_pais, estado, descripcion_estado, municipio, decripcion_municipio, localidad, descripcion_localidad, zona, descripcion_zona, colonia, descripcion_colonia, realiza_refrendos, tipo_entrega, descripcion_entrega, clasificacion, descripcion_clasificacion, observacion, direccion_electronica, referencia, nombre_comercial2, correo_1, correo_2, sector_empresa,descripcion_sector) FROM '/home/<USER>/Descargas/ProveedoresD.csv' DELIMITER ',' CSV

ALTER TABLE provider ADD COLUMN respaldo BOOLEAN DEFAULT FALSE;
ALTER TABLE provider ADD COLUMN nombre TEXT;
ALTER TABLE provider ADD COLUMN ap_paterno TEXT;
ALTER TABLE provider ADD COLUMN ap_materno TEXT;


ALTER SEQUENCE provider_provider_id_seq RESTART WITH 20;

ALTER TABLE public.provider ALTER COLUMN cp_fiscal TYPE TEXT;

INSERT INTO provider(tipo_persona, rfc, name_razon_social, name_comercial, calle_fiscal, numero_fiscal, interior_fiscal,
colonia_fiscal, cp_fiscal, telfono, email, descripcion, respaldo, tipo_provider)
SELECT 'Persona moral', pro.rfc, pro.nombre_proveedor, pro.nombre_comercial1, pro.calle, pro.no_exterior, pro.no_interior,
pro.colonia, cast(nullif(pro.codigo_postal, '') AS BIGINT), pro.telefono, pro.direccion_electronica, pro.observacion, TRUE, 'bys'
FROM temporal.proveedores pro
WHERE char_length(trim(pro.rfc)) = 12 AND pro.habilitado NOT LIKE 'D';


INSERT INTO provider(tipo_persona, rfc, nombre, ap_paterno, ap_materno, calle_fiscal, numero_fiscal, interior_fiscal,
colonia_fiscal, cp_fiscal, telfono, email, descripcion, respaldo, tipo_provider)
SELECT 'Persona fisica', pro.rfc, concat(split_part( nombre_proveedor, ' ' , 3), ' ', split_part( nombre_proveedor, ' ' , 4)),
split_part( nombre_proveedor, ' ', 1 ), split_part( nombre_proveedor, ' ', 2 ), pro.calle, pro.no_exterior,
pro.no_interior, pro.colonia, pro.codigo_postal, pro.telefono, pro.direccion_electronica, pro.observacion, TRUE, 'bys'
FROM temporal.proveedores pro
WHERE char_length(trim(pro.rfc)) = 13 AND pro.habilitado NOT LIKE 'D';

-- Hacer un update con la columna nombre de la ciudad con UPPER

--psql -U postgres -d proveedores
--\COPY cat_entidad(cv_entidad, nombre, abreviatura) FROM '/home/<USER>/Documentos/Cat_entidadesD.csv' DELIMITER ',' CSV QUOTE'~'
--\COPY cat_municipio(cv_municipio, cv_entidad, nombre) FROM '/home/<USER>/Documentos/Cat_municipiosD.csv' DELIMITER ',' CSV QUOTE'~'

SELECT split_part( nombre_proveedor, ' ' , 1 ) AS nombre,
split_part( nombre_proveedor, ' ', 2 ) AS ap_paterno,
split_part( nombre_proveedor, ' ', 3 ) AS ap_materno
FROM temporal.proveedores
WHERE clave_proveedor = '0000655'



--psql -U postgres -d actividades_economicas
--\COPY sat.cat_actividades(clave, nombre_actividad, descripcion, rama_id) FROM '/home/<USER>/Descargas/ActividadesEconomicasD.csv' DELIMITER ',' CSV QUOTE'''

--Not null | unique | default
CREATE DATABASE actividades_economicas
  WITH ENCODING='UTF8'
       CONNECTION LIMIT=-1;

CREATE SCHEMA sat;

CREATE TABLE sat.cat_sectores
(
  sector_id     BIGSERIAL PRIMARY KEY NOT NULL,
  nombre_sector TEXT
);

CREATE TABLE sat.cat_ramas
(
  rama_id     BIGSERIAL PRIMARY KEY NOT NULL,
  nombre_rama TEXT,
  sector_id   BIGINT REFERENCES sat.cat_sectores(sector_id)
);

CREATE TABLE sat.cat_actividades
(
  actividad_id     BIGSERIAL PRIMARY KEY NOT NULL,
  clave            BIGINT,
  nombre_actividad TEXT NOT NULL,
  descripcion      TEXT,
  rama_id          BIGINT REFERENCES sat.cat_ramas(rama_id)
);

ALTER TABLE sat.cat_actividades DROP CONSTRAINT "cat_actividades_clave_key"


--psql -U postgres -d actividades_economicas
--\COPY sat.act_temporal(cv_consecutivo, cv_actividad, descripcion_sat, ayuda, tipo, nivel_padre, clasificacion) FROM '/home/<USER>/Descargas/ActividadesEconomicas2.csv' DELIMITER ',' CSV QUOTE''''
CREATE TABLE sat.act_temporal
(
  cv_consecutivo INTEGER,
  cv_actividad INTEGER,
  descripcion_sat TEXT,
  ayuda TEXT,
  tipo INTEGER,
  nivel_padre INTEGER,
  clasificacion INTEGER
)

ALTER TABLE sat.cat_actividades ADD COLUMN clave_sat INTEGER;

UPDATE padron.cat_actividades act1
SET act1.clave_sat=subquery.cv_actividad
FROM (
SELECT cv_actividad, nombre_actividad
FROM sat.act_temporal) AS subquery
WHERE UPPER(act1.descripcion_sat) = subquery.nombre_actividad

--psql -U postgres -d padron4
--\COPY temporal.proveedor_temporal(municipio, oacvim, oage, cvmu, tipo_persona, sucursal, cuenta, nombre, rfc) FROM '/home/<USER>/Descargas/proveedoresD.csv' DELIMITER ',' CSV QUOTE'"'
CREATE TABLE temporal.proveedor_temporal
(
  municipio    TEXT,
  oacvim       INTEGER,
  oage         TEXT,
  cvmu         INTEGER,
  tipo_persona INTEGER,
  sucursal     INTEGER,
  cuenta       INTEGER,
  nombre       TEXT,
  rfc          TEXT
)


INSERT INTO padron.personas_fisicas(numero_cuenta, nombre, ap_paterno, ap_materno, rfc, rfc_nuevo)
SELECT DISTINCT pt.cuenta, concat(split_part( pt.nombre, ' ' , 3), ' ', split_part( pt.nombre, ' ' , 4)),
split_part( pt.nombre, ' ', 1 ) AS ap_paterno, split_part( pt.nombre, ' ', 2 ), pt.rfc, pt.rfc
FROM temporal.proveedor_temporal pt
WHERE pt.sucursal = 0 AND pt.tipo_persona = 1
ORDER BY ap_paterno ASC;


INSERT INTO padron.personas_morales(rfc, rfc_nuevo, razon_social, numero_cuenta)
SELECT DISTINCT ON (rfc) rfc, rfc, nombre, cuenta
FROM temporal.proveedor_temporal pt
WHERE pt.sucursal = 0 AND pt.tipo_persona = 2;


INSERT INTO padron.numero_cuenta_rfc(numero_cuenta, persona_fisica_id)
SELECT DISTINCT pt.cuenta, pf.persona_fisica_id
FROM padron.personas_fisicas pf, temporal.proveedor_temporal pt
WHERE  BTRIM(pf.rfc, ' ') = BTRIM(pt.rfc,' ')  AND pt.tipo_persona = 1 AND pt.sucursal != 0
ORDER BY persona_fisica_id;

INSERT INTO padron.numero_cuenta_rfc(numero_cuenta, persona_moral_id)
SELECT pt.cuenta, pm.persona_moral_id
FROM padron.personas_morales pm, temporal.proveedor_temporal pt
WHERE  BTRIM(pm.rfc, ' ') = BTRIM(pt.rfc,' ')  AND pt.tipo_persona = 2 AND pt.sucursal != 0
ORDER BY persona_moral_id;


UPDATE public.provider pro
SET city_fiscal= CAST(subquery.cv_municipio AS BIGINT)
FROM (
SELECT m.nombre, m.cv_municipio, pro2.rfc AS rfc, pro2.decripcion_municipio
FROM temporal.proveedores pro2 , public.cat_municipio m
WHERE BTRIM(UPPER(m.nombre), ' ') = BTRIM(UPPER(pro2.decripcion_municipio), ' '))
AS subquery
WHERE  pro.rfc = subquery.rfc AND pro.respaldo = TRUE;


ALTER TABLE padron.numero_cuenta_rfc ADD COLUMN rfc TEXT;

UPDATE padron.numero_cuenta_rfc cuenta
SET rfc=subquery.rfc
FROM (
SELECT persona_moral_id, rfc
FROM padron.personas_morales) AS subquery
WHERE cuenta.persona_moral_id = subquery.persona_moral_id

UPDATE padron.numero_cuenta_rfc
SET rfc = BTRIM(rfc, ' ')

ALTER TABLE padron.numero_cuenta_rfc DROP COLUMN persona_fisica_id, DROP COLUMN persona_moral_id;

SELECT nombre, ap_paterno, ap_materno, nombre_comercial, rfc, rfc_nuevo, curp, fecha_nacimiento, ciudad_nacimiento,
pais_origen, pais_residencia, telefono_fijo, telefono_movil, email, sexo, civil_id, nivel_estudio_id, rango_salario_id,
religion_id, orientacion_sexual, numero_seguro_social, cuenta_seguro, ultima_modificacion, usuario_modificacion, username,
contrasenia, geolocalizacion, vivo, notas, otros, fecha_inicio_op, status, motivo_rechazo, numero_cuenta
FROM padron.personas_fisicas
WHERE status = 'AS400'


SELECT rfc, rfc_nuevo, razon_social, sociedad_id, nombre_comercial, telefono_fijo, telefono_movil, email,
fecha_nacimiento, fecha_inicio_operacion, pais_origen, pais_residencia, ultima_modificacion, usuario_modificacion,
username, contrasenia, geolocalizacion, activo, notas, otros, actividad_economica_id, status, motivo_rechazo,
numero_cuenta
FROM padron.personas_morales
WHERE status = 'AS400'



CREATE TABLE temporal.proveedor_temporal2 (LIKE temporal.proveedor_temporal INCLUDING CONSTRAINTS INCLUDING INDEXES);
CREATE TABLE padron.personas_fisicas2 (LIKE padron.personas_fisicas INCLUDING CONSTRAINTS INCLUDING INDEXES);
CREATE TABLE padron.personas_morales2 (LIKE padron.personas_morales INCLUDING CONSTRAINTS INCLUDING INDEXES);
CREATE TABLE padron.numero_cuenta_rfc2 (LIKE padron.numero_cuenta_rfc INCLUDING CONSTRAINTS INCLUDING INDEXES);

--\COPY temporal.proveedor_temporal2(municipio, oacvim, oage, cvmu, tipo_persona, sucursal, cuenta, nombre, rfc) FROM '/home/<USER>/Descargas/20170124_PadrónREC.csv' DELIMITER ',' CSV

INSERT INTO padron.personas_fisicas2(numero_cuenta, nombre, ap_paterno, ap_materno, rfc, rfc_nuevo)
SELECT DISTINCT pt.cuenta, concat(split_part( pt.nombre, ' ' , 3), ' ', split_part( pt.nombre, ' ' , 4)),
split_part( pt.nombre, ' ', 1 ) AS ap_paterno, split_part( pt.nombre, ' ', 2 ), pt.rfc, pt.rfc
FROM temporal.proveedor_temporal2 pt
WHERE pt.sucursal = 0 AND pt.tipo_persona = 1
ORDER BY ap_paterno ASC;


INSERT INTO padron.personas_morales2(rfc, rfc_nuevo, razon_social, numero_cuenta)
SELECT DISTINCT ON (rfc) rfc, rfc, nombre, cuenta
FROM temporal.proveedor_temporal2 pt
WHERE pt.sucursal = 0 AND pt.tipo_persona = 2;


INSERT INTO padron.numero_cuenta_rfc2(numero_cuenta, rfc)
SELECT DISTINCT pt.cuenta, BTRIM(pf.rfc, ' ')
FROM padron.personas_fisicas2 pf, temporal.proveedor_temporal2 pt
WHERE  BTRIM(pf.rfc, ' ') = BTRIM(pt.rfc,' ')  AND pt.tipo_persona = 1 AND pt.sucursal != 0
ORDER BY BTRIM(pf.rfc, ' ');

INSERT INTO padron.numero_cuenta_rfc2(numero_cuenta, rfc)
SELECT pt.cuenta, BTRIM(pm.rfc, ' ')
FROM padron.personas_morales2 pm, temporal.proveedor_temporal2 pt
WHERE  BTRIM(pm.rfc, ' ') = BTRIM(pt.rfc,' ')  AND pt.tipo_persona = 2 AND pt.sucursal != 0
ORDER BY persona_moral_id;


UPDATE padron.personas_fisicas2
SET status = 'AS400'

UPDATE padron.personas_morales2
SET status = 'AS400'

UPDATE padron.personas_fisicas2
SET rfc = BTRIM(rfc, ' ')

UPDATE padron.personas_morales2
SET rfc = BTRIM(rfc, ' ')

CREATE TABLE sepomex.catalogo
(
  d_codigo            TEXT,
  d_asentamiento      TEXT,
  d_tipo_asentamiento TEXT,
  d_municipio         TEXT,
  d_estado            TEXT,
  d_ciudad            TEXT,
  d_cp                TEXT,
  c_estado            TEXT,
  c_oficina           TEXT,
  c_CP                TEXT,
  c_tipo_asentamiento TEXT,
  c_municipio         TEXT,
  id_asentamiento_cp  TEXT,
  descripcion_zona    TEXT,
  clave_ciudad        TEXT
)


--\COPY sepomex.catalogo(d_codigo, d_asentamiento, d_tipo_asentamiento, d_municipio, d_estado, d_ciudad, d_cp, c_estado, c_oficina, c_CP, c_tipo_asentamiento, c_municipio, id_asentamiento_cp, descripcion_zona, clave_ciudad) FROM '/home/<USER>/Descargas/CPdescarga.txt' DELIMITER '|' CSV

-- unique, not null, default
CREATE TABLE sepomex.cat_entidades
(
  entidad_id BIGSERIAL,
  cv_entidad TEXT UNIQUE,
  nombre     TEXT
);

CREATE TABLE sepomex.cat_municipios
(
  municipio_id BIGSERIAL UNIQUE,
  cv_municipio TEXT,
  nombre       TEXT,
  cv_entidad   TEXT REFERENCES sepomex.cat_entidades(cv_entidad)
);

CREATE TABLE sepomex.cat_tipo_asentamientos
(
  tipo_asentamiento_id BIGSERIAL UNIQUE,
  cv_asentamiento TEXT,
  nombre          TEXT
);

CREATE TABLE sepomex.cat_asentamientos
(
  colonia_id           BIGSERIAL,
  nombre               TEXT,
  cp                   TEXT,
  tipo_asentamiento_id BIGINT REFERENCES sepomex.cat_tipo_asentamientos(tipo_asentamiento_id),
  municipio_id         BIGINT REFERENCES sepomex.cat_municipios(municipio_id)
);


INSERT INTO sepomex.cat_entidades(cv_entidad, nombre)
SELECT DISTINCT c_estado, d_estado
FROM sepomex.catalogo
ORDER BY d_estado;

INSERT INTO sepomex.cat_municipios(cv_municipio, nombre, cv_entidad)
SELECT DISTINCT c_municipio, d_municipio, c_estado
FROM sepomex.catalogo
ORDER BY d_municipio;

INSERT INTO sepomex.cat_tipo_asentamientos(cv_asentamiento, nombre)
SELECT DISTINCT c_tipo_asentamiento, d_tipo_asentamiento
FROM sepomex.catalogo
ORDER BY d_tipo_asentamiento;

INSERT INTO sepomex.cat_asentamiento(nombre, cp, tipo_asentamiento_id, municipio_id)
SELECT DISTINCT ON (col.d_asentamiento, col.d_codigo) col.d_asentamiento, col.d_codigo, ase.tipo_asentamiento_id, mun.municipio_id
FROM sepomex.catalogo col, sepomex.cat_tipo_asentamientos ase, sepomex.cat_municipios mun
WHERE col.c_municipio = mun.cv_municipio AND col.d_municipio = mun.nombre
AND col.c_tipo_asentamiento = ase.cv_asentamiento AND col.d_tipo_asentamiento = ase.nombre
ORDER BY col.d_asentamiento;

INSERT INTO sepomex.cat_asentamientos(nombre, cp, tipo_asentamiento_id, municipio_id)
SELECT col.d_asentamiento, col.d_codigo, ase.tipo_asentamiento_id, mun.municipio_id
FROM sepomex.catalogo col, sepomex.cat_tipo_asentamientos ase, sepomex.cat_municipios mun
WHERE col.c_municipio = mun.cv_municipio AND col.d_municipio = mun.nombre
AND col.c_tipo_asentamiento = ase.cv_asentamiento AND col.d_tipo_asentamiento = ase.nombre
AND col.c_estado = mun.cv_entidad
ORDER BY col.d_asentamiento ASC;


CREATE index ON sepomex.catalogo (d_codigo, d_asentamiento, c_municipio);