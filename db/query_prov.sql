﻿insert into last_send_validation(provider_id,model_validar,model_name,provider_type) select provider_id,'validClientes','clientes_contratos','bys' from provider;
insert into last_send_validation(provider_id,model_validar,model_name,provider_type) select provider_id,'validClientes','clientes_contratos','op' from provider;

insert into last_send_validation(provider_id,model_validar,model_name,provider_type) select provider_id,'mainHacienda','alta_hacienda','bys' from provider;
insert into last_send_validation(provider_id,model_validar,model_name,provider_type) select provider_id,'mainHacienda','alta_hacienda','op' from provider;

insert into last_send_validation(provider_id,model_validar,model_name,provider_type) select provider_id,'mainFilterLegales','rfc','bys' from provider;
insert into last_send_validation(provider_id,model_validar,model_name,provider_type) select provider_id,'mainFilterLegales','rfc','op' from provider;



insert into last_send_validation(provider_id,model_validar,model_name,provider_type) select provider_id,'metodoPagoVal','intervencion_bancaria','bys' from provider;
insert into last_send_validation(provider_id,model_validar,model_name,provider_type) select provider_id,'metodoPagoVal','intervencion_bancaria','op' from provider;


insert into last_send_validation(provider_id,model_validar,model_name,provider_type) select provider_id,'mainPropiedad','escritura_publica','bys' from provider;
insert into last_send_validation(provider_id,model_validar,model_name,provider_type) select provider_id,'mainPropiedad','escritura_publica','op' from provider;



insert into last_send_validation(provider_id,model_validar,model_name,provider_type) select provider_id,'validRelacionAccionistas','relacion_accionistas','bys' from provider;
insert into last_send_validation(provider_id,model_validar,model_name,provider_type) select provider_id,'validRelacionAccionistas','relacion_accionistas','op' from provider;

insert into last_send_validation(provider_id,model_validar,model_name,provider_type) select provider_id,'validModificacionActa','modificacion_acta','bys' from provider;
insert into last_send_validation(provider_id,model_validar,model_name,provider_type) select provider_id,'validModificacionActa','modificacion_acta','op' from provider;



insert into last_send_validation(provider_id,model_validar,model_name,provider_type) select provider_id,'validRepresentante','representante_legal','bys' from provider;
insert into last_send_validation(provider_id,model_validar,model_name,provider_type) select provider_id,'validRepresentante','representante_legal','op' from provider;

insert into last_send_validation(provider_id,model_validar,model_name,provider_type) select provider_id,'mainFilterBalanceEstado','balance_estado','bys' from provider;
insert into last_send_validation(provider_id,model_validar,model_name,provider_type) select provider_id,'mainFilterBalanceEstado','balance_estado','op' from provider;


insert into last_send_validation(provider_id,model_validar,model_name,provider_type) select provider_id,'mainFilterCapcidadContratacion','capacidad_contratacion','bys' from provider;
insert into last_send_validation(provider_id,model_validar,model_name,provider_type) select provider_id,'mainFilterCapcidadContratacion','capacidad_contratacion','op' from provider;


insert into last_send_validation(provider_id,model_validar,model_name,provider_type) select provider_id,'mainFilterDeclaracionIva','declaracion_iva','bys' from provider;
insert into last_send_validation(provider_id,model_validar,model_name,provider_type) select provider_id,'mainFilterDeclaracionIva','declaracion_iva','op' from provider;

insert into last_send_validation(provider_id,model_validar,model_name,provider_type) select provider_id,'mainFilterEstadoFinanciero','estado_financiero','bys' from provider;
insert into last_send_validation(provider_id,model_validar,model_name,provider_type) select provider_id,'mainFilterEstadoFinanciero','estado_financiero','op' from provider;

insert into last_send_validation(provider_id,model_validar,model_name,provider_type) select provider_id,'mainFilterDeclaracionIsr','declaracion_isr','bys' from provider;
insert into last_send_validation(provider_id,model_validar,model_name,provider_type) select provider_id,'mainFilterDeclaracionIsr','declaracion_isr','op' from provider;

insert into last_send_validation(provider_id,model_validar,model_name,provider_type) select provider_id,'mainFilterUltimaDeclaracion','ultima_declaracion','bys' from provider;
insert into last_send_validation(provider_id,model_validar,model_name,provider_type) select provider_id,'mainFilterUltimaDeclaracion','ultima_declaracion','op' from provider;


insert into last_send_validation(provider_id,model_validar,model_name,provider_type) select provider_id,'validExperiencia','experiencia','op' from provider;
	

insert into last_send_validation(provider_id,model_validar,model_name,provider_type) select provider_id,'validPersonal','personal_tecnico','op' from provider;

insert into last_send_validation(provider_id,model_validar,model_name,provider_type) select provider_id,'validOrganigrama','organigrama','op' from provider;


insert into last_send_validation(provider_id,model_validar,model_name,provider_type) select provider_id,'validMaquinaria','maquinaria_equipos','op' from provider;

insert into last_send_validation(provider_id,model_validar,model_name,provider_type) select provider_id,'validCertificacion','certificacion','bys' from provider;

insert into last_send_validation(provider_id,model_validar,model_name,provider_type) select provider_id,'validFotografia','fotografia_negocio','bys' from provider;
insert into last_send_validation(provider_id,model_validar,model_name,provider_type) select provider_id,'validFotografia','fotografia_negocio','op' from provider;

insert into last_send_validation(provider_id,model_validar,model_name,provider_type) select provider_id,'validUbicacion','ubicacion','bys' from provider;
insert into last_send_validation(provider_id,model_validar,model_name,provider_type) select provider_id,'validUbicacion','ubicacion','op' from provider;

insert into last_send_validation(provider_id,model_validar,model_name,provider_type) select provider_id,'mainFilterUbicacionNl','direccion_nl','bys' from provider;
insert into last_send_validation(provider_id,model_validar,model_name,provider_type) select provider_id,'mainFilterUbicacionNl','direccion_nl','op' from provider;