--unique default not null

CREATE TABLE provider.isr
(
  isr_id      BIGSERIAL NOT NULL,
  archivo     TEXT NOT NULL,
  ejercicio   TEXT NOT NULL,   --anio
  fecha       DATE NOT NULL,   --fecha en que declaro
  acuse       TEXT NOT NULL,   --archivo
  tipo        TEXT,
  provider_id BIGINT NOT NULL,
  registro_fecha TIMESTAMP WITHOUT TIME ZONE NOT NULL DEFAULT NOW(),
  CONSTRAINT isr_pk PRIMARY KEY (isr_id),
  CONSTRAINT fk_isr_provider_id FOREIGN KEY (provider_id)
      REFERENCES public.provider (provider_id) MATCH SIMPLE
      ON UPDATE NO ACTION ON DELETE NO ACTION
);

CREATE TABLE provider.estado_financiero
(
  estado_financiero_id BIGSERIAL NOT NULL,
  archivo              TEXT NOT NULL,
  fecha_dictaminado    DATE NOT NULL,
  nombre_dictamina     TEXT NOT NULL,
  cedula_profesional   TEXT NOT NULL, --archivo
  provider_id          BIGINT NOT NULL,
  registro_fecha TIMESTAMP WITHOUT TIME ZONE NOT NULL DEFAULT NOW(),
  CONSTRAINT estado_financiero_pk PRIMARY KEY (estado_financiero_id),
  CONSTRAINT fk_estado_financiero_provider_id FOREIGN KEY (provider_id)
      REFERENCES public.provider (provider_id) MATCH SIMPLE
      ON UPDATE NO ACTION ON DELETE NO ACTION
);

CREATE TABLE provider.capacidad_contratacion
(
  capacidad_contratacion_id BIGSERIAL NOT NULL,
  activo_circulante         NUMERIC,
  pasivo_circulante         NUMERIC,
  pasivo_largo_plazo        NUMERIC,
  pasivo_diferido           NUMERIC,
  capital_contable          NUMERIC,
  ventas_totales            BIGINT,
  provider_id               BIGINT,
  registro_fecha TIMESTAMP WITHOUT TIME ZONE NOT NULL DEFAULT NOW(),
  CONSTRAINT capacidad_contratacion_pk PRIMARY KEY (capacidad_contratacion_id),
  CONSTRAINT fk_capacidad_contratacion_provider_id FOREIGN KEY (provider_id)
      REFERENCES public.provider (provider_id) MATCH SIMPLE
      ON UPDATE NO ACTION ON DELETE NO ACTION
);


--PROVEEDORES

CREATE TABLE provider.ultima_declaracion
(
  ultima_declaracion_id BIGSERIAL NOT NULL,
  archivo               TEXT NOT NULL,
  anexo                 TEXT NOT NULL, --archivo
  acuse_recibo          TEXT NOT NULL, --archivo
  comprobante_pago      TEXT NOT NULL, --archivo
  provider_id           BIGINT NOT NULL,
  registro_fecha TIMESTAMP WITHOUT TIME ZONE NOT NULL DEFAULT NOW(),
  CONSTRAINT ultima_declaracion_pk PRIMARY KEY (ultima_declaracion_id),
  CONSTRAINT fk_ultima_declaracion_provider_id FOREIGN KEY (provider_id)
      REFERENCES public.provider (provider_id) MATCH SIMPLE
      ON UPDATE NO ACTION ON DELETE NO ACTION
);

CREATE TABLE provider.balance_estado
(
  balance_estado_id     BIGSERIAL NOT NULL,
  archivo               TEXT NOT NULL,
  anexo                 TEXT NOT NULL, --archivo
  acuse_recibo          TEXT NOT NULL, --archivo
  comprobante_pago      TEXT NOT NULL, --archivo
  informacion_anio      TEXT,
  provider_id           BIGINT NOT NULL,
  registro_fecha TIMESTAMP WITHOUT TIME ZONE NOT NULL DEFAULT NOW(),
  CONSTRAINT balance_estado_pk PRIMARY KEY (balance_estado_id),
  CONSTRAINT fk_balance_estado_provider_id FOREIGN KEY (provider_id)
      REFERENCES public.provider (provider_id) MATCH SIMPLE
      ON UPDATE NO ACTION ON DELETE NO ACTION
);

CREATE TABLE provider.declaracion_iva
(
  declaracion_iva_id    BIGSERIAL NOT NULL,
  archivo               TEXT NOT NULL,
  ejercicio             TEXT NOT NULL, --anio
  fecha                 DATE NOT NULL,
  acuse                 TEXT NOT NULL, --archivo
  provider_id           BIGINT NOT NULL,
  registro_fecha TIMESTAMP WITHOUT TIME ZONE NOT NULL DEFAULT NOW(),
  CONSTRAINT declaracion_iva_pk PRIMARY KEY (declaracion_iva_id),
  CONSTRAINT fk_declaracion_iva_provider_id FOREIGN KEY (provider_id)
      REFERENCES public.provider (provider_id) MATCH SIMPLE
      ON UPDATE NO ACTION ON DELETE NO ACTION
);