insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '1.1','EQUIPO Y ACCESORIOS DE OFICINA',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'OFICINA '  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '1.2','SERVICIOS DE OFICINA',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'OFICINA '  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '2.1','ARTICULOS DE PAPELERIA',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'PAPELERIA'  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '2.2','HOJAS',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'PAPELERIA'  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '2.3','CARPETAS',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'PAPELERIA'  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '2.4','BOLIGRAFOS-LAPICES',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'PAPELERIA'  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '2.5','LIBRETAS',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'PAPELERIA'  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '2.6','ETIQUETAS',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'PAPELERIA'  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '2.7','SELLOS',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'PAPELERIA'  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '2.8','PRODUCTOS DE PAPEL O CARTON',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'PAPELERIA'  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '3.1','PAQUETES ESCOLARES',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'ESCOLAR'  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '3.2','MATERIAL Y UTILES DE ENSEÑANZA',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'ESCOLAR'  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '3.3','MATERIAL ESTADISTICO Y GEOGRAFICO',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'ESCOLAR'  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '3.4','MATERIAL DIDACTICO',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'ESCOLAR'  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '4.1','BANDERAS',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'ARTICULOS PATRIOS'  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '4.2','SIMBOLOS PATRIOS',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'ARTICULOS PATRIOS'  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '4.3','LETRAS Y ESCUDO',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'ARTICULOS PATRIOS'  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '5.1','IMPRENTA',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'IMPRESOS '  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '5.2','FORMAS IMPRESAS',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'IMPRESOS '  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '5.3','PAPELERIA MEMBRETADA',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'IMPRESOS '  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '5.4','LIBROS, REVISTAS Y PERIODICOS',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'IMPRESOS '  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '5.5','TRIPTICOS, BOLETAS, FOLLETOS, POSTERS',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'IMPRESOS '  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '5.6','VALES - TARJETAS (DESPENSA, GASOLINA, COMIDA, ETC.)',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'IMPRESOS '  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '5.7','GAFETES',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'IMPRESOS '  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '5.8','PROMOCIONALES E IMPRESOS',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'IMPRESOS '  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '5.9','BORDADOS IMPRESOS',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'IMPRESOS '  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '5.10','ELABORACION DE LONAS, MANTAS, PENDONES, BANNERS',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'IMPRESOS '  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '5.11','ROTULACION DE UNIDADES',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'IMPRESOS '  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '5.12','RECONOCIMIENTOS, MEDALLAS, TROFEOS',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'IMPRESOS '  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '6.1','AGENCIA DE PUBLICIDAD',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'PUBLICIDAD'  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '6.2','ESPACIOS PUBLICITARIOS',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'PUBLICIDAD'  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '6.3','SERVICIOS DE PUBLICIDAD',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'PUBLICIDAD'  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '6.4','DISEÑO DE CAMPAÑAS PUBLICITARIAS',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'PUBLICIDAD'  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '6.5','DISEÑO DE REDES SOCIALES',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'PUBLICIDAD'  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '6.6','DISEÑO GRAFICO',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'PUBLICIDAD'  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '6.7','ENTREGA DE PERIODICOS Y REVISTAS',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'PUBLICIDAD'  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '7.1','MATERIAL ARTISTICO',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'ARTISTICO - CULTURAL'  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '7.2','SERVICIOS ARTISTICOS',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'ARTISTICO - CULTURAL'  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '8.1','ARTICULOS MUSICALES',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'MUSICA - ESPARCIMIENTO'  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '8.2','ESPARCIMIENTO',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'MUSICA - ESPARCIMIENTO'  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '9.1','ARTICULOS DEPORTIVOS',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'DEPORTES'  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '9.2','SERVICIOS DEPORTIVOS',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'DEPORTES'  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '10.1','JUGUETES',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'JUGUETES - JUEGOS'  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '10.2','PELOTAS',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'JUGUETES - JUEGOS'  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '10.3','BICICLETAS, TRICICLOS',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'JUGUETES - JUEGOS'  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '10.4','JUEGOS INFANTILES',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'JUGUETES - JUEGOS'  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '10.5','EQUIPAMIENTO DE PARQUES',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'JUGUETES - JUEGOS'  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '11.1','RELOJES',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'JOYERIA'  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '11.2','ANILLOS',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'JOYERIA'  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '11.3','PULSERAS',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'JOYERIA'  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '11.4','COLLARES',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'JOYERIA'  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '11.5','BISUTERIA',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'JOYERIA'  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '12.1','ARTICULOS DE BELLEZA',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'BELLEZA'  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '12.2','EQUIPO DE BELLEZA',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'BELLEZA'  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '12.3','SERVICIOS DE BELLEZA',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'BELLEZA'  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '13.1','BLANCOS',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'TEXTILES '  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '13.2','MATERIAL TEXTIL',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'TEXTILES '  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '13.3','ROPA - UNIFORMES',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'TEXTILES '  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '13.4','CALZADO',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'TEXTILES '  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '13.5','SERVICIOS TEXTILES',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'TEXTILES '  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '14.1','PRODUCTOS DE PLASTICO',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'PLASTICO'  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '14.2','CUBETAS ',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'PLASTICO'  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '14.3','HULES, PLASTICOS Y DERIVADOS',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'PLASTICO'  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '14.4','BOLSAS',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'PLASTICO'  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '14.5','MATERIALES RECICLABLES',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'PLASTICO'  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '15.1','ASESORIA Y CONSULTORIA ',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'SERVICIOS PROFESIONALES'  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '15.2','CONFERENCIAS',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'SERVICIOS PROFESIONALES'  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '15.3','CURSOS',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'SERVICIOS PROFESIONALES'  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '15.4','SERVICIO DE CAPACITACION',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'SERVICIOS PROFESIONALES'  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '15.5','ELABORACION DE MATERIAL INFORMATIVO',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'SERVICIOS PROFESIONALES'  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '15.6','ESTUDIOS DE FACTIBILIDAD',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'SERVICIOS PROFESIONALES'  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '15.7','SERVICIO DE COBRANZA',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'SERVICIOS PROFESIONALES'  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '15.8','SERVICIO DE TRADUCCION',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'SERVICIOS PROFESIONALES'  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '15.9','SERVICIOS LEGALES',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'SERVICIOS PROFESIONALES'  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '15.10','SERVICIOS DE CONTABILIDAD Y AUDITORIA',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'SERVICIOS PROFESIONALES'  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '15.11','SERVICIOS NOTARIALES',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'SERVICIOS PROFESIONALES'  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '15.12','SERVICIOS DE AVALUOS',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'SERVICIOS PROFESIONALES'  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '15.13','INVESTIGACION DE MERCADO',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'SERVICIOS PROFESIONALES'  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '15.14','ENCUESTAS DE MERCADO',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'SERVICIOS PROFESIONALES'  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '15.15','MEDICION DE RATINGS',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'SERVICIOS PROFESIONALES'  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '15.16','SERVICIOS RELACIONADOS CON MONITOREO DE INFORMACION EN MEDIOS MASIVOS',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'SERVICIOS PROFESIONALES'  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '15.17','SUBASTAS',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'SERVICIOS PROFESIONALES'  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '15.18','AGENCIA DE COLOCACION Y SELECCION DE PERSONAL',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'SERVICIOS PROFESIONALES'  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '15.19','SERVICIOS DE ASISTENCIA SOCIAL',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'SERVICIOS PROFESIONALES'  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '15.20','DESARROLLO PERSONAL',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'SERVICIOS PROFESIONALES'  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '15.21','BECAS',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'SERVICIOS PROFESIONALES'  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '15.22','FACTURACION ELECTRONICA',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'SERVICIOS PROFESIONALES'  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '15.23','COMPROBANTES DIGITALES',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'SERVICIOS PROFESIONALES'  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '15.24','SERVICIO DE LOGISTICA',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'SERVICIOS PROFESIONALES'  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '15.25','AGENCIA ADUANAL',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'SERVICIOS PROFESIONALES'  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '15.26','ESTUDIOS DE CALIDAD',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'SERVICIOS PROFESIONALES'  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '15.26','ESTUDIOS AMBIENTALES',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'SERVICIOS PROFESIONALES'  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '15.27','ESTUDIOS DE SUELO',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'SERVICIOS PROFESIONALES'  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '15.28','CERTIFICACION ISO',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'SERVICIOS PROFESIONALES'  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '16.1','ORGANIZACION DE EVENTOS',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'EVENTOS'  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '16.2','ARREGLOS FLORALES',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'EVENTOS'  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '16.3','RENTA DE SALONES, TEATROS, AUDITORIOS',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'EVENTOS'  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '16.4','RENTA DE EQUIPO DE SONIDO, AUDIO, PROYECCION',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'EVENTOS'  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '16.5','RENTA DE TOLDOS, LONAS, CARPAS',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'EVENTOS'  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '16.6','RENTA DE TARIMAS, MAMPARAS',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'EVENTOS'  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '16.7','RENTA DE MODULOS, STANDS',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'EVENTOS'  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '16.8','RENTA DE CABINAS FOTOGRAFICAS',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'EVENTOS'  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '16.9','DECORACION',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'EVENTOS'  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '16.10','RENTA DE EQUIPO PARA SALON DE EVENTOS',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'EVENTOS'  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '16.11','EXPOSICIONES',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'EVENTOS'  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '16.12','MATERIAL ESCENOGRAFICO',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'EVENTOS'  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '16.13','MESEROS',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'EVENTOS'  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '16.14','RENTA DE MESAS, SILLAS, MANTELES, CUCHILLERIA',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'EVENTOS'  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '16.15','RENTA DE PANTALLAS',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'EVENTOS'  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '16.16','RENTA DE PLANTAS DE LUZ',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'EVENTOS'  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '16.17','RENTA DE PLANTAS DE ENERGIA',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'EVENTOS'  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '16.18','RENTA DE SANITARIOS PORTATILES',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'EVENTOS'  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '16.19','RENTA DE UNIFILAS, VALLAS',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'EVENTOS'  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '16.20','RENTA DE GRADAS',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'EVENTOS'  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '16.21','RENTA DE SALAS TIPO LOUNGE',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'EVENTOS'  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '16.22','SERVICIOS DE BANQUETES',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'EVENTOS'  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '16.23','SERVICIO DE COFFEE BREAK',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'EVENTOS'  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '16.24','SERVICIOS DE AMBIENTACION EN EVENTOS',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'EVENTOS'  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '16.25','RENTA DE ARCOS DETECTORES DE METALES',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'EVENTOS'  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '16.26','ADMINISTRACION Y OPERACION DE ESTACIONAMIENTOS',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'EVENTOS'  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '16.27','ILUMINACION DECORATIVA',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'EVENTOS'  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '16.28','EDECANES',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'EVENTOS'  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '16.29','PIPAS DE AGUA',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'EVENTOS'  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '16.30','ACTIVIDADES CULTURALES Y DEPORTIVAS',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'EVENTOS'  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '16.31','SERVICIOS RECREATIVOS',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'EVENTOS'  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '17.1','EQUIPO Y MATERIAL DE AUDIO',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'AUDIO '  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '17.2','SERVICIOS DE AUDIO',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'AUDIO '  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '18.1','EQUIPO Y MATERIAL DE VIDEO Y FOTOGRAFIA',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'VIDEO - FOTOGRAFIA'  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '18.2','SERVICIOS DE VIDEO Y FOTOGRAFIA',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'VIDEO - FOTOGRAFIA'  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '19.1','PRODUCTOS ALIMENTICIOS',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'ALIMENTOS'  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '19.2','SERVICIO DE ALIMENTOS',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'ALIMENTOS'  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '20.1','BEBIDAS',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'BEBIDAS'  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '21.1','ANIMALES',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'ANIMALES'  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '21.2','PRODUCTOS PARA ANIMALES',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'ANIMALES'  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '21.3','SERVICIOS PARA ANIMALES',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'ANIMALES'  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '22.1','MOBILIARIO Y EQUIPO',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'MOBILIARIO'  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '22.2','SERVICIO PARA MOBILIARIO',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'MOBILIARIO'  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '23.1','ARTICULOS PARA EL HOGAR',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'HOGAR '  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '23.2','LINEA BLANCA',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'HOGAR '  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '23.3','PERSIANAS',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'HOGAR '  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '23.4','ABANICOS',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'HOGAR '  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '23.5','CAMAS, CATRES, COLCHONES',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'HOGAR '  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '24.1','ARTICULOS DE COCINA',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'COCINA'  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '24.2','UTENSILIOS DE COCINA',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'COCINA'  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '24.3','VAJILLAS',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'COCINA'  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '24.4','CUBIERTOS',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'COCINA'  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '24.5','VASOS',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'COCINA'  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '24.6','PLATOS',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'COCINA'  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '24.7','DESHECHABLES',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'COCINA'  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '24.9','ELECTRODOMESTICOS',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'COCINA'  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '25.1','PASAJES AEREOS',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'VIAJES'  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '25.2','PASAJES TERRESTRES',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'VIAJES'  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '25.3','SERVICIOS DE TRASLADO',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'VIAJES'  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '25.4','SERVICIO DE HOSPEDAJE',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'VIAJES'  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '25.6','AGENCIA DE VIAJES - ORGANIZACION DE PAQUETES TURISTICOS',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'VIAJES'  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '25.7','HOTEL',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'VIAJES'  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '26.1','VEHICULOS TERRESTRES',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'VEHICULOS'  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '26.2','ACCESORIOS, REFACCIONES Y EQUIPO PARA VEHICULOS TERRESTRES',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'VEHICULOS'  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '26.3','SERVICIOS DE VEHICULOS TERRESTRES',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'VEHICULOS'  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '26.4','VEHICULOS AEREOS',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'VEHICULOS'  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '26.5','ACCESORIOS, REFACCIONES Y EQUIPO PARA VEHICULOS AEREOS',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'VEHICULOS'  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '26.6','SERVICIOS DE VEHICULOS AEREOS',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'VEHICULOS'  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '26.7','VEHICULOS MARITIMOS',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'VEHICULOS'  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '26.8','ACCESORIOS, REFACCIONES Y EQUIPO PARA VEHICULOS MARITIMOS',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'VEHICULOS'  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '29.9','SERVICIOS DE VEHICULOS MARITIMOS',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'VEHICULOS'  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '27.1','FLETES Y MANIOBRAS',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'TRANSPORTE'  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '27.2','MUDANZAS',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'TRANSPORTE'  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '27.3','MENSAJERIA',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'TRANSPORTE'  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '27.4','DESMONTAJE',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'TRANSPORTE'  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '27.5','EMBALAJE',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'TRANSPORTE'  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '27.6','DESMONTAJE, EMBALAJE, TRASLADO E INSTALACION',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'TRANSPORTE'  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '27.8','ALMACENAJE, ENVASE Y EMBALAJE',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'TRANSPORTE'  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '27.9','SERVICIOS POSTALES ',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'TRANSPORTE'  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '27.10','TRANSPORTE DE PERSONAL',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'TRANSPORTE'  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '28.1','PRODUCTOS DE COMBUSTIBLES',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'COMBUSTIBLES '  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '28.2','SERVICIOS DE COMBUSTIBLES',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'COMBUSTIBLES '  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '29.1','PRODUCTOS LUBRICANTES',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'LUBRICANTES'  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '30.1','EQUIPO DE COMPUTO - IMPRESORAS - COPIADORAS',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'COMPUTO'  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '30.2','PRODUCTOS DE COMPUTO - IMPRESORAS - COPIADORAS',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'COMPUTO'  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '30.3','SERVICIOS DE COMPUTACION - IMPRESORAS - COPIADORAS',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'COMPUTO'  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '31.1','EQUIPO TELECOMUNICACIONES-COMUNICACIÓN',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'TELECOMUNICACIONES-COMUNICACIÓN'  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '31.2','SERVICIOS DE TELECOMUNICACIONES-COMUNICACIÓN',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'TELECOMUNICACIONES-COMUNICACIÓN'  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '32.1','EQUIPO DE TELEFONIA ',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'TELEFONIA'  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '32.2','SERVICIOS DE TELEFONIA',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'TELEFONIA'  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '33.1','EQUIPO DE REDES',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'REDES'  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '33.2','SERVICIOS DE REDES',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'REDES'  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '34.1','EQUIPO Y MATERIAL DE SEGURIDAD',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'SEGURIDAD '  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '34.2','PRENDAS DE SEGURIDAD',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'SEGURIDAD '  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '34.3','SERVICIOS DE SEGURIDAD',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'SEGURIDAD '  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '35.1','MATERIAL, ACCESORIOS Y EQUIPO MEDICO',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'MEDICO '  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '35.2','SERVICIOS PARA EQUIPO MEDICO',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'MEDICO '  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '36.1','HOSPITAL',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'SERVICOS DE SALUD'  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '36.2','EXAMENES MEDICOS Y TOXICOLOGICOS',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'SERVICOS DE SALUD'  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '36.3','LABORATORIO DE ANALISIS',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'SERVICOS DE SALUD'  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '36.4','SERVICIO MEDICO',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'SERVICOS DE SALUD'  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '36.5','SERVICIO PSIQUIATRICO',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'SERVICOS DE SALUD'  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '36.6','SERVICIO PSICOLOGICO',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'SERVICOS DE SALUD'  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '36.7','PRUEBAS PSICOLOGICAS',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'SERVICOS DE SALUD'  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '36.8','SERVICIO DE AMBULANCIA',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'SERVICOS DE SALUD'  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '36.9','REHABILITACION MEDICA',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'SERVICOS DE SALUD'  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '36.1','RADIOLOGIA',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'SERVICOS DE SALUD'  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '36.11','IMAGENOLOGIA',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'SERVICOS DE SALUD'  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '36.12','OPTICA',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'SERVICOS DE SALUD'  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '37.1','PAÑALES',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'CUIDADO PERSONAL'  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '37.2','TOALLAS SANITARIAS',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'CUIDADO PERSONAL'  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '37.3','SHAMPOO',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'CUIDADO PERSONAL'  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '37.4','JABON',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'CUIDADO PERSONAL'  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '37.5','DESODORANTE',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'CUIDADO PERSONAL'  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '37.6','PASTA DE DIENTES',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'CUIDADO PERSONAL'  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '37.8','ARTICULOS PERSONALES DE ASEO',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'CUIDADO PERSONAL'  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '38.1','MATERIAL DE LIMPIEZA ',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'LIMPIEZA '  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '38.2','EQUIPO DE LIMPIEZA ',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'LIMPIEZA '  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '38.3','SERVICIO DE LIMPIEZA ',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'LIMPIEZA '  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '39.1','MATERIAL DE SANITIZACION',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'SANITIZACION'  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '39.2','EQUIPO DE SANITIZACION',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'SANITIZACION'  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '39.3','SERVICIO DE SANITIZACION',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'SANITIZACION'  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '40.1','PRODUCTOS QUIMICOS',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'QUIMICOS - SUSTANCIAS '  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '40.2','PRODUCTOS MINERALES',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'QUIMICOS - SUSTANCIAS '  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '40.3','REACTIVOS',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'QUIMICOS - SUSTANCIAS '  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '40.4','CARBON Y SUS DERIVADOS',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'QUIMICOS - SUSTANCIAS '  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '40.5','GASES MEDICINALES',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'QUIMICOS - SUSTANCIAS '  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '41.1','SUSTANCIAS Y MATERIALES EXPLOSIVOS',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'EXPLOSIVOS'  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '41.2','FUEGOS ARTIFICIALES / JUEGOS PIROTECNICOS',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'EXPLOSIVOS'  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '42.1','MATERIAL Y EQUIPO DE FUMIGACION',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'FUMIGACION'  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '42.2','SERVICIOS DE FUMIGACION',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'FUMIGACION'  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '43.1','MATERIAL Y EQUIPO DE JARDINERIA ',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'JARDINERIA '  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '43.2','SERVICIOS DE JARDINERIA ',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'JARDINERIA '  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '44.1','MAQUINARIA Y EQUIPO INDUSTRIAL',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'INDUSTRIAL'  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '44.2','MANTENIMIENTO A MAQUINARIA Y EQUIPO INDUSTRIAL',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'INDUSTRIAL'  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '46.1','MATERIAL DE CONSTRUCCION',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'CONSTRUCCION'  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '45.2','EQUIPO DE CONSTRUCCION',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'CONSTRUCCION'  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '45.3','SERVICIOS DE CONSTRUCCION',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'CONSTRUCCION'  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '46.1','MATERIAL Y EQUIPO ELECTRICO ',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'ELECTRICO '  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '46.2','SERVICIOS ELECTRICOS',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'ELECTRICO '  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '47.1','EQUIPO ELECTRONICO',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'ELECTRONICOS'  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '47.2','MATERIAL ELECTRONICO',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'ELECTRONICOS'  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '47.3','SERVICIOS ELECTRONICOS',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'ELECTRONICOS'  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '48.1','EQUIPO DE ENERGIA',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'ENERGIA'  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '48.2','SERVICIOS DE ENERGIA',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'ENERGIA'  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '49.1','EQUIPOS DE AIRE ACONDICIONADO, CALEFACCION Y REFRIGERACION',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'AIRE ACONDICIONADO - CALEFACCION - REFRIGERACION'  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '49.2','SERVICIOS DE CLIMAS, CALEFACCION Y REFRIGERACION',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'AIRE ACONDICIONADO - CALEFACCION - REFRIGERACION'  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '50.1','HERRAMIENTAS',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'FERRETERIA - TLAPALERIA'  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '50.2','MATERIAL DE CARPINTERIA',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'FERRETERIA - TLAPALERIA'  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '50.3','MATERIAL DE ALBAÑILERIA',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'FERRETERIA - TLAPALERIA'  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '50.4','HERRERIA',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'FERRETERIA - TLAPALERIA'  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '50.5','TORINILLOS',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'FERRETERIA - TLAPALERIA'  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '50.6','ALAMBRE',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'FERRETERIA - TLAPALERIA'  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '50.7','CONECTORES CONEXIONES',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'FERRETERIA - TLAPALERIA'  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '50.8','VALVULAS',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'FERRETERIA - TLAPALERIA'  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '50.9','TUBERIA',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'FERRETERIA - TLAPALERIA'  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '50.10','RECUBRIMIENTOS',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'FERRETERIA - TLAPALERIA'  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '50.11','IMPERMEABILIZANTE',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'FERRETERIA - TLAPALERIA'  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '50.12','PINTURA',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'FERRETERIA - TLAPALERIA'  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '50.13','PINTURA DE TRAFICO, DECORATIVA E INDUSTRIAL',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'FERRETERIA - TLAPALERIA'  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '51.1','MATERIAL DE PLOMERIA',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'PLOMERIA '  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '51.2','SERVICIOS DE PLOMERIA',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'PLOMERIA '  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '52.1','ARRENDAMIENTO DE INMUEBLES',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'ARRENDAMIENTO'  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '53.1','SERVICIOS FUNERARIOS ',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'FUNERARIA'  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '53.2','CEMENTERIOS',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'FUNERARIA'  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '54.1','SEGURO DE BIENES PATRIMONIALES',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'SEGUROS '  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '54.2','SEGURO DE RESPONSABILIDAD PATRIMONIAL Y FIANZAS',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'SEGUROS '  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '54.3','SEGURO MEDICO',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'SEGUROS '  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '54.4','SEGURO DE VIDA',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'SEGUROS '  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '54.5','AFIANZADORA',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'SEGUROS '  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '55.1','EQUIPO BANCARIO',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'BANCARIO'  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '55.2','SERVICIOS BANCARIOS',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'BANCARIO'  ;
insert into productos_servicios.concepto_grupo(grupo,concepto,concepto_familia_id) select '56.1','OTRO PRODUCTO O SERVICIO NO INCLUIDO EN EL CATALOGO',concepto_familia_id from productos_servicios.concepto_familia where concepto = 'OTROS'  ;


insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'COMERCIO_AL_MAYOR  ' from productos_servicios.concepto_grupo where concepto = 'EQUIPO Y ACCESORIOS DE OFICINA';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'COMERCIO_AL_MENOR  ' from productos_servicios.concepto_grupo where concepto = 'EQUIPO Y ACCESORIOS DE OFICINA';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'INDUSTRIAS_MANUFACTURERAS ' from productos_servicios.concepto_grupo where concepto = 'EQUIPO Y ACCESORIOS DE OFICINA';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'SERVICIOS_PROFESIONALES_CIENTIFICOS_TECNICOS ' from productos_servicios.concepto_grupo where concepto = 'EQUIPO Y ACCESORIOS DE OFICINA';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'SERVICIOS_PROFESIONALES_CIENTIFICOS_TECNICOS ' from productos_servicios.concepto_grupo where concepto = 'SERVICIOS DE OFICINA';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'COMERCIO_AL_MAYOR ó COMERCIO_AL_MENOR  ' from productos_servicios.concepto_grupo where concepto = 'ARTICULOS DE PAPELERIA';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'COMERCIO_AL_MAYOR ó COMERCIO_AL_MENOR  ' from productos_servicios.concepto_grupo where concepto = 'HOJAS';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'COMERCIO_AL_MAYOR ó COMERCIO_AL_MENOR  ' from productos_servicios.concepto_grupo where concepto = 'CARPETAS';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'COMERCIO_AL_MAYOR ó COMERCIO_AL_MENOR ó INDUSTRIAS_MANUFACTURERAS  ' from productos_servicios.concepto_grupo where concepto = 'BOLIGRAFOS-LAPICES';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'COMERCIO_AL_MAYOR ó COMERCIO_AL_MENOR  ' from productos_servicios.concepto_grupo where concepto = 'LIBRETAS';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'COMERCIO_AL_MAYOR ó COMERCIO_AL_MENOR ó INDUSTRIAS_MANUFACTURERAS  ' from productos_servicios.concepto_grupo where concepto = 'ETIQUETAS';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'COMERCIO_AL_MAYOR ó COMERCIO_AL_MENOR ó INDUSTRIAS_MANUFACTURERAS  ' from productos_servicios.concepto_grupo where concepto = 'SELLOS';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'COMERCIO_AL_MAYOR ó COMERCIO_AL_MENOR ó INDUSTRIAS_MANUFACTURERAS  ' from productos_servicios.concepto_grupo where concepto = 'PRODUCTOS DE PAPEL O CARTON';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'COMERCIO_AL_MAYOR ó COMERCIO_AL_MENOR  ' from productos_servicios.concepto_grupo where concepto = 'PAQUETES ESCOLARES';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'COMERCIO_AL_MAYOR ó COMERCIO_AL_MENOR  ' from productos_servicios.concepto_grupo where concepto = 'MATERIAL Y UTILES DE ENSEÑANZA';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'COMERCIO_AL_MAYOR ó COMERCIO_AL_MENOR  ' from productos_servicios.concepto_grupo where concepto = 'MATERIAL ESTADISTICO Y GEOGRAFICO';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'COMERCIO_AL_MAYOR ó COMERCIO_AL_MENOR  ' from productos_servicios.concepto_grupo where concepto = 'MATERIAL DIDACTICO';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'COMERCIO_AL_MAYOR ó COMERCIO_AL_MENOR  ' from productos_servicios.concepto_grupo where concepto = 'BANDERAS';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'COMERCIO_AL_MAYOR ó COMERCIO_AL_MENOR  ' from productos_servicios.concepto_grupo where concepto = 'SIMBOLOS PATRIOS';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'COMERCIO_AL_MAYOR ó COMERCIO_AL_MENOR  ' from productos_servicios.concepto_grupo where concepto = 'LETRAS Y ESCUDO';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'COMERCIO_AL_MAYOR ó COMERCIO_AL_MENOR ó INDUSTRIAS_MANUFACTURERAS  ' from productos_servicios.concepto_grupo where concepto = 'IMPRENTA';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'COMERCIO_AL_MAYOR ó COMERCIO_AL_MENOR ó INDUSTRIAS_MANUFACTURERAS  ' from productos_servicios.concepto_grupo where concepto = 'FORMAS IMPRESAS';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'COMERCIO_AL_MAYOR ó COMERCIO_AL_MENOR ó INDUSTRIAS_MANUFACTURERAS  ' from productos_servicios.concepto_grupo where concepto = 'PAPELERIA MEMBRETADA';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'COMERCIO_AL_MAYOR ó COMERCIO_AL_MENOR ó INDUSTRIAS_MANUFACTURERAS  ' from productos_servicios.concepto_grupo where concepto = 'LIBROS, REVISTAS Y PERIODICOS';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'COMERCIO_AL_MAYOR ó COMERCIO_AL_MENOR ó INDUSTRIAS_MANUFACTURERAS  ' from productos_servicios.concepto_grupo where concepto = 'TRIPTICOS, BOLETAS, FOLLETOS, POSTERS';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'COMERCIO_AL_MAYOR ó COMERCIO_AL_MENOR  ' from productos_servicios.concepto_grupo where concepto = 'VALES - TARJETAS (DESPENSA, GASOLINA, COMIDA, ETC.)';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'COMERCIO_AL_MAYOR ó COMERCIO_AL_MENOR  ' from productos_servicios.concepto_grupo where concepto = 'GAFETES';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'COMERCIO_AL_MAYOR ó COMERCIO_AL_MENOR  ' from productos_servicios.concepto_grupo where concepto = 'PROMOCIONALES E IMPRESOS';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'COMERCIO_AL_MAYOR ó COMERCIO_AL_MENOR ó INDUSTRIAS_MANUFACTURERAS  ' from productos_servicios.concepto_grupo where concepto = 'BORDADOS IMPRESOS';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'COMERCIO_AL_MAYOR ó COMERCIO_AL_MENOR ó INDUSTRIAS_MANUFACTURERAS  ' from productos_servicios.concepto_grupo where concepto = 'ELABORACION DE LONAS, MANTAS, PENDONES, BANNERS';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'COMERCIO_AL_MAYOR ó COMERCIO_AL_MENOR  ' from productos_servicios.concepto_grupo where concepto = 'ROTULACION DE UNIDADES';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'COMERCIO_AL_MAYOR ó COMERCIO_AL_MENOR ó INDUSTRIAS_MANUFACTURERAS  ' from productos_servicios.concepto_grupo where concepto = 'RECONOCIMIENTOS, MEDALLAS, TROFEOS';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'COMERCIO_AL_MAYOR ó COMERCIO_AL_MENOR  ' from productos_servicios.concepto_grupo where concepto = 'AGENCIA DE PUBLICIDAD';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'COMERCIO_AL_MAYOR ó COMERCIO_AL_MENOR  ' from productos_servicios.concepto_grupo where concepto = 'ESPACIOS PUBLICITARIOS';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'SERVICIOS_PROFESIONALES_CIENTIFICOS_TECNICOS ' from productos_servicios.concepto_grupo where concepto = 'SERVICIOS DE PUBLICIDAD';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'SERVICIOS_PROFESIONALES_CIENTIFICOS_TECNICOS ' from productos_servicios.concepto_grupo where concepto = 'DISEÑO DE CAMPAÑAS PUBLICITARIAS';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'SERVICIOS_PROFESIONALES_CIENTIFICOS_TECNICOS ' from productos_servicios.concepto_grupo where concepto = 'DISEÑO DE REDES SOCIALES';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'SERVICIOS_PROFESIONALES_CIENTIFICOS_TECNICOS ' from productos_servicios.concepto_grupo where concepto = 'DISEÑO GRAFICO';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'INFORMACIÓN_MEDIOS_MASIVOS ' from productos_servicios.concepto_grupo where concepto = 'ENTREGA DE PERIODICOS Y REVISTAS';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'SERV_ESPARCIMIENTO_CULTURALES_DEPORTIVOS_O ' from productos_servicios.concepto_grupo where concepto = 'MATERIAL ARTISTICO';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'SERVICIOS_PROFESIONALES_CIENTIFICOS_TECNICOS ' from productos_servicios.concepto_grupo where concepto = 'MATERIAL ARTISTICO';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'SERV_ESPARCIMIENTO_CULTURALES_DEPORTIVOS_O ' from productos_servicios.concepto_grupo where concepto = 'SERVICIOS ARTISTICOS';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'SERVICIOS_PROFESIONALES_CIENTIFICOS_TECNICOS ' from productos_servicios.concepto_grupo where concepto = 'SERVICIOS ARTISTICOS';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'COMERCIO_AL_MAYOR ó COMERCIO_AL_MENOR  ' from productos_servicios.concepto_grupo where concepto = 'ARTICULOS MUSICALES';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'SERV_ESPARCIMIENTO_CULTURALES_DEPORTIVOS_O ' from productos_servicios.concepto_grupo where concepto = 'ESPARCIMIENTO';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'COMERCIO_AL_MAYOR ó COMERCIO_AL_MENOR  ' from productos_servicios.concepto_grupo where concepto = 'ARTICULOS DEPORTIVOS';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'SERVICIOS_PROFESIONALES_CIENTIFICOS_TECNICOS ' from productos_servicios.concepto_grupo where concepto = 'SERVICIOS DEPORTIVOS';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'COMERCIO_AL_MAYOR ó COMERCIO_AL_MENOR  ' from productos_servicios.concepto_grupo where concepto = 'JUGUETES';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'COMERCIO_AL_MAYOR ó COMERCIO_AL_MENOR  ' from productos_servicios.concepto_grupo where concepto = 'PELOTAS';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'COMERCIO_AL_MAYOR ó COMERCIO_AL_MENOR  ' from productos_servicios.concepto_grupo where concepto = 'BICICLETAS, TRICICLOS';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'COMERCIO_AL_MAYOR ó COMERCIO_AL_MENOR  ' from productos_servicios.concepto_grupo where concepto = 'JUEGOS INFANTILES';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'COMERCIO_AL_MAYOR ó COMERCIO_AL_MENOR  ' from productos_servicios.concepto_grupo where concepto = 'EQUIPAMIENTO DE PARQUES';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'COMERCIO_AL_MAYOR ó COMERCIO_AL_MENOR  ' from productos_servicios.concepto_grupo where concepto = 'RELOJES';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'COMERCIO_AL_MAYOR ó COMERCIO_AL_MENOR  ' from productos_servicios.concepto_grupo where concepto = 'ANILLOS';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'COMERCIO_AL_MAYOR ó COMERCIO_AL_MENOR  ' from productos_servicios.concepto_grupo where concepto = 'PULSERAS';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'COMERCIO_AL_MAYOR ó COMERCIO_AL_MENOR  ' from productos_servicios.concepto_grupo where concepto = 'COLLARES';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'COMERCIO_AL_MAYOR ó COMERCIO_AL_MENOR  ' from productos_servicios.concepto_grupo where concepto = 'BISUTERIA';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'COMERCIO_AL_MAYOR ó COMERCIO_AL_MENOR  ' from productos_servicios.concepto_grupo where concepto = 'ARTICULOS DE BELLEZA';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'COMERCIO_AL_MAYOR ó COMERCIO_AL_MENOR  ' from productos_servicios.concepto_grupo where concepto = 'EQUIPO DE BELLEZA';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'COMERCIO_AL_MAYOR ó COMERCIO_AL_MENOR  ' from productos_servicios.concepto_grupo where concepto = 'SERVICIOS DE BELLEZA';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'COMERCIO_AL_MAYOR ó COMERCIO_AL_MENOR  ' from productos_servicios.concepto_grupo where concepto = 'BLANCOS';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'INDUSTRIAS_MANUFACTURERAS ' from productos_servicios.concepto_grupo where concepto = 'BLANCOS';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'COMERCIO_AL_MAYOR ó COMERCIO_AL_MENOR  ' from productos_servicios.concepto_grupo where concepto = 'MATERIAL TEXTIL';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'INDUSTRIAS_MANUFACTURERAS ' from productos_servicios.concepto_grupo where concepto = 'MATERIAL TEXTIL';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'COMERCIO_AL_MAYOR ó COMERCIO_AL_MENOR  ' from productos_servicios.concepto_grupo where concepto = 'ROPA - UNIFORMES';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'INDUSTRIAS_MANUFACTURERAS ' from productos_servicios.concepto_grupo where concepto = 'ROPA - UNIFORMES';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'COMERCIO_AL_MAYOR ó COMERCIO_AL_MENOR  ' from productos_servicios.concepto_grupo where concepto = 'CALZADO';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'INDUSTRIAS_MANUFACTURERAS ' from productos_servicios.concepto_grupo where concepto = 'CALZADO';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'SERVICIOS_PROFESIONALES_CIENTIFICOS_TECNICOS ' from productos_servicios.concepto_grupo where concepto = 'SERVICIOS TEXTILES';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'COMERCIO_AL_MAYOR ó COMERCIO_AL_MENOR  ' from productos_servicios.concepto_grupo where concepto = 'SERVICIOS TEXTILES';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'INDUSTRIAS_MANUFACTURERAS ' from productos_servicios.concepto_grupo where concepto = 'SERVICIOS TEXTILES';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'COMERCIO_AL_MAYOR ó COMERCIO_AL_MENOR  ' from productos_servicios.concepto_grupo where concepto = 'PRODUCTOS DE PLASTICO';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'COMERCIO_AL_MAYOR ó COMERCIO_AL_MENOR  ' from productos_servicios.concepto_grupo where concepto = 'CUBETAS ';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'COMERCIO_AL_MAYOR ó COMERCIO_AL_MENOR  ' from productos_servicios.concepto_grupo where concepto = 'HULES, PLASTICOS Y DERIVADOS';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'COMERCIO_AL_MAYOR ó COMERCIO_AL_MENOR  ' from productos_servicios.concepto_grupo where concepto = 'BOLSAS';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'COMERCIO_AL_MAYOR ó COMERCIO_AL_MENOR  ' from productos_servicios.concepto_grupo where concepto = 'MATERIALES RECICLABLES';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'SERVICIOS_PROFESIONALES_CIENTIFICOS_TECNICOS ' from productos_servicios.concepto_grupo where concepto = 'ASESORIA Y CONSULTORIA ';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'SERVICIOS_PROFESIONALES_CIENTIFICOS_TECNICOS ' from productos_servicios.concepto_grupo where concepto = 'CONFERENCIAS';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'SERVICIOS_PROFESIONALES_CIENTIFICOS_TECNICOS ' from productos_servicios.concepto_grupo where concepto = 'CURSOS';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'SERVICIOS_PROFESIONALES_CIENTIFICOS_TECNICOS ' from productos_servicios.concepto_grupo where concepto = 'SERVICIO DE CAPACITACION';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'SERVICIOS_PROFESIONALES_CIENTIFICOS_TECNICOS ' from productos_servicios.concepto_grupo where concepto = 'ELABORACION DE MATERIAL INFORMATIVO';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'SERVICIOS_PROFESIONALES_CIENTIFICOS_TECNICOS ' from productos_servicios.concepto_grupo where concepto = 'ESTUDIOS DE FACTIBILIDAD';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'SERVICIOS_PROFESIONALES_CIENTIFICOS_TECNICOS ' from productos_servicios.concepto_grupo where concepto = 'SERVICIO DE COBRANZA';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'SERVICIOS_PROFESIONALES_CIENTIFICOS_TECNICOS ' from productos_servicios.concepto_grupo where concepto = 'SERVICIO DE TRADUCCION';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'SERVICIOS_PROFESIONALES_CIENTIFICOS_TECNICOS ' from productos_servicios.concepto_grupo where concepto = 'SERVICIOS LEGALES';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'SERVICIOS_PROFESIONALES_CIENTIFICOS_TECNICOS ' from productos_servicios.concepto_grupo where concepto = 'SERVICIOS DE CONTABILIDAD Y AUDITORIA';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'SERVICIOS_PROFESIONALES_CIENTIFICOS_TECNICOS ' from productos_servicios.concepto_grupo where concepto = 'SERVICIOS NOTARIALES';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'SERVICIOS_PROFESIONALES_CIENTIFICOS_TECNICOS ' from productos_servicios.concepto_grupo where concepto = 'SERVICIOS DE AVALUOS';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'SERVICIOS_PROFESIONALES_CIENTIFICOS_TECNICOS ' from productos_servicios.concepto_grupo where concepto = 'INVESTIGACION DE MERCADO';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'SERVICIOS_PROFESIONALES_CIENTIFICOS_TECNICOS ' from productos_servicios.concepto_grupo where concepto = 'ENCUESTAS DE MERCADO';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'SERVICIOS_PROFESIONALES_CIENTIFICOS_TECNICOS ' from productos_servicios.concepto_grupo where concepto = 'MEDICION DE RATINGS';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'SERVICIOS_PROFESIONALES_CIENTIFICOS_TECNICOS ' from productos_servicios.concepto_grupo where concepto = 'SERVICIOS RELACIONADOS CON MONITOREO DE INFORMACION EN MEDIOS MASIVOS';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'SERVICIOS_PROFESIONALES_CIENTIFICOS_TECNICOS ' from productos_servicios.concepto_grupo where concepto = 'SUBASTAS';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'SERVICIOS_PROFESIONALES_CIENTIFICOS_TECNICOS ' from productos_servicios.concepto_grupo where concepto = 'AGENCIA DE COLOCACION Y SELECCION DE PERSONAL';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'SERVICIOS_PROFESIONALES_CIENTIFICOS_TECNICOS ' from productos_servicios.concepto_grupo where concepto = 'SERVICIOS DE ASISTENCIA SOCIAL';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'SERVICIOS_PROFESIONALES_CIENTIFICOS_TECNICOS ' from productos_servicios.concepto_grupo where concepto = 'DESARROLLO PERSONAL';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'SERVICIOS_PROFESIONALES_CIENTIFICOS_TECNICOS ' from productos_servicios.concepto_grupo where concepto = 'BECAS';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'SERVICIOS_PROFESIONALES_CIENTIFICOS_TECNICOS ' from productos_servicios.concepto_grupo where concepto = 'FACTURACION ELECTRONICA';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'SERVICIOS_PROFESIONALES_CIENTIFICOS_TECNICOS ' from productos_servicios.concepto_grupo where concepto = 'COMPROBANTES DIGITALES';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'SERVICIOS_PROFESIONALES_CIENTIFICOS_TECNICOS ' from productos_servicios.concepto_grupo where concepto = 'SERVICIO DE LOGISTICA';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'SERVICIOS_PROFESIONALES_CIENTIFICOS_TECNICOS ' from productos_servicios.concepto_grupo where concepto = 'AGENCIA ADUANAL';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'SERVICIOS_PROFESIONALES_CIENTIFICOS_TECNICOS ' from productos_servicios.concepto_grupo where concepto = 'ESTUDIOS DE CALIDAD';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'SERVICIOS_PROFESIONALES_CIENTIFICOS_TECNICOS ' from productos_servicios.concepto_grupo where concepto = 'ESTUDIOS AMBIENTALES';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'SERVICIOS_PROFESIONALES_CIENTIFICOS_TECNICOS ' from productos_servicios.concepto_grupo where concepto = 'ESTUDIOS DE SUELO';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'SERVICIOS_PROFESIONALES_CIENTIFICOS_TECNICOS ' from productos_servicios.concepto_grupo where concepto = 'CERTIFICACION ISO';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'SERV_ESPARCIMIENTO_CULTURALES_DEPORTIVOS_O ó SERVICIOS_PROFESIONALES_CIENTIFICOS_TECNICOS ó                                     SERV_ALOJAMIENTO_TEMP_PREP_ALIM_BEBI ' from productos_servicios.concepto_grupo where concepto = 'ORGANIZACION DE EVENTOS';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'COMERCIO_AL_MAYOR ó COMERCIO_AL_MENOR  ' from productos_servicios.concepto_grupo where concepto = 'ARREGLOS FLORALES';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'SERV_INMOB_ALQUILER_BIENES_MUEBLES_E_INMUEBLES ' from productos_servicios.concepto_grupo where concepto = 'RENTA DE SALONES, TEATROS, AUDITORIOS';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'SERVICIOS_PROFESIONALES_CIENTIFICOS_TECNICOS ' from productos_servicios.concepto_grupo where concepto = 'RENTA DE EQUIPO DE SONIDO, AUDIO, PROYECCION';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'SERVICIOS_PROFESIONALES_CIENTIFICOS_TECNICOS ' from productos_servicios.concepto_grupo where concepto = 'RENTA DE TOLDOS, LONAS, CARPAS';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'SERVICIOS_PROFESIONALES_CIENTIFICOS_TECNICOS ' from productos_servicios.concepto_grupo where concepto = 'RENTA DE TARIMAS, MAMPARAS';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'SERVICIOS_PROFESIONALES_CIENTIFICOS_TECNICOS ' from productos_servicios.concepto_grupo where concepto = 'RENTA DE MODULOS, STANDS';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'COMERCIO_AL_MAYOR ó COMERCIO_AL_MENOR  ' from productos_servicios.concepto_grupo where concepto = 'RENTA DE CABINAS FOTOGRAFICAS';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'SERVICIOS_PROFESIONALES_CIENTIFICOS_TECNICOS ' from productos_servicios.concepto_grupo where concepto = 'DECORACION';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'SERVICIOS_PROFESIONALES_CIENTIFICOS_TECNICOS ' from productos_servicios.concepto_grupo where concepto = 'RENTA DE EQUIPO PARA SALON DE EVENTOS';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'SERVICIOS_PROFESIONALES_CIENTIFICOS_TECNICOS ' from productos_servicios.concepto_grupo where concepto = 'EXPOSICIONES';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'SERVICIOS_PROFESIONALES_CIENTIFICOS_TECNICOS ' from productos_servicios.concepto_grupo where concepto = 'MATERIAL ESCENOGRAFICO';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'SERVICIOS_PROFESIONALES_CIENTIFICOS_TECNICOS ' from productos_servicios.concepto_grupo where concepto = 'MESEROS';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'SERVICIOS_PROFESIONALES_CIENTIFICOS_TECNICOS ' from productos_servicios.concepto_grupo where concepto = 'RENTA DE MESAS, SILLAS, MANTELES, CUCHILLERIA';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'SERVICIOS_PROFESIONALES_CIENTIFICOS_TECNICOS ' from productos_servicios.concepto_grupo where concepto = 'RENTA DE PANTALLAS';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'SERVICIOS_PROFESIONALES_CIENTIFICOS_TECNICOS ' from productos_servicios.concepto_grupo where concepto = 'RENTA DE PLANTAS DE LUZ';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'SERVICIOS_PROFESIONALES_CIENTIFICOS_TECNICOS ' from productos_servicios.concepto_grupo where concepto = 'RENTA DE PLANTAS DE ENERGIA';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'SERVICIOS_PROFESIONALES_CIENTIFICOS_TECNICOS ' from productos_servicios.concepto_grupo where concepto = 'RENTA DE SANITARIOS PORTATILES';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'SERVICIOS_PROFESIONALES_CIENTIFICOS_TECNICOS ' from productos_servicios.concepto_grupo where concepto = 'RENTA DE UNIFILAS, VALLAS';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'SERVICIOS_PROFESIONALES_CIENTIFICOS_TECNICOS ' from productos_servicios.concepto_grupo where concepto = 'RENTA DE GRADAS';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'SERVICIOS_PROFESIONALES_CIENTIFICOS_TECNICOS ' from productos_servicios.concepto_grupo where concepto = 'RENTA DE SALAS TIPO LOUNGE';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'SERV_ALOJAMIENTO_TEMP_PREP_ALIM_BEBI ' from productos_servicios.concepto_grupo where concepto = 'SERVICIOS DE BANQUETES';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'SERV_ALOJAMIENTO_TEMP_PREP_ALIM_BEBI ' from productos_servicios.concepto_grupo where concepto = 'SERVICIO DE COFFEE BREAK';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'SERVICIOS_PROFESIONALES_CIENTIFICOS_TECNICOS ' from productos_servicios.concepto_grupo where concepto = 'SERVICIOS DE AMBIENTACION EN EVENTOS';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'SERVICIOS_PROFESIONALES_CIENTIFICOS_TECNICOS ' from productos_servicios.concepto_grupo where concepto = 'RENTA DE ARCOS DETECTORES DE METALES';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'SERVICIOS_PROFESIONALES_CIENTIFICOS_TECNICOS ' from productos_servicios.concepto_grupo where concepto = 'ADMINISTRACION Y OPERACION DE ESTACIONAMIENTOS';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'SERVICIOS_PROFESIONALES_CIENTIFICOS_TECNICOS ' from productos_servicios.concepto_grupo where concepto = 'ILUMINACION DECORATIVA';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'SERVICIOS_PROFESIONALES_CIENTIFICOS_TECNICOS ' from productos_servicios.concepto_grupo where concepto = 'EDECANES';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'SERVICIOS_PROFESIONALES_CIENTIFICOS_TECNICOS ' from productos_servicios.concepto_grupo where concepto = 'PIPAS DE AGUA';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'SERVICIOS_PROFESIONALES_CIENTIFICOS_TECNICOS ' from productos_servicios.concepto_grupo where concepto = 'ACTIVIDADES CULTURALES Y DEPORTIVAS';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'SERVICIOS_PROFESIONALES_CIENTIFICOS_TECNICOS ' from productos_servicios.concepto_grupo where concepto = 'SERVICIOS RECREATIVOS';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'COMERCIO_AL_MAYOR ó COMERCIO_AL_MENOR  ' from productos_servicios.concepto_grupo where concepto = 'EQUIPO Y MATERIAL DE AUDIO';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'SERVICIOS_PROFESIONALES_CIENTIFICOS_TECNICOS ' from productos_servicios.concepto_grupo where concepto = 'SERVICIOS DE AUDIO';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'COMERCIO_AL_MAYOR ó COMERCIO_AL_MENOR  ' from productos_servicios.concepto_grupo where concepto = 'EQUIPO Y MATERIAL DE VIDEO Y FOTOGRAFIA';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'SERVICIOS_PROFESIONALES_CIENTIFICOS_TECNICOS ' from productos_servicios.concepto_grupo where concepto = 'SERVICIOS DE VIDEO Y FOTOGRAFIA';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'COMERCIO_AL_MAYOR ó COMERCIO_AL_MENOR  ' from productos_servicios.concepto_grupo where concepto = 'SERVICIOS DE VIDEO Y FOTOGRAFIA';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'INFORMACIÓN_MEDIOS_MASIVOS ' from productos_servicios.concepto_grupo where concepto = 'SERVICIOS DE VIDEO Y FOTOGRAFIA';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'SERVICIOS_PROFESIONALES_CIENTIFICOS_TECNICOS ' from productos_servicios.concepto_grupo where concepto = 'PRODUCTOS ALIMENTICIOS';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'COMERCIO_AL_MAYOR ' from productos_servicios.concepto_grupo where concepto = 'PRODUCTOS ALIMENTICIOS';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'COMERCIO_AL_MENOR  ' from productos_servicios.concepto_grupo where concepto = 'PRODUCTOS ALIMENTICIOS';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'SERV_ALOJAMIENTO_TEMP_PREP_ALIM_BEBI ' from productos_servicios.concepto_grupo where concepto = 'PRODUCTOS ALIMENTICIOS';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'SERV_ALOJAMIENTO_TEMP_PREP_ALIM_BEBI ' from productos_servicios.concepto_grupo where concepto = 'SERVICIO DE ALIMENTOS';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'SERVICIOS_PROFESIONALES_CIENTIFICOS_TECNICOS ' from productos_servicios.concepto_grupo where concepto = 'SERVICIO DE ALIMENTOS';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'COMERCIO_AL_MAYOR ' from productos_servicios.concepto_grupo where concepto = 'SERVICIO DE ALIMENTOS';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'COMERCIO_AL_MAYOR ' from productos_servicios.concepto_grupo where concepto = 'BEBIDAS';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'COMERCIO_AL_MENOR  ' from productos_servicios.concepto_grupo where concepto = 'BEBIDAS';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'AGRICULTURA_CRIA_EXPLOTACION_ANIMALES_A_PROV ' from productos_servicios.concepto_grupo where concepto = 'ANIMALES';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'COMERCIO_AL_MAYOR ' from productos_servicios.concepto_grupo where concepto = 'ANIMALES';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'COMERCIO_AL_MENOR  ' from productos_servicios.concepto_grupo where concepto = 'ANIMALES';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'COMERCIO_AL_MAYOR ' from productos_servicios.concepto_grupo where concepto = 'PRODUCTOS PARA ANIMALES';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'COMERCIO_AL_MENOR  ' from productos_servicios.concepto_grupo where concepto = 'PRODUCTOS PARA ANIMALES';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'SERVICIOS_PROFESIONALES_CIENTIFICOS_TECNICOS ' from productos_servicios.concepto_grupo where concepto = 'SERVICIOS PARA ANIMALES';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'SERVICIOS_PROFESIONALES_CIENTIFICOS_TECNICOS ' from productos_servicios.concepto_grupo where concepto = 'SERVICIO PARA MOBILIARIO';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'COMERCIO_AL_MAYOR ó COMERCIO_AL_MENOR  ' from productos_servicios.concepto_grupo where concepto = 'ARTICULOS PARA EL HOGAR';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'COMERCIO_AL_MAYOR ó COMERCIO_AL_MENOR  ' from productos_servicios.concepto_grupo where concepto = 'LINEA BLANCA';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'COMERCIO_AL_MAYOR ó COMERCIO_AL_MENOR  ' from productos_servicios.concepto_grupo where concepto = 'PERSIANAS';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'COMERCIO_AL_MAYOR ó COMERCIO_AL_MENOR  ' from productos_servicios.concepto_grupo where concepto = 'ABANICOS';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'COMERCIO_AL_MAYOR ó COMERCIO_AL_MENOR  ' from productos_servicios.concepto_grupo where concepto = 'CAMAS, CATRES, COLCHONES';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'COMERCIO_AL_MAYOR ó COMERCIO_AL_MENOR  ' from productos_servicios.concepto_grupo where concepto = 'ARTICULOS DE COCINA';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'COMERCIO_AL_MAYOR ó COMERCIO_AL_MENOR  ' from productos_servicios.concepto_grupo where concepto = 'UTENSILIOS DE COCINA';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'COMERCIO_AL_MAYOR ó COMERCIO_AL_MENOR  ' from productos_servicios.concepto_grupo where concepto = 'VAJILLAS';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'COMERCIO_AL_MAYOR ó COMERCIO_AL_MENOR  ' from productos_servicios.concepto_grupo where concepto = 'CUBIERTOS';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'COMERCIO_AL_MAYOR ó COMERCIO_AL_MENOR  ' from productos_servicios.concepto_grupo where concepto = 'VASOS';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'COMERCIO_AL_MAYOR ó COMERCIO_AL_MENOR  ' from productos_servicios.concepto_grupo where concepto = 'PLATOS';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'COMERCIO_AL_MAYOR ó COMERCIO_AL_MENOR  ' from productos_servicios.concepto_grupo where concepto = 'DESHECHABLES';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'COMERCIO_AL_MAYOR ó COMERCIO_AL_MENOR  ' from productos_servicios.concepto_grupo where concepto = 'ELECTRODOMESTICOS';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'COMERCIO_AL_MAYOR ó COMERCIO_AL_MENOR  ' from productos_servicios.concepto_grupo where concepto = 'PASAJES AEREOS';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'COMERCIO_AL_MAYOR ó COMERCIO_AL_MENOR  ' from productos_servicios.concepto_grupo where concepto = 'PASAJES TERRESTRES';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'TRANSPORTES_CORREOS_ALMACENAMIENTO ' from productos_servicios.concepto_grupo where concepto = 'SERVICIOS DE TRASLADO';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'SERV_ALOJAMIENTO_TEMP_PREP_ALIM_BEBI ' from productos_servicios.concepto_grupo where concepto = 'SERVICIO DE HOSPEDAJE';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'SERVICIOS_PROFESIONALES_CIENTIFICOS_TECNICOS ' from productos_servicios.concepto_grupo where concepto = 'AGENCIA DE VIAJES - ORGANIZACION DE PAQUETES TURISTICOS';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'SERV_ALOJAMIENTO_TEMP_PREP_ALIM_BEBI ' from productos_servicios.concepto_grupo where concepto = 'HOTEL';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'COMERCIO_AL_MAYOR ó COMERCIO_AL_MENOR  ' from productos_servicios.concepto_grupo where concepto = 'VEHICULOS TERRESTRES';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'INDUSTRIAS_MANUFACTURERAS ' from productos_servicios.concepto_grupo where concepto = 'VEHICULOS TERRESTRES';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'COMERCIO_AL_MAYOR ó COMERCIO_AL_MENOR  ' from productos_servicios.concepto_grupo where concepto = 'ACCESORIOS, REFACCIONES Y EQUIPO PARA VEHICULOS TERRESTRES';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'SERVICIOS_PROFESIONALES_CIENTIFICOS_TECNICOS ' from productos_servicios.concepto_grupo where concepto = 'SERVICIOS DE VEHICULOS TERRESTRES';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'TRANSPORTES_CORREOS_ALMACENAMIENTO ' from productos_servicios.concepto_grupo where concepto = 'VEHICULOS AEREOS';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'COMERCIO_AL_MAYOR ó COMERCIO_AL_MENOR  ' from productos_servicios.concepto_grupo where concepto = 'ACCESORIOS, REFACCIONES Y EQUIPO PARA VEHICULOS AEREOS';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'SERVICIOS_PROFESIONALES_CIENTIFICOS_TECNICOS ' from productos_servicios.concepto_grupo where concepto = 'SERVICIOS DE VEHICULOS AEREOS';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'TRANSPORTES_CORREOS_ALMACENAMIENTO ' from productos_servicios.concepto_grupo where concepto = 'VEHICULOS MARITIMOS';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'COMERCIO_AL_MAYOR ó COMERCIO_AL_MENOR  ' from productos_servicios.concepto_grupo where concepto = 'ACCESORIOS, REFACCIONES Y EQUIPO PARA VEHICULOS MARITIMOS';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'SERVICIOS_PROFESIONALES_CIENTIFICOS_TECNICOS ' from productos_servicios.concepto_grupo where concepto = 'SERVICIOS DE VEHICULOS MARITIMOS';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'TRANSPORTES_CORREOS_ALMACENAMIENTO ' from productos_servicios.concepto_grupo where concepto = 'FLETES Y MANIOBRAS';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'TRANSPORTES_CORREOS_ALMACENAMIENTO ' from productos_servicios.concepto_grupo where concepto = 'MUDANZAS';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'TRANSPORTES_CORREOS_ALMACENAMIENTO ' from productos_servicios.concepto_grupo where concepto = 'MENSAJERIA';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'SERVICIOS_PROFESIONALES_CIENTIFICOS_TECNICOS ' from productos_servicios.concepto_grupo where concepto = 'DESMONTAJE';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'SERVICIOS_PROFESIONALES_CIENTIFICOS_TECNICOS ' from productos_servicios.concepto_grupo where concepto = 'EMBALAJE';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'TRANSPORTES_CORREOS_ALMACENAMIENTO ' from productos_servicios.concepto_grupo where concepto = 'DESMONTAJE, EMBALAJE, TRASLADO E INSTALACION';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'TRANSPORTES_CORREOS_ALMACENAMIENTO ' from productos_servicios.concepto_grupo where concepto = 'ALMACENAJE, ENVASE Y EMBALAJE';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'TRANSPORTES_CORREOS_ALMACENAMIENTO ' from productos_servicios.concepto_grupo where concepto = 'SERVICIOS POSTALES ';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'SERVICIOS_PROFESIONALES_CIENTIFICOS_TECNICOS ' from productos_servicios.concepto_grupo where concepto = 'TRANSPORTE DE PERSONAL';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'COMERCIO_AL_MAYOR ó COMERCIO_AL_MENOR  ' from productos_servicios.concepto_grupo where concepto = 'PRODUCTOS DE COMBUSTIBLES';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'COMERCIO_AL_MAYOR ó COMERCIO_AL_MENOR  ' from productos_servicios.concepto_grupo where concepto = 'SERVICIOS DE COMBUSTIBLES';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'SERVICIOS_PROFESIONALES_CIENTIFICOS_TECNICOS ' from productos_servicios.concepto_grupo where concepto = 'SERVICIOS DE COMBUSTIBLES';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'COMERCIO_AL_MAYOR ó COMERCIO_AL_MENOR  ' from productos_servicios.concepto_grupo where concepto = 'PRODUCTOS LUBRICANTES';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'COMERCIO_AL_MAYOR ó COMERCIO_AL_MENOR  ' from productos_servicios.concepto_grupo where concepto = 'EQUIPO DE COMPUTO - IMPRESORAS - COPIADORAS';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'COMERCIO_AL_MAYOR  ' from productos_servicios.concepto_grupo where concepto = 'PRODUCTOS DE COMPUTO - IMPRESORAS - COPIADORAS';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'COMERCIO_AL_MENOR  ' from productos_servicios.concepto_grupo where concepto = 'PRODUCTOS DE COMPUTO - IMPRESORAS - COPIADORAS';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'INDUSTRIAS_MANUFACTURERAS ' from productos_servicios.concepto_grupo where concepto = 'PRODUCTOS DE COMPUTO - IMPRESORAS - COPIADORAS';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'SERVICIOS_PROFESIONALES_CIENTIFICOS_TECNICOS ' from productos_servicios.concepto_grupo where concepto = 'SERVICIOS DE COMPUTACION - IMPRESORAS - COPIADORAS';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'COMERCIO_AL_MAYOR  ' from productos_servicios.concepto_grupo where concepto = 'EQUIPO TELECOMUNICACIONES-COMUNICACIÓN';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, ' COMERCIO_AL_MENOR  ' from productos_servicios.concepto_grupo where concepto = 'EQUIPO TELECOMUNICACIONES-COMUNICACIÓN';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'INDUSTRIAS_MANUFACTURERAS ' from productos_servicios.concepto_grupo where concepto = 'EQUIPO TELECOMUNICACIONES-COMUNICACIÓN';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'SERVICIOS_PROFESIONALES_CIENTIFICOS_TECNICOS ' from productos_servicios.concepto_grupo where concepto = 'SERVICIOS DE TELECOMUNICACIONES-COMUNICACIÓN';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'INFORMACIÓN_MEDIOS_MASIVOS ' from productos_servicios.concepto_grupo where concepto = 'SERVICIOS DE TELECOMUNICACIONES-COMUNICACIÓN';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'COMERCIO_AL_MAYOR  ' from productos_servicios.concepto_grupo where concepto = 'EQUIPO DE TELEFONIA ';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, ' COMERCIO_AL_MENOR  ' from productos_servicios.concepto_grupo where concepto = 'EQUIPO DE TELEFONIA ';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'SERVICIOS_PROFESIONALES_CIENTIFICOS_TECNICOS ' from productos_servicios.concepto_grupo where concepto = 'SERVICIOS DE TELEFONIA';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'INFORMACIÓN_MEDIOS_MASIVOS ' from productos_servicios.concepto_grupo where concepto = 'SERVICIOS DE TELEFONIA';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'COMERCIO_AL_MAYOR  ' from productos_servicios.concepto_grupo where concepto = 'EQUIPO DE REDES';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, ' COMERCIO_AL_MENOR  ' from productos_servicios.concepto_grupo where concepto = 'EQUIPO DE REDES';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'SERVICIOS_PROFESIONALES_CIENTIFICOS_TECNICOS ' from productos_servicios.concepto_grupo where concepto = 'SERVICIOS DE REDES';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'INFORMACIÓN_MEDIOS_MASIVOS ' from productos_servicios.concepto_grupo where concepto = 'SERVICIOS DE REDES';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'COMERCIO_AL_MAYOR  ' from productos_servicios.concepto_grupo where concepto = 'EQUIPO Y MATERIAL DE SEGURIDAD';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'COMERCIO_AL_MENOR ' from productos_servicios.concepto_grupo where concepto = 'EQUIPO Y MATERIAL DE SEGURIDAD';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'COMERCIO_AL_MAYOR  ' from productos_servicios.concepto_grupo where concepto = 'PRENDAS DE SEGURIDAD';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'COMERCIO_AL_MENOR ' from productos_servicios.concepto_grupo where concepto = 'PRENDAS DE SEGURIDAD';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'SERVICIOS_PROFESIONALES_CIENTIFICOS_TECNICOS ' from productos_servicios.concepto_grupo where concepto = 'SERVICIOS DE SEGURIDAD';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'SERVICIOS_SALUD_ASISTENCIA_SOCIAL ' from productos_servicios.concepto_grupo where concepto = 'MATERIAL, ACCESORIOS Y EQUIPO MEDICO';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'COMERCIO_AL_MAYOR  ' from productos_servicios.concepto_grupo where concepto = 'MATERIAL, ACCESORIOS Y EQUIPO MEDICO';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'COMERCIO_AL_MENOR  ' from productos_servicios.concepto_grupo where concepto = 'MATERIAL, ACCESORIOS Y EQUIPO MEDICO';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'INDUSTRIAS_MANUFACTURERAS ' from productos_servicios.concepto_grupo where concepto = 'MATERIAL, ACCESORIOS Y EQUIPO MEDICO';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'COMERCIO_AL_MAYOR ó COMERCIO_AL_MENOR, SERVICIOS_SALUD_ASISTENCIA_SOCIAL ' from productos_servicios.concepto_grupo where concepto = 'SERVICIOS PARA EQUIPO MEDICO';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'SERVICIOS_SALUD_ASISTENCIA_SOCIAL ' from productos_servicios.concepto_grupo where concepto = 'HOSPITAL';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'SERVICIOS_SALUD_ASISTENCIA_SOCIAL ' from productos_servicios.concepto_grupo where concepto = 'EXAMENES MEDICOS Y TOXICOLOGICOS';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'SERVICIOS_SALUD_ASISTENCIA_SOCIAL ' from productos_servicios.concepto_grupo where concepto = 'LABORATORIO DE ANALISIS';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'SERVICIOS_SALUD_ASISTENCIA_SOCIAL ' from productos_servicios.concepto_grupo where concepto = 'SERVICIO MEDICO';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'SERVICIOS_SALUD_ASISTENCIA_SOCIAL ' from productos_servicios.concepto_grupo where concepto = 'SERVICIO PSIQUIATRICO';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'SERVICIOS_SALUD_ASISTENCIA_SOCIAL ' from productos_servicios.concepto_grupo where concepto = 'SERVICIO PSICOLOGICO';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'SERVICIOS_SALUD_ASISTENCIA_SOCIAL ' from productos_servicios.concepto_grupo where concepto = 'PRUEBAS PSICOLOGICAS';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'SERVICIOS_SALUD_ASISTENCIA_SOCIAL ' from productos_servicios.concepto_grupo where concepto = 'SERVICIO DE AMBULANCIA';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'SERVICIOS_SALUD_ASISTENCIA_SOCIAL ' from productos_servicios.concepto_grupo where concepto = 'REHABILITACION MEDICA';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'SERVICIOS_SALUD_ASISTENCIA_SOCIAL ' from productos_servicios.concepto_grupo where concepto = 'RADIOLOGIA';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'SERVICIOS_SALUD_ASISTENCIA_SOCIAL ' from productos_servicios.concepto_grupo where concepto = 'IMAGENOLOGIA';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'SERVICIOS_SALUD_ASISTENCIA_SOCIAL ' from productos_servicios.concepto_grupo where concepto = 'OPTICA';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'COMERCIO_AL_MAYOR ó COMERCIO_AL_MENOR,  ' from productos_servicios.concepto_grupo where concepto = 'PAÑALES';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'COMERCIO_AL_MAYOR ó COMERCIO_AL_MENOR,  ' from productos_servicios.concepto_grupo where concepto = 'TOALLAS SANITARIAS';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'COMERCIO_AL_MAYOR ó COMERCIO_AL_MENOR,  ' from productos_servicios.concepto_grupo where concepto = 'SHAMPOO';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'COMERCIO_AL_MAYOR ó COMERCIO_AL_MENOR,  ' from productos_servicios.concepto_grupo where concepto = 'JABON';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'COMERCIO_AL_MAYOR ó COMERCIO_AL_MENOR,  ' from productos_servicios.concepto_grupo where concepto = 'DESODORANTE';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'COMERCIO_AL_MAYOR ó COMERCIO_AL_MENOR,  ' from productos_servicios.concepto_grupo where concepto = 'PASTA DE DIENTES';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'COMERCIO_AL_MAYOR ó COMERCIO_AL_MENOR,  ' from productos_servicios.concepto_grupo where concepto = 'ARTICULOS PERSONALES DE ASEO';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'COMERCIO_AL_MAYOR ó COMERCIO_AL_MENOR,  ' from productos_servicios.concepto_grupo where concepto = 'MATERIAL DE LIMPIEZA ';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'COMERCIO_AL_MAYOR ó COMERCIO_AL_MENOR,  ' from productos_servicios.concepto_grupo where concepto = 'MATERIAL DE LIMPIEZA ';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'COMERCIO_AL_MAYOR ' from productos_servicios.concepto_grupo where concepto = 'EQUIPO DE LIMPIEZA ';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'COMERCIO_AL_MENOR  ' from productos_servicios.concepto_grupo where concepto = 'EQUIPO DE LIMPIEZA ';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'SERVICIOS_PROFESIONALES_CIENTIFICOS_TECNICOS ' from productos_servicios.concepto_grupo where concepto = 'SERVICIO DE LIMPIEZA ';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'COMERCIO_AL_MAYOR ' from productos_servicios.concepto_grupo where concepto = 'MATERIAL DE SANITIZACION';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'COMERCIO_AL_MAYOR ó COMERCIO_AL_MENOR  ' from productos_servicios.concepto_grupo where concepto = 'EQUIPO DE SANITIZACION';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'SERVICIOS_PROFESIONALES_CIENTIFICOS_TECNICOS ' from productos_servicios.concepto_grupo where concepto = 'SERVICIO DE SANITIZACION';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'COMERCIO_AL_MAYOR ó COMERCIO_AL_MENOR  ' from productos_servicios.concepto_grupo where concepto = 'PRODUCTOS QUIMICOS';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'COMERCIO_AL_MAYOR ó COMERCIO_AL_MENOR  ' from productos_servicios.concepto_grupo where concepto = 'PRODUCTOS MINERALES';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'COMERCIO_AL_MAYOR ó COMERCIO_AL_MENOR  ' from productos_servicios.concepto_grupo where concepto = 'REACTIVOS';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'COMERCIO_AL_MAYOR ó COMERCIO_AL_MENOR  ' from productos_servicios.concepto_grupo where concepto = 'CARBON Y SUS DERIVADOS';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'COMERCIO_AL_MAYOR ó COMERCIO_AL_MENOR  ' from productos_servicios.concepto_grupo where concepto = 'GASES MEDICINALES';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'COMERCIO_AL_MAYOR ó COMERCIO_AL_MENOR  ' from productos_servicios.concepto_grupo where concepto = 'SUSTANCIAS Y MATERIALES EXPLOSIVOS';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'COMERCIO_AL_MAYOR ó COMERCIO_AL_MENOR  ' from productos_servicios.concepto_grupo where concepto = 'FUEGOS ARTIFICIALES / JUEGOS PIROTECNICOS';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'SERVICIOS_PROFESIONALES_CIENTIFICOS_TECNICOS ' from productos_servicios.concepto_grupo where concepto = 'MATERIAL Y EQUIPO DE FUMIGACION';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'SERVICIOS_PROFESIONALES_CIENTIFICOS_TECNICOS ' from productos_servicios.concepto_grupo where concepto = 'SERVICIOS DE FUMIGACION';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'AGRICULTURA_CRIA_EXPLOTACION_ANIMALES_A_PROV ' from productos_servicios.concepto_grupo where concepto = 'MATERIAL Y EQUIPO DE JARDINERIA ';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'COMERCIO_AL_MAYOR  ' from productos_servicios.concepto_grupo where concepto = 'MATERIAL Y EQUIPO DE JARDINERIA ';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'COMERCIO_AL_MENOR  ' from productos_servicios.concepto_grupo where concepto = 'MATERIAL Y EQUIPO DE JARDINERIA ';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'SERVICIOS_PROFESIONALES_CIENTIFICOS_TECNICOS ' from productos_servicios.concepto_grupo where concepto = 'SERVICIOS DE JARDINERIA ';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'COMERCIO_AL_MAYOR ' from productos_servicios.concepto_grupo where concepto = 'MAQUINARIA Y EQUIPO INDUSTRIAL';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'COMERCIO_AL_MENOR  ' from productos_servicios.concepto_grupo where concepto = 'MAQUINARIA Y EQUIPO INDUSTRIAL';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'SERVICIOS_PROFESIONALES_CIENTIFICOS_TECNICOS ' from productos_servicios.concepto_grupo where concepto = 'MANTENIMIENTO A MAQUINARIA Y EQUIPO INDUSTRIAL';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'CONSTRUCCION ' from productos_servicios.concepto_grupo where concepto = 'MATERIAL DE CONSTRUCCION';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'INDUSTRIAS_MANUFACTURERAS ' from productos_servicios.concepto_grupo where concepto = 'MATERIAL DE CONSTRUCCION';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'CONSTRUCCION ' from productos_servicios.concepto_grupo where concepto = 'EQUIPO DE CONSTRUCCION';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'SERVICIOS_PROFESIONALES_CIENTIFICOS_TECNICOS ' from productos_servicios.concepto_grupo where concepto = 'SERVICIOS DE CONSTRUCCION';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'CONSTRUCCION ' from productos_servicios.concepto_grupo where concepto = 'SERVICIOS DE CONSTRUCCION';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'COMERCIO_AL_MAYOR ' from productos_servicios.concepto_grupo where concepto = 'EQUIPO ELECTRONICO';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'COMERCIO_AL_MENOR  ' from productos_servicios.concepto_grupo where concepto = 'EQUIPO ELECTRONICO';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'COMERCIO_AL_MAYOR ' from productos_servicios.concepto_grupo where concepto = 'MATERIAL ELECTRONICO';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'COMERCIO_AL_MENOR  ' from productos_servicios.concepto_grupo where concepto = 'MATERIAL ELECTRONICO';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'SERVICIOS_PROFESIONALES_CIENTIFICOS_TECNICOS ' from productos_servicios.concepto_grupo where concepto = 'SERVICIOS ELECTRONICOS';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'COMERCIO_AL_MAYOR ' from productos_servicios.concepto_grupo where concepto = 'EQUIPO DE ENERGIA';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'COMERCIO_AL_MENOR  ' from productos_servicios.concepto_grupo where concepto = 'EQUIPO DE ENERGIA';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'SERVICIOS_PROFESIONALES_CIENTIFICOS_TECNICOS ' from productos_servicios.concepto_grupo where concepto = 'EQUIPO DE ENERGIA';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'SERVICIOS_PROFESIONALES_CIENTIFICOS_TECNICOS ' from productos_servicios.concepto_grupo where concepto = 'SERVICIOS DE ENERGIA';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'COMERCIO_AL_MAYOR ó COMERCIO_AL_MENOR  ' from productos_servicios.concepto_grupo where concepto = 'EQUIPOS DE AIRE ACONDICIONADO, CALEFACCION Y REFRIGERACION';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'SERVICIOS_PROFESIONALES_CIENTIFICOS_TECNICOS ' from productos_servicios.concepto_grupo where concepto = 'SERVICIOS DE CLIMAS, CALEFACCION Y REFRIGERACION';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'COMERCIO_AL_MAYOR ó COMERCIO_AL_MENOR  ' from productos_servicios.concepto_grupo where concepto = 'HERRAMIENTAS';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'COMERCIO_AL_MAYOR ó COMERCIO_AL_MENOR  ' from productos_servicios.concepto_grupo where concepto = 'MATERIAL DE CARPINTERIA';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'COMERCIO_AL_MAYOR ó COMERCIO_AL_MENOR  ' from productos_servicios.concepto_grupo where concepto = 'MATERIAL DE ALBAÑILERIA';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'COMERCIO_AL_MAYOR ó COMERCIO_AL_MENOR  ' from productos_servicios.concepto_grupo where concepto = 'HERRERIA';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'COMERCIO_AL_MAYOR ó COMERCIO_AL_MENOR  ' from productos_servicios.concepto_grupo where concepto = 'TORINILLOS';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'COMERCIO_AL_MAYOR ó COMERCIO_AL_MENOR  ' from productos_servicios.concepto_grupo where concepto = 'ALAMBRE';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'COMERCIO_AL_MAYOR ó COMERCIO_AL_MENOR  ' from productos_servicios.concepto_grupo where concepto = 'CONECTORES CONEXIONES';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'COMERCIO_AL_MAYOR ó COMERCIO_AL_MENOR  ' from productos_servicios.concepto_grupo where concepto = 'VALVULAS';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'COMERCIO_AL_MAYOR ó COMERCIO_AL_MENOR  ' from productos_servicios.concepto_grupo where concepto = 'TUBERIA';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'COMERCIO_AL_MAYOR ó COMERCIO_AL_MENOR  ' from productos_servicios.concepto_grupo where concepto = 'RECUBRIMIENTOS';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'COMERCIO_AL_MAYOR ó COMERCIO_AL_MENOR  ' from productos_servicios.concepto_grupo where concepto = 'IMPERMEABILIZANTE';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'COMERCIO_AL_MAYOR ó COMERCIO_AL_MENOR  ' from productos_servicios.concepto_grupo where concepto = 'PINTURA';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'COMERCIO_AL_MAYOR ó COMERCIO_AL_MENOR  ' from productos_servicios.concepto_grupo where concepto = 'PINTURA DE TRAFICO, DECORATIVA E INDUSTRIAL';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'COMERCIO_AL_MAYOR ' from productos_servicios.concepto_grupo where concepto = 'MATERIAL DE PLOMERIA';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'COMERCIO_AL_MENOR  ' from productos_servicios.concepto_grupo where concepto = 'MATERIAL DE PLOMERIA';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'SERVICIOS_PROFESIONALES_CIENTIFICOS_TECNICOS ' from productos_servicios.concepto_grupo where concepto = 'SERVICIOS DE PLOMERIA';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'SERV_INMOB_ALQUILER_BIENES_MUEBLES_E_INMUEBLES ' from productos_servicios.concepto_grupo where concepto = 'ARRENDAMIENTO DE INMUEBLES';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'SERVICIOS_PROFESIONALES_CIENTIFICOS_TECNICOS ' from productos_servicios.concepto_grupo where concepto = 'SERVICIOS FUNERARIOS ';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'SERVICIOS_PROFESIONALES_CIENTIFICOS_TECNICOS ' from productos_servicios.concepto_grupo where concepto = 'CEMENTERIOS';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'SEGUROS_FINANCIEROS_SEGUROS ó SERVICIOS_PROFESIONALES_CIENTIFICOS_TECNICOS ' from productos_servicios.concepto_grupo where concepto = 'SEGURO DE BIENES PATRIMONIALES';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'SEGUROS_FINANCIEROS_SEGUROS ó SERVICIOS_PROFESIONALES_CIENTIFICOS_TECNICOS ' from productos_servicios.concepto_grupo where concepto = 'SEGURO DE RESPONSABILIDAD PATRIMONIAL Y FIANZAS';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'SEGUROS_FINANCIEROS_SEGUROS ó SERVICIOS_PROFESIONALES_CIENTIFICOS_TECNICOS ' from productos_servicios.concepto_grupo where concepto = 'SEGURO MEDICO';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'SEGUROS_FINANCIEROS_SEGUROS ó SERVICIOS_PROFESIONALES_CIENTIFICOS_TECNICOS ' from productos_servicios.concepto_grupo where concepto = 'SEGURO DE VIDA';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'SEGUROS_FINANCIEROS_SEGUROS ó SERVICIOS_PROFESIONALES_CIENTIFICOS_TECNICOS ' from productos_servicios.concepto_grupo where concepto = 'AFIANZADORA';
insert into productos_servicios.concepto_grupo_inegi(concepto_grupo_id,concepto) select concepto_grupo_id, 'SEGUROS_FINANCIEROS_SEGUROS ó SERVICIOS_PROFESIONALES_CIENTIFICOS_TECNICOS ' from productos_servicios.concepto_grupo where concepto = 'EQUIPO BANCARIO';