insert into productos_servicios.concepto_familia(familia,concepto) values('1','OFICINA ');
insert into productos_servicios.concepto_familia(familia,concepto) values('2','PAPELERIA');
insert into productos_servicios.concepto_familia(familia,concepto) values('3','ESCOLAR');
insert into productos_servicios.concepto_familia(familia,concepto) values('4','ARTICULOS PATRIOS');
insert into productos_servicios.concepto_familia(familia,concepto) values('5','IMPRESOS ');
insert into productos_servicios.concepto_familia(familia,concepto) values('6','PUBLICIDAD');
insert into productos_servicios.concepto_familia(familia,concepto) values('7','ARTISTICO - CULTURAL');
insert into productos_servicios.concepto_familia(familia,concepto) values('8','MUSICA - ESPARCIMIENTO');
insert into productos_servicios.concepto_familia(familia,concepto) values('9','DEPORTES');
insert into productos_servicios.concepto_familia(familia,concepto) values('10','JUGUETES - JUEGOS');
insert into productos_servicios.concepto_familia(familia,concepto) values('11','JOYERIA');
insert into productos_servicios.concepto_familia(familia,concepto) values('12','BELLEZA');
insert into productos_servicios.concepto_familia(familia,concepto) values('13','TEXTILES ');
insert into productos_servicios.concepto_familia(familia,concepto) values('14','PLASTICO');
insert into productos_servicios.concepto_familia(familia,concepto) values('15','SERVICIOS PROFESIONALES');
insert into productos_servicios.concepto_familia(familia,concepto) values('16','EVENTOS');
insert into productos_servicios.concepto_familia(familia,concepto) values('17','AUDIO ');
insert into productos_servicios.concepto_familia(familia,concepto) values('18','VIDEO - FOTOGRAFIA');
insert into productos_servicios.concepto_familia(familia,concepto) values('19','ALIMENTOS');
insert into productos_servicios.concepto_familia(familia,concepto) values('20','BEBIDAS');
insert into productos_servicios.concepto_familia(familia,concepto) values('21','ANIMALES');
insert into productos_servicios.concepto_familia(familia,concepto) values('22','MOBILIARIO');
insert into productos_servicios.concepto_familia(familia,concepto) values('23','HOGAR ');
insert into productos_servicios.concepto_familia(familia,concepto) values('24','COCINA');
insert into productos_servicios.concepto_familia(familia,concepto) values('25','VIAJES');
insert into productos_servicios.concepto_familia(familia,concepto) values('26','VEHICULOS');
insert into productos_servicios.concepto_familia(familia,concepto) values('27','TRANSPORTE');
insert into productos_servicios.concepto_familia(familia,concepto) values('28','COMBUSTIBLES ');
insert into productos_servicios.concepto_familia(familia,concepto) values('29','LUBRICANTES');
insert into productos_servicios.concepto_familia(familia,concepto) values('30','COMPUTO');
insert into productos_servicios.concepto_familia(familia,concepto) values('31','TELECOMUNICACIONES-COMUNICACIÓN');
insert into productos_servicios.concepto_familia(familia,concepto) values('32','TELEFONIA');
insert into productos_servicios.concepto_familia(familia,concepto) values('33','REDES');
insert into productos_servicios.concepto_familia(familia,concepto) values('34','SEGURIDAD ');
insert into productos_servicios.concepto_familia(familia,concepto) values('35','MEDICO ');
insert into productos_servicios.concepto_familia(familia,concepto) values('36','SERVICOS DE SALUD');
insert into productos_servicios.concepto_familia(familia,concepto) values('37','CUIDADO PERSONAL');
insert into productos_servicios.concepto_familia(familia,concepto) values('38','LIMPIEZA ');
insert into productos_servicios.concepto_familia(familia,concepto) values('39','SANITIZACION');
insert into productos_servicios.concepto_familia(familia,concepto) values('40','QUIMICOS - SUSTANCIAS ');
insert into productos_servicios.concepto_familia(familia,concepto) values('41','EXPLOSIVOS');
insert into productos_servicios.concepto_familia(familia,concepto) values('42','FUMIGACION');
insert into productos_servicios.concepto_familia(familia,concepto) values('43','JARDINERIA ');
insert into productos_servicios.concepto_familia(familia,concepto) values('44','INDUSTRIAL');
insert into productos_servicios.concepto_familia(familia,concepto) values('45','CONSTRUCCION');
insert into productos_servicios.concepto_familia(familia,concepto) values('46','ELECTRICO ');
insert into productos_servicios.concepto_familia(familia,concepto) values('47','ELECTRONICOS');
insert into productos_servicios.concepto_familia(familia,concepto) values('48','ENERGIA');
insert into productos_servicios.concepto_familia(familia,concepto) values('49','AIRE ACONDICIONADO - CALEFACCION - REFRIGERACION');
insert into productos_servicios.concepto_familia(familia,concepto) values('50','FERRETERIA - TLAPALERIA');
insert into productos_servicios.concepto_familia(familia,concepto) values('51','PLOMERIA ');
insert into productos_servicios.concepto_familia(familia,concepto) values('52','ARRENDAMIENTO');
insert into productos_servicios.concepto_familia(familia,concepto) values('53','FUNERARIA');
insert into productos_servicios.concepto_familia(familia,concepto) values('54','SEGUROS ');
insert into productos_servicios.concepto_familia(familia,concepto) values('55','BANCARIO');
insert into productos_servicios.concepto_familia(familia,concepto) values('56','OTROS');


insert into productos_servicios.concepto_familia_inegi(concepto_familia_id,concepto)  select concepto_familia_id,'COMERCIO_AL_MAYOR ' from productos_servicios.concepto_familia where concepto = 'OFICINA ';
insert into productos_servicios.concepto_familia_inegi(concepto_familia_id,concepto)  select concepto_familia_id,'COMERCIO_AL_MENOR ' from productos_servicios.concepto_familia where concepto = 'OFICINA ';
insert into productos_servicios.concepto_familia_inegi(concepto_familia_id,concepto)  select concepto_familia_id,'INDUSTRIAS_MANUFACTURERAS' from productos_servicios.concepto_familia where concepto = 'OFICINA ';
insert into productos_servicios.concepto_familia_inegi(concepto_familia_id,concepto)  select concepto_familia_id,'COMERCIO_AL_MAYOR ' from productos_servicios.concepto_familia where concepto = 'PAPELERIA';
insert into productos_servicios.concepto_familia_inegi(concepto_familia_id,concepto)  select concepto_familia_id,'COMERCIO_AL_MENOR ' from productos_servicios.concepto_familia where concepto = 'PAPELERIA';
insert into productos_servicios.concepto_familia_inegi(concepto_familia_id,concepto)  select concepto_familia_id,'INDUSTRIAS_MANUFACTURERAS' from productos_servicios.concepto_familia where concepto = 'PAPELERIA';
insert into productos_servicios.concepto_familia_inegi(concepto_familia_id,concepto)  select concepto_familia_id,'COMERCIO_AL_MAYOR ' from productos_servicios.concepto_familia where concepto = 'ESCOLAR';
insert into productos_servicios.concepto_familia_inegi(concepto_familia_id,concepto)  select concepto_familia_id,'COMERCIO_AL_MENOR ' from productos_servicios.concepto_familia where concepto = 'ESCOLAR';
insert into productos_servicios.concepto_familia_inegi(concepto_familia_id,concepto)  select concepto_familia_id,'SERV_ESPARCIMIENTO_CULTURALES_DEPORTIVOS_O' from productos_servicios.concepto_familia where concepto = 'ARTICULOS PATRIOS';
insert into productos_servicios.concepto_familia_inegi(concepto_familia_id,concepto)  select concepto_familia_id,'COMERCIO_AL_MAYOR ' from productos_servicios.concepto_familia where concepto = 'IMPRESOS ';
insert into productos_servicios.concepto_familia_inegi(concepto_familia_id,concepto)  select concepto_familia_id,'COMERCIO_AL_MENOR ' from productos_servicios.concepto_familia where concepto = 'IMPRESOS ';
insert into productos_servicios.concepto_familia_inegi(concepto_familia_id,concepto)  select concepto_familia_id,'INDUSTRIAS_MANUFACTURERAS' from productos_servicios.concepto_familia where concepto = 'IMPRESOS ';
insert into productos_servicios.concepto_familia_inegi(concepto_familia_id,concepto)  select concepto_familia_id,'COMERCIO_AL_MAYOR ' from productos_servicios.concepto_familia where concepto = 'PUBLICIDAD';
insert into productos_servicios.concepto_familia_inegi(concepto_familia_id,concepto)  select concepto_familia_id,'COMERCIO_AL_MENOR ' from productos_servicios.concepto_familia where concepto = 'PUBLICIDAD';
insert into productos_servicios.concepto_familia_inegi(concepto_familia_id,concepto)  select concepto_familia_id,'INDUSTRIAS_MANUFACTURERAS' from productos_servicios.concepto_familia where concepto = 'PUBLICIDAD';
insert into productos_servicios.concepto_familia_inegi(concepto_familia_id,concepto)  select concepto_familia_id,'INFORMACIÓN_MEDIOS_MASIVOS' from productos_servicios.concepto_familia where concepto = 'PUBLICIDAD';
insert into productos_servicios.concepto_familia_inegi(concepto_familia_id,concepto)  select concepto_familia_id,'SERV_ESPARCIMIENTO_CULTURALES_DEPORTIVOS_O' from productos_servicios.concepto_familia where concepto = 'ARTISTICO - CULTURAL';
insert into productos_servicios.concepto_familia_inegi(concepto_familia_id,concepto)  select concepto_familia_id,'SERVICIOS_PROFESIONALES_CIENTIFICOS_TECNICOS' from productos_servicios.concepto_familia where concepto = 'ARTISTICO - CULTURAL';
insert into productos_servicios.concepto_familia_inegi(concepto_familia_id,concepto)  select concepto_familia_id,'SERV_ESPARCIMIENTO_CULTURALES_DEPORTIVOS_O' from productos_servicios.concepto_familia where concepto = 'MUSICA - ESPARCIMIENTO';
insert into productos_servicios.concepto_familia_inegi(concepto_familia_id,concepto)  select concepto_familia_id,'SERV_ESPARCIMIENTO_CULTURALES_DEPORTIVOS_O' from productos_servicios.concepto_familia where concepto = 'DEPORTES';
insert into productos_servicios.concepto_familia_inegi(concepto_familia_id,concepto)  select concepto_familia_id,'COMERCIO_AL_MAYOR ' from productos_servicios.concepto_familia where concepto = 'JUGUETES - JUEGOS';
insert into productos_servicios.concepto_familia_inegi(concepto_familia_id,concepto)  select concepto_familia_id,'COMERCIO_AL_MENOR ' from productos_servicios.concepto_familia where concepto = 'JUGUETES - JUEGOS';
insert into productos_servicios.concepto_familia_inegi(concepto_familia_id,concepto)  select concepto_familia_id,'COMERCIO_AL_MAYOR ' from productos_servicios.concepto_familia where concepto = 'BELLEZA';
insert into productos_servicios.concepto_familia_inegi(concepto_familia_id,concepto)  select concepto_familia_id,'COMERCIO_AL_MENOR ' from productos_servicios.concepto_familia where concepto = 'BELLEZA';
insert into productos_servicios.concepto_familia_inegi(concepto_familia_id,concepto)  select concepto_familia_id,'INDUSTRIAS_MANUFACTURERAS' from productos_servicios.concepto_familia where concepto = 'BELLEZA';
insert into productos_servicios.concepto_familia_inegi(concepto_familia_id,concepto)  select concepto_familia_id,'COMERCIO_AL_MAYOR ' from productos_servicios.concepto_familia where concepto = 'TEXTILES ';
insert into productos_servicios.concepto_familia_inegi(concepto_familia_id,concepto)  select concepto_familia_id,'COMERCIO_AL_MENOR ' from productos_servicios.concepto_familia where concepto = 'TEXTILES ';
insert into productos_servicios.concepto_familia_inegi(concepto_familia_id,concepto)  select concepto_familia_id,'INDUSTRIAS_MANUFACTURERAS' from productos_servicios.concepto_familia where concepto = 'TEXTILES ';
insert into productos_servicios.concepto_familia_inegi(concepto_familia_id,concepto)  select concepto_familia_id,'COMERCIO_AL_MAYOR' from productos_servicios.concepto_familia where concepto = 'PLASTICO';
insert into productos_servicios.concepto_familia_inegi(concepto_familia_id,concepto)  select concepto_familia_id,'COMERCIO_AL_MENOR ' from productos_servicios.concepto_familia where concepto = 'PLASTICO';
insert into productos_servicios.concepto_familia_inegi(concepto_familia_id,concepto)  select concepto_familia_id,'SERVICIOS_PROFESIONALES_CIENTIFICOS_TECNICOS' from productos_servicios.concepto_familia where concepto = 'SERVICIOS PROFESIONALES';
insert into productos_servicios.concepto_familia_inegi(concepto_familia_id,concepto)  select concepto_familia_id,'SERV_ESPARCIMIENTO_CULTURALES_DEPORTIVOS_O' from productos_servicios.concepto_familia where concepto = 'EVENTOS';
insert into productos_servicios.concepto_familia_inegi(concepto_familia_id,concepto)  select concepto_familia_id,'SERVICIOS_PROFESIONALES_CIENTIFICOS_TECNICOS' from productos_servicios.concepto_familia where concepto = 'EVENTOS';
insert into productos_servicios.concepto_familia_inegi(concepto_familia_id,concepto)  select concepto_familia_id,'COMERCIO_AL_MAYOR' from productos_servicios.concepto_familia where concepto = 'EVENTOS';
insert into productos_servicios.concepto_familia_inegi(concepto_familia_id,concepto)  select concepto_familia_id,'COMERCIO_AL_MENOR ' from productos_servicios.concepto_familia where concepto = 'EVENTOS';
insert into productos_servicios.concepto_familia_inegi(concepto_familia_id,concepto)  select concepto_familia_id,'SERV_ALOJAMIENTO_TEMP_PREP_ALIM_BEBI' from productos_servicios.concepto_familia where concepto = 'EVENTOS';
insert into productos_servicios.concepto_familia_inegi(concepto_familia_id,concepto)  select concepto_familia_id,'SERV_INMOB_ALQUILER_BIENES_MUEBLES_E_INMUEBLES' from productos_servicios.concepto_familia where concepto = 'EVENTOS';
insert into productos_servicios.concepto_familia_inegi(concepto_familia_id,concepto)  select concepto_familia_id,'COMERCIO_AL_MAYOR ó COMERCIO_AL_MENOR' from productos_servicios.concepto_familia where concepto = 'AUDIO ';
insert into productos_servicios.concepto_familia_inegi(concepto_familia_id,concepto)  select concepto_familia_id,'SERVICIOS_PROFESIONALES_CIENTIFICOS_TECNICOS' from productos_servicios.concepto_familia where concepto = 'AUDIO ';
insert into productos_servicios.concepto_familia_inegi(concepto_familia_id,concepto)  select concepto_familia_id,'COMERCIO_AL_MAYOR ó COMERCIO_AL_MENOR' from productos_servicios.concepto_familia where concepto = 'VIDEO - FOTOGRAFIA';
insert into productos_servicios.concepto_familia_inegi(concepto_familia_id,concepto)  select concepto_familia_id,'SERVICIOS_PROFESIONALES_CIENTIFICOS_TECNICOS' from productos_servicios.concepto_familia where concepto = 'ALIMENTOS';
insert into productos_servicios.concepto_familia_inegi(concepto_familia_id,concepto)  select concepto_familia_id,'COMERCIO_AL_MAYOR' from productos_servicios.concepto_familia where concepto = 'ALIMENTOS';
insert into productos_servicios.concepto_familia_inegi(concepto_familia_id,concepto)  select concepto_familia_id,'COMERCIO_AL_MENOR ' from productos_servicios.concepto_familia where concepto = 'ALIMENTOS';
insert into productos_servicios.concepto_familia_inegi(concepto_familia_id,concepto)  select concepto_familia_id,'SERV_ALOJAMIENTO_TEMP_PREP_ALIM_BEBI' from productos_servicios.concepto_familia where concepto = 'ALIMENTOS';
insert into productos_servicios.concepto_familia_inegi(concepto_familia_id,concepto)  select concepto_familia_id,'COMERCIO_AL_MAYOR' from productos_servicios.concepto_familia where concepto = 'BEBIDAS';
insert into productos_servicios.concepto_familia_inegi(concepto_familia_id,concepto)  select concepto_familia_id,'COMERCIO_AL_MENOR ' from productos_servicios.concepto_familia where concepto = 'BEBIDAS';
insert into productos_servicios.concepto_familia_inegi(concepto_familia_id,concepto)  select concepto_familia_id,'AGRICULTURA_CRIA_EXPLOTACION_ANIMALES_A_PROV' from productos_servicios.concepto_familia where concepto = 'ANIMALES';
insert into productos_servicios.concepto_familia_inegi(concepto_familia_id,concepto)  select concepto_familia_id,'COMERCIO_AL_MAYOR' from productos_servicios.concepto_familia where concepto = 'ANIMALES';
insert into productos_servicios.concepto_familia_inegi(concepto_familia_id,concepto)  select concepto_familia_id,'COMERCIO_AL_MENOR ' from productos_servicios.concepto_familia where concepto = 'ANIMALES';
insert into productos_servicios.concepto_familia_inegi(concepto_familia_id,concepto)  select concepto_familia_id,'COMERCIO_AL_MAYOR ó COMERCIO_AL_MENOR' from productos_servicios.concepto_familia where concepto = 'MOBILIARIO';
insert into productos_servicios.concepto_familia_inegi(concepto_familia_id,concepto)  select concepto_familia_id,'INDUSTRIAS_MANUFACTURERAS' from productos_servicios.concepto_familia where concepto = 'MOBILIARIO';
insert into productos_servicios.concepto_familia_inegi(concepto_familia_id,concepto)  select concepto_familia_id,'COMERCIO_AL_MAYOR' from productos_servicios.concepto_familia where concepto = 'HOGAR ';
insert into productos_servicios.concepto_familia_inegi(concepto_familia_id,concepto)  select concepto_familia_id,'COMERCIO_AL_MENOR' from productos_servicios.concepto_familia where concepto = 'HOGAR ';
insert into productos_servicios.concepto_familia_inegi(concepto_familia_id,concepto)  select concepto_familia_id,'COMERCIO_AL_MAYOR' from productos_servicios.concepto_familia where concepto = 'COCINA';
insert into productos_servicios.concepto_familia_inegi(concepto_familia_id,concepto)  select concepto_familia_id,'COMERCIO_AL_MENOR' from productos_servicios.concepto_familia where concepto = 'COCINA';
insert into productos_servicios.concepto_familia_inegi(concepto_familia_id,concepto)  select concepto_familia_id,'COMERCIO_AL_MAYOR' from productos_servicios.concepto_familia where concepto = 'VIAJES';
insert into productos_servicios.concepto_familia_inegi(concepto_familia_id,concepto)  select concepto_familia_id,'COMERCIO_AL_MENOR' from productos_servicios.concepto_familia where concepto = 'VIAJES';
insert into productos_servicios.concepto_familia_inegi(concepto_familia_id,concepto)  select concepto_familia_id,'TRANSPORTES_CORREOS_ALMACENAMIENTO' from productos_servicios.concepto_familia where concepto = 'VEHICULOS';
insert into productos_servicios.concepto_familia_inegi(concepto_familia_id,concepto)  select concepto_familia_id,'TRANSPORTES_CORREOS_ALMACENAMIENTO' from productos_servicios.concepto_familia where concepto = 'TRANSPORTE';
insert into productos_servicios.concepto_familia_inegi(concepto_familia_id,concepto)  select concepto_familia_id,'COMERCIO_AL_MAYOR' from productos_servicios.concepto_familia where concepto = 'COMBUSTIBLES ';
insert into productos_servicios.concepto_familia_inegi(concepto_familia_id,concepto)  select concepto_familia_id,'COMERCIO_AL_MENOR ' from productos_servicios.concepto_familia where concepto = 'COMBUSTIBLES ';
insert into productos_servicios.concepto_familia_inegi(concepto_familia_id,concepto)  select concepto_familia_id,'COMERCIO_AL_MAYOR' from productos_servicios.concepto_familia where concepto = 'LUBRICANTES';
insert into productos_servicios.concepto_familia_inegi(concepto_familia_id,concepto)  select concepto_familia_id,'COMERCIO_AL_MENOR ' from productos_servicios.concepto_familia where concepto = 'LUBRICANTES';
insert into productos_servicios.concepto_familia_inegi(concepto_familia_id,concepto)  select concepto_familia_id,'COMERCIO_AL_MAYOR ' from productos_servicios.concepto_familia where concepto = 'COMPUTO';
insert into productos_servicios.concepto_familia_inegi(concepto_familia_id,concepto)  select concepto_familia_id,' COMERCIO_AL_MENOR ' from productos_servicios.concepto_familia where concepto = 'COMPUTO';
insert into productos_servicios.concepto_familia_inegi(concepto_familia_id,concepto)  select concepto_familia_id,'INDUSTRIAS_MANUFACTURERAS' from productos_servicios.concepto_familia where concepto = 'COMPUTO';
insert into productos_servicios.concepto_familia_inegi(concepto_familia_id,concepto)  select concepto_familia_id,'COMERCIO_AL_MAYOR ' from productos_servicios.concepto_familia where concepto = 'TELECOMUNICACIONES-COMUNICACIÓN';
insert into productos_servicios.concepto_familia_inegi(concepto_familia_id,concepto)  select concepto_familia_id,' COMERCIO_AL_MENOR ' from productos_servicios.concepto_familia where concepto = 'TELECOMUNICACIONES-COMUNICACIÓN';
insert into productos_servicios.concepto_familia_inegi(concepto_familia_id,concepto)  select concepto_familia_id,'INDUSTRIAS_MANUFACTURERAS' from productos_servicios.concepto_familia where concepto = 'TELECOMUNICACIONES-COMUNICACIÓN';
insert into productos_servicios.concepto_familia_inegi(concepto_familia_id,concepto)  select concepto_familia_id,'SERVICIOS_PROFESIONALES_CIENTIFICOS_TECNICOS' from productos_servicios.concepto_familia where concepto = 'TELECOMUNICACIONES-COMUNICACIÓN';
insert into productos_servicios.concepto_familia_inegi(concepto_familia_id,concepto)  select concepto_familia_id,'INFORMACIÓN_MEDIOS_MASIVOS' from productos_servicios.concepto_familia where concepto = 'TELECOMUNICACIONES-COMUNICACIÓN';
insert into productos_servicios.concepto_familia_inegi(concepto_familia_id,concepto)  select concepto_familia_id,'COMERCIO_AL_MAYOR ó COMERCIO_AL_MENOR' from productos_servicios.concepto_familia where concepto = 'TELEFONIA';
insert into productos_servicios.concepto_familia_inegi(concepto_familia_id,concepto)  select concepto_familia_id,'INDUSTRIAS_MANUFACTURERAS' from productos_servicios.concepto_familia where concepto = 'TELEFONIA';
insert into productos_servicios.concepto_familia_inegi(concepto_familia_id,concepto)  select concepto_familia_id,'COMERCIO_AL_MAYOR ó COMERCIO_AL_MENOR' from productos_servicios.concepto_familia where concepto = 'REDES';
insert into productos_servicios.concepto_familia_inegi(concepto_familia_id,concepto)  select concepto_familia_id,'SERVICIOS_PROFESIONALES_CIENTIFICOS_TECNICOS' from productos_servicios.concepto_familia where concepto = 'REDES';
insert into productos_servicios.concepto_familia_inegi(concepto_familia_id,concepto)  select concepto_familia_id,'COMERCIO_AL_MAYOR ' from productos_servicios.concepto_familia where concepto = 'SEGURIDAD ';
insert into productos_servicios.concepto_familia_inegi(concepto_familia_id,concepto)  select concepto_familia_id,'COMERCIO_AL_MENOR' from productos_servicios.concepto_familia where concepto = 'SEGURIDAD ';
insert into productos_servicios.concepto_familia_inegi(concepto_familia_id,concepto)  select concepto_familia_id,'SERVICIOS_SALUD_ASISTENCIA_SOCIAL' from productos_servicios.concepto_familia where concepto = 'MEDICO ';
insert into productos_servicios.concepto_familia_inegi(concepto_familia_id,concepto)  select concepto_familia_id,'COMERCIO_AL_MAYOR ' from productos_servicios.concepto_familia where concepto = 'MEDICO ';
insert into productos_servicios.concepto_familia_inegi(concepto_familia_id,concepto)  select concepto_familia_id,'COMERCIO_AL_MENOR ' from productos_servicios.concepto_familia where concepto = 'MEDICO ';
insert into productos_servicios.concepto_familia_inegi(concepto_familia_id,concepto)  select concepto_familia_id,'INDUSTRIAS_MANUFACTURERAS' from productos_servicios.concepto_familia where concepto = 'MEDICO ';
insert into productos_servicios.concepto_familia_inegi(concepto_familia_id,concepto)  select concepto_familia_id,'COMERCIO_AL_MAYOR ' from productos_servicios.concepto_familia where concepto = 'CUIDADO PERSONAL';
insert into productos_servicios.concepto_familia_inegi(concepto_familia_id,concepto)  select concepto_familia_id,'COMERCIO_AL_MENOR ' from productos_servicios.concepto_familia where concepto = 'CUIDADO PERSONAL';
insert into productos_servicios.concepto_familia_inegi(concepto_familia_id,concepto)  select concepto_familia_id,'COMERCIO_AL_MAYOR' from productos_servicios.concepto_familia where concepto = 'LIMPIEZA ';
insert into productos_servicios.concepto_familia_inegi(concepto_familia_id,concepto)  select concepto_familia_id,'COMERCIO_AL_MENOR ' from productos_servicios.concepto_familia where concepto = 'LIMPIEZA ';
insert into productos_servicios.concepto_familia_inegi(concepto_familia_id,concepto)  select concepto_familia_id,'COMERCIO_AL_MAYOR' from productos_servicios.concepto_familia where concepto = 'SANITIZACION';
insert into productos_servicios.concepto_familia_inegi(concepto_familia_id,concepto)  select concepto_familia_id,'COMERCIO_AL_MENOR ' from productos_servicios.concepto_familia where concepto = 'SANITIZACION';
insert into productos_servicios.concepto_familia_inegi(concepto_familia_id,concepto)  select concepto_familia_id,'COMERCIO_AL_MAYOR' from productos_servicios.concepto_familia where concepto = 'QUIMICOS - SUSTANCIAS ';
insert into productos_servicios.concepto_familia_inegi(concepto_familia_id,concepto)  select concepto_familia_id,'COMERCIO_AL_MENOR ' from productos_servicios.concepto_familia where concepto = 'QUIMICOS - SUSTANCIAS ';
insert into productos_servicios.concepto_familia_inegi(concepto_familia_id,concepto)  select concepto_familia_id,'COMERCIO_AL_MAYOR' from productos_servicios.concepto_familia where concepto = 'EXPLOSIVOS';
insert into productos_servicios.concepto_familia_inegi(concepto_familia_id,concepto)  select concepto_familia_id,'COMERCIO_AL_MENOR ' from productos_servicios.concepto_familia where concepto = 'EXPLOSIVOS';
insert into productos_servicios.concepto_familia_inegi(concepto_familia_id,concepto)  select concepto_familia_id,'AGRICULTURA_CRIA_EXPLOTACION_ANIMALES_A_PROV' from productos_servicios.concepto_familia where concepto = 'FUMIGACION';
insert into productos_servicios.concepto_familia_inegi(concepto_familia_id,concepto)  select concepto_familia_id,'AGRICULTURA_CRIA_EXPLOTACION_ANIMALES_A_PROV' from productos_servicios.concepto_familia where concepto = 'JARDINERIA ';
insert into productos_servicios.concepto_familia_inegi(concepto_familia_id,concepto)  select concepto_familia_id,'COMERCIO_AL_MAYOR' from productos_servicios.concepto_familia where concepto = 'INDUSTRIAL';
insert into productos_servicios.concepto_familia_inegi(concepto_familia_id,concepto)  select concepto_familia_id,'COMERCIO_AL_MENOR ' from productos_servicios.concepto_familia where concepto = 'INDUSTRIAL';
insert into productos_servicios.concepto_familia_inegi(concepto_familia_id,concepto)  select concepto_familia_id,'CONSTRUCCION' from productos_servicios.concepto_familia where concepto = 'CONSTRUCCION';
insert into productos_servicios.concepto_familia_inegi(concepto_familia_id,concepto)  select concepto_familia_id,'INDUSTRIAS_MANUFACTURERAS' from productos_servicios.concepto_familia where concepto = 'CONSTRUCCION';
insert into productos_servicios.concepto_familia_inegi(concepto_familia_id,concepto)  select concepto_familia_id,'COMERCIO_AL_MAYOR' from productos_servicios.concepto_familia where concepto = 'ELECTRICO ';
insert into productos_servicios.concepto_familia_inegi(concepto_familia_id,concepto)  select concepto_familia_id,'COMERCIO_AL_MENOR ' from productos_servicios.concepto_familia where concepto = 'ELECTRICO ';
insert into productos_servicios.concepto_familia_inegi(concepto_familia_id,concepto)  select concepto_familia_id,'COMERCIO_AL_MAYOR' from productos_servicios.concepto_familia where concepto = 'ELECTRONICOS';
insert into productos_servicios.concepto_familia_inegi(concepto_familia_id,concepto)  select concepto_familia_id,'COMERCIO_AL_MENOR ' from productos_servicios.concepto_familia where concepto = 'ELECTRONICOS';
insert into productos_servicios.concepto_familia_inegi(concepto_familia_id,concepto)  select concepto_familia_id,'COMERCIO_AL_MAYOR' from productos_servicios.concepto_familia where concepto = 'ENERGIA';
insert into productos_servicios.concepto_familia_inegi(concepto_familia_id,concepto)  select concepto_familia_id,'COMERCIO_AL_MENOR ' from productos_servicios.concepto_familia where concepto = 'ENERGIA';
insert into productos_servicios.concepto_familia_inegi(concepto_familia_id,concepto)  select concepto_familia_id,'SERVICIOS_PROFESIONALES_CIENTIFICOS_TECNICOS' from productos_servicios.concepto_familia where concepto = 'ENERGIA';
insert into productos_servicios.concepto_familia_inegi(concepto_familia_id,concepto)  select concepto_familia_id,'COMERCIO_AL_MAYOR' from productos_servicios.concepto_familia where concepto = 'AIRE ACONDICIONADO - CALEFACCION - REFRIGERACION';
insert into productos_servicios.concepto_familia_inegi(concepto_familia_id,concepto)  select concepto_familia_id,'COMERCIO_AL_MENOR ' from productos_servicios.concepto_familia where concepto = 'AIRE ACONDICIONADO - CALEFACCION - REFRIGERACION';
insert into productos_servicios.concepto_familia_inegi(concepto_familia_id,concepto)  select concepto_familia_id,'COMERCIO_AL_MAYOR' from productos_servicios.concepto_familia where concepto = 'FERRETERIA - TLAPALERIA';
insert into productos_servicios.concepto_familia_inegi(concepto_familia_id,concepto)  select concepto_familia_id,'COMERCIO_AL_MENOR ' from productos_servicios.concepto_familia where concepto = 'FERRETERIA - TLAPALERIA';
insert into productos_servicios.concepto_familia_inegi(concepto_familia_id,concepto)  select concepto_familia_id,'COMERCIO_AL_MAYOR' from productos_servicios.concepto_familia where concepto = 'PLOMERIA ';
insert into productos_servicios.concepto_familia_inegi(concepto_familia_id,concepto)  select concepto_familia_id,'COMERCIO_AL_MENOR ' from productos_servicios.concepto_familia where concepto = 'PLOMERIA ';
insert into productos_servicios.concepto_familia_inegi(concepto_familia_id,concepto)  select concepto_familia_id,'SERVICIOS_PROFESIONALES_CIENTIFICOS_TECNICOS' from productos_servicios.concepto_familia where concepto = 'PLOMERIA ';
insert into productos_servicios.concepto_familia_inegi(concepto_familia_id,concepto)  select concepto_familia_id,'SERV_INMOB_ALQUILER_BIENES_MUEBLES_E_INMUEBLES' from productos_servicios.concepto_familia where concepto = 'ARRENDAMIENTO';
insert into productos_servicios.concepto_familia_inegi(concepto_familia_id,concepto)  select concepto_familia_id,'SERVICIOS_PROFESIONALES_CIENTIFICOS_TECNICOS' from productos_servicios.concepto_familia where concepto = 'FUNERARIA';
insert into productos_servicios.concepto_familia_inegi(concepto_familia_id,concepto)  select concepto_familia_id,'SEGUROS_FINANCIEROS_SEGUROS' from productos_servicios.concepto_familia where concepto = 'SEGUROS ';
insert into productos_servicios.concepto_familia_inegi(concepto_familia_id,concepto)  select concepto_familia_id,'SERVICIOS_PROFESIONALES_CIENTIFICOS_TECNICOS' from productos_servicios.concepto_familia where concepto = 'SEGUROS ';